const TextFieldComponentforLocation = Formio.Components.components.textfield;
import { LocationEditForm } from './location.editForm.ts';
export default class LocationComponent extends TextFieldComponentforLocation {

    constructor(component, options, data) {
        super(component, options, data);
        // console.log(options);
    }

    static schema(...extend) {
        return TextFieldComponentforLocation.schema({
            type: 'location',
            label: 'Location',
            input: true,
            key: 'textfield',
            inputType: 'text',
            inputFormat: 'plain',
            disabled: true
        }, ...extend);
    }

    static get builderInfo() {
        return {
            title: 'Location',
            group: 'premium',
            icon: 'map-marker',
            weight: 50,
            documentation: '/userguide/#location',
            schema: LocationComponent.schema()
        };
    }

    static addTemplate(name, template) {
        Templates.templates[name] = template;
    }

    get defaultSchema() {
        return LocationComponent.schema();
    }

    get inputInfo() {
        const info = super.inputInfo;
        info.type = 'input';
        if (this.component.hasOwnProperty('spellcheck')) {
            info.attr.spellcheck = this.component.spellcheck;
        }

        if (this.component.mask) {
            info.attr.type = 'password';
        } else {
            info.attr.type = (this.component.inputType === 'password') ? 'password' : 'text';
        }
        info.changeEvent = 'input';
        return info;
    }

    get prefix() {
        if (this.component.type && this.component.type === 'location') {
            const locationIcon = this.renderTemplate('icon', {
                ref: 'icon',
                className: this.iconClass('map-marker'),
                styles: '',
                content: ''
            }).trim();
            return locationIcon;
        }
    }

    get emptyValue() {
        return '';
    }

    maskValue(value, flags = {}) {
        if (!value || (typeof value !== 'object')) {
            value = {
                value,
                maskName: this.component.inputMasks[0].label
            };
        }

        // If no value is provided, then set the defaultValue.
        if (!value.value) {
            const defaultValue = flags.noDefault ? this.emptyValue : this.defaultValue;
            value.value = Array.isArray(defaultValue) ? defaultValue[0] : defaultValue;
        }

        return value;
    }

    normalizeValue(value, flags = {}) {
        if (!this.isMultipleMasksField) {
            return super.normalizeValue(value);
        }
        if (Array.isArray(value)) {
            return super.normalizeValue(value.map((val) => this.maskValue(val, flags)));
        }
        return super.normalizeValue(this.maskValue(value, flags));
    }

    setValueAt(index, value, flags = {}) {
        if (!this.isMultipleMasksField) {
            return super.setValueAt(index, value, flags);
        }
        value = this.maskValue(value, flags);
        const textValue = value.value || '';
        const textInput = this.refs.mask ? this.refs.mask[index] : null;
        const maskInput = this.refs.select ? this.refs.select[index] : null;
        const mask = this.getMaskPattern(value.maskName);
        if (textInput && maskInput && mask) {
            textInput.value = conformToMask(textValue, FormioUtils.getInputMask(mask)).conformedValue;
            maskInput.value = value.maskName;
        } else {
            return super.setValueAt(index, textValue, flags);
        }
    }

    getValueAt(index) {
        if (!this.isMultipleMasksField) {
            return super.getValueAt(index);
        }
        const textInput = this.refs.mask ? this.refs.mask[index] : null;
        const maskInput = this.refs.select ? this.refs.select[index] : null;
        return {
            value: textInput ? textInput.value : undefined,
            maskName: maskInput ? maskInput.value : undefined
        };
    }

    isEmpty(value = this.dataValue) {
        if (!this.isMultipleMasksField) {
            return super.isEmpty((value || '').toString().trim());
        }
        return super.isEmpty(value) || (this.component.multiple ? value.length === 0 : (!value.maskName || !value.value));
    }

    render() {
        return super.render(
            this.renderTemplate('input', {
                prefix: this.prefix,
                input: this.inputInfo,
            })
        );
    }

    attach(element) {
        this.loadRefs(element, {
            prefix: 'icon'
        });
        this.input = this.refs.prefix[0];
        // let componentObj = this;
        if (this.input) {
            this.addEventListener(this.input, 'click', (e) => {
                console.log("location clicked");
                console.log("before executing fetch api");
                // return new Promise((resolve, reject) => {
                if ("geolocation" in navigator) {
                    // Get the user's current location
                    navigator.geolocation.getCurrentPosition((position) => {
                        const latitude = position.coords.latitude;
                        const longitude = position.coords.longitude;
                        var location = "";
                        location = 'latitude = ' + latitude + ', longitude = ' + longitude;
                        location = `${latitude.toString()}, ${longitude.toString()}`;
                        this.setValue(location);
                    }, (error) => {
                        // Handle any errors that occur when trying to obtain the location
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                console.error("User denied the request for Geolocation.");
                                break;
                            case error.POSITION_UNAVAILABLE:
                                console.error("Location information is unavailable.");
                                break;
                            case error.TIMEOUT:
                                console.error("The request to get user location timed out.");
                                break;
                            case error.UNKNOWN_ERROR:
                                console.error("An unknown error occurred.");
                                break;
                        }
                    });
                } else {
                    console.log("Geolocation is not available in this browser.");
                }
                // Check if the Geolocation API is available in the browser
            });
            // });
        }

        this.addShortcut(this.input);
        return super.attach(element);

    }
}
LocationComponent.editForm = LocationEditForm;
// Register the component to the Formio.Components registry.
Formio.Components.addComponent("location", LocationComponent);
