
const field = Formio.Components.components.field;
import { SmartTableEditForm } from './smart-table.editForm';

let tableSelectedRows;
let rowSelEventname;
let rowDeselEventname;
let actionButtonEventName;
let tabledata = [];
let compId;
let multiselect = '';
let columnsHeaderObj = [];
let itemsHeaderObj = [];
let allowColHeaderHistory = false;
let allowItemHeaderHistory = false;
let updatedRows = [];
class SmartTableComponent extends field {
  static schema(...extend) {
    return field.schema({
      type: 'smarttable',
      label: 'Smart Table',
      input: false,
      key: 'table',
    }, ...extend);
  }

  static get builderInfo() {
    return {
      title: 'Smart Table',
      group: 'premium',
      icon: 'table',
      weight: 40,
      documentation: '/userguide/#smarttable',
      schema: SmartTableComponent.schema()
    };
  }

  get defaultSchema() {
    return SmartTableComponent.schema();
  }

  init() {
    return super.init();
  }

  render(element) {
    return super.render(this.renderTemplate('field', {
      element: element
    }));
  }

  attach(element) {
    let that = this;
    const superAttach = super.attach(element);
    that.component.multiple = true;
    rowSelEventname = that.component.key + '_select';
    rowDeselEventname = that.component.key + '_deselect';
    actionButtonEventName = that.component.key + '_action';
    tabledata = [];
    compId = '';
    multiselect = '';
    columnsHeaderObj = [];
    itemsHeaderObj = [];

    // if (window.platform.submissionData && window.platform.submissionData[that.component.key]) {
    //   tabledata = window.platform.submissionData[that.component.key];
    // }
    tabledata = [{ "id": 1, "name": "Billy Bob", "age": "12", "gender": "male", "dob": "14/04/1984", "location": "United Kingdom", "_children": [{ "id": 2, "name": "Mary May", "age": "1", "gender": "female", "dob": "14/05/1982", "location": "USA" }, { "id": 3, "name": "Christine Lobowski", "age": "42", "gender": "female", "dob": "12/07/1982", "location": "Canada" }, { "id": 4, "name": "Brendon Philips", "age": "125", "gender": "male", "dob": "21/11/1988", "location": "Australia", "_children": [{ "id": 5, "name": "Margret Marmajuke", "age": "16", "gender": "female", "dob": "24/01/1983", "location": "Russia" }, { "id": 6, "name": "Frank Peoney", "age": "12", "gender": "male", "dob": "01/05/1982", "location": "France" }] }] }, { "id": 7, "name": "Jenny Jane", "age": "10", "gender": "female", "dob": "08/05/1988", "location": "Russia" }, { "id": 8, "name": "Martha Tiddly", "age": "42", "gender": "female", "dob": "01/05/1983", "location": "India", "_children": [{ "id": 9, "name": "Frasier Franks", "age": "125", "gender": "female", "dob": "01/09/1987", "location": "India" }] }, { "id": 10, "name": "Oli Bob", "age": "12", "gender": "female", "dob": "11/06/1982", "location": "South Korea" }, { "id": 11, "name": "Mary May", "age": "1", "gender": "female", "dob": "09/12/1981", "location": "China" }, { "id": 12, "name": "Christine Lobowski", "age": "42", "gender": "female", "dob": "23/05/1982", "location": "Japan" }, { "id": 13, "name": "Chris", "age": "32", "gender": "male", "dob": "15/08/1989", "location": "France" }, { "id": 14, "name": "Christine", "age": "22", "gender": "female", "dob": "13/02/1982", "location": "USA" }, { "id": 15, "name": "Lobowski", "age": "44", "gender": "male", "dob": "11/04/1987", "location": "China" }, { "id": 16, "name": "Lobo", "age": "44", "gender": "male", "dob": "01/05/1987", "location": "Russia" }, { "id": 17, "name": "Christi", "age": "22", "gender": "female", "dob": "10/05/1985", "location": "Canada" }, { "id": 18, "name": "Lobowski S", "age": "44", "gender": "male", "dob": "12/12/1984", "location": "France" }, { "id": 19, "name": "Lobo M", "age": "44", "gender": "male", "dob": "11/11/1984", "location": "India" }, { "id": 20, "name": "Bobby Green", "age": "11", "gender": "female", "dob": "10/05/1982", "location": "Germany" }, { "id": 21, "name": "Ford", "age": "22", "gender": "male", "dob": "20/02/1990", "location": "Japan", "serviceHistory": [{ "date": "01/02/2016", "engineer": "Steve Boberson", "actions": "Changed oli filter" }, { "date": "07/02/2017", "engineer": "Martin Stevenson", "actions": "Break light broken" }] }, { "id": 22, "name": "Ford", "age": "22", "gender": "male", "dob": "14/09/1982", "location": "Germany", "serviceHistory": [{ "date": "22/05/2017", "engineer": "Jimmy Brown", "actions": "Aligned wheels" }, { "date": "11/02/2018", "engineer": "Lotty Ferberson", "actions": "Changed Oil" }, { "date": "04/04/2018", "engineer": "Franco Martinez", "actions": "Fixed Tracking" }] }];

    compId = '#' + that.component.id;

    if (!that.component.multiSelect) {
      multiselect = 1;
    }
    //define row context menu contents
    var rowMenu = [{
        label: "<i class='fas fa-user'></i> Change Name",
        action: function (e, row) {
          row.update({
            name: "Steve Bobberson"
          });
        }
      },
      {
        label: "<i class='fas fa-check-square'></i> Select Row",
        action: function (e, row) {
          row.select();
        }
      },
      {
        separator: true,
      },
      {
        label: "Admin Functions",
        menu: [{
            label: "<i class='fas fa-trash'></i> Delete Row",
            action: function (e, row) {
              row.delete();
            }
          },
          {
            label: "<i class='fas fa-ban'></i> Disabled Option",
            disabled: true,
          },
        ]
      }
    ]

    //define column header menu as column visibility toggle
    var headerMenu = function () {
      var menu = [];
      var columns = this.getColumns();

      for (let column of columns) {

        if (column.getDefinition().title == undefined) {
          continue;
        }

        //create checkbox element using font awesome icons
        let icon = document.createElement("i");
        icon.classList.add("fa");
        icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");

        //build label
        let label = document.createElement("span");
        let title = document.createElement("span");

        title.textContent = " " + column.getDefinition().title;


        label.appendChild(icon);
        label.appendChild(title);

        //create menu item
        menu.push({
          label: label,
          action: function (e) {
            //prevent menu closing
            e.stopPropagation();

            //toggle current column visibility
            column.toggle();

            //change menu item icon
            if (column.isVisible()) {
              icon.classList.remove("fa-square");
              icon.classList.add("fa-check-square");
            } else {
              icon.classList.remove("fa-check-square");
              icon.classList.add("fa-square");
            }
          }
        });
      }

      return menu;
    };

    if (that.component.multiSelect) {
      let checkboxObj = {
        formatter: "rowSelection",
        titleFormatter: "rowSelection",
        hozAlign: "center",
        headerSort: false,

        cellClick: function (e, cell) {
          cell.getRow().toggleSelect();
          let eventCustom = new CustomEvent(actionButtonEventName, {
            detail: {
              data: [cell.getRow().getData()]
            }
          });
          document.dispatchEvent(eventCustom);
        }
      }
      columnsHeaderObj.push(checkboxObj);
      itemsHeaderObj.push(checkboxObj);

    }
    //Generate action icon
    if (that.component.actionButton) {
      var ActionButtonIcon = function (cell, formatterParams) { //plain text value
        let iconClass = "fa";
        that.component.fontawesomeIconTag = iconClass + ' ' + that.component.fontawesomeIconTag;
        return "<i class='" + that.component.fontawesomeIconTag + "'></i>"
      };
      let actionButtonObj = {
        formatter: ActionButtonIcon,
        hozAlign: "center",
        cellClick: function (e, cell) {
          let eventCustom = new CustomEvent(actionButtonEventName, {
            detail: {
              data: [cell.getRow().getData()]
            }
          });
          document.dispatchEvent(eventCustom);
        }
      }
      columnsHeaderObj.push(actionButtonObj);
      itemsHeaderObj.push(actionButtonObj);

    }
    // preparing column header obj
    if (that.component.columnHeaderNames && that.component.columnHeaderNames.length > 0) {
      allowColHeaderHistory = false;
      for (let i = 0; i < that.component.columnHeaderNames.length; i++) {
        if(!that.component.columnHeaderNames[i].readOnly ){
          allowColHeaderHistory = true;
        }
        
        if (that.component.frozen) {
          if (i == 0) {
            that.component.columnHeaderNames[0].frozen = true;
            that.component.columnHeaderNames[0].width = '150';
          } else {
            that.component.columnHeaderNames[i].frozen = false;
            that.component.columnHeaderNames[i].width = '150';
          }
        } else {
          if (i == 0) {
            that.component.columnHeaderNames[0].frozen = false;
          }
        }
        if (that.component.columnHeaderNames[i].label.includes(",")) {
          let labelArray = that.component.columnHeaderNames[i].label.split(',');
          if (labelArray.length > 0) {
            let columnArray = [];
            for (let j = 1; j < labelArray.length; j++) {
              var subLabel = labelArray[j]
              console.log(subLabel)

              let obj = {
                title: subLabel,
                field: that.component.columnHeaderNames[i].value,
                headerFilter: that.component.filterHeader,
                frozen: that.component.columnHeaderNames[i].frozen,
                width: that.component.columnHeaderNames[i].width,
                pMode:'A'
              }

              if (!that.component.columnHeaderNames[i].readOnly) {
                obj.editor = 'input';
                obj.cssClass = 'cell-editable'
              }
              if (that.component.columnSelectionMenu) {
                obj.headerMenu = headerMenu
              }
              columnArray.push(obj);
            }
            let obj = {
              title: labelArray[0],
              columns: columnArray,
            };
            columnsHeaderObj.push(obj);
          }
        } else {
          let obj = {
            title: that.component.columnHeaderNames[i].label,
            field: that.component.columnHeaderNames[i].value,
            headerFilter: that.component.filterHeader,
            frozen: that.component.columnHeaderNames[i].frozen,
            width: that.component.columnHeaderNames[i].width,
            pMode:'A'
          }
          if (!that.component.columnHeaderNames[i].readOnly) {
            obj.editor = 'input';
            obj.cssClass = 'cell-editable';

          }
          if (that.component.columnSelectionMenu) {
            obj.headerMenu = headerMenu
          }
          columnsHeaderObj.push(obj);
        }
      }
    }
    // preparing item Header obj
    if (that.component.itemHeaderNames && that.component.itemHeaderNames.length > 0) {
      allowItemHeaderHistory = false;
      for (let i = 0; i < that.component.itemHeaderNames.length; i++) {
        if (!that.component.itemHeaderNames[i].readOnly) {
          allowItemHeaderHistory = true;
        }
        if (that.component.frozen) {
          if (i == 0) {
            that.component.itemHeaderNames[0].frozen = true;
            that.component.itemHeaderNames[0].width = '150';
          } else {
            that.component.itemHeaderNames[i].frozen = false;
            that.component.itemHeaderNames[i].width = '150';
          }
        } else {
          if (i == 0) {
            that.component.itemHeaderNames[0].frozen = false;
          }
        }
        if (that.component.itemHeaderNames[i].label.includes(",")) {
          let labelArray = that.component.itemHeaderNames[i].label.split(',');
          if (labelArray.length > 0) {
            let columnArray = [];
            for (let j = 1; j < labelArray.length; j++) {
              var subLabel = labelArray[j]
              console.log(subLabel)

              let obj = {
                title: subLabel,
                field: that.component.itemHeaderNames[i].value,
                headerFilter: that.component.filterHeader,
                frozen: that.component.itemHeaderNames[i].frozen,
                width: that.component.itemHeaderNames[i].width,
                pMode:'A'
              }

              if (!that.component.itemHeaderNames[i].readOnly) {
                obj.editor = 'input';
                obj.cssClass = 'cell-editable';

              }
              if (that.component.columnSelectionMenu) {
                obj.headerMenu = headerMenu
              }
              columnArray.push(obj);
            }
            let obj = {
              title: labelArray[0],
              columns: columnArray,
            };
            itemsHeaderObj.push(obj);
          }
        } else {
          let obj = {
            title: that.component.itemHeaderNames[i].label,
            field: that.component.itemHeaderNames[i].value,
            headerFilter: that.component.filterHeader,
            frozen: that.component.itemHeaderNames[i].frozen,
            width:  that.component.itemHeaderNames[i].width,
            pMode:'A'
          }
          if (!that.component.itemHeaderNames[i].readOnly) {
            obj.editor = 'input';
            obj.cssClass = 'cell-editable';
          }
          if (that.component.columnSelectionMenu) {
            obj.headerMenu = headerMenu
          }
          itemsHeaderObj.push(obj);
        }
      }
    }

    that.initializeTable();

    document.addEventListener(actionButtonEventName, (e) => {
      console.log(" actionButtonEventName event listening ")
    }, false);

    document.addEventListener('sdcDoneEvent', function (event) {
      console.log("sdcDoneEvent listening in smart table");
      if (event.detail.data && that.component.key && event.detail.data[that.component.key]) {
        tabledata = event.detail.data[that.component.key];
        that.initializeTable();
      }
    }, false);
    return superAttach;
  }

  initializeTable() {
    let that = this;
    console.log("initializeTable() called")
 
    var table = new Tabulator(compId, {

      data: tabledata,
      layout: that.component.columnsLayoutMode,
      pagination: "local",
      paginationSize: 5,
      paginationSizeSelector: [5, 10, 15, 20, 25],
      movableRows: that.component.movableRows,
      columnHeaderVertAlign: that.component.columnHeaderVertAlign,
      dataTree: false,
      dataTreeStartExpanded: false,
      selectable: multiselect,
      cellHozAlign: "center",
      headerHozAlign: "center",
      history: allowColHeaderHistory,
      columns: columnsHeaderObj,
      rowFormatter: function (row) {
        //create and style holder elements
        if (row.getData().serviceHistory && row.getData().serviceHistory.length > 0) {
          var holderEl = document.createElement("div");
          var tableEl = document.createElement("div");

          holderEl.style.boxSizing = "border-box";
          holderEl.style.padding = "10px 30px 10px 10px";
          holderEl.style.borderTop = "1px solid #333";
          holderEl.style.borderBotom = "1px solid #333";
          holderEl.style.background = "#ddd";

          tableEl.style.border = "1px solid #333";

          holderEl.appendChild(tableEl);

          row.getElement().appendChild(holderEl);

          var subTable = new Tabulator(tableEl, {
            data: row.getData().serviceHistory,
            layout: that.component.columnsLayoutMode,
            pagination: "local",
            paginationSize: 5,
            paginationSizeSelector: [5, 10, 15, 20, 25],
            // movableRows: that.component.movableRows,
            columnHeaderVertAlign: that.component.columnHeaderVertAlign,
            dataTree: false,
            dataTreeStartExpanded: false,
            selectable: multiselect,
            cellHozAlign: "center",
            headerHozAlign: "center",
            history:allowItemHeaderHistory,
            columns: itemsHeaderObj,
            rowSelectionChanged: function (data, rows) {
              console.log(" row selected data subtable" + JSON.stringify(data))
              that.component.selectedRows = data;
              tableSelectedRows = data;
              that.triggerChange();
              that.component.getSelected = function () {
                return tableSelectedRows;
              }
            },
            // dataEdited:function(data, row){
            //   //data - the updated table data
            //   console.log(data, row)
            //   },
            rowSelected: function (row) {
              //row - row component for the selected row
              console.log("row selected = " + row);
              let eventCustom = new CustomEvent(rowSelEventname, {
                detail: {
                  data: row.getData()
                }
              });
              document.dispatchEvent(eventCustom);

            },
            rowDeselected: function (row) {
              console.log("row rowDeselected = " + row);
              let eventCustom = new CustomEvent(rowDeselEventname, {
                detail: {
                  data: row.getData()
                }
              });
              document.dispatchEvent(eventCustom);
            },

            cellEdited: function (cell) {
              console.log("cell edited = " + cell.getRow().getIndex())
              that.triggerChange();
              console.log("method calling " + that.component.getSelected());
            }
          })
        }
      },
      rowSelectionChanged: function (data, rows) {
        console.log(" row selected data " + JSON.stringify(data))
        that.component.selectedRows = data;
        tableSelectedRows = data;
        that.triggerChange();
        let removeButtonObj = document.getElementById('removerow-button');
        if (removeButtonObj) {
          if (tableSelectedRows.length > 0) {
            removeButtonObj.style.display = "block";
          } else {
            removeButtonObj.style.display = "none";
          }
        }
        that.component.getSelected = function () {
          return tableSelectedRows;
        }
      },
      rowSelected: function (row) {
        //row - row component for the selected row
        console.log("row selected = " + row);
        let eventCustom = new CustomEvent(rowSelEventname, {
          detail: {
            data: row.getData()
          }
        });
        document.dispatchEvent(eventCustom);
      },
      rowDeselected: function (row) {
        console.log("row rowDeselected = " + row);
        let eventCustom = new CustomEvent(rowDeselEventname, {
          detail: {
            data: row.getData()
          }
        });
        document.dispatchEvent(eventCustom);
      },

      cellEdited: function (cell) {
        console.log("cell edited = " + cell.getRow().getIndex())
        console.log("method calling " + that.component.getSelected());
        console.log("row data before edited = " + cell.getRow().getData().pMode)
      
        cell.getRow().getData().pMode = "M";
        console.log("row data after edited = " + cell.getRow().getData().pMode)
    //     updatedRows = [];
    // for(let i=0; i<table.getEditedCells().length; i++){
    //   updatedRows.push(table.getEditedCells()[i].getRow().getIndex());
    // }
     that.triggerChange();

      },
      editCheck:function(cell){
        //cell - the cell component for the editable cell
    
        //get row data
        var data = cell.getRow().getData();
    
        return data.name == 'Mary May'; // only allow the name cell to be edited if the age is over 18
    },
      rowUpdated:function(row){
        //row - row component
        console.log("row updated")
        },
        rowEdited:function(row){
          //row - row component
          console.log("row edited")
          },
    });

    let div = document.getElementById(this.component.id);
    if(div && tabledata && tabledata.length > 0){
      let childDiv = div.getElementsByTagName('div')[0];
      let newElement = document.createElement("div");
      newElement.setAttribute('class', 'table-controls');
      if(allowColHeaderHistory){
      let undoBtn = document.createElement("button");
        undoBtn.setAttribute('class', 'smarttable-button');
        undoBtn.setAttribute('class', 'btn-primary');

      // undoBtn.innerHTML = "Undo Edit";
      undoBtn.innerHTML = '<i class="fa fa-undo" aria-hidden="true"></i>';

      undoBtn.onclick = function () {
        table.undo();
      };
      newElement.appendChild(undoBtn);

      let redoBtn = document.createElement("button");
      redoBtn.setAttribute('class', 'smarttable-button');
      redoBtn.setAttribute('class', 'btn-primary');

      // redoBtn.innerHTML = "Redo Edit";
      redoBtn.innerHTML = '<i class="fa fa-repeat" aria-hidden="true"></i>';

      redoBtn.onclick = function () {
        table.redo();
      };
      newElement.appendChild(redoBtn);
    }

      let addRowBtn = document.createElement("button");
      addRowBtn.setAttribute('class', 'smarttable-button');
      addRowBtn.setAttribute('class', 'btn-primary');


      // addRowBtn.innerHTML = "Add Row";
      addRowBtn.innerHTML = '<i class="fa fa-plus" aria-hidden="true"></i>';

      addRowBtn.onclick = function () {
        table.addRow({pMode:"A"})
        table.addRow({ })


        .then(function(row){
          console.log("row added");
        that.triggerChange();

          //row - the row component for the row updated or added
      
          //run code after data has been updated
      })
      .catch(function(error){
          //handle error updating data
      });
      };
      newElement.appendChild(addRowBtn);

      let removeRowBtn = document.createElement("button");
      removeRowBtn.setAttribute('class', 'smarttable-button');
      removeRowBtn.setAttribute('id', 'removerow-button');
      removeRowBtn.setAttribute('class', 'btn-primary');



      // removeRowBtn.innerHTML = "Remove Row";
      removeRowBtn.innerHTML = '<i class="fa fa-times" aria-hidden="true"></i>';
      

      removeRowBtn.onclick = function () {
        // let ind = table.rowManager.rows.length - 1;
        // table.deleteRow(2);
        // table.getSelectedRows()[0].getIndex()
        for (let i = 0; i < table.getSelectedRows().length; i++) {
          table.getSelectedRows()[i].getData().pMode = 'D';

          // if (table.getSelectedRows()[i].getIndex()){
          // table.deleteRow(table.getSelectedRows()[i].getIndex());
          // }

          if (table.getSelectedRows()[i]) {
            table.getSelectedRows()[i].delete();
          }
        }
      };
      newElement.appendChild(removeRowBtn);
      div.insertBefore(newElement, childDiv);

      let removeButtonObj = document.getElementById('removerow-button');
      if (removeButtonObj) {
        if (tableSelectedRows.length > 0) {
          removeButtonObj.style.display = "block";
        } else {
          removeButtonObj.style.display = "none";
        }
      }

    }
  }

  destroy(all) {
    super.destroy(all);
  }

}
SmartTableComponent.editForm = SmartTableEditForm;
Formio.registerComponent('smarttable', SmartTableComponent);