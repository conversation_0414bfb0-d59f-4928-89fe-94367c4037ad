import ComponentEditConditional from 'formiojs/components/_classes/component/editForm/Component.edit.conditional';
import ComponentEditData from 'formiojs/components/_classes/component/editForm/Component.edit.data';
import ComponentEditAPI from 'formiojs/components/_classes/component/editForm/Component.edit.api';
import ComponentEditDisplay from 'formiojs/components/_classes/component/editForm/Component.edit.display';

import ComponentEditLogic from 'formiojs/components/_classes/component/editForm/Component.edit.logic';
import ComponentEditValidation from 'formiojs/components/_classes/component/editForm/Component.edit.validation';
import ComponentEditLayout from 'formiojs/components/_classes/component/editForm/Component.edit.layout';
import _ from 'lodash';
import EditFormUtils from 'formiojs/components/_classes/component/editForm/utils';
export function columnEditForm() {
  return {
    components: [
      { key: 'type', type: 'hidden' },
      {
        type: 'tabs',
        key: 'tabs',
        components: [
          {
            label: 'Display',
            key: 'display',
            weight: 0,
            components:
            [
              {
                weight: 0,
                type: 'textfield',
                input: true,
                key: 'label',
                label: 'Label',
                placeholder: 'Field Label',
                tooltip: 'The label for this field that will appear next to it.',
                validate: {
                  required: true
                }
              },
              {
                weight: 150,
                type: 'datagrid',
                input: true,
                key: 'columns',
                label: 'Column Properties',
                addAnother: 'Add Column',
                tooltip: 'The size and width settings for each column. One row is equal to 16. (e.g., a row with two columns spanning the entire page should be 8 and 8)',
                reorder: true,
                components: [
                  {
                    type: 'hidden',
                    key: 'components',
                    defaultValue: []
                  },
                  {
                    type: 'select',
                    key: 'size',
                    defaultValue: 'md',
                    label: 'Size',
                    data: {
                      values: [
                        { label: 'xs', value: 'xs' },
                        { label: 'sm', value: 'sm' },
                        { label: 'md', value: 'md' },
                        { label: 'lg', value: 'lg' },
                        { label: 'xl', value: 'xl' },
                      ],
                    },
                  },
                  {
                    type: 'number',
                    key: 'width',
                    defaultValue: 8,
                    label: 'Width'
                  }
                ]
              },
              {
                weight: 160,
                type: 'checkbox',
                label: 'Auto adjust columns',
                tooltip: 'Will automatically adjust columns based on if nested components are hidden.',
                key: 'autoAdjust',
                input: true
              },
              {
                weight: 500,
                type: 'textfield',
                input: true,
                key: 'customClass',
                label: 'Custom CSS Class',
                placeholder: 'Custom CSS Class',
                tooltip: 'Custom CSS class to add to this component.'
              },
              {
                weight: 1100,
                type: 'checkbox',
                label: 'Hidden',
                tooltip: 'A hidden field is still a part of the form, but is hidden from view.',
                key: 'hidden',
                input: true
              },
              {
                weight: 1200,
                type: 'checkbox',
                label: 'Hide Label',
                tooltip: 'Hide the label of this component. This allows you to show the label in the form builder, but not when it is rendered.',
                key: 'hideLabel',
                input: true
              },
              {
                weight: 1600,
                type: 'checkbox',
                label: 'Modal Edit',
                tooltip: 'Opens up a modal to edit the value of this component.',
                key: 'modalEdit',
                input: true
              }

            ]
          },
         
          {
            label: 'API',
            key: 'api',
            weight: 30,
            components: [
              {
                weight: 0,
                type: 'textfield',
                input: true,
                key: 'key',
                label: 'Property Name',
                tooltip: 'The name of this field in the API endpoint.',
                validate: {
                  pattern: '(\\w|\\w[\\w-.]*\\w)',
                  patternMessage: 'The property name must only contain alphanumeric characters, underscores, dots and dashes and should not be ended by dash or dot.'
                }
              }, {
                weight: 100,
                type: 'tags',
                input: true,
                label: 'Field Tags',
                storeas: 'array',
                tooltip: 'Tag the field for use in custom logic.',
                key: 'tags'
              }, {
                weight: 200,
                type: 'datamap',
                label: 'Custom Properties',
                tooltip: 'This allows you to configure any custom properties for this component.',
                key: 'properties',
                valueComponent: {
                  type: 'textfield',
                  key: 'value',
                  label: 'Value',
                  placeholder: 'Value',
                  input: true
                }
              },
            ]
          },
          {
            label: 'Conditional',
            key: 'conditional',
            weight: 40,
            components: ComponentEditConditional
          },
          {
            label: 'Logic',
            key: 'logic',
            weight: 50,
            components: ComponentEditLogic
          },
          {
            label: 'Layout',
            key: 'layout',
            weight: 60,
            components: ComponentEditLayout
          }
        ]
      }
    ]
  };
}
