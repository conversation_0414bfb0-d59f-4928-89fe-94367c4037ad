export function SystemPropertiesHtmlHelpString() {
  return  `
  <h2 mat-dialog-title>SYSTEM PROPERTIES:</h2>
  <p>A system properties file contains the properties keyword which is mapped
    to a list of key:value pairs that define system property names and values.
    The key:value format is systemName.propertyName:value.
    The system properties file can have properties for one or more systems as required.</p>
  <p>Here’s what the contents of our system properties file looks like:</p>
  <div class="system-props-help-text">
  namespace: unvired.flow<br /><br />
  properties:<br />
  <div style="padding-left:16px;">
  <span>- forms-db.type: mysql</span><br />
  <span>  - forms-db.connection_pool: 10</span><br />
  <span> - forms-db.server: 127.0.0.1</span><br />
  <span>  - forms-db.port: 3306</span><br />
  <span>  - forms-db.database: digitalforms</span><br />
  <span>  - forms-db.user: root</span><br />
  <span>  - forms-db.password:</span><br />
  </div>
  <div style="padding-left:48px;">
    <span>value: xxxxxxxxx</span><br />
    <span>sensitive: true</span><br />
    <span>  - forms-db.connection_parameters: allowMultiQueries=true;ssl=false;</span><br />
    <span> - forms-db.multiquery: true</span><br />
  </div>
    <p>
    In the example above, the system name is forms-db and the properties are type, connection_pool etc.
    When the workflow is invoked, only the name of the system needs to be passed in the input system.
    For e.g. in the above case the system name passed would be forms-db.
  </p>
  </div>
`;
}
