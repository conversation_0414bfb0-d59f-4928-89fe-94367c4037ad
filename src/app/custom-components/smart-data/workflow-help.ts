export function WorkflowHtmlHelpString(workflow_data) {

      const dialogHeaderContent = `
      <h2 mat-dialog-title><strong>${workflow_data.name.toUpperCase()}</strong></h2>
      <p> ${workflow_data.description}</p> `;
      let Inputs = ` <h3 mat-dialog-title><strong>INPUT PROPERTIES</strong></h3>`;
      workflow_data.inputs.forEach(element => {
        // tslint:disable-next-line: no-unused-expression
      const newDiv = ` <div>
        <h4 style="padding-left:16px;">${element.inputName.charAt(0).toUpperCase()}${element.inputName.slice(1)}</h4>
        <div>
            <h5 style="padding-left:48px;">Description</h5>
            <p  style="padding-left:64px;">${element.description}</p>
        </div>
        <div>
            <h5 style="padding-left:48px;">Example</h5>
            <p  style="padding-left:64px;">${element.example}</p>
        </div>
        <div>
            <h5 style="padding-left:48px;">Notes</h5>
            <p  style="padding-left:64px;">${element.notes}</p>
        </div>
        <div>
            <h5 style="padding-left:48px;">Required</h5>
            <p  style="padding-left:64px;">${element.required}</p>
        </div>
        <div>
            <h5 style="padding-left:48px;">Type</h5>
            <p  style="padding-left:64px;">${element.type}</p>
        </div>
        </div>
      </div>`;
      Inputs = `${Inputs}${newDiv}`;
      });
      const DialogMainContent = Inputs;

      const DialogFooterContent = `
      <h3 mat-dialog-title>OUTPUTS</h3>
        <div>
          <h4 style="padding-left:16px;">Error</h4>
          <p style="padding-left:48px;">${workflow_data.outputs.error}</p>
          <h4 style="padding-left:16px;">Result</h4>
          <p style="padding-left:48px;">${workflow_data.outputs.result}</p>
        </div>
        <h3 mat-dialog-title>RESULTS</h3>
        <div>
          <h4 style="padding-left:16px;">Success</h4>
          <p style="padding-left:48px;">${workflow_data.results.SUCCESS}</p>
          <h4 style="padding-left:16px;">Failure</h4>
          <p style="padding-left:48px;">${workflow_data.results.FAILURE}</p>
        </div>
      `;

      const DialogContent = ` ${dialogHeaderContent} ${DialogMainContent} ${DialogFooterContent}`;

      return DialogContent;
}
