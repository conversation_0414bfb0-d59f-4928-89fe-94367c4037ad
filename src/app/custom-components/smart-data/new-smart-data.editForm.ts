import { Formio } from 'formiojs';
import { CustomEncoder } from '../../shared/customencoder';

import EditFormUtils from 'formiojs/components/_classes/component/editForm/utils';

export function NewSmartDataEditForm() {
  return {
    components: [
      {
        type: 'tabs',
        key: 'tabs',
        components: [
          {
            label: 'Data',
            key: 'data',
            weight: 0,
            components: [
              {
                weight: 0,
                type: 'textfield',
                input: true,
                key: 'label',
                label: 'Label',
                placeholder: 'Field Label',
                tooltip: 'The label for this field that will appear next to it.',
                validate: {
                  required: true
                }
              },
              // {
              //   type: 'checkbox',
              //   key: 'hidden',
              //   defaultValue: true,
              //   label: 'Hidden',
              //   tooltip: 'A hidden field is still a part of the form, but is hidden from view.',
              //   input: true,
              // }
            ]
          },
          {
            label: 'Trigger',
            key: 'trigger',
            weight: 10,
            components: [
              {
                type: 'panel',
                title: 'Before Execute',
                theme: 'default',
                collapsible: true,
                collapsed: true,
                key: 'trigger.beforefetch',
                // weight: 150,
                components: [
                  {
                    type: 'panel',
                    title: 'JavaScript',
                    collapsible: true,
                    collapsed: false,
                    style: { 'margin-bottom': '10px' },
                    key: 'trigger.beforefetch.panel',
                    // customConditional() {
                    //   return !Evaluator.noeval;
                    // },
                    components: [
                      {
                        type: 'textarea',
                        key: 'trigger.beforefetch.panel.javascript',
                        rows: 5,
                        editor: 'ace',
                        hideLabel: true,
                        input: true
                      },
                      {
                        type: 'htmlelement',
                        tag: 'div',
                        content: `<p>Enter custom javascript code.</p>`
                      }
                    ]
                  }
                ]
              },
              {
                type: 'panel',
                title: 'Execute',
                theme: 'default',
                collapsible: true,
                collapsed: false,
                key: 'trigger.prefetch',
                // weight: 150,
                components: [
                  // {
                  //   type: 'checkbox',
                  //   input: false,
                  //   key: 'trigger.prefetch.init',
                  //   defaultValue: false,
                  //   label: 'Trigger on form init',
                  //   tooltip: 'When enabled the request will be made when the form is initialized.',
                  //   weight: 15
                  // },
                  // {
                  //   type: 'checkbox',
                  //   input: true,
                  //   key: 'trigger.prefetch.triggerbycomponent',
                  //   defaultValue: true,
                  //   label: 'Triggered by form component',
                  //   tooltip: 'The smartdata fetch will be triggered by another form component',
                  //   weight: 15
                  // },
                  // {
                  //   type: 'htmlelement',
                  //   tag: 'div',
                  //   content: `<p>SDC can be triggered by Barcode or by a button.  In the barcode or button add an action to trigger the SDC.  If the SDC has to be triggered by any other generic component, set the other component to fire an event with selected property name from the SDC API tab.</p>`

                  //   // content: (context) => {
                  //   //   // console.log(context);
                  //   //     return `SDC can be triggered by Barcode or by a button.  In the barcode or button add an action to trigger the SDC.  If the SDC has to be triggered by any other generic component, set the other component to fire an event with selected property name from the SDC API tab.`;
                  //   // }
                  // },
                  {
                    type: 'select',
                    input: true,
                    label: 'Trigger Smart Data Component',
                    key: 'trigger.prefetch.triggerSDC',
                    clearOnHide: true,
                    tooltip: 'Trigger Smart Data Component on these options.',
                    defaultValue: 'onComponentAction',
                    weight: 11,
                    template: '<span>{{ item.label }}</span>',
                    valueProperty: 'value',
                    dataSrc: 'values',
                    data: {
                      values: [{
                        label: 'On Form Load',
                        value: 'onFormLoad'
                      }, {
                        label: 'On Component Action',
                        value: 'onComponentAction'
                      }, {
                        label: 'On Data Change',
                        value: 'onDataChange'
                      }]
                    }
                  },
                  {
                    type: 'htmlelement',
                    tag: 'div',
                    content: `<p class='border-helptext'>SDC API will be executed when the form is loaded (initialized)</p>`,
                    conditional: {
                      json: {
                        '===': [{
                          var: 'data.trigger.prefetch.triggerSDC'
                        }, 'onFormLoad']
                      }
                    }

                  },
                  {
                    type: 'htmlelement',
                    tag: 'div',
                    content: `<p class='border-helptext'>SDC API will be executed based on a form component action such as barcode or button click.  For Barcode and Button click add an action to execute the SDC API and for any other component, set the other component to fire an event with name set to the SDC API Property Name.</p>`,
                    conditional: {
                      json: {
                        '===': [{
                          var: 'data.trigger.prefetch.triggerSDC'
                        }, 'onComponentAction']
                      }
                    }

                  },
                  {
                    type: 'htmlelement',
                    tag: 'div',
                    content: `<p class='border-helptext'>SDC API will be executed when a related form component data changes</p>`,
                    conditional: {
                      json: {
                        '===': [{
                          var: 'data.trigger.prefetch.triggerSDC'
                        }, 'onDataChange']
                      }
                    }

                  },
                  {
                    type: 'select',
                    input: true,
                    key: 'trigger.prefetch.refreshOn',
                    label: 'Trigger on Data change',
                    weight: 10,
                    tooltip: 'Refresh data when another field changes.',
                    dataSrc: 'custom',
                    valueProperty: 'value',
                    clearOnHide: true,
                    conditional: {
                      json: {
                        '===': [{
                          var: 'data.trigger.prefetch.triggerSDC'
                        }, 'onDataChange']
                      }
                    },
                    data: {
                      custom: function (e) {
                        // console.log('Edit form:', e);
                        const t = [];
                        return t.push({
                          label: 'Any Change',
                          value: 'data'
                        }), e.utils.eachComponent(e.instance.options.editForm.components,
                          (function (n, i) {
                            // console.log(n, i);
                            // tslint:disable-next-line: no-unused-expression
                            n.key !== e.data.key && t.push({
                              label: n.label || n.key,
                              value: i
                            });
                          })), t;
                      }
                    }
                  }
                ]
              },
            
              {
                type: 'panel',
                title: 'After Execute',
                theme: 'default',
                collapsible: true,
                collapsed: true,
                key: 'trigger.postfetch',
                // weight: 150,
                components: [
                  // this.logicVariablesTable(additionalParams),
                  {
                    type: 'textfield',
                    label: 'Fire Event',
                    key: 'trigger.postfetch.event',
                    input: true,
                    weight: 10,
                    tooltip: 'The event to fire after fetch data API has been executed.',
                    defaultValue: 'SmartDataEvent'
                    // description: 'When in a datagrid or editgrid, { { rowIndex } } is available for interpolation to target a specific row.'
                  },
                  {
                    type: 'panel',
                    title: 'JavaScript',
                    collapsible: true,
                    collapsed: false,
                    style: { 'margin-bottom': '10px' },
                    key: 'trigger.postfetch.panel',
                    // customConditional() {
                    //   return !Evaluator.noeval;
                    // },
                    components: [
                      {
                        type: 'textarea',
                        key: 'trigger.postfetch.panel.javascript',
                        rows: 5,
                        editor: 'ace',
                        hideLabel: true,
                        input: true
                      },
                      {
                        type: 'htmlelement',
                        tag: 'div',
                        content: `<p>Enter custom javascript code.</p>`
                      }
                    ]
                  }

                ]
              }
            ]
          },
          {
            label: 'API',
            key: 'fetchapi',
            weight: 20,
            components: [
              {
                weight: 0,
                type: 'textfield',
                input: true,
                key: 'key',
                label: 'Property Name',
                tooltip: 'The name of this field in the API endpoint.',
                validate: {
                  pattern: '(\\w|\\w[\\w-.]*\\w)',
                  patternMessage: 'The property name must only contain alphanumeric characters, underscores, dots and dashes and should not be ended by dash or dot.',
                  required: true
                }
              },
              {
                type: 'select',
                input: true,
                weight: 0,
                tooltip: 'The source to get the data data. You can fetch from a URL or use javascript to get the value.',
                key: 'dataSrc',
                defaultValue: 'workflow',
                label: 'Data Source Type',
                dataSrc: 'values',
                data: {
                  values: [
                    {
                      label: 'URL',
                      value: 'url'
                    },
                    {
                      label: 'Workflow',
                      value: 'workflow'
                    }
                  ]
                }
              }, {
                type: 'textfield',
                input: true,
                key: 'fetchapi.url',
                weight: 10,
                label: 'Data Source URL',
                placeholder: 'Data Source URL',
                tooltip: 'A URL that returns data. You can interpolate form data using curly bracket notation.',
                clearOnHide: true,
                conditional: {
                  json: {
                    '===': [{
                      var: 'data.dataSrc'
                    }, 'url']
                  }
                }
              }, {
                type: 'select',
                input: true,
                label: 'Method',
                key: 'fetchapi.method',
                clearOnHide: true,
                tooltip: 'The HTTP Request method to use when making the request.',
                defaultValue: 'get',
                weight: 11,
                template: '<span>{{ item.label }}</span>',
                dataSrc: 'values',
                data: {
                  values: [{
                    label: 'Get',
                    value: 'get'
                  }, {
                    label: 'Put',
                    value: 'put'
                  }, {
                    label: 'Post',
                    value: 'post'
                  }, {
                    label: 'Patch',
                    value: 'patch'
                  }, {
                    label: 'Delete',
                    value: 'delete'
                  }]
                },
                conditional: {
                  json: {
                    '===': [{
                      var: 'data.dataSrc'
                    }, 'url']
                  }
                }
              }, {
                type: 'datagrid',
                input: true,
                label: 'Request Headers',
                key: 'fetchapi.headers',
                tooltip: 'Set any headers that should be sent along with the request to the url. This is useful for authentication.',
                weight: 12,
                components: [{
                  label: 'Key',
                  key: 'key',
                  input: true,
                  type: 'textfield'
                }, {
                  label: 'Value',
                  key: 'value',
                  input: true,
                  type: 'textfield'
                }],
                clearOnHide: true,
                conditional: {
                  json: {
                    '===': [{
                      var: 'data.dataSrc'
                    }, 'url']
                  }
                }
              },
              {
                label: 'FormId',
                key: 'fetchapi.formId',
                input: true,
                type: 'textfield',
                hidden: true,
                customDefaultValue: (context) => {
                  // console.log('In Input select data', context);
                  const URLArray = window.location.pathname.split('/');
                  // console.log(pathArray);
                  // URLArray[3] = atob(URLArray[3]); 
                  // const urlmatrixstrings = URLArray[3].split(';');
                  // const formId = urlmatrixstrings[0];
                  URLArray[3] =JSON.parse(atob(decodeURIComponent(URLArray[3])));
                  const formId = URLArray[3]['formId'];
                  // console.log(formId);
                  return formId;
                },
              },
              // {
              //   type: 'select',
              //   label: 'Workflow Name',
              //   key: 'fetchapi.workflowname',
              //   customClass: "is-flipped",
              //   placeholder: 'Select workflow',
              //   dataSrc: 'url',
              //   data: {
              //     url: `${localStorage.getItem('UMP_url')}/UMP/API/v3/applications/DIGITAL_FORMS/execute/DIGITAL_FORMS_PA_GET_ALL_WORKFLOWS`,
              //     method: 'POST',
              //     headers: [
              //       { key: 'Authorization', value: 'Bearer ' + localStorage.getItem('token') },
              //       { key: 'Content-Type', value: 'application/x-www-form-urlencoded' },
              //       { key: 'accept', value: 'application/json' }
              //     ]
              //   },
              //   valueProperty: 'wfName',
              //   lazyLoad: true,
              //   template: '<span>{{ item.wfTitle }}</span>',
              //   // refreshOn: 'make',
              //   clearOnRefresh: true,
              //   selectValues: 'workflows',
              //   validate: {
              //     required: true
              //   },
              //   customDefaultValue: (context) => {
              //     console.log("SDC comp")
              //     // console.log('In Input select data', context);
              //     const URLArray = window.location.pathname.split('/');
              //     // URLArray[3] = 'eyJ0eXBlIjoiZm9ybSIsImZvcm1UeXBlIjoiRm9ybSIsImZvcm1JZCI6IkU3OTIzNDAzMjc2QjQ3RTc4NDFGOTg3N0Q5N0QxNzk1IiwiY2F0ZWdvcnkiOiJEZWZhdWx0In0';
              //     // console.log(pathArray);
              //     URLArray[3] = JSON.parse(atob(decodeURIComponent(URLArray[3])));
              //     // const urlmatrixstrings = URLArray[3].split(';');
              //     // const formId = urlmatrixstrings[0];
              //     const formId = URLArray[3]['formId'];
              //     // console.log(formId);
              //     const inputparams = {
              //       formId: `${formId}`,
              //       onlySDC: true
              //     };
              //     const inputparamsjson = JSON.stringify(inputparams);
              //     // console.log(inputparamsjson);
              //     const encodedInput = new CustomEncoder().encodeValue(inputparamsjson);
              //     const body = `queuedExecute=false&messageFormat=custom&inputMessage=${encodedInput}`;
              //     context.component.data.body = body;
              //   },
              //   conditional: {
              //     json: {
              //       '===': [{
              //         var: 'data.dataSrc'
              //       }, 'workflow']
              //     }
              //   }
              // },
              {
                label: 'Columns',
                key: 'workflowNameColumns',
                type: 'columns',
                input: false,
                tableView: false,
                hidden: false,
                // ignoreCache: true,
                columns: [
                  {
                    components: [
                      {
                        type: 'select',
                        input: true,
                        label: 'Workflow Name',
                        key: 'fetchapi.workflowname',
                        customClass: "is-flipped",
                        placeholder: 'Select workflow',
                        dataSrc: 'custom',
                        valueProperty: 'value',
                        template: "<span>{{ item.label }}</span>",
                        validate: {
                          required: true
                        },
                        conditional: {
                          json: {
                            '===': [{
                              var: 'data.dataSrc'
                            }, 'workflow']
                          }
                        },
                        data: {
                          // "custom": getCategoryValues()
                           custom(context) {
                            // return new Promise((resolve, reject) => {
                              // tslint:disable-next-line:prefer-const
                              var values = [];
                              const URLArray = window.location.pathname.split("/");
                              URLArray[3] = JSON.parse(atob(decodeURIComponent(URLArray[3])));
                              let formId = URLArray[3]['formId'];
                              const inputparams = {
                                formId: `${formId}`,
                                onlySDC: true
                              };
                              const inputparamsjson = JSON.stringify(inputparams);
                              // console.log(inputparamsjson);
                              const encodedInput = new CustomEncoder().encodeValue(inputparamsjson);
                              const body = `queuedExecute=false&messageFormat=custom&inputMessage=${encodedInput}`;

                              Formio.makeStaticRequest(
                                `${localStorage.getItem('UMP_url')}/UMP/API/v3/applications/DIGITAL_FORMS/execute/DIGITAL_FORMS_PA_GET_ALL_WORKFLOWS?${body}`,
                                "POST",
                                null,
                                {
                                  headers: {
                                    "Authorization": "Bearer " + localStorage.getItem("token"),
                                    "Accept": "application/json",
                                    "Content-type": 'application/x-www-form-urlencoded'
                                  },
                                }
                              ).then(function (workflowdata) {
                                if (workflowdata && workflowdata.status === 'Success') {
                                  try {
                                    let wfs = workflowdata.workflows;
                                    for (let i = 0; i < wfs.length; i++) {
                                      values.push({
                                        'label': wfs[i].wfTitle,
                                        'value': wfs[i].wfName
                                      });
                                    }
                                  } catch (err) {

                                  }

                                } else {
                                  if (workflowdata && workflowdata.error) {
                                    console.log("error on getting work flows" + workflowdata.error);
                                  }
                                }
                                // resolve(values);
                                context.instance.setItems(values, true)
                                // return values;

                              })
                              .catch((error) => {

                              })
                              // workflowdata.then((result) => {
                              //   console.log("result" + result);
                              //   if (result && result.status === 'Success') {
                              //     try {
                              //       let wfs = result.workflows;
                              //       for (let i = 0; i < wfs.length; i++) {
                              //         values.push({
                              //           'label': wfs[i].wfTitle,
                              //           'value': wfs[i].wfName
                              //         });
                              //       }
                              //     } catch (err) {

                              //     }

                              //   } else {
                              //     if (workflowdata && workflowdata.error) {
                              //       console.log("error on getting work flows" + workflowdata.error);
                              //     }
                              //   }
                              //   resolve(values);

                              // })
                            // });
                          }

                        }
                      },
                    ],
                    offset: 0,
                    push: 0,
                    pull: 0,
                    size: 'md',
                    currentWidth: 14,
                    width: 14
                  },
                  {
                    components: [
                      {
                        label: '<i class="plus icon"></i>',
                        action: 'custom',
                        showValidations: false,
                        tableView: false,
                        key: 'createFlow',
                        type: 'button',
                        input: true,
                        // tooltip: 'Create Work flow',
                        custom: (context) => {
                         console.log("test flow")
                         let eventCustom = new CustomEvent('createWorkFlow',
                         {
                           detail: {
                             data: 'shyamala'
                           }
                         });
                        document.dispatchEvent(eventCustom);
                        },
                        customClass: 'icon create-flow',
                        conditional: {
                          json: {
                            '===': [{
                              var: 'data.dataSrc'
                            }, 'workflow']
                          }
                        }
                      }
                    ],
                    offset: 0,
                    push: 0,
                    pull: 0,
                    size: 'md',
                    currentWidth: 2,
                    width: 2
                  },
                  
                ],
              },
            ]
          },
        ]
      }
    ]
  }
}
 function getCategoryValues() {
  // return new Promise(async (resolve, reject) => {
    // tslint:disable-next-line:prefer-const
    var values = [{"label":"sdc0","value":"sdc0","id":"F2FF1CD19DEF4D4FA817962F99314649"},{"label":"sdc10","value":"sdc10","id":"E20E873DBA754F3BA23AC6711A2380B1"},{"label":"sdc11","value":"sdc11","id":"597A6B12F10A428E9DC0A4239B5B6363"},{"label":"sdc12","value":"sdc12","id":"21AC79494AFE45669C65171A0019F7C2"},{"label":"sdc14","value":"sdc14","id":"07CD6FBAFC1742F9805E1BB0E9C8E751"},{"label":"sdc15","value":"sdc15","id":"8D35E17675BA4886B257FF74DF68A656"},{"label":"sdc16","value":"sdc16","id":"3D074C9180794DB9B49C3AA0A68A0AF9"},{"label":"sdc18","value":"sdc18","id":"75AD0CC742E44233A3DD09AE95901924"},{"label":"sdc19","value":"sdc19","id":"A026497C21B24AB0B6F62DFDE265093E"},{"label":"sdc20","value":"sdc20","id":"F2B6B1CAB34F48608AA1815A4C62C0C2"},{"label":"sdc21","value":"sdc21","id":"5EFF33440E8F4942BFCF0EFAE3F4A0F5"},{"label":"sdc4","value":"sdc4","id":"9E1D4DCAC73F4A2EA450A564AB9E0622"},{"label":"sdc5","value":"sdc5","id":"E9AD976F7235450D917DCBBECE3B2239"},{"label":"sdc6","value":"sdc6","id":"312B0F41F6C14DBFBBD5275146604792"},{"label":"sdc7","value":"sdc7","id":"C5235135B6694F2C939986069230AA0A"},{"label":"sdc8","value":"sdc8","id":"34FBBF916EDB482D9CB0EDB489ED4C0B"},{"label":"sdc9","value":"sdc9","id":"C9844B7D97114AC29CBD89C6A1B704E3"},{"label":"sdc testing 22","value":"sdctesting22","id":"CDEBE56C57704B5F9DBC95C238285957"},{"label":"smart data test","value":"smartdatatest","id":"364E2737AFD243039D487DAEE7D1D2AC"},{"label":"test sdc","value":"testsdc","id":"BA4C716BABBB43569969F4CF528E3F4B"},{"label":"test sdc1","value":"testsdc1","id":"68806499A1124E3E8FF908BBE8ED490E"},{"label":"test sdc2","value":"testsdc2","id":"B7DC9615AE164E7D9B1B599CF1DE0C8D"},{"label":"test sdc22","value":"testsdc22","id":"5AE3D064ED7746EAA05445B0C1DC963C"},{"label":"test sdc3","value":"testsdc3","id":"6569EDA490694716861EE6631D98A2AB"}];
    return values;
    // var values = [];
    // const URLArray = window.location.pathname.split("/");
    // URLArray[3] = JSON.parse(atob(decodeURIComponent(URLArray[3])));
    // let formId = URLArray[3]['formId'];
    // const inputparams = {
    //   formId: `${formId}`,
    //   onlySDC: true
    // };
    // const inputparamsjson = JSON.stringify(inputparams);
    // // console.log(inputparamsjson);
    // const encodedInput = new CustomEncoder().encodeValue(inputparamsjson);
    // const body = `queuedExecute=false&messageFormat=custom&inputMessage=${encodedInput}`;

    // let workflowdata = await Formio.makeStaticRequest(
    //   `${localStorage.getItem('UMP_url')}/UMP/API/v3/applications/DIGITAL_FORMS/execute/DIGITAL_FORMS_PA_GET_ALL_WORKFLOWS?${body}`,
    //   "POST",
    //   null,
    //   {
    //     headers: {
    //       "Authorization": "Bearer " + localStorage.getItem("token"),
    //       "Accept": "application/json",
    //       "Content-type": 'application/x-www-form-urlencoded'
    //     },
    //   }
    // )
    // // workflowdata.then((result) => {
    //   // console.log("result" + result);
    //   let result = workflowdata;
    //   if (result && result.status === 'Success') {
    //     try {
    //       let wfs = result.workflows;
    //       for (let i = 0; i < wfs.length; i++) {
    //         values.push({
    //           label: wfs[i].wfTitle,
    //           value: wfs[i].wfName,
    //           id: wfs[i].wfId
    //         });
    //       }
    //     } catch (err) {

    //     }

    //   } else {
    //     if (workflowdata && workflowdata.error) {
    //       console.log("error on getting work flows" + workflowdata.error);
    //     }
    //   }
    //   return values;
      // resolve(values);
    // })
  // });
}


