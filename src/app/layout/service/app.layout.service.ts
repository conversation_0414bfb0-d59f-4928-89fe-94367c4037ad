import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { SettingsService } from 'src/app/services/settings.service';

export interface AppConfig {
    inputStyle: string;
    colorScheme: string;
    theme: string;
    ripple: boolean;
    menuMode: string;
    scale: number;
}

interface LayoutState {
    staticMenuDesktopInactive: boolean;
    overlayMenuActive: boolean;
    profileSidebarVisible: boolean;
    configSidebarVisible: boolean;
    staticMenuMobileActive: boolean;
    menuHoverActive: boolean;
}

@Injectable({
    providedIn: 'root',
})
export class LayoutService {

    cursorX: number = 0;
    cursorY: number = 0;
    sidebar: any;
    // profileData: any;
    defaultPage: any;
    returnURL: string;

    // @HostListener('document:mousemove', ['$event'])
    // onMouseMove(event: MouseEvent) {
    //     this.cursorX = event.clientX;
    //     console.log('host',this.sidebar)
    //     console.log('xxx',this.cursorX)
    //     if (this.sidebar === 'Collapsed') {
    //         if (this.cursorX < 15) {
    //             this.state.overlayMenuActive = true;
    //         } else if (this.cursorX > 190) {
    //             this.state.overlayMenuActive = false;
    //         }
    //     }
    // }
    
    // public sidebarChange: BehaviorSubject<string>;
    public profileData: BehaviorSubject<object>;
    private companySettings: any;

    constructor(private settingService: SettingsService, private messageService: MessageService, private route: ActivatedRoute) {
        // this.sidebar = localStorage.getItem('sidebar');
        this.returnURL = this.route.snapshot.queryParams['returnUrl']
        if (localStorage.getItem('token')) {
            this.getProfileSetting();
        }
        // this.sidebarChange = new BehaviorSubject('Expanded');
        this.profileData = new BehaviorSubject({});
        // this.getSidebar().subscribe((res: any)=>{ // sidebar is expande and collaps multiple time while login
        //     this.sidebar = res;
        //     // console.log('service sidebar',this.sidebar)
        //     if (this.sidebar === 'Collapsed') {
        //         this.onButtonHover();
        //     } else {
        //         this.onClickSidebar();
        //     }
        // });   
        
        // if (this.returnURL) {
        //     this.router.navigate([this.returnURL]);
        // } else {
        //     this.router.events.subscribe(event => {
        //     // console.log('navigation',event)
        //     if (event instanceof NavigationEnd) {
        //         if (this.defaultPage && event.urlAfterRedirects === '/') {
        //         console.log('urlAfterRedirects',event.urlAfterRedirects === '/')
        //         this.router.navigate([this.defaultPage]);
        //         }
        //     }
        //     });
        // }     
        
    }
    config: AppConfig = {
        ripple: false,
        inputStyle: 'outlined',
        menuMode: 'overlay',
        colorScheme: 'light',
        theme: 'lara-light-indigo',
        scale: 14,
    };

    state: LayoutState = {
        staticMenuDesktopInactive: true,
        overlayMenuActive: false,
        profileSidebarVisible: false,
        configSidebarVisible: false,
        staticMenuMobileActive: false,
        menuHoverActive: false
    };

    private configUpdate = new Subject<AppConfig>();

    private overlayOpen = new Subject<any>();

    configUpdate$ = this.configUpdate.asObservable();

    overlayOpen$ = this.overlayOpen.asObservable();

    onMenuToggle() {
        if (this.isOverlay()) {
            this.state.overlayMenuActive = !this.state.overlayMenuActive;
            if (this.state.overlayMenuActive) {
                this.overlayOpen.next(null);
            }
        }

        if (this.isDesktop()) {
            this.config.menuMode = 'overlay';
            this.state.overlayMenuActive = !this.state.overlayMenuActive;
            // if (this.state.staticMenuDesktopInactive) { this.config.menuMode = 'overlay' }
        }
        else {
            this.config.menuMode = 'static';
            // if (this.state.staticMenuDesktopInactive) { this.config.menuMode = 'overlay' }
            this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;
            if (this.state.staticMenuMobileActive) {
                this.overlayOpen.next(null);
            }
        }
    }

    showProfileSidebar() {
        this.state.profileSidebarVisible = !this.state.profileSidebarVisible;
        if (this.state.profileSidebarVisible) {
            this.overlayOpen.next(null);
        }
    }

    showConfigSidebar() {
        this.state.configSidebarVisible = true;
    }

    isOverlay() {
        return this.config.menuMode === 'overlay';
    }

    isDesktop() {
        return window.innerWidth > 991;
    }

    isMobile() {
        return !this.isDesktop();
    }

    onConfigUpdate() {
        this.configUpdate.next(this.config);
    }

    changeTheme(theme: string, colorScheme: string) {
        // console.log('theme called', {theme, colorScheme})
        const themeLink = <HTMLLinkElement>document.getElementById('theme-css');
        const newHref = themeLink.getAttribute('href')!.replace(this.config.theme, theme);
        this.config.colorScheme
        this.replaceThemeLink(newHref, () => {
            this.config.theme = theme;
            this.config.colorScheme = colorScheme;
            this.onConfigUpdate();
        });
        localStorage.setItem('theme',JSON.stringify({theme: theme, colorScheme: colorScheme}))
    }

    replaceThemeLink(href: string, onComplete: Function) {
        const id = 'theme-css';
        const themeLink = <HTMLLinkElement>document.getElementById('theme-css');
        const cloneLinkElement = <HTMLLinkElement>themeLink.cloneNode(true);

        cloneLinkElement.setAttribute('href', href);
        cloneLinkElement.setAttribute('id', id + '-clone');

        themeLink.parentNode!.insertBefore(cloneLinkElement, themeLink.nextSibling);

        cloneLinkElement.addEventListener('load', () => {
            themeLink.remove();
            cloneLinkElement.setAttribute('id', id);
            onComplete();
        });
    }

    // getSidebar(): Observable<any> {
    //     return this.sidebarChange.asObservable();
    // }

    onButtonHover() {
        // console.log('hoverr',this.sidebar)
        if (this.sidebar === 'Collapsed') {
            this.config.menuMode = 'overlay';
            this.state.overlayMenuActive = true;
            // console.log('hoverr',this.config.menuMode)
        }
    }

    onClickSidebar() {
        // console.log('sidebar',this.sidebar)
        if (this.sidebar === 'Expanded') {
            this.config.menuMode = 'static';
            this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;  
            // console.log('this.state.staticMenuDesktopInactive',this.state.staticMenuDesktopInactive)
        }
    //   if (!this.state.staticMenuDesktopInactive) { this.config.menuMode = 'overlay'; }
    }

    async getProfileSetting() {
        // this.profileData = new BehaviorSubject({});
        this.companySettings = localStorage.getItem('companysettings') ? JSON.parse(localStorage.getItem('companysettings')) : null;

        this.settingService.getPersonalization().subscribe(response => {
            if (response.status.toLowerCase() === 'success') {
                this.profileData.next(response);
                if (response?.profileSettings?.sidebar) {
                    this.sidebar = response.profileSettings.sidebar;
                    if (this.sidebar === 'Collapsed') {
                        this.onButtonHover();
                    } else {
                        this.onClickSidebar();
                    }
                } else {
                  this.sidebar = "Expanded";
                }
                // this.sidebarChange.next(this.sidebar)
                if (response?.profileSettings?.defaultPage) {
                    this.defaultPage = response?.profileSettings?.defaultPage;
                    localStorage.setItem('defaultPage', this.defaultPage);
                }
                
                if (response.profileSettings?.theme && this.companySettings?.company?.allowThemeSelection) {
                    this.changeTheme(response.profileSettings?.theme.theme, response.profileSettings?.theme.colorScheme);
                } else {
                    this.companySettings?.company?.theme?.theme && this.companySettings?.company?.theme?.colorScheme
                     ? this.changeTheme(this.companySettings?.company?.theme.theme, this.companySettings?.company?.theme.colorScheme) 
                     : this.changeTheme('lara-light-indigo', 'light');
                }
                if (response.profileSettings?.formDesigner) {
                    const formDesignerOption = response.profileSettings?.formDesigner;
                    localStorage.setItem('Designer', formDesignerOption);
                }
                
            } else {
              const errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: errmsg });
            }
        });
    }

    getProfileData(): Observable<any> {
        return this.profileData.asObservable();
    }

    destroyObservable() {
        this.profileData.next({});
    }

}
