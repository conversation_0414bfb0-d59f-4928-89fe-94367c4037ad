<div class="layout-topbar ">
    <!-- topbar strat
    <div class="flex flex-row justify-content-between align-items-center w-full gap-4">
        <div class="cursor-pointer" (click)="gotoAboutPage()">
            <p-avatar *ngIf="base64comapnyimage" [image]="transform()" size="xlarge"></p-avatar>
            <p-avatar *ngIf="!base64comapnyimage" image="assets/images/{{layoutService.config.colorScheme === 'light' ? 'product_logo_dark' : 'product_logo_light'}}.png" size="xlarge"></p-avatar>
        </div>
        <div class="w-full">
            <p-menubar [model]="model" (click)="activeMenu($event)" [style]="{ 'border': 'none' , 'background': 'none' }"></p-menubar>
        </div>
        <div class="flex flex-row align-items-center justify-content-end">
            <p-avatar *ngIf="profileURL" [image]="profileURL" shape="circle" [style]="{ 'margin-top': '0.5rem', color: '#ffffff' }"></p-avatar>
            <p-avatar *ngIf="!profileURL" [label]="convertNameToAvatar(firstName)" [style]="{ 'background-color': 'var(--primary-color)', color: '#ffffff' }" shape="circle"></p-avatar>
            <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:120px;margin-left: 4px;">{{firstName + ' ' + lastName}}</span>
        </div>
    </div>
    topbar end -->

    <!-- <a class="layout-topbar-logo" routerLink="">
        <img src="assets/layout/images/{{layoutService.config.colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg" alt="logo">
        <span>SAKAI</span>
    </a>

    <button #menubutton class="p-link layout-menu-button layout-topbar-button" (click)="layoutService.onMenuToggle()">
        <i class="pi pi-bars"></i>
    </button>

    <button #topbarmenubutton class="p-link layout-topbar-menu-button layout-topbar-button" (click)="layoutService.showProfileSidebar()">
        <i class="pi pi-ellipsis-v"></i>
    </button>

    <div #topbarmenu class="layout-topbar-menu" [ngClass]="{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}">
        <button class="p-link layout-topbar-button">
            <i class="pi pi-calendar"></i>
            <span>Calendar</span>
        </button>
        <button class="p-link layout-topbar-button">
            <i class="pi pi-user"></i>
            <span>Profile</span>
        </button>
        <button class="p-link layout-topbar-button" [routerLink]="'/documentation'">
            <i class="pi pi-cog"></i>
            <span>Settings</span>
        </button>
    </div> -->
</div>