<div class="layout-wrapper" [ngClass]="containerClass">
    <!-- <app-topbar *ngIf="menu === 'Topbar'"></app-topbar> -->
    <p-toast></p-toast>
    <div [class.hidden]="!show">
        <div class="loader-overlay">
            <p-progressSpinner *ngIf="show"></p-progressSpinner>
        </div>
    </div>
    
    <div class="layout-sidebar" [ngStyle]="{'left': layoutService.state.overlayMenuActive ? '1rem' : '0rem' }">
        <app-sidebar></app-sidebar>
    </div>
    
    <div class="layout-main-container" [ngStyle]="{'padding': menu === 'Sidebar' ? '1rem' : '6rem 1rem 1rem 1rem' }">
        <div class="layout-main">
            <router-outlet></router-outlet>
        </div>
        <!-- <app-footer></app-footer> -->
    </div>
    <app-config></app-config>
    <div class="layout-mask"></div>
</div>
