<div class="layout-menu">
    <div class="flex flex-column h-full gap-2">
        <li class="text-center cursor-pointer" (click)="gotoAboutPage()">
            <img *ngIf="base64comapnyimage" [src]="transform()" width="80%"/>
            <img *ngIf="!base64comapnyimage" src="assets/images/{{layoutService.config.colorScheme === 'light' ? 'product_logo_dark' : 'product_logo_light'}}.png" alt="logo" width="80%"/>
        </li>
        <div class="flex flex-column justify-content-between h-full">
            <ng-container *ngFor="let item of model; let i = index;">
                <li app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
                <li *ngIf="item.separator" class="menu-separator"></li>
            </ng-container>
            <div class="flex flex-row gap-2 align-items-center ml-2 mb-2">
                <p-avatar *ngIf="profileURL" [image]="profileURL" shape="circle" [style]="{ 'margin-top': '0.25rem', color: '#ffffff' }"></p-avatar>
                <p-avatar *ngIf="!profileURL" [label]="convertNameToAvatar(firstName)" [style]="{ 'background-color': 'var(--primary-color)', color: '#ffffff' }" shape="circle"></p-avatar>
                <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:120px">{{firstName + ' ' + lastName}}</span>
            </div>
        </div>
    </div>
</div>
<!-- <p-toolbar class="sticky top-0 z-5" styleClass="shadow-5">
    <div class="p-toolbar-group-start">
        <div class="flex flex-row justify-content-between align-items-center">
            <div class="text-center cursor-pointer" (click)="gotoAboutPage()">
                <img *ngIf="base64comapnyimage" [src]="transform()" width="80%"/>
                <img *ngIf="!base64comapnyimage" src="assets/images/{{layoutService.config.colorScheme === 'light' ? 'product_logo_dark' : 'product_logo_light'}}.png" alt="Prime Blocks" width="80%"/>
            </div>
            <div>
                <p-megaMenu [model]="model"/>
            </div>
            <div class="flex flex-row gap-2 align-items-center" style="position: absolute;bottom: 10px;">
                <p-avatar *ngIf="profileURL" [image]="profileURL" shape="circle" [style]="{ 'margin-top': '0.25rem', color: '#ffffff' }"></p-avatar>
                <p-avatar *ngIf="!profileURL" [label]="convertNameToAvatar(firstName)" [style]="{ 'background-color': 'var(--primary-color)', color: '#ffffff' }" shape="circle"></p-avatar>
                <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:120px">{{firstName + ' ' + lastName}}</span>
            </div>
        </div>
    </div>
</p-toolbar> -->
