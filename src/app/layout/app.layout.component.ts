import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { delay, filter, Subscription } from 'rxjs';
import { LayoutService } from "./service/app.layout.service";
import { AppSidebarComponent } from "./app.sidebar.component";
import { AppTopBarComponent } from './app.topbar.component';
import { LoaderService, LoaderState } from '../services/loader.service';
import { SettingsService } from '../services/settings.service';
import { DialogService } from 'primeng/dynamicdialog';
import { AboutComponent } from '../components/settings/about/about.component';

@Component({
    selector: 'app-layout',
    templateUrl: './app.layout.component.html',
    providers: [DialogService]
})
export class AppLayoutComponent implements OnInit,OnDestroy {
    cursorX: number = 0;
    cursorY: number = 0;

    // @HostListener('document:mousemove', ['$event'])
    // onMouseMove(event: MouseEvent) {
    //     this.cursorX = event.clientX;
    //     this.cursorY = event.clientY;
    //     console.log('yyyy',this.cursorY)
    //     // console.log('xxxx',(this.cursorX > 0 && this.cursorX < 40))
    //     if (this.layoutService.isOverlay()) {
    //         if (!this.layoutService.state.overlayMenuActive) {
    //             if ((this.cursorX > 0 && this.cursorX < 40) && (this.cursorY > 70 && this.cursorY < 115)) {
    //                 this.layoutService.state.overlayMenuActive = true;
    //             } else {
    //                 this.layoutService.state.overlayMenuActive = false;
    //             }
    //         } else {
    //             if (this.cursorX < 190) {
    //                 this.layoutService.state.overlayMenuActive = true;
    //             } else {
    //                 this.layoutService.state.overlayMenuActive = false;
    //             }
    //         }
    //     }
    // }

    public show: boolean;
    private subscription: Subscription;

    overlayMenuOpenSubscription: Subscription;

    menuOutsideClickListener: any;

    profileMenuOutsideClickListener: any;

    @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;

    @ViewChild(AppTopBarComponent) appTopbar!: AppTopBarComponent;

    appVersion: any;
    public menu;

    constructor(public layoutService: LayoutService, public renderer: Renderer2, public router: Router, private loaderService: LoaderService, private settingservice: SettingsService, public dialogService: DialogService) {
        this.overlayMenuOpenSubscription = this.layoutService.overlayOpen$.subscribe(() => {
            if (!this.menuOutsideClickListener) {
                this.menuOutsideClickListener = this.renderer.listen('document', 'click', event => {
                    const isOutsideClicked = !(this.appSidebar.el.nativeElement.isSameNode(event.target) || this.appSidebar.el.nativeElement.contains(event.target) 
                        || (this.appTopbar && this.appTopbar.menuButton.nativeElement.isSameNode(event.target)) || (this.appTopbar && this.appTopbar.menuButton.nativeElement.contains(event.target)));
                    
                    if (isOutsideClicked) {
                        this.hideMenu();
                    }
                });
            }

            if (!this.profileMenuOutsideClickListener) {
                this.profileMenuOutsideClickListener = this.renderer.listen('document', 'click', event => {
                    const isOutsideClicked = !((this.appTopbar && this.appTopbar.menu.nativeElement.isSameNode(event.target)) || (this.appTopbar && this.appTopbar.menu.nativeElement.contains(event.target))
                        || (this.appTopbar && this.appTopbar.topbarMenuButton.nativeElement.isSameNode(event.target)) || (this.appTopbar && this.appTopbar.topbarMenuButton.nativeElement.contains(event.target)));

                    if (isOutsideClicked) {
                        this.hideProfileMenu();
                    }
                });
            }

            if (this.layoutService.state.staticMenuMobileActive) {
                this.blockBodyScroll();
            }
        });

        this.router.events.pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(() => {
                this.hideMenu();
                this.hideProfileMenu();
            });
    }

    ngOnInit() {
        this.menu = 'Sidebar'
        // this.layoutService.getTopOrSide().subscribe(
        // (res: any)=>{ 
        //     this.menu = res;
        //     if (this.menu === 'Topbar') {
        //         this.layoutService.state.staticMenuDesktopInactive = true;
        //     }
        // });
        this.subscription = this.loaderService.loaderState.pipe(
          delay(0),
        )
        .subscribe((state: LoaderState) => {
          this.show = state.show;
          // console.log(this.show);
        });
        // API to get app version
        this.settingservice.loadappversion().subscribe(
            {
                next: (versiondata: any) => {
                    this.appVersion = versiondata.version;
                    // this.buildNumber = versiondata.buildNumber;
                },
                error: (error: any) => {
                    console.error('File not found', error.error);
                }
            }
        );
    }

    hideMenu() {
        this.layoutService.state.overlayMenuActive = false;
        this.layoutService.state.staticMenuMobileActive = false;
        this.layoutService.state.menuHoverActive = false;
        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
            this.menuOutsideClickListener = null;
        }
        this.unblockBodyScroll();
    }

    hideProfileMenu() {
        this.layoutService.state.profileSidebarVisible = false;
        if (this.profileMenuOutsideClickListener) {
            this.profileMenuOutsideClickListener();
            this.profileMenuOutsideClickListener = null;
        }
    }

    blockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.add('blocked-scroll');
        }
        else {
            document.body.className += ' blocked-scroll';
        }
    }

    unblockBodyScroll(): void {
        if (document.body.classList) {
            document.body.classList.remove('blocked-scroll');
        }
        else {
            document.body.className = document.body.className.replace(new RegExp('(^|\\b)' +
                'blocked-scroll'.split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
        }
    }

    get containerClass() {
        return {
            'layout-theme-light': this.layoutService.config.colorScheme === 'light',
            'layout-theme-dark': this.layoutService.config.colorScheme === 'dark',
            'layout-overlay': this.layoutService.config.menuMode === 'overlay',
            'layout-static': this.layoutService.config.menuMode === 'static',
            'layout-static-inactive': this.layoutService.state.staticMenuDesktopInactive && this.layoutService.config.menuMode === 'static',
            'layout-overlay-active': this.layoutService.state.overlayMenuActive,
            'layout-mobile-active': this.layoutService.state.staticMenuMobileActive,
            'p-input-filled': this.layoutService.config.inputStyle === 'filled',
            'p-ripple-disabled': !this.layoutService.config.ripple
        }
    }

    gotoAboutPage() {
        const ref = this.dialogService.open(AboutComponent, {
            header: 'About',
            width: '50%',
            contentStyle: { 'overflow-y': 'scroll' },
        });
        ref.onClose.subscribe();
    }

    ngOnDestroy() {
        if (this.overlayMenuOpenSubscription) {
            this.overlayMenuOpenSubscription.unsubscribe();
        }

        if (this.menuOutsideClickListener) {
            this.menuOutsideClickListener();
        }
        this.subscription.unsubscribe();
    }
}
