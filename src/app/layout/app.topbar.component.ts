import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { LayoutService } from "./service/app.layout.service";
import { AuthenticationService } from '../services/authentication.service';
import { PopupService } from '../services/popup.service';
import { SettingsService } from '../services/settings.service';
import { FormsService } from '../services/forms.service';
import { DialogService } from 'primeng/dynamicdialog';
import { DomSanitizer } from '@angular/platform-browser';
import { AboutComponent } from '../components/settings/about/about.component';
import { ConfigService } from '../services/config.service';

@Component({
    selector: 'app-topbar',
    templateUrl: './app.topbar.component.html'
})
export class AppTopBarComponent implements OnInit {

    items!: MenuItem[];
    role: string;
    model: MenuItem[] | undefined;
    profileURL: any
    firstName: string;
    lastName: string;
    logintype: string;
    companysettings: any;
    base64comapnyimage: any;

    @ViewChild('menubutton') menuButton!: ElementRef;

    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;

    @ViewChild('topbarmenu') menu!: ElementRef;

    constructor(public layoutService: LayoutService, 
        private authservice: AuthenticationService, 
        private popupservice: PopupService, 
        private settingservice: SettingsService,
        private formsService: FormsService,
        private sanitizer: DomSanitizer,
        public dialogService: DialogService,
        private configService: ConfigService) { }

    ngOnInit(): void {
        const usr = this.authservice.decode();
        this.role = usr.ROLE;
        this.firstName = usr.FNAME;
        this.lastName = usr.LNAME;
        if (this.role.toLowerCase() === 'admin') {
            this.model = [
                { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        { label: 'Users', icon: 'pi pi-fw pi-users', routerLink: ['/users'] },
                        { label: 'Reports', icon: 'pi pi-fw pi-check-circle', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        // { label: 'Flows', icon: 'pi pi-fw pi-qrcode', routerLink: ['/flows'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog',
                            items: [
                                {
                                    label: 'General',
                                    icon: 'pi pi-fw pi-globe',
                                    routerLink: ['/setting/general']
                                },
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Customization',
                                    icon: 'pi pi-fw pi-sliders-h',
                                    routerLink: ['/setting/customization']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Dashboard',
                                    icon: 'pi pi-fw pi-desktop',
                                    routerLink: ['/setting/dashboard']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                                // {
                                //     label: 'About',
                                //     icon: 'pi pi-fw pi-exclamation-circle',
                                //     routerLink: ['/setting/about']
                                // }
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
            ];
        } else if (this.role.toLowerCase() === 'manager') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Reports', icon: 'pi pi-fw pi-check-circle', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        } else if (this.role.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() !== 'production') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        { label: 'Reports', icon: 'pi pi-fw pi-check-circle', routerLink: ['/reports'] },
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/email-template'],
                            items: [
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Dashboard',
                                    icon: 'pi pi-fw pi-desktop',
                                    routerLink: ['/setting/dashboard']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        } else if (this.role.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() === 'production') {
            this.model = [
                {
                    items: [
                        { label: 'Home', icon: 'pi pi-fw pi-home', routerLink: ['/home']},
                        { label: 'Forms', icon: 'pi pi-fw pi-id-card', routerLink: ['/forms']},
                        { label: 'Personalization', icon: 'fa-solid fa-wand-magic-sparkles', routerLink: ['/personalization'] },
                        { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/setting/email-template'],
                            items: [
                                {
                                    label: 'Mail Templates',
                                    icon: 'pi pi-fw pi-envelope',
                                    routerLink: ['/setting/email-template']
                                },
                                {
                                    label: 'PDF',
                                    icon: 'pi pi-fw pi-file-pdf',
                                    routerLink: ['/setting/pdf']
                                },
                                {
                                    label: 'Communication',
                                    icon: 'pi pi-fw pi-comments',
                                    routerLink: ['/setting/communication']
                                },
                                {
                                    label: 'Developer',
                                    icon: 'pi pi-fw pi-user-edit',
                                    routerLink: ['/setting/developer']
                                },
                            ]
                        },
                        { label: 'Logout', icon: 'pi pi-fw pi-sign-out', command: () => { this.onLogout();} }
                    ]
                }
            ];
        }
        if(this.configService.app && this.configService.app.length > 0) {
            let cols = 1, rows = 1;
            this.configService.app.forEach(element => {
              let appobj = {
                title:element.name,
                icon: element.icon,
                iconcolor: element.icon || '#000',
                desc: element.description || 'App Description',
                cols: cols,
                rows: rows,
                url: element.url,
                target: element.target
              };
            //   this.externalapps.push(appobj);
            });
      
          }
          
          let res = localStorage.getItem('companysettings');
          if (res) {
            this.useComapnySettingsData(JSON.parse(res));
      
          } else {
            this.settingservice.getcompanysettings()
              .subscribe(
                res => {
                  localStorage.setItem('companysettings', JSON.stringify(res));
                  this.useComapnySettingsData(res);
                }
              );
          }
        this.getProfileSetting();
    }

    onLogout() {
        if (this.logintype === 'SSO') {
        this.popupservice.openSSO('logout');
      } else {
        this.authservice.removeTokenAndlogOut();
      }
    }

    getProfileSetting() {
        this.settingservice.getPersonalization().subscribe(response => {
            if (response.status.toLowerCase() === 'success') {
              if (response.userAvatar) {
                this.profileURL = response.userAvatar;
              }
            } else {
              console.log(response?.error)
            }
        });
    }

    useComapnySettingsData(res){
        this.companysettings = res.company;
        this.formsService.companySettings.next(res);
        this.base64comapnyimage = res.company.attachmentId;
    }

    transform() {
        return this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpeg;base64,' + this.base64comapnyimage);
    }

    gotoAboutPage() {
        const ref = this.dialogService.open(AboutComponent, {
            header: 'About',
            width: '50%'
        });
        ref.onClose.subscribe();
    }

    convertNameToAvatar(str: string) {
        return str.charAt(0).toUpperCase();
    }

    activeMenu(e) {
        console.log('e',e)
    }

}
