import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface LoaderState {
  show: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  private loaderSubject = new Subject<LoaderState>();
  loaderState = this.loaderSubject.asObservable();
  constructor() { }
  show() {
    // console.log('reached service show');
    this.loaderSubject.next(<LoaderState>{ show: true });
  }
  hide() {
    // console.log('reached service hide');
    this.loaderSubject.next(<LoaderState>{ show: false });
  }
}
