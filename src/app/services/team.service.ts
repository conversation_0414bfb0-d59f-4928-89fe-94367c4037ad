import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { UtilsService } from './utils.service';
@Injectable({
  providedIn: 'root'
})
export class TeamsService {
  public baseUrl: string;
  private UMP_URL: string;
  constructor(private http: HttpClient, private utilsservice: UtilsService) {
    this.baseUrl = this.utilsservice.getUMPUrl();
    this.UMP_URL = `${this.baseUrl}/UMP/API/v3/applications/DIGITAL_FORMS/execute`;
  }
  getteams(
    query: string,
    teams: any,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    formids: any,
    createdteamid: string,
    sortcolumn?: string
  ): Observable<any> {
    const teamsarr = teams.toString();
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      teams: teamsarr,
      query: query,
      sort_order: sortOrder,
      sort_on: sortcolumn,
      filter: {
        formId: formids.formId,
        formsetId: formids.formsetId,
        newTeamId: createdteamid
      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };

    // if (null !== query || teams === null || teams === '') {
      // console.log(query);
      return this.http
        .post<any>(
          `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_TEAMS`,
          {},
          httpOptions
        );
    //  }
  }
  getmanagers(
    sortOrder: string,
    pageNumber: number,
    pageSize: number
  ): Observable<any> {
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      sort_order: sortOrder
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_MANAGERS`,
        {},
        httpOptions
      );
  }
  createteams(teamsform): Observable<any> {
    const inputParams = teamsform;
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_CREATE_TEAM`,
        {},
        httpOptions
      );
  }
  updatTeamDescription(teamid, teamdescription, teammanager): Observable<any> {
    const inputParams = {
      id: teamid,
      teamDescription: teamdescription,
      teamManager: teammanager
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_UPDATE_TEAM`,
        {},
        httpOptions
      );
  }
  getteamdetails(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_TEAM_DETAILS`,
        {},
        httpOptions
      );
  }
  getformsofteam(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_FORMS_OF_TEAM`,
        {},
        httpOptions
      );
  }
  getformsetsofteam(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_FORMSET_OF_TEAM`,
        {},
        httpOptions
      );
  }
  deteteteams(teams): Observable<any> {
    const teamsarr = teams.toString();
    const inputParams = {
      teamId: teamsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_DELETE_TEAM`,
        {},
        httpOptions
      );
  }
  deteteforms(teamid, formid): Observable<any> {
    // let teamsarr = teams.toString();
    const inputParams = {
      teamId: teamid,
      formId: formid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_FORM_FROM_TEAM`,
        {},
        httpOptions
      );
  }
  deteteformset(teamid, formsetid): Observable<any> {
    // let teamsarr = teams.toString();
    const inputParams  = {
      teamId: teamid,
      formId: formsetid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_FORMSET_FROM_TEAM`,
        {},
        httpOptions
      );
  }
}
