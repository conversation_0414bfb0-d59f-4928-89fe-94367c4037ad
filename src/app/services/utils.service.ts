import { Injectable, Ng<PERSON>one } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { CustomEncoder } from '../shared/customencoder';
import { ConfigService } from './config.service';
import { BehaviorSubject, Observable } from 'rxjs';
import * as jsonLogic from 'json-logic-js/logic.js';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: "root",
})
/**
 *
 * Service class consisting of basic utility methods
 *
 */
export class UtilsService {
 // public canDeploy = new BehaviorSubject<boolean>(false);
  private eventSource: EventSource;
  constructor(
    private appConfigService: ConfigService,
    private messageService: MessageService,private http: HttpClient,
    private _zone: NgZone
  ) {}

  /**
   * @returns {string} - Basic UMP Url which will be used in making http requests
   */
  getUMPUrl(): string {
    return this.appConfigService.apiBaseUrl;
  }
  public baseUrl = this.getUMPUrl();
  private UMP_URL = `${this.baseUrl}/UMP/API/v3/applications/DIGITAL_FORMS/execute`;
  /**
   * @param {string} contentType - For json or x-www-form-urlencoded
   * @param {string} accept - Defaults to application/json type
   *
   * @returns {HttpHeaders} - Request content headers
   */

  setBasicHeaders(contentType: string): HttpHeaders {
    let Headers = new HttpHeaders();
    Headers = Headers.append("Content-Type", contentType);
    Headers = Headers.append("accept", "application/json");
    return Headers;
  }
  setHeaders() {
    let headers_object = new HttpHeaders();
    headers_object = headers_object.append('Content-Type','application/x-www-form-urlencoded');
    headers_object = headers_object.append('accept', 'application/json');
    const headers = {
      headers: headers_object,
    };
    return headers;
  }
  /**
     * @param {string} queuedExecute - Default parameter
     * @param {string} messageFormat - Default parameter
     * @param {string} inputMessage - Request data to send
     * @param {string} sensitive - Specifies the request data is sensitive or not
     * @param {string} externalReference - For making Async request calls
     *
     * @returns {HttpParams} - Request parameters
  */
  setParams(inputMessage: any, sensitive?: string, externalReference?: string, queuedExecute='false'): HttpParams {
    const inputParams = JSON.stringify(inputMessage);
    let params = new HttpParams({ encoder: new CustomEncoder() });
    params = params.append('queuedExecute', queuedExecute);
    params = params.append('messageFormat', 'custom');
    params = params.append('inputMessage', inputParams);
    params = params.append('sensitive', sensitive);
    params = params.append('externalReference', externalReference);
    return params;
  }

  /**
   *
   * @param inputMessage - Request data to send within the body
   *
   * @returns {string} - body for http request
   */
  setBody(inputMessage: any): string {
    const inputParams = JSON.stringify(inputMessage);
    const encodedInput = new CustomEncoder().encodeValue(inputParams);
    const body = `queuedExecute=false&messageFormat=custom&inputMessage=${encodedInput}`;
    return body;
  }
  /**
   *
   * @param {number} length - Length of the string to be generated
   *
   * @returns {string} - Random String
   */
  generateRandomString(length: number): string {
    let randomString = "";
    const characters =
      "0123ABCDEFGHIJPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzKLMNO456789";
    for (let i = length; i > 0; i--) {
      randomString += characters[Math.floor(Math.random() * characters.length)];
    }
    return randomString;
  }
  /**
   *
   * @param workflowJsonData - The json data representing workflow node
   */
  getAllNodeNames(workflowJsonData: any) {
    const allnodes = workflowJsonData.nodes;
    const nodenamesarr = [];
    const values = Object.values(allnodes);
    values.forEach((element: any) => {
      nodenamesarr.push(element.data.name);
    });
    return nodenamesarr;
  }
  /**
   * @param object - JavaScript object
   *
   * @returns {boolean} - whether Object is empty or not
   */
  isObjEmpty(object: any) {
    return Object.keys(object).length === 0 && object.constructor === Object;
  }
  /**
   * @param object - Object consisting checkbox values
   *
   * @returns {boolean} - whether object of checkbox has atleast one checked or not
   */
  public checkboxvalidity(checkboxgroup: any): boolean {
    let counter = 0;
    Object.values(checkboxgroup).forEach(checked => {
    if (checked) {
      counter++;
    }
    });
    if (counter > 0) {
      return true;
    } else {
      return false;
    }
  }

  /**
   *
   * @param jsonLogicRule - Rule to apply
   * @param selectedParams - User selected parameters
   *
   * @returns {Array} - Empty or array with missing parrameters
   */
  applyJsonLogic(jsonLogicRule: any, selectedParams: any) {
    const jsonLogicData = {};
    selectedParams.map( param => {
      jsonLogicData[param.inputName] = param.values;
    })
    return jsonLogic.apply(
      jsonLogicRule,   // Rule
      jsonLogicData    // Data
    );

  } 
  /**
   *
   * @param {number} nodeid  - Nodeid assiged to node via rete editor
   * @param {string} nodename - Name of the node from previous nodes will be returned
   * @param {any} nodesfound - Used for recursion, to let function know previous nodes found after each iteration
   * @param {any} editorJson - Workflow Editor json of all nodes and connection
   *
   * @returns - All previous nodes found before given nodename
   */
  getpreviousnodes(nodeId: number,selectedNodeName: string,nodesFound: any,editorJson: any) {
    let nodeName = selectedNodeName;
    const previousnodes = nodesFound;
    const preparednode = editorJson.nodes[nodeId];   
       if(nodeName === 'merge'){
        let mergeNodeLength = Object.keys(preparednode.inputs).length;

        for (let key = 0; key < mergeNodeLength; key++) {
          const dynamicNodeName = `merge_${key}`;
          if (preparednode.inputs[dynamicNodeName]?.connections.length > 0) {
            const connections = preparednode.inputs[dynamicNodeName].connections;

            connections.forEach((connection: any) => {
              const incomingNodeId = connection.node;
              const incomingNodeData = editorJson.nodes[incomingNodeId];

              if (incomingNodeData) {
                nodesFound.push(incomingNodeData);
               return this.getpreviousnodes(incomingNodeId, incomingNodeData.name, nodesFound, editorJson);
              }
            });
          }else{
            return previousnodes;
          }
      } 
       }else{
    if (preparednode.inputs[nodeName].connections.length === 0) {
      return previousnodes;
    } else {
      if (preparednode.inputs[nodeName].connections[0].node === 1) {
        return previousnodes;
      } else {
        const incominginputnodeid = preparednode.inputs[nodeName].connections[0].node;
        const incominginputnodedata = editorJson.nodes[incominginputnodeid].name;
        if (incominginputnodedata !== "Condition" && incominginputnodedata !== "SendEmail" && incominginputnodedata !== "FormAlert") {
          const node = editorJson.nodes[incominginputnodeid];
          previousnodes.push(node);
        }
        return this.getpreviousnodes(incominginputnodeid,incominginputnodedata,previousnodes,editorJson);
      }
    }
  }
  }
  /**
   *
   * @param {any} previousnodesfound - All previous nodes found before given nodename
   * @param {string} data - Node data
   * @returns - nodes Tree
   */
  async preparePreviousNodesTree(previousnodesfound: any, data: any) {
    let count = 0;
    let previousNodesTree = [];
    let currentNodesTree = [];
    const EnvironmentTree =  await this.getEnvironmentVariables(data.formId);
    previousnodesfound.forEach(element => {
     if(element.name !== 'calculate') {
       if(element.name === 'execjavascript' || element.name === 'execpython') {        
            let items = [];
            if(typeof(element.data.outputparams) == 'string'){
              element.data.outputparams = JSON.parse(element.data.outputparams)
            }
            if(element.data.outputparams && element.data.outputparams.length > 0) {
              (element.data.outputparams).forEach(outputresult => {
                items.push({
                  label: `${outputresult}`,
                  value:'${' + `${element.data.name}_${outputresult}` + '}'
                });
              });
              const node = {
                label: element.data.name,
                items: items
              };
            previousNodesTree.push(node);
          }

       }
       else if(element.name === 'ForLoop' ) {
        count++; 
        if(previousnodesfound[0].name === 'ForLoop' && count == 1){ 
        const node = {
          label: element.data.name,
          items: [
            {
             label: `Result`,
             value: '${' + `${element.data.name}_result` + '}'
           }
          ]
        };
        previousNodesTree.push(node);
      }else{

      }
       } else {        
        const node = {
          label: element.data.description,
          items: [
            {
              label: `Error`,
              value: '${' + `${element.data.name}_error` + '}'
            },
            {
             label: `Result`,
             value: '${' + `${element.data.name}_result` + '}'
           }
          ]
        };
        previousNodesTree.push(node);
       }


     } else {
       let value = [];
       if(element.data.expressions && element.data.expressions.length > 0){
       element.data.expressions.forEach(expression => {
         value.push({
          label: `${expression.key}`,
          value:'${' + `${element.data.name}_${expression.key}` + '}'
         });
       });
       value.push(
        {
          label: `Error`,
          value: '${' + `${element.data.name}_error` + '}'
        })
       const node = {
         label: element.data.name,
         items: value
       };
       previousNodesTree.push(node);
      }
   }
    });
    if(data.addedExpressions && data.addedExpressions.length > 0) {
        data.addedExpressions.forEach((expressionvar, index) => {
          if(expressionvar.key !== data.currentExpressionKey && data.index > index) {
          currentNodesTree.push({
          label: `${expressionvar.key}`,
          value:'${' + `${data.node.data.name}_${expressionvar.key}` + '}'
          });
        }
        });
    }
    if(previousNodesTree.length > 0 && currentNodesTree.length > 0) {
     return [
      {
        label: 'Others',
        items: [{
          label: 'Flow Input',
          value:'${flowinput}'
          }]
      },
      {
        label: 'Previous Node',
        items: previousNodesTree
      },
      {
          label: 'Current Node',
          items: currentNodesTree
      },
      {
          label: 'Environment',
          items: EnvironmentTree
      }
     ];
    } else if (previousNodesTree.length > 0 && currentNodesTree.length === 0) {
      return [
        {
          label: 'Others',
          items: [{
            label: 'Flow Input',
            value:'${flowinput}'
            }]
        },
        {
          label: 'Previous Node',
          items: previousNodesTree
        },
        {
          label: 'Environment',
          items: EnvironmentTree
        }
      ];
    } else if (previousNodesTree.length === 0 && currentNodesTree.length > 0  ) {
      return [
        {
          label: 'Others',
          items: [{
            label: 'Flow Input',
            value:'${flowinput}'
            }]
        },
        {
          label: 'Current Nodes',
          items: currentNodesTree
        },
        {
          label: 'Environment',
          items: EnvironmentTree
        }
      ];
    } else {
      return [
        {
          label: 'Others',
          items: [{
            label: 'Flow Input',
            value:'${flowinput}'
            }]
        },
        {
        label: 'Environment',
        items: EnvironmentTree
      }
  ];
    }

   }
   /**
    *
    * @param {string} formId - Id of the form to get attributes
    *
    * @returns - All Macro + Attributes variable for Environment Tree
    */
  getEnvironmentVariables(formId: string) {
     let macros = [];
     this.getAttributesForExpressionBuilder(formId)
    .subscribe( async res => {
      const response = res;
      if (response.error === '') {
        if (response.status === 'Success') {
          const attributes = response.attributes;
          if(attributes.length > 0) {
           attributes.forEach(element => {
            let environmentobj = {
              label: element.key,
              value: '${env_'+ element.key + '}',
              icon: `add_to_drive`
            }
             response.macros.push(environmentobj);
          });
        }
        macros = response.macros;
        }
      }
    });
   // Added 2secs delay to wait until get response of DIGITAL_FORMS_PA_GET_FORM_ATTRIBUTES
   return new Promise(resolve => {
     setTimeout(() => {
       if (macros && macros.length > 0) {
         return resolve(macros);
       } else {
         setTimeout(() => {
           return resolve(macros);
         }, 1000);
       }
     }, 1000);
   });
  }
  getAttributesForExpressionBuilder(formId: string) {
    const inputparams = {
      formId: formId
    };
    const body = this.setBody(inputparams);
    const headers = this.setHeaders();
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_FORM_ATTRIBUTES`,
      body,
      headers
    );
  }
  /**
   *
   * @param {any} previousnodesfound - All previous nodes found before given nodename
   *
   * @returns - All nodes outputs results compatible with Angular QueryBuilder fields
   */
  prepareFromfieldsFromPreviousNodes(previousnodesfound: any, isDataMapper?:boolean ) {
    let count = 0;
    let formfields = [];
    let allresultfield=[];
    previousnodesfound.forEach((element) => {
      if (element.name !== "calculate") {
         if(element.name !== "ForLoop"){
        if(element.name === 'execjavascript' || element.name === 'execpython') {
          let items = [];
          let name;
          let key;
          let operators;
          let errorfield;
          let resultfield;
          if(typeof(element.data.outputparams) == 'string'){
            element.data.outputparams = JSON.parse(element.data.outputparams)
          }
          if (element.data.outputparams && element.data.outputparams.length > 0) {       
            (element.data.outputparams).forEach(outputresult => {
              // name = `${element.data.name} ${outputresult}`;
              // key = '${' + `${element.data.name}_${outputresult}` + '}';
              operators = [
                  "contains",
                  "does not contain",
                  "begins with",
                  "does not being with",
                  "ends with",
                  "does not end with",
                  "equals",
                  "not equals",
                  "in",
                  "not in",
                  "is empty",
                  "is not empty",
                  "is null",
                  "is not null",
                  "is empty list",
                  "is not empty list"
                ];
         
              if (outputresult === 'error') {
                name = `${element.data.name} ${outputresult}`;
                key = '${' + `${element.data.name}_${outputresult}` + '}';
                  errorfield = {
                  name: name,
                  type: 'string',
                  input: "text",
                  key: key,
                  operators: operators,
              }
              } else {   
                name = `${outputresult}`;
                key = '${' + `${element.data.name}_${outputresult}` + '}';           
                resultfield = {
                  name: name,
                  type: 'string',
                  input: "text",
                  key: key,
                  operators: operators,
              }
              allresultfield.push(resultfield)
            }
            });
            
          }
          if(errorfield != undefined || resultfield != undefined){
            for(let key in allresultfield){
              formfields.push(allresultfield[key], errorfield);
            }
          }
        }else{
          const errorfield = this.prepareField(
            `${element.data.name}`,
            "error",
            "string"
          );
          const resultfield = this.prepareField(
            `${element.data.name}`,
            "result",
            "string"
          );
          formfields.push(resultfield, errorfield);
        }
       }else{
        count++; 
        if(previousnodesfound[0].name === 'ForLoop' && count == 1){ 
        const resultfield = this.prepareField(
          `${element.data.name}`,
          "result",
          "string"
        );
        formfields.push(resultfield);
        }
       }
      } else {
        if (element.data.expressions && element.data.expressions.length > 0) {
          element.data.expressions.forEach((expression) => {
            formfields.push(this.prepareField(expression.key, element.data.name , expression.type, isDataMapper));
          });
          // const errorfield = {
          //   label: `${element.data.name} Error`,
          //   value: "${" + `${element.data.name}_error` + "}",
          // };
          // formfields.push(errorfield);
          formfields.push(this.prepareField(element.data.name, "error" , "string", isDataMapper));
        }
      }
    });
    formfields  = formfields.reduce((acc, current) => {
      const x = acc.find(item => item.name === current.name);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    return formfields;
  }
  /**
   *
   * @param {string} fieldname - node name for preparing Label for field to display in QueryBuilder
   * @param {string} fieldresulttype - node output for  preparing key for field to use internally in QueryBuilder
   * @param {string} type - node output type for selecting operators in QueryBuilder for parlicular field type
   *
   * @returns - Angular QueryBuilder Compatible field object
   */
  prepareField(fieldname: string, fieldresulttype: string, type: string, isDataMapper?: boolean) {
    let name;
    let key;
    const operators = {
      string: [
        "contains",
        "does not contain",
        "begins with",
        "does not being with",
        "ends with",
        "does not end with",
        "equals",
        "not equals",
        "in",
        "not in",
        "is empty",
        "is not empty",
        "is null",
        "is not null",
        "is empty list",
        "is not empty list"
      ],
      number: [
        "equals",
        "not equals",
        "less than",
        "less than or equals",
        "greater than",
        "greater than or equals",
        "between",
        "not between",
      ],
      date: [
        "equals",
        "not equals",
        "less than",
        "less than or equals",
        "greater than",
        "greater than or equals",
        "between",
        "not between",
        "is empty",
        "is not empty",
        "is null",
        "is not null",
      ],
    };
    if (fieldresulttype === "error") {
      name = `${fieldname} Error`;
      key = "${" + `${fieldname}_${fieldresulttype}` + "}";
    } else if (fieldresulttype === "result") {
      name = `${fieldname} Result`;
      key = "${" + `${fieldname}_${fieldresulttype}` + "}";
    } else {
      name =  `${fieldname}`//`${fieldresulttype} ${fieldname}`;
      if(isDataMapper) {
       key  = '${' + `${fieldresulttype}_${fieldname}` + '}';
      } else {
        key = "${" + `${fieldresulttype}_${fieldname}` + "}";
      }
    }
    return {
      name: name,
      type: type,
      input: "text",
      key: key,
      operators: operators[type],
    };
  }
  /**
   *
   * @param {string} workflowId - Id of the workflow to get
   *
   * @returns {any} - Candeploy boolean obserable
   */

  /**
   *
   * @param {string} url - url to get event stream
   * @returns {EventSource} - EventSource object
   */
  private getEventSource(url: string): EventSource {
    if (this.eventSource) {
      // console.log('Connection to server closed.');
      this.eventSource.close();
    }
    this.eventSource = new EventSource(url);
    return this.eventSource;
  }
  /**
   *
   * @param url - url to get event stream
   * @returns {Observable} - Observable stream of event sent by server url
   */
  getServerSentEvent(url: string): Observable<any> {
    return new Observable(observer => {
      const eventSource = this.getEventSource(url);
      eventSource.onopen = (ev) => {
        // console.log('Connection to server opened.', ev);
      };
      eventSource.onmessage = event => {
        // console.log(JSON.parse(JSON.parse(event.data).NotificationMessage));
        const eventdata = JSON.parse(JSON.parse(event.data).NotificationMessage);
        if(eventdata.event === "END-OF-STREAM") {
          // console.log('Connection to server closed.');
          eventSource.close();
        } else {
        this._zone.run(() => {
          observer.next(event);
        });
      }
      };
      eventSource.onerror = error => {
        this._zone.run(() => {
          observer.error(error);
        });
      };
      // return () => eventSource.close();
    });
  }
  /**
   *
   * @param {string} workflowId - Id of the workflow to save
   * @param {any} editorJson - Workflow Editor json of all nodes and connection
   * @param {boolean} saveNodedata - Whether the call is to save node data or entire workflow
   */


  isExpand:boolean=false;

  getIsExpand(){
    return this.isExpand;
  }
  
  setIsExpand(value:boolean){
    this.isExpand = value;
  }
}
