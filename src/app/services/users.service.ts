import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { UtilsService } from './utils.service';
@Injectable({
  providedIn: 'root'
})
export class UsersService {
  public baseUrl: string;
  private UMP_URL: string;
  constructor(
    private http: HttpClient,
    private utilsservice: UtilsService
  ) {
    this.baseUrl = this.utilsservice.getUMPUrl();
    this.UMP_URL = `${this.baseUrl}/UMP/API/v3/applications/DIGITAL_FORMS/execute`;
  }
  getusers(
    filter: string,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    excludeusers: any,
    teamid: string,
    excludeteamusers: boolean,
    newuseremail: string,
    sortcolumn?: string
  ): Observable<any> {
    const excludeusrstr = excludeusers.toString();
    // console.log(excludeusrstr);
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      query: filter,
      sort_order: sortOrder,
      sort_on : sortcolumn,
      exclude: excludeusrstr,
      filter: {
        teamId: teamid,
        excludeUsers: excludeteamusers,
        newUserEmail: newuseremail
      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_USERS`,
      {},
      httpOptions
    );
  }


  getusersForReportswithteamId(
    filter: string,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    excludeusers: any,
    teamid: string,
    excludeteamusers: boolean,
    newuseremail: string
  ): Observable<any> {
    const excludeusrstr = excludeusers.toString();
    // console.log(excludeusrstr);
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      query: filter,
      sort_order: sortOrder,
      exclude: excludeusrstr,
      filter: {
        teamId: teamid,

      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_USERS`,
      {},
      httpOptions
    );
  }
  createusers(userform, teams): Observable<any> {
    // console.log(typeof userform);
    const teamsarr = teams.toString();
    userform['teams'] = teamsarr;
    userform['password'] = btoa(userform.password);
    const inputParams = userform;
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams, 'true');
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_CREATE_USER`,
      {},
      httpOptions
    );
  }
  addbulkusers(usercsvdata): Observable<any> {
    // console.log(usercsvdata);
    const userscsvcontent = {
      data: usercsvdata
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const body = this.utilsservice.setBody(userscsvcontent);
    const HttpOptions = {
      headers: headers
    };
     return this.http.post<any>(
       `${this.UMP_URL}/DIGITAL_FORMS_PA_BULK_USER_CREATE`,
       body,
       HttpOptions
     );
  }

  updateusers(userform, teams, userId): Observable<any> {
    // console.log(typeof userform);
    const teamsarr = teams.toString();
    if (teamsarr === '') {
      userform['deleteTeams'] = 'Y';
    }
    userform['teams'] = teamsarr;
    userform['userName'] = userId;
    userform['password'] = btoa(userform.password);
    const inputParams = userform;
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams, 'true');
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_UPDATE_USER`,
      {},
      httpOptions
    );
  }
  disableusers(usernames): Observable<any> {
    const usernamesarr = usernames.toString();
    const inputParams = {
      userName: usernamesarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_DISABLE_USER`,
      {},
      httpOptions
    );
  }
  deleteusers(teamid, userids): Observable<any> {
    const useridsarr = userids.toString();
    const inputParams = {
      teamId: teamid,
      userId: useridsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_MEMBER`,
      {},
      httpOptions
    );
  }

  assignteams(teams, userids, removeteams): Observable<any> {
    const teamsarr = teams.toString();
    const useridsarr = userids.toString();
    const removeteamssarr = removeteams.toString();
    // let removeteamsarr = teamstoremove.toString();
    const inputParams = {
      removeTeams: removeteamssarr,
      teamId: teamsarr,
      userId: useridsarr
      // removeteams: removeteamsarr
    };
    // console.log(teamformjson);
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_ADD_MEMBER_TO_TEAM`,
      {},
      httpOptions
    );
  }
  addUsersToTeam(teamid, userids): Observable<any> {
    const useridsarr = userids.toString();
    const inputParams = {
      teamId: teamid,
      userId: useridsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_ADD_MEMBER_TO_TEAM`,
      {},
      httpOptions
    );
  }
  getuserrole(): Observable<any> {
    const inputParams = {};
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_APP_SETTINGS`,
      {},
      httpOptions
    );
  }

  getteamdetails(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_TEAM_DETAILS`,
        {},
        httpOptions
      );
  }

}
