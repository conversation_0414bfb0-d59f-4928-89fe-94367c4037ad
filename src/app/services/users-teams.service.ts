import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UtilsService } from './utils.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UsersTeamsService {
  public baseUrl: string;
  private UMP_URL: string;
  constructor(
    private http: HttpClient,
    private utilsservice: UtilsService
  ) {
    this.baseUrl = this.utilsservice.getUMPUrl();
    this.UMP_URL = `${this.baseUrl}/UMP/API/v3/applications/DIGITAL_FORMS/execute`;
  }
  getusers(
    filter: string,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    excludeusers: any,
    teamid: string,
    excludeteamusers: boolean,
    newuseremail: string,
    sortcolumn: string
  ): Observable<any> {
    const excludeusrstr = excludeusers.toString();
    // console.log(excludeusrstr);
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      query: filter,
      sort_order: sortOrder,
      sort_on : sortcolumn,
      exclude: excludeusrstr,
      includeAvatar: true,
      filter: {
        teamId: teamid,
        excludeUsers: excludeteamusers,
        newUserEmail: newuseremail
      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_USERS`,
      {},
      httpOptions
    );
  }


  getusersForReportswithteamId(
    filter: string,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    excludeusers: any,
    teamid: string,
    excludeteamusers: boolean,
    newuseremail: string
  ): Observable<any> {
    const excludeusrstr = excludeusers.toString();
    // console.log(excludeusrstr);
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      query: filter,
      sort_order: sortOrder,
      exclude: excludeusrstr,
      filter: {
        teamId: teamid,

      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_USERS`,
      {},
      httpOptions
    );
  }
  createusers(userform, teams): Observable<any> {
    const teamsarr = teams.toString();
    const inputParams = {
      email: userform.email,
      firstName: userform.firstName,
      lastName: userform.lastName,
      letUserChoosePassword: userform.letUserChoosePassword,
      password: btoa(userform.password),
      phoneNumber: userform.phoneNumber,
      role: userform.role,
      team: "",
      teams: teamsarr,
      avatar: userform.avatar ? userform.avatar : '',
      adsusername: userform.adsusername
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const body = this.utilsservice.setBody(inputParams);
    const httpOptions = {
      headers: headers
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_CREATE_USER`,
      body,
      httpOptions
    );
  }
  addbulkusers(usercsvdata): Observable<any> {
    // console.log(usercsvdata);
    const userscsvcontent = {
      data: usercsvdata
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const body = this.utilsservice.setBody(userscsvcontent);
    const HttpOptions = {
      headers: headers
    };
     return this.http.post<any>(
       `${this.UMP_URL}/DIGITAL_FORMS_PA_BULK_USER_CREATE`,
       body,
       HttpOptions
     );
  }

  updateusers(userform, teams, userId): Observable<any> {
    // console.log(typeof userform);
    const teamsarr = teams.toString();
    if (teamsarr === '') {
      userform['deleteTeams'] = 'Y';
    }
    const inputParams = {
      userName: userId,
      email: userform.email,
      firstName: userform.firstName,
      lastName: userform.lastName,
      letUserChoosePassword: userform.letUserChoosePassword,
      password: btoa(userform.password),
      phoneNumber: userform.phoneNumber,
      role: userform.role,
      team: "",
      teams: teamsarr,
      avatar: userform.avatar
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const body = this.utilsservice.setBody(inputParams);
    const httpOptions = {
      headers: headers
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_UPDATE_USER`,
      body,
      httpOptions
    );
  }
  disableusers(usernames): Observable<any> {
    const usernamesarr = usernames.toString();
    const inputParams = {
      userName: usernamesarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_DISABLE_USER`,
      {},
      httpOptions
    );
  }
  deleteusers(teamid, userids): Observable<any> {
    const useridsarr = userids.toString();
    const inputParams = {
      teamId: teamid,
      userId: useridsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_MEMBER`,
      {},
      httpOptions
    );
  }

  assignteams(teams, userids, removeteams): Observable<any> {
    const teamsarr = teams.toString();
    const useridsarr = userids.toString();
    const removeteamssarr = removeteams.toString();
    // let removeteamsarr = teamstoremove.toString();
    const inputParams = {
      removeTeams: removeteamssarr,
      teamId: teamsarr,
      userId: useridsarr
      // removeteams: removeteamsarr
    };
    // console.log(teamformjson);
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_ADD_MEMBER_TO_TEAM`,
      {},
      httpOptions
    );
  }
  addUsersToTeam(teamid, userids): Observable<any> {
    const useridsarr = userids.toString();
    const inputParams = {
      teamId: teamid,
      userId: useridsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_ADD_MEMBER_TO_TEAM`,
      {},
      httpOptions
    );
  }
  getuserrole(): Observable<any> {
    const inputParams = {};
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_APP_SETTINGS`,
      {},
      httpOptions
    );
  }

  

  // ************************Teams start***************************


  getteams(
    query: string,
    teams: any,
    sortOrder: string,
    pageNumber: number,
    pageSize: number,
    formids: any,
    createdteamid: string,
    sortcolumn?: string
  ): Observable<any> {
    const teamsarr = teams.toString();
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      teams: teamsarr,
      query: query,
      sort_order: sortOrder,
      sort_on: sortcolumn,
      filter: {
        formId: formids.formId,
        formsetId: formids.formsetId,
        newTeamId: createdteamid
      }
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };

    // if (null !== query || teams === null || teams === '') {
      // console.log(query);
      return this.http
        .post<any>(
          `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_TEAMS`,
          {},
          httpOptions
        );
    //  }
  }
  getmanagers(
    sortOrder: string,
    pageNumber: number,
    pageSize: number
  ): Observable<any> {
    const inputParams = {
      limit: pageSize,
      offset: pageNumber,
      sort_order: sortOrder
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_MANAGERS`,
        {},
        httpOptions
      );
  }
  createteams(teamsform): Observable<any> {
    const inputParams = teamsform;
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_CREATE_TEAM`,
        {},
        httpOptions
      );
  }
  updatTeamDescription(teamid, teamdescription, teammanager): Observable<any> {
    const inputParams = {
      id: teamid,
      teamDescription: teamdescription,
      teamManager: teammanager
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_UPDATE_TEAM`,
        {},
        httpOptions
      );
  }
  getteamdetails(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_TEAM_DETAILS`,
        {},
        httpOptions
      );
  }
  getformsofteam(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_FORMS_OF_TEAM`,
        {},
        httpOptions
      );
  }
  getformsetsofteam(teamid): Observable<any> {
    const inputParams = {
      teamId: teamid
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_GET_FORMSET_OF_TEAM`,
        {},
        httpOptions
      );
  }
  deteteteams(teams): Observable<any> {
    const teamsarr = teams.toString();
    const inputParams = {
      teamId: teamsarr
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_DELETE_TEAM`,
        {},
        httpOptions
      );
  }
  deteteforms(teamid, formid): Observable<any> {
    // let teamsarr = teams.toString();
    const inputParams = {
      teamId: teamid,
      formId: formid
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_FORM_FROM_TEAM`,
        {},
        httpOptions
      );
  }
  deteteformset(teamid, formsetid): Observable<any> {
    // let teamsarr = teams.toString();
    const inputParams  = {
      teamId: teamid,
      formId: formsetid
    };
    const headers = this.utilsservice.setBasicHeaders('application/x-www-form-urlencoded');
    const params = this.utilsservice.setParams(inputParams);
    const httpOptions = {
      headers: headers,
      params: params
    };
    return this.http
      .post<any>(
        `${this.UMP_URL}/DIGITAL_FORMS_PA_REMOVE_FORMSET_FROM_TEAM`,
        {},
        httpOptions
      );
  }
  downloadSampleFile() {
    const headers = this.utilsservice.setBasicHeaders('application/json');
    const httpOptions = {
      headers: headers
    };
    return this.http.post<any>(
      `${this.UMP_URL}/DIGITAL_FORMS_PA_DOWNLOAD_IMPORT_USER_TEMPLATE`,
      {},
      httpOptions
    );
  }

}