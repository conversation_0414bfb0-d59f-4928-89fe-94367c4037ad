import { AfterViewInit, Component, ElementRef, HostListener, OnInit } from '@angular/core';
import { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router, Event, ActivatedRoute } from '@angular/router';
import { PrimeNGConfig } from 'primeng/api';
// import { NavigationEnd, Router,Event } from '@angular/router';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { OverlayContainer } from '@angular/cdk/overlay';
import { SettingsService } from './services/settings.service';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html'
})
export class AppComponent implements OnInit, AfterViewInit {
  public loading: boolean;
  returnURL: string;
  cursorX: number = 0;
  sidebar: any;
  defaultPage: any;
  profileData: any;

  // @HostListener('document:mousemove', ['$event'])
  // onMouseMove(event: MouseEvent) {
  //   this.cursorX = event.clientX;
  //   // console.log('yyyy',event.clientY)
  //   // console.log('xxxx',this.cursorX)
  //   if (this.layoutService.isOverlay()) {
  //     if (!this.layoutService.state.overlayMenuActive) {
  //       if ((this.cursorX > 50 && this.cursorX < 70) && (event.clientY > 50 && event.clientY < 70)) {
  //         this.layoutService.state.overlayMenuActive = true;
  //       } else {
  //         this.layoutService.state.overlayMenuActive = false;
  //       }
  //     } else {
  //       if (this.cursorX < 190) {
  //         this.layoutService.state.overlayMenuActive = true;
  //       } else {
  //         this.layoutService.state.overlayMenuActive = false;
  //       }
  //     }
  //   }
  // }

  @HostListener('document:mousemove', ['$event'])
    onMouseMove(event: MouseEvent) {
      this.cursorX = event.clientX;
      if (this.sidebar === 'Collapsed') {
        if (this.cursorX > 195) {
          this.layoutService.state.overlayMenuActive = false;
        }
        // if (this.cursorX < 15) {
        //   this.layoutService.state.overlayMenuActive = true;
        // } else if (this.cursorX > 195) {
        //   this.layoutService.state.overlayMenuActive = false;
        // }
      }
    }


    constructor(private primengConfig: PrimeNGConfig,private router: Router,public layoutService: LayoutService, private route: ActivatedRoute,
           public overlayContainer: OverlayContainer, private settingService: SettingsService
    ) { 
      this.router.events.subscribe((routerEvent: Event) => {
        this.checkRouterEvent(routerEvent);
        // if (routerEvent instanceof NavigationEnd) {}
      });
    }
      
    ngOnInit () {
      this.returnURL = this.route.snapshot.queryParams['returnUrl']
      this.layoutService.getProfileData().subscribe((res: any)=>{ 
        if (res.status?.toLowerCase() === 'success') {
          this.profileData = res;
          if (this.profileData?.profileSettings?.sidebar) {
            this.sidebar = this.profileData.profileSettings.sidebar;
          } else {
            this.sidebar = "Expanded";
          }
          // this.layoutService.sidebarChange.next(this.sidebar)
          if (this.profileData?.profileSettings?.defaultPage) {
            this.defaultPage = this.profileData?.profileSettings?.defaultPage;
          }
          this.ngAfterViewInit();
        } else {
          this.sidebar = "Expanded";
        }
        // if (this.profileData.profileSettings?.theme) {
        //   this.layoutService.changeTheme(this.profileData.profileSettings?.theme.theme, this.profileData.profileSettings?.theme.colorScheme);
        // } else {
        //   this.layoutService.changeTheme('lara-light-indigo', 'light');
        // }
        // if (this.profileData.profileSettings?.formDesigner) {
        //   const formDesignerOption = this.profileData.profileSettings?.formDesigner;
        //   localStorage.setItem('Designer', formDesignerOption);
        // }
      });
      
        this.loading = true;
        this.primengConfig.ripple = true;
        document.removeEventListener('openImagePreview', this.openImagePreviewHandler.bind(this), true)
        document.addEventListener('openImagePreview', this.openImagePreviewHandler.bind(this), true)
    }

    ngAfterViewInit(): void {
      const defaultPage = localStorage.getItem('defaultPage');
      this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          if (defaultPage && event.urlAfterRedirects === '/') {
            this.router.navigate([defaultPage]);
          }
        }
      });
    }

    openImagePreviewHandler(event){
      this.openImagePreview(event.detail.data)
    }

    async openImagePreview(imageData){
    // const dialogRef = this.dialog.open(ImagePreviewComponent, {
    //   minWidth: '70%',
    //   height:'90%',
    //   panelClass: 'custom-dialog',
    //   data: {
    //     imageData: imageData
    //   },
    // });
    // dialogRef.afterClosed().subscribe((response) => {
    //   if (response) {
    //     document.removeEventListener('openImagePreview', this.openImagePreviewHandler.bind(this), true)
    //   }
    // });
  }
    checkRouterEvent(routerEvent: Event): void {
      if (routerEvent instanceof NavigationStart) {
        this.loading = true;
      }
      if (
        routerEvent instanceof NavigationEnd ||
        routerEvent instanceof NavigationCancel ||
        routerEvent instanceof NavigationError) {
        this.loading = false;
      }
    }

}
