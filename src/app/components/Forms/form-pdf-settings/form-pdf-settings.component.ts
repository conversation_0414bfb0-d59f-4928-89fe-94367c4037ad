import { After<PERSON>iewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';
import { FormsService } from 'src/app/services/forms.service';


@Component({
  selector: 'app-form-pdf-settings',
  templateUrl: './form-pdf-settings.component.html',
  styleUrls: ['./form-pdf-settings.component.scss']
})
export class FormPdfSettingsComponent implements OnInit {
  @ViewChild('editor') editor!: ElementRef <HTMLElement>;
  editorOptions = {theme: 'vs-dark', language: 'html'};
  headerCode: string = '';
  footerCode: string = '';
  errmsg: string;
  pdfsettingsform: FormGroup;
  showEditor: boolean = false;  // added this to make sure editor take 100% width
  papers: any[] = [
    { key : 'Letter: 8.5in x 11in', value : 'Letter:8.5in x 11in'},
    { key : 'Legal: 8.5in x 14in', value : 'Legal:8.5in x 14in'},
    { key : 'Tabloid: 11in x 17in', value : 'Tabloid:11in x 17in'},
    { key : 'Ledger: 17in x 11in', value : 'Ledger:17in x 11in'},
    { key : 'A0: 33.1in x 46.8in', value : 'A0:33.1in x 46.8in'},
    { key : 'A1: 23.4in x 33.1in', value : 'A1:23.4in x 33.1in'},
    { key : 'A2: 16.54in x 23.4in', value : 'A2:16.54in x 23.4in'},
    { key : 'A3: 11.7in x 16.54in', value : 'A3:11.7in x 16.54in'},
    { key : 'A4: 8.27in x 11.7in', value : 'A4:8.27in x 11.7in'},
    { key : 'A5: 5.83in x 8.27in', value : 'A5:5.83in x 8.27in'},
    { key : 'A6: 4.13in x 5.83in', value : 'A6:4.13in x 5.83in'}
  ];
  selectedOption: any = 'Paper Layout';
  layouts: any[] = [
    { key : 'Portrait', value : 'Portrait'},
    { key : 'Landscape', value : 'Landscape'},
  ];
  formId: string = '';
  formFieldsArr: any[] = [];
  PDFTemplateValue: string = '';
  private showOnlyHiddenFields: boolean = false;

  constructor(private fb: FormBuilder, 
    private formservice: FormsService, 
    private messageService: MessageService,
    public layoutService: LayoutService,
    private ref: DynamicDialogRef,
    public data: DynamicDialogConfig,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit() {
    this.formId = this.data.data.formId;
    this.pdfsettingsform = this.fb.group({
      fileName: [''],
      papertype: ['A4:8.27in x 11.7in'],
      layout: ['Portrait'],
      leftmargin: [''],
      rightmargin: [''],
      topmargin: [''],
      bottommargin: ['']
    });
    this.getFormFields(this.formId);
    this.getpdfsettings();
  }

  getpdfsettings() {
    this.formservice.getpdfsettings(this.formId).subscribe((response) => 
      {
        if (response.status.toLowerCase() === 'success') {
          if (response?.pdfSettings && Object.keys(response.pdfSettings).length > 0) {
            const pdfSettings = response.pdfSettings;
            this.headerCode = atob(pdfSettings.header);
            this.footerCode = atob(pdfSettings.footer);
            if (pdfSettings && Object.keys(pdfSettings).length > 0) {
              this.pdfsettingsform.patchValue({
                fileName: pdfSettings?.fileName,
                papertype: pdfSettings?.papertype,
                layout: pdfSettings?.layout,
                leftmargin: pdfSettings?.leftmargin,
                rightmargin: pdfSettings?.rightmargin,
                topmargin: pdfSettings?.topmargin,
                bottommargin: pdfSettings?.bottommargin
              });
            }
          }
          this.showEditor = true;
        } else {
          this.errmsg = response.error;
          this.messageService.add({severity:'error', summary:'Error', detail: this.errmsg});
          this.showEditor = true;
        }
      });
  }

  getFormFields(formid: string): void {
    this.formservice.getFormFieldsForAttr(formid, this.showOnlyHiddenFields).subscribe((response: any) => {
      const formFieldsResponse = response;
      if (formFieldsResponse?.status?.toLowerCase() === 'success') {
        if (formFieldsResponse.formFields.length > 0) {
          this.formFieldsArr = formFieldsResponse.formFields;
        }
      } else {
        this.errmsg = formFieldsResponse.error;
        return;
      }
    });
  }

  savePDFsettings() {
    const obj = {
      fileName: this.pdfsettingsform.get('fileName').value,
      papertype: this.pdfsettingsform.get('papertype').value,
      layout: this.pdfsettingsform.get('layout').value,
      leftmargin: this.pdfsettingsform.get('leftmargin').value,
      rightmargin: this.pdfsettingsform.get('rightmargin').value,
      topmargin: this.pdfsettingsform.get('topmargin').value,
      bottommargin: this.pdfsettingsform.get('bottommargin').value,
      header: btoa(this.headerCode),
      footer: btoa(this.footerCode),
    }
    this.formservice.savepdfSettingsData(this.formId, obj)
      .subscribe((response) => {
          if (response.status.toLowerCase() === 'success') {
             this.getpdfsettings();
             this.messageService.add({severity:'success', summary:'Success', detail: 'Successfully saved'});
             this.closeDialog();
          } else {
            this.errmsg = response.error;
            this.messageService.add({severity:'error', summary:'Error', detail: this.errmsg});
          }
        }
      );
  }

  showPDFoverlay(element: any, event: Event) {
    element.toggle(event);
    if (this.pdfsettingsform.get('fileName').value) {
      this.PDFTemplateValue = this.pdfsettingsform.get('fileName').value;
      setTimeout(() => {
        this.renderHighlightedText();
      }, 500);
    }
  }

  async seteditorvalue(event: any) {
    const editor = this.editor.nativeElement as HTMLElement;
    editor.focus();
  
    const caretPosition = this.getCaretPosition();
    const text = editor.innerText;
    const inserted = `\${${event.value}}`;
  
    this.PDFTemplateValue = text.slice(0, caretPosition) + inserted + text.slice(caretPosition);
    this.renderHighlightedText();
  }

  renderHighlightedText() {
    const editor = this.editor.nativeElement;
    const regex = /\$\{[^}]+\}/g;
    const rawText = this.PDFTemplateValue;
  
    // Avoid double-wrapping already styled spans
    const cleanText = rawText.replace(/<\/?span[^>]*>/g, '');
  
    const highlightedText = cleanText.replace(regex, (match) => {
      return `<span style='background-color: var(--primary-100);display: inline-block; padding: 0px 4px; border-radius: 5px;margin: 2px;'>${match}</span>`;
    });
  
    editor.innerHTML = highlightedText;
    this.moveCursorToEnd(editor);
  }

  async moveCursorToEnd(element: HTMLElement) {
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(element);
    range.collapse(false); // collapse to end
    selection?.removeAllRanges();
    selection?.addRange(range);
    element.focus();
  }

  handleKeyDown() {
    const editor = this.editor.nativeElement;
    const caretPosition = this.saveCaretPosition(editor);
  
    this.PDFTemplateValue = editor.innerText;
    this.renderHighlightedText();
  
    this.restoreCaretPosition(editor, caretPosition);
  }

  saveCaretPosition(element: HTMLElement): number {
    let caretOffset = 0;
    const selection = window.getSelection();
    if (selection?.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(element);
      preCaretRange.setEnd(range.endContainer, range.endOffset);
      caretOffset = preCaretRange.toString().length;
    }
    return caretOffset;
  }

  restoreCaretPosition(element: HTMLElement, caretOffset: number) {
    const range = document.createRange();
    const selection = window.getSelection();
    range.setStart(element, 0);
    range.collapse(true);
    let nodeStack = [element];
    let node;
    let found = false;
    let offset = 0;
    while (!found && (node = nodeStack.pop())) {
      if (node.nodeType === 3) {
        const nextOffset = offset + node.length;
        if (caretOffset <= nextOffset) {
          range.setStart(node, caretOffset - offset);
          found = true;
        } else {
          offset = nextOffset;
        }
      } else {
        let i = node.childNodes.length;
        while (i--) {
          nodeStack.push(node.childNodes[i]);
        }
      }
    }
    selection?.removeAllRanges();
    selection?.addRange(range);
    element.focus();
  }

  getCaretPosition(): number {
    const editor = this.editor.nativeElement;
    const selection = window.getSelection();
    let caretOffset = 0;
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(editor);
      preCaretRange.setEnd(range.endContainer, range.endOffset);
      caretOffset = preCaretRange.toString().length;
    }
    return caretOffset;
  }

  sendit() {
    const editor = this.editor.nativeElement;
    this.PDFTemplateValue = editor.innerText;    
  }

  savePDFfileName(element: any) {
    if (this.pdfsettingsform.get('fileName').value !== this.PDFTemplateValue) {
      this.pdfsettingsform.controls['fileName'].setValue(this.PDFTemplateValue);
      this.pdfsettingsform.controls['fileName'].updateValueAndValidity();  
    }
    element.hide();
  }

  hidePDFoverlay(event: Event, element: any) {
    if (this.pdfsettingsform.get('fileName').value !== this.PDFTemplateValue) {
      this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Are you sure that you want to proceed?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          element.hide();
        },
        reject: () => {}
      });
    }
  }

  closeDialog() {
    this.ref.close();
  }

  helpButtonFunction() {
    window.open("https://github.com/puppeteer/puppeteer/blob/v2.1.1/docs/api.md#pagepdfoptions", '_blank');
  }

}
