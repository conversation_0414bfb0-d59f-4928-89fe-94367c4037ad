.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.editor-container{
    height: 250px;
    width: 100%;
}

.help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }

::ng-deep .pdf-template .explistbox .p-listbox{
  border-radius: 0px !important;
}

#editor {
  width: 100%;
  height: 100px;
  white-space: pre-wrap !important;
  text-wrap: wrap !important;
  line-height: 30px;
  overflow: auto !important;
}

#editor:focus{
outline: 0 none !important;
outline-offset: 0 !important;
box-shadow: 0 0 0 0.2rem #C7D2FE !important;
border-color: #6366F1 !important;
}
