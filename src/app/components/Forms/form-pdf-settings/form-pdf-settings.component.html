<a href="https://docs.unvired.com/builder/advanced/settings/#pdf" style="padding: 0.45625rem 0.45625rem;"
    target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded"
    pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>

<div class="pdf">

    <div class="flex flex-row justify-content-between w-full align-items-center mb-3 pt-2">
        <span></span>
        <div class="flex flex-wrap gap-4">
            <div class="flex align-items-center">
                <p-radioButton value="Paper Layout" [(ngModel)]="selectedOption" inputId="layout"></p-radioButton>
                <label for="layout" class="ml-2 mb-0">Paper Layout</label>
            </div>
            <div class="flex align-items-center">
                <p-radioButton value="PDF Header" [(ngModel)]="selectedOption" inputId="header"></p-radioButton>
                <label for="header" class="ml-2 mb-0">PDF Header</label>
            </div>
            <div class="flex align-items-center">
                <p-radioButton value="PDF Footer" [(ngModel)]="selectedOption" inputId="footer"></p-radioButton>
                <label for="footer" class="ml-2 mb-0">PDF Footer</label>
            </div>
        </div>
        <button class="p-button-sm p-button-rounded" pButton type="button" icon="pi pi-question"
            [disabled]="selectedOption !== 'PDF Header'" (click)="helpButtonFunction()" pTooltip="Help"
            tooltipPosition="left"></button>
    </div>

    <!-- PDF Header -->
    <div *ngIf="selectedOption === 'PDF Header' && showEditor" class="editor-container">
        <ngx-monaco-editor style="height: 100%;width: 100%;" [options]="editorOptions"
            pTooltip="Add HTML for PDF header" tooltipPosition="top" life="2000" [(ngModel)]="headerCode">
        </ngx-monaco-editor>
    </div>

    <!-- PDF Footer -->
    <div *ngIf="selectedOption === 'PDF Footer' && showEditor" class="editor-container">
        <ngx-monaco-editor style="height: 100%;" [options]="editorOptions" pTooltip="Add HTML for PDF footer"
            tooltipPosition="top" life="2000" [(ngModel)]="footerCode"></ngx-monaco-editor>
    </div>

    <!-- PDF Layout -->
    <form *ngIf="selectedOption === 'Paper Layout'" [formGroup]="pdfsettingsform" novalidate class="card my-4">
        <div class="grid">
            <div class="col-6 field mt-3">
                <span class="p-input-icon-right p-float-label w-full">
                    <i class="pi pi-plus cursor-pointer" (click)="showPDFoverlay(PDFoverlay, $event)"></i>
                    <input id="filename" class="w-full" pInputText type="text" formControlName="fileName"
                        (keyup)="onSearch()" />
                    <label htmlFor="filename">PDF Filename Template</label>
                </span>
            </div>
            <div class="col-3 field mt-3">
                <span class="p-float-label w-full">
                    <p-dropdown id="pageSize" class="p-inputtext-sm" styleClass="w-full" formControlName="papertype"
                        [options]="papers" optionLabel="key" optionValue="value"></p-dropdown>
                    <label htmlFor="pageSize">Page Size</label>
                </span>
            </div>
            <div class="col-3 field mt-3">
                <span class="p-float-label w-full">
                    <p-dropdown id="Layout" class="p-inputtext-sm" styleClass="w-full" formControlName="layout"
                        [options]="layouts" optionLabel="key" optionValue="value"></p-dropdown>
                    <label htmlFor="Layout">Layout</label>
                </span>
            </div>
            <div class="col-3 ">
                <span class="p-float-label w-full">
                    <input id="leftMargin" pInputText formControlName="leftmargin" type="text" placeholder="Left Margin"
                        class="w-full">
                    <label htmlFor="leftMargin">Left Margin</label>
                </span>
            </div>
            <div class="col-3 ">
                <span class="p-float-label w-full">
                    <input id="rightMargin" pInputText formControlName="rightmargin" type="text"
                        placeholder="Right Margin" class="w-full">
                    <label htmlFor="rightMargin">Right Margin</label>
                </span>
            </div>
            <div class="col-3 ">
                <span class="p-float-label w-full">
                    <input id="TopMargin" pInputText formControlName="topmargin" type="text" placeholder="Top Margin"
                        class="w-full">
                    <label htmlFor="TopMargin">Top Margin</label>
                </span>
            </div>
            <div class="col-3 ">
                <span class="p-float-label w-full">
                    <input id="BottomMargin" pInputText formControlName="bottommargin" type="text"
                        placeholder="Bottom Margin" class="w-full">
                    <label htmlFor="BottomMargin">Bottom Margin</label>
                </span>
            </div>
        </div>
    </form>

    <div class="col-12 text-right px-0">
        <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel"
            (click)="closeDialog()"></button>
        <button class="p-button-sm " pButton type="button" label="Save" [disabled]="!pdfsettingsform.valid"
            (click)="savePDFsettings()"></button>
    </div>
</div>

<p-overlayPanel #PDFoverlay [dismissable]="true" styleClass="add-edit" [style]="{'width':'400px'}" appendTo="body">
    <div class="flex flex-row justify-content-between align-items-center p-2">
        <b>PDF Filename Template</b>
        <i class="pi pi-times cursor-pointer" (click)="PDFoverlay.hide()" style="color: #6c757d;"></i>
    </div>
    <ng-template pTemplate="body">
        <div class="col-12 p-2 pdf-template">
            <p-listbox [options]="formFieldsArr" filterBy="name" [filter]="true" (onChange)="seteditorvalue($event)"
                optionLabel="name" optionValue="key" [listStyle]="{'height': '200px'}" styleClass="w-full"
                class="explistbox" />

            <div class="mt-2">
                <textarea style="display: none;" class="w-full" rows="3" cols="30" pInputTextarea
                    [(ngModel)]="PDFTemplateValue" HighlightDirective [autoResize]="false">
                </textarea>
                <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true"
                    (keydown)="handleKeyDown()" (keyup)="sendit()">
                </div>
            </div>
        </div>

        <div class="col-12 p-2 text-right">
            <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times"
                (onClick)="hidePDFoverlay($event,PDFoverlay)"></p-button>
            <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save"
                (onClick)="savePDFfileName(PDFoverlay)"></p-button>
        </div>
    </ng-template>
</p-overlayPanel>