<p-confirmPopup></p-confirmPopup>
<div class="card" style="height: calc(100vh - 20px);">
    <p-toolbar styleClass="mb-2 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <span>
                        <span class="highlight" (click)="goBack()">Home</span>
                        <span class="highlight" (click)="openNestedFormListDialog()"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> Forms </span>
                        <!-- <span *ngIf="formType === 'Templates'" class="highlight" [routerLink]="['/templates']"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> Templates </span> -->
                        <span> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> Design </span>
                    </span>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="center">
            <div class="flex flex-row align-items-center">
                <span *ngIf="formId !== '1' && !isCreateForm">{{formCategory}}</span>
                <p-divider *ngIf="formId !== '1' && !isCreateForm" styleClass="mx-2" layout="vertical"></p-divider>
                <span>{{formtitle ? formtitle : formType === 'Templates' ? 'Template Untitled' : type === 'form' ? 'Form Untitled' : 'Masterdata Untitled'}}</span>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <div class="container-div">
                <div class="flex justify-content-between header-bar align-items-center">
                    <div *ngIf="formVersion || formVersionComment" class="flex flex-row align-items-center">
                        <label *ngIf="landsacpe?.toLowerCase() !== 'production'">Version: {{ formVersion }}</label>
                        <label *ngIf="landsacpe?.toLowerCase() === 'production'">Version: {{isdraft ? 'Draft' : formVersion }}</label>
                        <p-divider *ngIf="formVersionComment && !isDraft" styleClass="mx-2" layout="vertical"></p-divider>
                        <div *ngIf="formVersionComment && !isDraft" class="flex flex-column justify-content-start mr-2">
                          <span>Comment: {{ formVersionComment }}</span>
                        </div>
                    </div>
                    <div class="flex align-items-center ml-2">
                        <button pButton pRipple (click)="showThemeSidebar()" label="Theme"
                            class="mx-1 p-button-sm bg-blue"></button>
                        <button pButton pRipple (click)="testform()" [disabled]="!issaved" label="Preview"
                            class="mx-1 p-button-sm bg-blue"></button>
                        <button pButton pRipple [disabled]="!isdraft"
                            (click)="showPublishFormPannel(publishFormPannel, $event)" label="Publish"
                            class="mx-1 p-button-sm bg-blue"></button>
                        <button *ngIf="checkFormSaveButtonVisiblity()" pButton pRipple (click)="isCreateForm ? saveNewForm() : saveFormData()" label="Save"
                            class="mx-1 p-button-sm bg-blue"></button>
                        <button *ngIf="landsacpe?.toLowerCase() !== 'production' && releaseType === 'preRelease'" pButton pRipple (click)="callPublishAPI(null, true, $event)" label="Unpublish"
                            class="mx-1 p-button-sm p-button-danger"></button>
                        <!-- <a href="https://docs.unvired.com/builder/admin/forms/#form-components" target="_blank"
                            class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help"
                            tooltipPosition="left"><i class="pi pi-question"></i></a> -->
                        <button pButton class="p-button-sm ml-1" icon="pi pi-question" (click)="helpURL()" 
                            pTooltip="Help" tooltipPosition="left"></button>
                    </div>
                </div>
            </div>

        </ng-template>
    </p-toolbar>
    <div class="formbuilder-container mat-app-background">
        <as-split direction="horizontal" style="height:calc(100vh - 0px);" [useTransition]="true">
            <as-split-area size="60">
                <form-builder [form]="forminfo" (change)="onchange($event)" [options]="formBuilderOptions">
                </form-builder>
            </as-split-area>
            <as-split-area size="40" class="splitareaconfig" *ngIf="showConfiguration">
            </as-split-area>
        </as-split>
    </div>
    <p-toast></p-toast>

    
</div>

<!-- Publish form -->
<p-overlayPanel #publishFormPannel styleClass="p-0" [style]="{'min-width': '400px'}" appendTo="body" (onHide)="resetPublishForm(publishFormPannel)">
    <form [formGroup]="publishform" novalidate>
        <div class="grid">
            <div class="col-12 w-full mt-3 publish">
                <p-messages *ngIf="landsacpe?.toLowerCase() !== 'production'" severity="info">
                    <ng-template pTemplate>
                        <i style="font-size: 1.5rem" class="pi pi-exclamation-circle mr-3"></i>
                        <div class="flex flex-column justify-content-center align-items-start gap-1">
                            <span><b>Pre Release : </b>Publish a pre-release for testing, further edits possible.</span>
                            <span><b>Final Release : </b>Publish a final release for users to fill, no more edits possible.</span>
                        </div>
                    </ng-template>
                </p-messages>
                <div *ngIf="landsacpe?.toLowerCase() !== 'production'" class="flex flex-row align-items-center mb-3">
                    <p-inputSwitch id="Relase" formControlName="formRelease" (onChange)="onReleaseChange($event)"></p-inputSwitch>
                    <label htmlFor="Relase" class="ml-2 mb-1">{{ formRelease === 'finalRelease' ? 'Final Release' : 'Pre Release' }}</label>
                </div>
                <span class="p-float-label">
                    <textarea id="Comment" rows="2" pInputTextarea formControlName="comments" class="w-full" [required]="publishform.get('formRelease').value"></textarea>
                    <label htmlFor="Comment" class="_required" [ngClass]="{'_required': publishform.get('formRelease').value || landsacpe?.toLowerCase() === 'production'}">Comments</label>
                </span>
                <span *ngIf="publishform.get('comments').hasError('maxlength')" class="error"
                    style="margin-top: 0px;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 100 characters.</small></span>
                <div class="col-12 pr-0 pb-0 text-right">
                    <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel"
                        (click)="resetPublishForm(publishFormPannel)"></button>
                    <button class="p-button-sm bg-blue" pButton type="button" label="Publish"
                        (click)="publishForm(publishFormPannel, false)" [disabled]="!publishform.valid"></button>
                </div>
            </div>
        </div>
    </form>
</p-overlayPanel>

<!-- Theme -->
<p-sidebar [(visible)]="sidebarVisible" position="right" (onHide)="cancelThemeSetting()" [modal]="false" [style]="{ width: '350px'}" [transitionOptions]="'.3s cubic-bezier(0, 0, 0.2, 1)'" class="custom-colorpicker" [showCloseIcon]="false">
    <ng-template pTemplate="header">
        <div class="flex flex-row justify-content-between align-items-center w-full">
            <h3 class="mb-0">Form Appearance</h3>
            <button class="p-button-text p-button-rounded" pButton type="button" icon="pi pi-times" (click)="cancelThemeSetting()"></button>
        </div>
    </ng-template>
    <h4 class="mb-0">Layouts</h4>
    <p-divider styleClass="mt-2"/>
    <div class="flex flex-row justify-content-between align-items-center w-full mb-4">
        <div class="image-hover" (click)="displayHeader(false);displayCard(false)">
            <img src="assets/images/form-header.png" [ngClass]="{ 'form-card-border': !showCard && !showHeader }" class="cursor-pointer" style="width: 140px;height: 175px;"  alt="Page" />
            <span class="image-hover-text">Page</span>
        </div>
        <div class="image-hover" (click)="displayHeader(true);displayCard(false)">
            <img src="assets/images/form-header-footer.png" [ngClass]="{ 'form-card-border': showHeader && !showCard }" class="cursor-pointer" style="width: 140px;height: 175px;"  alt="Page with Title" />
            <span class="image-hover-text">Page with Title</span>
        </div>
    </div>
    
    <div class="flex flex-row justify-content-between align-items-center w-full">
        <div class="image-hover" (click)="displayCard(true);displayHeader(false)">
            <img src="assets/images/form-card.png" [ngClass]="{ 'form-card-border': showCard && !showHeader }" class="cursor-pointer" style="width: 140px;height: 175px;"  alt="Card" />
            <span class="image-hover-text">Card</span>
        </div>
        <div class="image-hover" (click)="displayCard(true);displayHeader(true)">
            <img src="assets/images/card-with-title.png" [ngClass]="{ 'form-card-border': showCard && showHeader }" class="cursor-pointer" style="width: 140px;height: 175px;"  alt="Card with Title" />
            <span class="image-hover-text">Card with Title</span>
        </div>
    </div>
    <!-- <div class="flex flex-row align-items-center justify-content-between mt-3 mb-2">
        <label>Show Header</label>
        <p-inputSwitch [(ngModel)]="showHeader" inputId="binary" />
    </div>
    <div *ngIf="showHeader" class="flex flex-row align-items-center justify-content-between mb-3">
        <label>Header Background Color</label>
        <p-colorPicker [(ngModel)]="headerBgColor" appendTo="body" (onChange)="bgColorChange($event)"/>
    </div> -->
    <h4 class="mb-0">Forms</h4>
    <p-divider styleClass="mt-2"/>

    <div *ngIf="showCard" class="flex flex-row align-items-center justify-content-between my-3">
        <label>Card Color</label>
        <p-colorPicker [(ngModel)]="cardBgColor" appendTo="body"/>
    </div>
    <div *ngIf="showHeader" class="flex flex-row align-items-center justify-content-between my-3">
        <label>Title Color</label>
        <p-colorPicker [(ngModel)]="headerBgColor" appendTo="body"/>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Action Bar Color</label>
        <p-colorPicker [(ngModel)]="footerBgColor" appendTo="body"/>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Background Color</label>
        <p-colorPicker [(ngModel)]="bgColor" appendTo="body"/>
    </div>
    <!-- <p-divider /> -->
    <div class="flex flex-column">
        <!-- <label>Background Image</label> -->
        <div class="flex flex-row align-items-center justify-content-center my-2">
        <button *ngIf="!selectedImage" pButton class="p-button-sm w-full" (click)="showSelectImageDialog()" label="Add Background Image"></button>    
            <div *ngIf="selectedImage" class="image-container" (click)="showSelectImageDialog()">
                <!-- <img *ngIf="!selectedImage" src="assets/images/dummy.jpg" alt="Hover Image" class="hover-image"> -->
                <img *ngIf="selectedImage" [src]="selectedImage" alt="Hover Image" class="hover-image mx-auto">
                <i *ngIf="selectedImage" class="pi pi-times closeIcon" (click)="removeSelectedImage();$event.stopPropagation()"></i>
                <div class="hover-overlay mx-auto">
                    <span style="color: var(--primary-color);font-weight: 700;font-size: 18px;">Change Image</span>
                </div>
            </div>
        </div>
    </div>
    <h4 class="my-0">Controls</h4>
    <p-divider styleClass="mt-2"/>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Label Color</label>
        <p-colorPicker [(ngModel)]="labelColor" appendTo="body"/>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Input Color</label>
        <p-colorPicker [(ngModel)]="answerColor" appendTo="body"/>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Input Focus Color</label>
        <p-colorPicker [(ngModel)]="inputFocusColor" appendTo="body"/>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3">
        <label>Font</label>
        <div [ngStyle]="selectedFont ? { 'font-family': selectedFont } : {}">{{ selectedFont }}<span class="edit-font ml-1" (click)="showFontsDialog = true">Aa</span></div>
    </div>
    <div class="flex flex-row align-items-center justify-content-between my-3 w-full">
        <label class="w-3">Font Size</label>
        <div class="flex flex-row align-items-center justify-content-end gap-2 w-full">
            <span class="font-size-style">{{themeFontSize}}</span>
            <span style="font-size: 12px;" class="font-size-style" (click)="onSelectFontSize('increase')" [ngClass]="{'cursor-pointer': themeFontSize < 16}">Aa<i class="pi pi-angle-up"></i></span>
            <span style="font-size: 10px;" class="font-size-style" (click)="onSelectFontSize('decrease')" [ngClass]="{'cursor-pointer': themeFontSize > 10}">Aa<i class="pi pi-angle-down"></i></span>
            <!-- <span style="font-size: 14px;padding: 3px 5px;" [ngClass]="{ 'selected-font-size': themeFontSize === '14'}" class="font-size-style" (click)="onSelectFontSize('14')" pTooltip="14px" tooltipPosition="top">Aa</span>
            <span style="font-size: 16px;padding: 3px 4px;" [ngClass]="{ 'selected-font-size': themeFontSize === '16'}" class="font-size-style" (click)="onSelectFontSize('16')" pTooltip="16px" tooltipPosition="top">Aa</span> -->
        </div>
        <!-- <span style="font-size: 10px;padding: 3px 8px;" [ngClass]="{ 'selected-font-size': themeFontSize === '10'}" class="font-size-style" (click)="onSelectFontSize('10')" pTooltip="10px" tooltipPosition="top">Aa</span>
        <span style="font-size: 12px;padding: 3px 7px;" [ngClass]="{ 'selected-font-size': themeFontSize === '12'}" class="font-size-style" (click)="onSelectFontSize('12')" pTooltip="12px" tooltipPosition="top">Aa</span>
        <span style="font-size: 14px;padding: 3px 5px;" [ngClass]="{ 'selected-font-size': themeFontSize === '14'}" class="font-size-style" (click)="onSelectFontSize('14')" pTooltip="14px" tooltipPosition="top">Aa</span>
        <span style="font-size: 16px;padding: 3px 4px;" [ngClass]="{ 'selected-font-size': themeFontSize === '16'}" class="font-size-style" (click)="onSelectFontSize('16')" pTooltip="16px" tooltipPosition="top">Aa</span> -->
    </div>
    <div class="flex flex-row gap-2 justify-content-end mt-4">
        <button pButton class="p-button-sm p-button-danger" (click)="cancelThemeSetting()" label="Cancel" ></button>
        <button pButton class="p-button-sm" (click)="SaveThemeSetting()" label="Save"></button>    
    </div>
</p-sidebar>

<!-- Theme Unsplash Image Dialog -->
<p-dialog [(visible)]="showImageDialog" [modal]="true" (onHide)="showImageDialog = false" [style]="{ width: '40vw', height: 'calc(100vh - 30vh)' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="header">
        <span style="font-weight: 700;font-size: 1.25rem;">Select Image</span>
    </ng-template>
    <p-tabView>
        <p-tabPanel header="Gallary">
            <div class="grid">
                <div class="col-12">
                    <span class="p-input-icon-left w-full mb-2">
                        <i class="pi pi-search"></i>
                        <input class="p-inputtext-sm w-full" pInputText type="text" [(ngModel)]="searchQuery"
                            (keyup)="onSearch()" placeholder="Search..." />
                    </span>
                    <span style="font-weight: 600;">Images by <a href="https://unsplash.com/" target="_blank">Unsplash</a></span>
                </div>
                <div class="col-4 p-1" *ngFor="let img of imageArr">
                    <div class="card border-none">
                        <!-- <img [src]="img.urls.thumb" alt="Hover Image"> -->
                        <img [src]="img.urls.thumb" (click)="onSelecteImage(img.urls.regular)" class="hover-card-image"/>
                    </div>
                </div>
                <div *ngIf="imageArr" class="flex flex-row gap-3 justify-content-center mt-2 w-full">
                    <button pButton class="p-button-sm p-button-danger" (click)="cancelDialog()" label="Cancel" ></button>
                    <button pButton class="p-button-sm" (click)="saveUnsplaceImage()" label="Save"></button>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel header="Upload">
            <div class="flex flex-column justify-content-center align-items-center w-full">
                <image-cropper class="max-h-30rem max-w-30rem"
                    [imageChangedEvent]="imageChangedEvent"
                    format="png"
                    (imageCropped)="imageCropped($event)"
                    (imageLoaded)="imageLoaded($event)"
                    [style.display]="showCropper ? null : 'none'"
                    output="base64"
                    [maintainAspectRatio]="true"
                    [aspectRatio]="16 / 9"
                ></image-cropper>
                <span *ngIf="!imageChangedEvent" (click)="showUploadPopup()" class="upload-custom-image"><i class="pi pi-image"></i> Upload Image</span>
                <input *ngIf="!imageChangedEvent" class="file-input" pInputText id="imageInput" accept="image/*" type="file" (change)="fileChangeEvent($event)" hidden>
            </div>
            <div *ngIf="imageChangedEvent" class="flex flex-row justify-content-center gap-3 mt-2">
                <button pButton class="p-button-sm p-button-danger" (click)="resetUploadImage()" label="Cancel"></button>
                <button pButton class="p-button-sm" (click)="uploadedImage()" label="Save"></button>    
            </div>
        </p-tabPanel>
        <p-tabPanel header="Link">
            <span class="w-full">
                <textarea class="w-full" rows="3" cols="30" pInputTextarea [(ngModel)]="imageLinkURL" placeholder="Paste any image link here"></textarea>
            </span>
            <div class="flex flex-row gap-3 justify-content-center mt-2">
                <button pButton class="p-button-sm p-button-danger" (click)="cancelDialog()" label="Cancel" ></button>
                <button pButton class="p-button-sm" (click)="saveLinkURL()" label="Save"></button>    
            </div>
        </p-tabPanel>
    </p-tabView>
</p-dialog>

<!-- Fonts Dialog -->
<p-dialog [(visible)]="showFontsDialog" [modal]="true" (onHide)="showFontsDialog = false" [style]="{ width: '40vw'}" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="header">
        <span style="font-weight: 700;font-size: 1.25rem;">Font</span>
    </ng-template>
    <div class="flex flex-column gap-3">
        <div class="col-12 px-0 mt-3">
            <span class="p-float-label">
                <p-dropdown id="font" [(ngModel)]="selectedFont" (onChange)="OnSelectFont($event)" [filter]="true" filterBy="name"
                    [options]="fontsArr" optionLabel="name" optionValue="name" styleClass="w-full" appendTo="body" [autoDisplayFirst]="false">
                </p-dropdown>
                <label htmlFor="font">Select Font</label>
            </span>
        </div>
        <div class="col-12 text-center border-1 surface-border border-round-md py-4">
            <span [ngStyle]="selectedFont ? { 'font-family': selectedFont } : {}" class="text-4xl">
                The quick brown fox jumped over the lazy dog
            </span>
        </div>
        <div class="flex flex-row gap-2 justify-content-end mt-4">
            <button pButton class="p-button-sm p-button-danger" (click)="hideFontDialog()" label="Cancel" ></button>
            <button pButton class="p-button-sm" (click)="saveFont()" label="Save"></button>    
        </div>
    </div>
</p-dialog>