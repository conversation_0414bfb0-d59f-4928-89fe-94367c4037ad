import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>derer2, <PERSON><PERSON><PERSON>d, ViewEncapsulation } from '@angular/core';
import { cloneDeep } from 'lodash';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { options } from './options';
import { FormsService } from '../../../services/forms.service';
import { LayoutService } from '../../../layout/service/app.layout.service';
import { Templates, FormBuilderComponent, FormioUtils } from '@formio/angular';
import { CreateFormComponent } from '../create-form/create-form.component';
import _ from 'lodash';
import semantic from '@formio/semantic';
import { Formio } from 'formiojs';
import { UtilsService } from '../../../services/utils.service';
import { CreateFlowComponent } from '../create-flow/create-flow.component';
import { NestedFormsListComponent } from '../nested-forms-list/nested-forms-list.component';
import { Observable, Subject, debounceTime, tap } from 'rxjs';
import { CropperSettings, ImageCroppedEvent, ImageCropperComponent, LoadedImage, base64ToFile } from 'ngx-image-cropper';
import WebFont from 'webfontloader';

Formio.use(semantic);
@Component({
  selector: 'app-form-builder',
  templateUrl: './form-builder.component.html',
  styleUrls: ['./form-builder.component.scss'],
  providers: [DialogService]
})

export class FormioBuilderComponent {
  public forminfo: any = {};
  public formBuilderOptions: any;
  breadcrumbItems: MenuItem[];
  public formdetailsform: FormGroup;
  public formname: string;
  public formCategory: string;
  public errmsg: string;
  public type: string;
  public formType: string;
  public formdescription: string;
  public formtitle: string;
  public formId: string;
  private _form: any = {};
  public formicon: string;
  public pickedicon: string;
  public isdraft: boolean;
  public issaved = true;
  @ViewChild(FormBuilderComponent) formBuilder: FormBuilderComponent;
  public builder: any = null;
  public language: string;
  public isNestedForm: boolean;
  publishform: FormGroup;
  public isformcompsopen: boolean = true;
  public isSideBarOpen: boolean = false;
  displaySidebar = false;
  showConfiguration: boolean = false;
  public formChangedData: any;
  public selectedCompindex: any;
  ref: DynamicDialogRef | undefined;
  public selectedEditIndex;
  menuType: string;
  formTitleByRouter: any;
  formIdByRouter: any;
  sidebarVisible: boolean = false;
  bgColor: any;
  answerColor: any;
  labelColor: any;
  inputFocusColor: any;
  showImageDialog: boolean = false;
  imageArr: any;
  showCard: boolean = false;
  cardBgColor: any;
  headerBgColor: any;
  footerBgColor: any;
  showHeader: boolean = false;
  selectedImage: string;
  searchQuery: string;
  searchSubject: Subject<string> = new Subject();
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper: boolean = false;
  cropperSettings: CropperSettings;
  imageLinkURL: any;
  private routerData: any;
  formVersion: string;
  formVersionComment: string;
  isCreateForm: boolean = false;
  formRelease: string = 'preRelease';
  landsacpe: string;
  releaseType: string;
  fontsArr: any[] = [];
  showFontsDialog: boolean = false;
  selectedFont: string | null;
  themeFontSize: number = 12;

  constructor(
    public layoutService: LayoutService,
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private formservice: FormsService,
    public dialogService: DialogService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private zone: NgZone
  ) {
    // this.cropperSettings = new CropperSettings();
    this.formBuilderOptions = options;
    if (this.layoutService.state.staticMenuDesktopInactive === false) {
      this.layoutService.state.staticMenuDesktopInactive = true;
    }
  }

  ngOnInit() {
    // const link = this.renderer.createElement('link');
    // link.rel = 'stylesheet';
    // link.href = 'assets/js/semantic.min.css';
    // this.renderer.appendChild(document.head, link);

    this.menuType = localStorage.getItem('menuType');
    this.landsacpe = localStorage.getItem('landscape');
    this.breadcrumbItems = [
      { label: 'Forms', routerLink: '/forms' },
      { label: 'Form builder' }
    ];
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.routerData = decodeURIComponent(params.get('data') || "");
      this.routerData = JSON.parse(this.routerData);
      this.formId = this.routerData.formId;
      this.formIdByRouter = this.routerData.nestedFormId;
      this.type = this.routerData.type;
      this.formType = this.routerData?.formType;
      this.formTitleByRouter = this.routerData?.formTitle;
      this.formCategory = this.routerData.category;
      this.isCreateForm = this.routerData.isCreateForm;
    });
    if (this.formId !== '1') {
      this.getformdata(this.formId, this.type);
    } else {
      if (this.formType.toLowerCase() === "wizard") {
        this.forminfo = {
          display: "wizard"
        };
      } else if (this.formType !== "nestedForm") {
        this.forminfo = {
          components: [{
            "autofocus": false, "input": true, "tableView": false, "label": "Submit", "key": "submit", "multiple": false,
            "protected": false, "unique": false, "persistent": false, "hidden": false, "clearOnHide": false, "spellcheck": false,
            "conditional": { "show": "", "eq": "" }, "type": "button", "tags": [], "properties": {}, "rows": 0, "wysiwyg": false, "size": "md", "leftIcon": "",
            "rightIcon": "", "block": false, "action": "submit", "disableOnInvalid": false, "theme": "primary", "lockKey": true
          }]
        };
      }
    }
    // if (this.formType.toLowerCase() === "theme") {
    //   this.formBuilderOptions.builder.basic = false;
    //   this.formBuilderOptions.builder.advanced = false;
    //   this.formBuilderOptions.builder.data = false;
    //   this.formBuilderOptions.builder.premium.components.smartId = false;
    //   this.formBuilderOptions.builder.premium.components.smartdata = false;
    //   this.formBuilderOptions.builder.premium.components.location = false;
    //   this.formBuilderOptions.builder.premium.components.barcode = false;
    //   this.formBuilderOptions.builder.premium.components.file = false;
    //   this.formBuilderOptions.builder.premium.components.barcodeselect = false;
    // } else {
    //   this.formBuilderOptions.builder.basic = {};
    //   this.formBuilderOptions.builder.advanced = {};
    //   this.formBuilderOptions.builder.data = {};
    //   this.formBuilderOptions.builder.premium.components.smartId = true;
    //   this.formBuilderOptions.builder.premium.components.smartdata = true;
    //   this.formBuilderOptions.builder.premium.components.location = true;
    //   this.formBuilderOptions.builder.premium.components.barcode = true;
    //   this.formBuilderOptions.builder.premium.components.file = true;
    //   this.formBuilderOptions.builder.premium.components.barcodeselect = true;
    // }
    // this.breadcrumbItems = [
    //   { label: 'Home', routerLink: '/home' },
    //   { label: 'Forms', routerLink: '/forms' },
    //   { label: 'Design' }
    // ];
    this.publishform = this.fb.group({
      comments: [''],
      formRelease: [false]
    });
    document.addEventListener('createWorkFlow', this.createWorkFlow.bind(this), true)
  }

  createWorkFlow(event) {
    if (event.detail.data) {
      console.log("data" + event.detail.data);
      // if (!this.ref.openDialogs || !this.ref.openDialogs.length) return;
      this.ref = this.dialogService.open(CreateFlowComponent, {
        header: `Create Flow`,
        width: '60%',
        contentStyle: { 'overflow-y': 'scroll' },
        data: {
          type: 'workflow',
          iscopy: false,
          isupdate: false,
          create: false,
          add: true,
          formData: { formId: this.formId, formType: this.formType === 'Masterdata' ? 'masterdata' : 'form' },
          flowType: 'Smart Data Flow',
        }
      });
      this.ref.onClose.subscribe((data: any) => {
        if (data) {
          this.builder.emit('change', this.formChangedData.form);
        }
      });
    }
  }
  ngAfterViewInit() {
    let that = this
    this.formBuilder.ready.then((formio) => {
      that.builder = formio;
    });
    setTimeout(() => {
      this.addClickEvent();
    }, 5000);
    // setTimeout(() => {
      // let customFormiobuilder = document.querySelector<HTMLElement>(".customFormiobuilder");
      // if (customFormiobuilder && customFormiobuilder.children && customFormiobuilder.children.length > 0 && customFormiobuilder.children.length === 2) {
      //   let formbuilderComponents = customFormiobuilder.children[0];
      //   customFormiobuilder.children[0].remove();
      //   var formbuilder = document.querySelector<HTMLElement>('.formbuilder-container');
      //   if (formbuilder) {
      //    formbuilder.prepend(formbuilderComponents);
      //   }
      // }
    //  this.onRender();      
    // }, 3000);
  }
  myfunc(index, event) {
    this.selectedEditIndex = index
  }
  getformdata(formid: string, formtype: string) {
    this.formservice
      .getformdata(formid, formtype)
      .subscribe((getformAPIres: any) => {
        let form = getformAPIres;
        // if (typeof getformAPIres !== 'object') {
        //   form = JSON.parse(getformAPIres);
        // }
        if (!this.isCreateForm) {
          this.formname = form?.formName;
          this.formdescription = form?.description;
          this.isdraft = form?.isDraft;
          this.releaseType = form?.releaseType;
          this.formVersion = form?.version ? this.getVersionInfo(form.version) : null;
          this.formVersionComment = form?.versionComments;
          this.formicon = form?.avatar;
          this.isNestedForm = form?.formData?.formtype === 'nestedForm' ? true : false;
          if (!this.isNestedForm) {
            this.formBuilderOptions.builder.premium.components.form = true;
          }
          this.formtitle = form?.formTitle; 
          localStorage.setItem('formCategory', JSON.stringify(form?.category));
        }
        
        // this.setformvalues(this.formdescription, this.formicon);
        // const resourcedata = JSON.parse(getformAPIres).resourceData;
        this.forminfo = form.formData;
        this._form = this.forminfo;
        setTimeout(() => {
          let eventCustom = new CustomEvent('FormRenderingComplete', {});
          document.dispatchEvent(eventCustom);
        }, 1000);
        this.setTheme(form?.theme);
        // if (Array.isArray(resourcedata) && resourcedata.length) {
        //   this.addExistingResourceFields(resourcedata);
        // }
      });
  }
  // addExistingResourceFields(resources: any) {
  //   resources.forEach((resource, index) => {
  //     const resourceKey = `resource-${resource.name}`;
  //     const subgroup = {
  //       key: resourceKey,
  //       title: resource.title,
  //       components: [],
  //       componentOrder: [],
  //       default: index === 0,
  //     };
  //     resource.components.forEach((component) => {
  //       if (component.type === 'button' || component.action === 'submit') { return; }
  //       let componentName = component.label;
  //       if (!componentName && component.key) {
  //         componentName = component.key.toUpperCase();
  //       }
  //       subgroup.componentOrder.push(component.key);
  //       subgroup.components[component.key] =
  //       {
  //         group: 'resource',
  //         type: component.type,
  //         schema: {
  //           ...component,
  //           label: component.label,
  //           key: component.key,
  //           isNew: true
  //         }
  //       };
  //       // this.formBuilderOptions.builder.resource.components[component.key] = subgroup.components[component.key];
  //     }, true);
  //     this.formBuilderOptions.builder.resource.groups[resourceKey] = subgroup;
  //     // console.log(this.formBuilderOptions);
  //   });
  //   // this.rebuildEmitter.next(this.formBuilderOptions);
  //   // console.log(this.formBuilderOptions);
  // }
  // public setformvalues(description, icon) {
  //   this.formdetailsform.patchValue({
  //     formdescription: description,
  //     icon: icon
  //   });
  // }
  // get icon() {
  //   return this.formdetailsform.get('icon');
  // }
  // public openiconpicker() {
  //   const dialogRef: iconPickerOverlayRef = this.iconpickerservice.open();
  //   dialogRef.afterClosed().subscribe((icondata: any) => {
  //     // console.log(icondata);
  //     if (icondata) {
  //       this.formicon = icondata;
  //       this.icon.setValue(icondata);
  //     }
  //   });
  // }

  saveNewForm() {
    // const obj = {
    //   isCard: this.showCard,
    //   isHeader: this.showHeader,
    //   cardBackground: this.showCard ? this.cardBgColor : '',
    //   headerBackground: this.showHeader ? this.headerBgColor : '',
    //   footerBackground: this.footerBgColor,
    //   backgroundColor: this.bgColor,
    //   labelColor: this.labelColor,
    //   answerColor: this.answerColor,
    //   inputFocusColor: this.inputFocusColor,
    //   fontFamily: this.selectedFont,
    //   fontSize: this.themeFontSize,
    //   backgroundImage: this.selectedImage
    // }
    const ref = this.dialogService.open(CreateFormComponent, {
      header: this.type === 'masterdata' ? 'Create Masterdata' : (this.formType === 'Templates' ? 'Create Template' : 'Create Form'),
      width: '50%',
      // contentStyle: { 'overflow-y': 'scroll' },
      data: {
        type: this.type,
        formType: this.formType,
        isNewForm: true,
        content: this.forminfo.components,
        // theme: obj
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        if (this.formId === '1' && this.isCreateForm) {
          this.formId = data.formId;
        }
        this.formCategory = data.category;
        this.formservice.getformdata(data.formId, data.type)
          .subscribe((getformAPIres: any) => {
            this.formname = getformAPIres?.formName;
            this.formdescription = getformAPIres?.description;
            this.isdraft = getformAPIres?.isDraft;
            this.formicon = getformAPIres?.avatar;
            this.isNestedForm = getformAPIres?.formData?.formtype === 'nestedForm' ? true : false;
            if (!this.isNestedForm) {
              this.formBuilderOptions.builder.premium.components.form = true;
            }
            this.formtitle = getformAPIres?.formTitle;
            // this.forminfo = getformAPIres.formData;
            this.setTheme(getformAPIres?.theme);
          });
      }
    });
  }

  saveform(): Observable<any> {
    const obj = {
      formdescription: this.formdescription,
      icon: this.formicon,
      // isCard: this.showCard,
      // isHeader: this.showHeader,
      // cardBackground: this.showCard ? this.cardBgColor : '',
      // headerBackground: this.showHeader ? this.headerBgColor : '',
      // footerBackground: this.footerBgColor,
      // backgroundColor: this.bgColor,
      // labelColor: this.labelColor,
      // answerColor: this.answerColor,
      // inputFocusColor: this.inputFocusColor,
      // fontFamily: this.selectedFont,
      // fontSize: this.themeFontSize,
      // backgroundImage: this.selectedImage
    };
    return this.formservice.saveform(this.formId, this.formname, obj, this.forminfo, this.type).pipe(
      tap(response => {
        if (typeof response !== 'object') {
          response = JSON.parse(response);
        }
        if (response.error === '') {
          if (response.status === 'Success') {
            this.issaved = true;
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Saved successfully!' });
  
            this.formservice.getformdata(this.formId, this.type).subscribe((getformAPIres: any) => {
              this.formname = getformAPIres?.formName;
              this.formdescription = getformAPIres?.description;
              this.isdraft = getformAPIres?.isDraft;
              this.formicon = getformAPIres?.avatar;
              this.formtitle = getformAPIres?.formTitle;
              this.formVersionComment = getformAPIres?.versionComments;
  
              setTimeout(() => {
                const eventCustom = new CustomEvent('FormRenderingComplete', {});
                document.dispatchEvent(eventCustom);
              }, 1000);
              this.setTheme(getformAPIres?.theme);
            });
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      })
    );
  }

  saveFormData() {
    this.saveform().subscribe();
  }

  public testform() {
    let data = {
      submission: {},
      formId: this.formId,
      type: this.type,
      isPreview: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    // const str = "preview/"+routerData;
    // const url = this.router.serializeUrl(
    //   this.router.createUrlTree([`/preview/${routerData}`])
    // );
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    window.open(baseUrl + newRelativeUrl, '_blank');
    // window.open(`http://localhost:4200/test-form/${routerData}`, '_blank');
    // const dialogRef = this.matDialog.open(TestformrendererDialogComponent, {
    //   minWidth: '90%',
    //   panelClass: 'custom-dialog',
    //   data: {
    //     submission:{},
    //     formId: this.formid,
    //     type: this.type
    //   },
    // });
  }

  publishForm(element: any, isUnpublished: boolean) {
    if (this.landsacpe?.toLowerCase() !== 'production' && !isUnpublished) {
      this.saveform().subscribe(() => {
        this.callPublishAPI(element, isUnpublished);
      });
    } else {
      this.callPublishAPI(element, isUnpublished);
    }
  }

  private callPublishAPI(element: any, isUnpublished: boolean, event?: any) {
    if (this.landsacpe?.toLowerCase() !== 'production' && isUnpublished) {
      this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Unpublish this version?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          const data = {
            comments: this.publishform.value.comments,
            release: this.landsacpe?.toLowerCase() === 'production' ? 'finalRelease' : this.formRelease,
            unPublish: this.landsacpe?.toLowerCase() === 'production' ? false : isUnpublished
          };
        
          this.formservice.publishform(this.formId, data).subscribe(response => {
            if (response.status.toLowerCase() === 'success') {
              element?.hide();
              this.getformdata(this.formId, this.type);
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully Unpublished!' });
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
          });
        },
        reject: () => {
          this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Action aborted!' });
        }
      });
    } else {
      const data = {
        comments: this.publishform.value.comments,
        release: this.landsacpe?.toLowerCase() === 'production' ? 'finalRelease' : this.formRelease,
        unPublish: this.landsacpe?.toLowerCase() === 'production' ? false : isUnpublished
      };
    
      this.formservice.publishform(this.formId, data).subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          element?.hide();
          this.getformdata(this.formId, this.type);
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Published successfully!' });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
    }
  }

  addClickEvent() {
    let editForm = document.querySelectorAll<HTMLElement>('div[ref="editComponent"]');
    for (var i = 0; i < editForm.length; i++) {
      editForm[i].addEventListener('click', this.myfunc.bind(this, i), true)
    }
  }
  onchange(event: any) {
    console.log("change" + event.type);
    if (event.type === 'addComponent') {
      this.addClickEvent();
      if (this.showConfiguration) {
        this.selectedEditIndex = undefined;
        for (let i = 0; i < event?.parent?.components.length; i++) {
          if (event.parent.components[i]?.component?.key === event.component.key) {
            let ele = event.parent.components[i].element;
            event.parent.components[i].element.children[0].children[5].click();
          }
        }
      }
    }
    if (['addComponent', 'saveComponent', 'deleteComponent'].indexOf(event.type) > -1) {
      if (event.form.display === 'wizard') {
        this._form = this.forminfo;
      } else {
        this._form = cloneDeep(event.form);
      }
    }
    this.issaved = false;
    if ((!this.showConfiguration && event.type === "saveComponent")) {
      return;
    }
    if (event.type !== 'deleteComponent') {
      this.formChangedData = event;
    }
    if (event.type === 'deleteComponent' && this.showConfiguration && this.formChangedData.component.key === event.component.key) {
      this.resetFormDisplay();
    }

    let compnentApikey = document.querySelector<HTMLElement>('div[ref="compnentApikey"]');
    if (compnentApikey) {
      compnentApikey.innerHTML = 'API - Property: ' + event.component.key;
    }
    if (event.type === 'updateComponent' || event.type === "saveComponent" || (event.type === 'deleteComponent' && this.showConfiguration)) {
      this.formChangedData.component.keyModified = false;
      this.showConfiguration = true;
      this.zone.run(async () => {
        let customFormiobuilder = document.querySelector<HTMLElement>(".customFormiobuilder");
        if (customFormiobuilder && customFormiobuilder.children && customFormiobuilder.children.length > 0 && customFormiobuilder.children.length === 2) {
          let formbuilderComponents = customFormiobuilder.children[0];
          customFormiobuilder.children[0].remove();
          var formbuilder = document.querySelector<HTMLElement>('.formbuilder-container');
          if (formbuilder) {
            formbuilder.prepend(formbuilderComponents);
          }
        }
        let resetButton = document.querySelector<HTMLElement>('button[ref="resetUI"]');
        if (resetButton) {
          resetButton.addEventListener('click', closeEditContainer.bind(this));

        }
        function closeEditContainer(event) {
          this.resetFormDisplay();

        }
        let formarea = document.querySelector<HTMLElement>(".formarea");
        if (formarea) {
          formarea.style.marginTop = "5px";
        }
        let dialogContentsAll = document.querySelectorAll<HTMLElement>(".formio-dialog");
        let dialogContents = dialogContentsAll[dialogContentsAll.length - 1]
        if (dialogContents && dialogContents.classList.contains('component-rendering-hidden')) {
          let dialogCancelButton = document.querySelector<HTMLElement>('button[ref="dialogCancelButton"]');
          if (dialogCancelButton) {
            dialogCancelButton.classList.add("ui");
            dialogCancelButton.classList.add("button");
            dialogCancelButton.classList.add("mini");
            dialogCancelButton.classList.add("info");
          }
        } else {
          let modalClearChanges = document.querySelector<HTMLElement>('div[ref="modalClearChanges"]');
          if (modalClearChanges) {
            modalClearChanges.style.display = "none"
          }

          if (dialogContents && dialogContents.classList.contains('formio-dialog')) {
            let cancelButton = document.querySelectorAll<HTMLElement>('button[ref="dialogClose"]');
            if (cancelButton && cancelButton.length > 0) {
              for (let i = 0; i < cancelButton.length; i++) {
                cancelButton[i].style.display = "none";
              }
            }


            dialogContents.classList.remove('formio-dialog');
            dialogContents.classList.remove('formio-dialog-theme-default');
            dialogContents.classList.remove('component-settings');
            if (dialogContents && dialogContents.parentElement)
              dialogContents.parentElement.style.width = '100%'
            let dialogContentchildnodes = dialogContents.childNodes;
            if (dialogContentchildnodes && dialogContentchildnodes.length > 0) {
              if (dialogContents.classList.contains('component-rendering-hidden')) {
              } else {
                dialogContentchildnodes[1].remove();
              }
            }
            setTimeout(() => {
              let formbuilder = document.querySelector<HTMLElement>(".splitareaconfig");
              if (formbuilder) {
                if (formbuilder.childNodes.length > 0 && formbuilder.childNodes[0]) {
                  if (dialogContents.classList.contains('component-rendering-hidden')) {
                  } else {
                    formbuilder.childNodes[0].remove();

                  }
                }
                dialogContents.classList.add('card');
                dialogContents.style.marginTop = "5px"
                dialogContents.style.background = "#f8f9fa";
                // dialogContents.style.height = "100%"
                if (dialogContents.classList.contains('component-rendering-hidden')) {
                } else {
                  formbuilder.appendChild(dialogContents);
                }
              }
            }, 0);

          }
        }

        var builderComp = document.querySelectorAll<HTMLElement>('.builder-component');
        if (builderComp && builderComp.length > 0) {
          let isFound = false;
          let eleIndex;
          let ele = 0;
          for (let i = 0; i < builderComp.length; i++) {
            if (this.formChangedData.component.key === builderComp[i]['component']?.key) {
              ele = ele + 1;
              isFound = true;
              eleIndex = i;
              let formarea = document.querySelector<HTMLElement>(".formarea");
              if (formarea) {
                formarea.classList.remove("twelve");
              }
            }
          }
          if (ele === 1) {
            this.selectedEditIndex = eleIndex;
          }
          eleIndex = this.selectedEditIndex;
          console.log("this.selectedEditIndex "+ this.selectedEditIndex);
          for (let i = 0; i < builderComp.length; i++) {
            if (this.selectedEditIndex !== undefined && i === this.selectedEditIndex) {
              builderComp[i].style.padding = "10px";
              builderComp[i].style.borderRadius = "0.28571429rem";
              builderComp[i].style.boxShadow = "0 0 0 2px var(--primary-color)";
            }
             else if (this.selectedEditIndex !== undefined && i !== this.selectedEditIndex) {
              builderComp[i].style.padding = "";
              builderComp[i].style.boxShadow = "";
              builderComp[i].style.background = "";
            } else {

            }
          }
          if (event.type === "saveComponent") {
            let editComponent = document.querySelectorAll<HTMLElement>('div[ref="editComponent"]');
            if (editComponent && editComponent.length > 0 && eleIndex !== undefined) {
              editComponent[eleIndex].click();
            }
          }
        }
        if (event.type === "saveComponent") {
          this.addClickEvent();
        }
        let formcompLabel = document.querySelectorAll<HTMLElement>(".formio-component-label");
        if (formcompLabel && formcompLabel.length > 0) {
          for (let i = 0; i < formcompLabel.length; i++) {
            let children = formcompLabel[i].children;
            if (children && children.length > 0 && children[1] && children[1].children && children[1].children.length > 0) {
              let inputfield = children[1];
              for (let j = 0; j < children.length; j++) {
                if (children[j].classList.contains('update-key')) {
                  return;
                }
              }
              children[1].remove();
              var div = document.createElement('div');
              div.classList.add('update-key');
              div.append(inputfield);
              var button = document.createElement('button');
              // Set button attributes and properties
              button.innerHTML = '<i class="key icon"></i>';
              button.style.border = "none";
              button.style.marginLeft = "5px";
              button.style.width = "40px";
              button.classList.add('ui')
              button.classList.add('button');
              button.classList.add('icon')
              button.classList.add('mini')
              button.classList.add('primary')
              button.addEventListener('click', updateKey.bind(this));
              div.style.display = "inline-flex";
              div.style.width = "100%";
              div.appendChild(button);
              formcompLabel[i].append(div);
              if (div && div.children && div.children.length > 0) {
                let child = <HTMLElement>div.children[0]
                child.style.width = "100%"
              }
            }
          }
        }
        function updateKey() {
          let label = _.camelCase(this.formChangedData.label);
          this.formChangedData.key = label;
          let value = this.builder.updateComponentKey(this.formChangedData.component);
          this.formChangedData.component.key = value;
          let saveComponent = document.querySelector<HTMLElement>('button[ref="saveButton"]');
          if (saveComponent) {
            saveComponent.click();
          }
        }
      });
    } else {
      if (event.type === 'addComponent') {
        if (this.showConfiguration) {
          if (document.getElementById(event.component.id) && this.builder.getParentElement(document.getElementById(event.component.id))) {
            let parent = this.builder.getParentElement(this.builder.getParentElement(document.getElementById(event.component.id)))
            this.builder.editComponent(event.component, parent, false, false, event.component)
          }
        }
      }
    }
  }
  changeLanguage(language: string) {
    this.language = language;
    this.formBuilderOptions.language = this.language;
  }

  openNav() {
    this.isformcompsopen = !this.isformcompsopen;
    let formarea = document.querySelector<HTMLElement>(".formarea");
    if (this.isformcompsopen) {
      if (formarea)
        formarea.style.flexBasis = "75%"

    } else {
      if (formarea) {
        formarea.style.flexBasis = "100%";
        formarea.style.marginLeft = "10px";
      }
    }
  }

  resetFormDisplay() {
    this.showConfiguration = false;
    var formbuilder = document.querySelector<HTMLElement>('.formbuilder-container');
    var formbuilder = document.querySelector<HTMLElement>('.formbuilder-editor-container');
    if (formbuilder) {
      if (formbuilder.childNodes.length > 0 && formbuilder.childNodes[3]) {
        formbuilder.childNodes[3].remove();
      }

    }
    var builderComp = document.querySelectorAll<HTMLElement>('.builder-component');
    if (builderComp && builderComp.length > 0) {
      for (let i = 0; i < builderComp.length; i++) {
        builderComp[i].style.padding = "";
        builderComp[i].style.boxShadow = "";
        builderComp[i].style.background = "";
      }
    }
    var formbuilder = document.querySelector<HTMLElement>('.formbuilder-editor-container');
    if (formbuilder) {
      formbuilder.style.display = "contents";
    }

    let formarea = document.querySelector<HTMLElement>(".formarea");
    if (formarea) {
      formarea.classList.remove("formareaWidth");
      formarea.classList.remove('wide')
      formarea.classList.remove('column')

      formarea.classList.add("twelve");
      formarea.classList.add('wide')
      formarea.classList.add('column')
      formarea.style.width = "100% !important";
    }
  }

  openNestedFormListDialog() {
    let data = this.formservice.getNavigationData();
    if (this.formType === "Nested Form" && data.isNestedForm) {
      const ref = this.dialogService.open(NestedFormsListComponent,{
        header: `Form list (${this.formTitleByRouter})`,
        width: '60%',
        // height: '65%',
        contentStyle: { 'overflow-y': 'scroll' },
        data: {
          formId: this.formIdByRouter,
          formType: this.formType,
          formTitle: this.formTitleByRouter
        }
      });
    }
    setTimeout(() => {
      this.router.navigate(['forms']);
    }, 300);
    if (this.layoutService.state.staticMenuDesktopInactive === true) {
      this.layoutService.state.staticMenuDesktopInactive = false;
    }
    data.isNestedForm = false;
    this.formservice.setNavigationData(data);
  }

  goBack() {
    this.router.navigate(['home']);
    if (this.layoutService.state.staticMenuDesktopInactive === true) {
      this.layoutService.state.staticMenuDesktopInactive = false;
    }
  }

  showSelectImageDialog() {
    this.searchQuery = undefined;
    this.getAllUnsplashImages();
    // this.showImageDialog = true;
    this.searchSubject.pipe(
      debounceTime(500) 
    ).subscribe(searchText => {
      this.getAllUnsplashImages();
    });
  }

  getAllUnsplashImages() {
    this.showImageDialog = true;
    this.formservice.getImages(this.searchQuery).subscribe((res: any)=>{
      this.imageArr = res.results;
    })
  }

  onSelecteImage(url: any) {
    this.selectedImage = url;
    this.convertImageToBase64(this.selectedImage).then(base64 => {
      this.selectedImage = base64;
    });
  }

  convertImageToBase64(url: string): Promise<string> {
    return fetch(url)
      .then(response => response.blob())
      .then(blob => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      });
  }

  removeSelectedImage() {
    this.selectedImage = null;
  }

  onSearch() {
    this.searchSubject.next(this.searchQuery);
  }

  saveUnsplaceImage() {
    this.showImageDialog = false;
    this.imageChangedEvent = null;
    this.imageLinkURL = null;
  }

  showUploadPopup() {
    document.getElementById('imageInput').click();
  }

  fileChangeEvent(event: any): void {
    this.imageChangedEvent = event;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
    this.selectedImage = this.croppedImage;
  }

  imageLoaded(image: LoadedImage) {
    this.showCropper = true;
    this.imageLinkURL = null;
  }

  uploadedImage() {
    this.showImageDialog = false;
  }

  resetUploadImage() {
    this.imageChangedEvent = null;
    this.removeSelectedImage();
  }

  saveLinkURL() {
    this.imageChangedEvent = null;
    this.selectedImage = this.imageLinkURL;
    this.showImageDialog = false;
    this.convertImageToBase64(this.selectedImage).then(base64 => {
      this.selectedImage = base64;
    });
  }

  cancelDialog() {
    this.showImageDialog = false;
    this.selectedImage = null;
    this.searchQuery = null;
    this.imageChangedEvent = null;
    this.imageLinkURL = null;
  }

  cancelThemeSetting() {
    this.sidebarVisible = false;
    this.selectedImage = null;
    this.showCard = false;
    this.showHeader = false;
    this.cardBgColor = null;
    this.headerBgColor = null;
    this.footerBgColor = null;
    this.bgColor = null;
    this.labelColor = null;
    this.answerColor = null;
    this.inputFocusColor = null;
    this.selectedFont = null;
    this.themeFontSize = 12;
  }

  SaveThemeSetting() {
    const data = {
      formId: this.formId,
      name: this.formname,
      theme: {
        isCard: this.showCard,
        isHeader: this.showHeader,
        cardBackground: this.showCard ? this.cardBgColor : '',
        headerBackground: this.showHeader ? this.headerBgColor : '',
        footerBackground: this.footerBgColor,
        backgroundColor: this.bgColor,
        labelColor: this.labelColor,
        answerColor: this.answerColor,
        inputFocusColor: this.inputFocusColor,
        fontFamily: this.selectedFont,
        fontSize: this.themeFontSize,
        backgroundImage: this.selectedImage
      }
    }
    this.formservice.savetheme(data).subscribe({
        next:(res: any) => {
          if (res.status.toLowerCase() === 'success') {
            this.sidebarVisible = false;
            this.messageService.add({ severity:'success', summary:'Success', detail: 'Theme saved successfully.' });
          } else {
            this.messageService.add({ severity:'error', summary:'Error', detail: 'Something went wrong.' });
          }
        },
        error: (error) => {
          this.errmsg = error.error.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
    });
  }

  helpURL() {
    window.open("https://docs.unvired.com/builder/admin/forms/#form-components", '_blank');
  }

  onReleaseChange(event: any) {
    if (event.checked) {
      this.formRelease = 'finalRelease';
      this.publishform.controls['comments'].setValue(this.publishform.controls['comments'].value);
      this.publishform.controls['comments'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.publishform.controls['comments'].updateValueAndValidity();
    } else {
      this.formRelease = 'preRelease';
      this.publishform.controls['comments'].setValue(this.publishform.controls['comments'].value);
      this.publishform.controls['comments'].clearValidators();
      this.publishform.controls['comments'].updateValueAndValidity();
    }
  }

  showPublishFormPannel(element: any, event: any) {
    if (this.landsacpe?.toLowerCase() === 'production') {
      this.publishform.controls['comments'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.publishform.controls['comments'].updateValueAndValidity();
    }
    this.publishform.reset();
    this.publishform.controls['formRelease'].setValue(false);
    this.publishform.controls['formRelease'].updateValueAndValidity();
    this.formRelease = this.landsacpe?.toLowerCase() === 'production' ? null : 'preRelease';
    element.show(event);
  }

  resetPublishForm(element: any) {
    element.hide();
    this.publishform.controls['comments'].setValue(null);
    this.publishform.controls['comments'].clearValidators();
    this.publishform.controls['comments'].updateValueAndValidity();
    this.publishform.controls['formRelease'].setValue(false);
    this.publishform.controls['formRelease'].updateValueAndValidity();
    this.publishform.reset();
  }

  getVersionInfo(version: number): string {
    if (this.landsacpe?.toLowerCase() === 'production') return version?.toString();
    if (this.releaseType === 'preRelease') {
      return `${version} (Pre Release)`;
    } else if (this.releaseType === 'finalRelease') {
      return `${version}`;
    } else {
      return 'Draft';
    }
  }

  checkFormSaveButtonVisiblity(): boolean {
    if (this.landsacpe?.toLowerCase() === 'production') {
      return true;
    } else if (this.releaseType === 'preRelease') {
      return false;
    } else {
      return true;
    }
  }

  showThemeSidebar() {
    this.sidebarVisible = true;
    this.formservice.getAllFonts().subscribe((data: string[]) => {
      if (data && data?.length > 0) {
        this.fontsArr = data.map((font: string) => {
          return { name: font };
        });
      } else {
        this.fontsArr = [];
      }
    });
  }

  displayCard(isCard: boolean) {
    this.showCard = isCard;
  }

  displayHeader(isHeader: boolean) {
    this.showHeader = isHeader;
  }

  OnSelectFont(event: any) {
    this.selectedFont = event.value;
    WebFont.load({
      google: {
        families: [this.selectedFont],
      },
    });
  }

  saveFont() {
    this.showFontsDialog = false;
  }

  hideFontDialog() {
    this.showFontsDialog = false;
    this.selectedFont = null;
  }

  onSelectFontSize(type: string) {
    if (type === 'increase' && this.themeFontSize < 16) {
      this.themeFontSize += 1;
    } else if (type === 'decrease' && this.themeFontSize > 10) {
      this.themeFontSize -= 1;
    }
  }

  setTheme(theme: any) {
    if (theme) {
      this.showCard = theme?.isCard;
      this.showHeader = theme?.isHeader;
      this.cardBgColor = theme?.cardBackground;
      this.headerBgColor = theme?.headerBackground;
      this.footerBgColor = theme?.footerBackground;
      this.bgColor = theme?.backgroundColor;
      this.labelColor = theme?.labelColor;
      this.answerColor = theme?.answerColor;
      this.inputFocusColor = theme?.inputFocusColor;
      this.selectedFont = theme?.fontFamily;
      this.themeFontSize = theme?.fontSize || 12;
      this.selectedImage = theme?.backgroundImage;
    }
  }

}