// @import url("https://cdn.jsdelivr.net/npm/semantic-ui@2.5.0/dist/semantic.min.css");
.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}
@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}
.highlight {
    cursor: pointer;
    color: var(--primary-color) !important;
}

.formbuilder-container {
    box-sizing: border-box;
    width: 100%;
    margin-top: 10px;
    padding-left: 0px;
    // padding-right: 25px;
}

.mat-form-field {
    width: 500px;
}

/* .save-button {
    float: right;
    margin-top: 10px;
    padding-right: 10px;
} */

.mat-flat-button {
    margin-left: 10px;
}

.error-msg {
    color: #f44336;
    margin: 24px;
    width: 600px;
    box-sizing: border-box;
    word-break: break-word;
}
:host ::ng-deep .card{
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}
:host ::ng-deep .card-header {
    border: none !important;
}
body {
    margin: 0;
    padding: 0;
    height: 100vh;
}

.ui.container {
    // position: relative;
    // height: 100vh;
}

#floatingButton {
    position: absolute;
    top: 50%; /* Adjust the top margin based on your preference */
    left: 0; /* Adjust the left margin based on your preference */
}
.config-button {
    display: block;
    position: fixed;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    background: var(--primary-color);
    color: var(--primary-color-text);
    text-align: center;
    top: 17%;
    // right: 0;
    margin-top: -1.5rem;
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    transition: background-color var(--transition-duration);
    overflow: hidden;
    cursor: pointer;
    z-index: 999;
    box-shadow: -.25rem 0 1rem rgba(0,0,0,.15);

    i {
        font-size: 1rem;
        line-height: inherit;
        transform: rotate(0deg);
        transition: transform 1s;
    }
    
    &:hover {
        background: var(--primary-400);
    }
    .ui.segment{
        text-align: left !important;
    }
 
}
.close-button{
    display: block;
    position: fixed;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    background: var(--primary-color);
    color: var(--primary-color-text);
    text-align: center;
    top: 17%;
    right: 0;
    margin-top: -1.5rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
    transition: background-color var(--transition-duration);
    overflow: hidden;
    cursor: pointer;
    z-index: 999;
    box-shadow: -.25rem 0 1rem rgba(0,0,0,.15);

    i {
        font-size: 1rem;
        line-height: inherit;
        transform: rotate(0deg);
        transition: transform 1s;
    }
    
    &:hover {
        background: var(--primary-400);
    }
    .ui.segment{
        text-align: left !important;
    }
}

.custom-height {
    height: calc(100vh - 14vh) !important;
}
// .formbuilder-container{
//     display: table !important;
//     table-layout: fixed !important;
// }

// .formbuilder-container div {
//     display: table-cell !important;
// }

// .formbuilder-editor-container div {
//         display: table-cell !important;
//     }

.hover-image {
    display: block;
    width: 90%;
    height: 90%;
    border-radius: 4px;
}

.image-container {
    // border: 1px solid darkgrey;
    // border-radius: 5px;
    // padding: 8px;
    width: 80%;
    height: 90%;
    position: relative;
    display: inline-block;
}
.hover-overlay {
    position: absolute;
    top: 0;
    left: 13px;
    width: 90%;
    height: 100%;
    background-color: #74747412;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    border-radius: 3px;
}
.image-container:hover  {
    cursor: pointer;
    .closeIcon, .hover-overlay {
        opacity: 1;
    }
}
.closeIcon {
    position: absolute;
    top: -5%;
    right: 2%;
    z-index: 2;
    font-size: 14px;
    background: #EF4444;
    border-radius: 50%;
    padding: 4px;
    color: white;
    opacity: 0;
}
.hover-card-image {
    border-radius: 4px;
}
.hover-card-image:hover {
    cursor: pointer;
    transform: scale(1.05, 1.05);
    transition: transform 0.5s;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
}
.upload-custom-image {
    padding: 20px;
    border: 2px dashed darkgray;
    background-color: var(--highlight-bg);
    color: var(--primary-color);
    cursor: pointer;
    font-weight: 600;
    border-radius: 5px;
    width: 100%;
    text-align: center;
}
.image-cropper-container {
    width: 500px; /* Set your desired width */
    height: 400px; /* Set your desired height */
    position: relative;
  }

::ng-deep .publish p-messages .p-message {
    margin: 0rem 0rem 1rem 0rem !important;
    .p-message-wrapper {
        padding: 1rem !important;
    }
}

.edit-font {
    padding: 3px 4px;
    border: 1px solid var(--primary-color);
    border-radius: 5px;
    cursor: pointer;
    color: var(--primary-color);
    font-weight: bold;
}

.font-size-style {
    border: 1px solid var(--surface-300);
    border-radius: 5px;
    padding: 3px 8px;
}

.selected-font-size {
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

.form-card-border {
    border: 4px solid var(--primary-color);
    border-radius: 4px;
}

::ng-deep .custom-colorpicker p-colorPicker .p-colorpicker-preview, .p-fluid .p-colorpicker-preview.p-inputtext {
    width: 3.5rem !important;
    height: 1.75rem !important;
}

.image-hover {
    position: relative;
    cursor: pointer;
    img {
      border-radius: 5px;
    }
  }
  
  .image-hover-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 10px;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    // pointer-events: none;
    // display: flex;
    // flex-direction: row;
    text-align: center;
    gap: 5px;
    font-weight: 500;
  }
  
  .image-hover:hover .image-hover-text {
    opacity: 1;
  }