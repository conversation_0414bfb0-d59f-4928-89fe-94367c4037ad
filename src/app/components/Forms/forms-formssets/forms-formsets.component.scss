.main-title {
    font-size: 14px;
    font-weight: 700;
  }
  .description {
    font-size: 14px;
    font-weight: 400;
  }
  .form-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  .schedule-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
  }
  ::ng-deep .set p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 100px;
  }
  ::ng-deep .set p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }

  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  ::ng-deep .set p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 10px 15px 10px 15px !important;
  }

.scrolling {
    display: block;
    overflow-x: hidden;
    overflow-y: scroll;
    height: 280px;
}
.custom-icon {
    position: absolute;
    top: 18%;
    left: 1%;
    color: var(--primary-color) !important;
}

.form-catergory {
  padding: 5px 8px;
  border-radius: 5px;
  background-color: rgba(228, 81, 81, 0.093);
  font-size: 14px;
  font-weight: 400;
}

::ng-deep .set p-fieldset .p-fieldset-legend {
  padding: 0px !important;
  border: none !important;
  font-weight: normal !important;
}
.help-icon {
  position: absolute;
  top: 22px;
  right: 55px;
}