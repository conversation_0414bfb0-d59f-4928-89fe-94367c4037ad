<a href="https://docs.unvired.com/builder/admin/forms/#formsets" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="set" [ngClass]="{'card': formsets?.length > 0}">
    <div class="flex justify-content-end mb-3">
        <button pButton (click)="newFormset.show($event)" label="Create" pTooltip="Create Formset" icon="pi pi-plus" class="p-button-sm mr-2"></button>
        <button pButton (click)="assignFormsets.toggle($event);resetAssignFormset()" label="Assign" pTooltip="Assign Formset" class="p-button-sm"></button>
    </div>
    <p-accordion *ngIf="formsets" (onOpen)="expand = true" (onClose)="expand = false">
        <p-accordionTab *ngFor="let element of formsets" (click)="selectedFormsetData(element)">
            <ng-template pTemplate="header">
                <div class="flex flex-row align-items-center gap-3 ml-2 w-full">
                    <span class="material-icons" style="color:var(--primary-color) !important;">{{element.avatar}}</span>
                    <div class="flex flex-row align-items-center justify-content-between flex-1">
                        <div class="flex flex-column align-items-start gap-1">
                            <div class="main-title">{{ element.name }}</div>
                            <div class="description">{{ element.help }}</div>
                        </div>
                        <div *ngIf="expand && SelectedFormset?.id === element.id" class="flex flex-wrap gap-3">
                            <div class="flex align-items-center">
                                <p-radioButton value="showForms" [(ngModel)]="selectedOption" (click)="$event.stopPropagation()" inputId="forms"></p-radioButton>
                                <label for="forms" class="ml-2 mb-0">Forms</label>
                            </div>
                            <div *ngIf="!isDeveloperAndProd" class="flex align-items-center">
                                <p-radioButton value="showAssignments" [(ngModel)]="selectedOption" (click)="$event.stopPropagation()" inputId="assignments"></p-radioButton>
                                <label for="assignments" class="ml-2 mb-0">Assignments</label>
                            </div>
                            <div class="flex align-items-center">
                                <p-radioButton value="showSchedules" [(ngModel)]="selectedOption" (click)="$event.stopPropagation()" inputId="schedules"></p-radioButton>
                                <label for="schedules" class="ml-2 mb-0">Schedules</label>
                            </div>
                        </div>
                        <div class="description"><b class="mr-2">{{ element.totalForms }}</b>Forms</div>
                    </div>
                    <button pButton (click)="deleteFormset(element.id, $event);$event.stopPropagation()" icon="pi pi-fw pi-times" class="p-button-text p-button-danger p-button-rounded" pTooltip="Remove"></button>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <!-- Forms Start -->
                <div *ngIf="selectedOption === 'showForms'" (click)="$event.stopPropagation()">
                    <p-table #forms 
                        [value]="formsetForms" dataKey="formId"
                        responsiveLayout="scroll" 
                        [scrollable]="true" 
                        selectionMode="multiple" 
                        [(selection)]="deleteFormsArr"
                        styleClass="p-datatable-sm p-datatable-gridlines"
                        [paginator]="true"
                        [rows]="5"
                        [rowsPerPageOptions]="[5, 10, 25]"
                        [tableStyle]="{ 'min-width': '50rem' }"
                        [globalFilterFields]="['formName','description', 'category']">
                    
                        <ng-template pTemplate="caption">
                            <div class="flex flex-row align-items-center justify-content-between">
                                <div>
                                    <span class="p-input-icon-left">
                                        <i class="pi pi-search"></i>
                                        <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="search"
                                        (input)="forms.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                                    </span>
                                    <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                                        (click)="clearAllFilter(forms)" pTooltip="Clear all filters" tooltipPosition="top">
                                    </button>
                                </div>
                                <div>
                                    <button pButton (click)="removeMultipleForms($event)" icon="pi pi-trash" class="p-button-sm p-button-danger mr-2" [disabled]="deleteFormsArr?.length === 0"></button>
                                    <button pButton (click)="assignForms.toggle($event);getAllForms()" label="Assign" class="p-button-sm"></button>
                                </div>
                            </div>
                        </ng-template>
                    
                    <!-- Header -->
                        <ng-template pTemplate="header" let-columns>
                            <tr>
                                <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                                <th style="min-width: 10rem">Title</th>
                                <th style="min-width: 10rem">Description </th>
                                <th style="min-width: 10rem">Category </th>
                                <th class="text-center">Is mandatory? </th>
                            </tr>
                        </ng-template>
                    
                    <!-- Table data -->
                        <ng-template pTemplate="body" let-form let-columns="columns">
                            <tr (click)="goToPreview(form)" class="cursor-pointer">
                                <td class="text-center">
                                    <p-tableCheckbox [value]="form" (click)="$event.stopPropagation()"></p-tableCheckbox>
                                </td>
                                <td>
                                    <button pButton class="p-button-text p-button-sm text-left">
                                        <span class="material-icons" style="color:var(--primary-color) !important;">{{form.avatar}}</span>
                                        <span class="px-2">{{form.formName}}</span>
                                    </button>
                                </td>
                        
                                <td>
                                    {{form.description}}
                                </td>
                        
                                <td>
                                    {{form.category}}
                                </td>

                                <td class="text-center">
                                    <div class="flex flex-row justify-content-center">
                                        <p-inputSwitch id="mandatory" [(ngModel)]="form.mandatory" (onChange)="IsFormMandatory(form)" (click)="$event.stopPropagation()"></p-inputSwitch>
                                        <label htmlFor="mandatory" class="ml-2 mb-0">{{form.mandatory === true ? 'Yes' : 'No'}}</label>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>
                    
                    <!-- No data -->
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td class="text-center" style="font-weight: bold;" colspan="5">No Forms Assigned yet!</td>
                            </tr>
                        </ng-template>
                    </p-table>

                    <!-- <div class="col-12 pl-0">
                        <button pButton (click)="assignForms.toggle($event);getAllForms()" label="Assign Form" class="p-button-sm bg-blue"></button>
                    </div>
                    <div *ngIf="formsetForms?.length > 0">
                        <div *ngFor="let form of formsetForms" class="col-12 form-list" (click)="goToPreview(form)">
                            <div class="flex flex-row align-items-center gap-3">
                                <span class="material-icons">{{form.avatar}}</span>
                                <div class="flex flex-column align-items-start gap-1">
                                    <div class="main-title">{{ form.formName }}</div>
                                    <div class="description">{{ form.description }}</div>
                                </div>
                                <div class="flex flex-row justify-content-end align-items-center flex-1">
                                    <div class="flex align-items-center mr-3">
                                        <p-checkbox [value]="form" [(ngModel)]="form.mandatory" [inputId]="form.formId" (click)="IsFormMandatory(form);$event.stopPropagation()" [binary]="true"></p-checkbox>
                                        <label [for]="form.formId" class="ml-2 mb-0">Is mandatory?</label>
                                    </div>
                                    <div class="form-catergory">{{ form.category }}</div>
                                </div>
                                <button pButton (click)="removeForm(form.formId, $event);$event.stopPropagation()" icon="pi pi-fw pi-times" class="p-button-text p-button-danger p-button-rounded" pTooltip="Remove"></button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!formsetForms?.length > 0" class="col text-center">
                        <span>No Forms Assigned yet!</span>
                    </div> -->
                </div>

                <!-- Assignment Start -->
                <div *ngIf="selectedOption === 'showAssignments'" (click)="$event.stopPropagation()">
                    <p-table #assignments 
                        [value]="assignmentsArr" dataKey="assignmentId"
                        responsiveLayout="scroll" 
                        [scrollable]="true" 
                        selectionMode="multiple" 
                        styleClass="p-datatable-sm p-datatable-gridlines"
                        [tableStyle]="{ 'min-width': '50rem' }"
                        [(selection)]="selectedTeamsArr"
                        rowExpandMode="single"
                        [paginator]="true"
                        [rows]="5"
                        [rowsPerPageOptions]="[5, 10, 25]"
                        (onRowCollapse)="onRowCollapse()" 
                        (onRowExpand)="onRowExpand()" 
                        [globalFilterFields]="['teamId', 'description', 'members', 'forms']">
                
                        <ng-template pTemplate="caption">
                            <div class="flex flex-row align-items-center justify-content-between">
                                <div>
                                    <span class="p-input-icon-left">
                                        <i class="pi pi-search"></i>
                                        <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                                            (input)="assignments.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                                    </span>
                                    <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                                        (click)="clearAllFilter(assignments)" pTooltip="Clear all filters" tooltipPosition="top">
                                    </button>
                                </div>
                                <div>
                                    <button pButton (click)="removeTeamsAssignments($event)" [disabled]="selectedTeamsArr.length === 0"
                                        icon="pi pi-fw pi-trash" pTooltip="Remove Assignments" tooltipPosition="left" class="p-button-sm p-button-danger mr-2">
                                    </button>
                                    <button pButton (click)="assignTeamPanel.toggle($event);getAllTeams()" label="Assign" class="p-button-sm bg-blue">
                                    </button>
                                </div>
                            </div>
                        </ng-template>
                <!-- Header -->
                        <ng-template pTemplate="header" let-columns>
                            <tr>
                                <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                                <th style="min-width: 10rem">Team</th>
                                <th style="min-width: 10rem">Description</th>
                                <th>Members</th>
                                <th>Forms</th>
                            </tr>
                        </ng-template>
                <!-- Table data -->
                        <ng-template pTemplate="body" let-item let-columns="columns" let-expanded="expanded">
                            <tr  (click)="assignments.toggleRow(item, $event);showUpdateAssignmentDialog(item.assignmentId,item.teamId)" class="cursor-pointer">
                                <td class="text-center" id="{{'rowExpansion'+ item.assignmentId}}">
                                    <p-tableCheckbox [value]="item" (click)="$event.stopPropagation()"></p-tableCheckbox>
                                </td>
                                <td>
                                    {{item.teamId}}
                                </td>
                        
                                <td>
                                    {{item.description}}
                                </td>
                        
                                <td>
                                    {{item.members}}
                                </td>
                        
                                <td>
                                    {{item.forms}}
                                </td>
                            </tr>
                        </ng-template>
                <!-- Row Expand -->
                        <ng-template pTemplate="rowexpansion" let-element>
                            <tr>
                                <td colspan="12">
                                    <div class="card">
                                        <form [formGroup]="assignTeamToForm" class="pt-3" novalidate (click)="$event.stopPropagation()">
                                            <div class="col-12 p-0">
                                                <div hidden>
                                                  <span class="p-float-label">
                                                    <input id="teamid" pInputText formControlName="team" class="w-full p-inputtext-sm" readonly="true">
                                                    <label htmlFor="teamid" class="_required">Teams</label>
                                                  </span>
                                                </div>
                                                <div class="flex w-full align-items-center">
                                                    <div class="col-4 p-0">
                                                        <p-checkbox formControlName="enableShared" id="enableShared" binary="true" (onChange)="onUserChange($event)"></p-checkbox>
                                                        <label for="enableShared" class="mb-0 ml-2">Sharing <i class="pi pi-question-circle" pTooltip="Everyone works on same data"></i></label>
                                                    </div>
                                                    <div class="col-8 pr-0">
                                                        <span class="p-float-label">
                                                            <p-dropdown id="Shared" [formControl]="sharedUsers" optionLabel="label" required="true" [autoDisplayFirst]="false"
                                                                optionValue="key" [options]="sharedArr" class="p-inputtext-sm" styleClass="w-full">
                                                            </p-dropdown>
                                                            <label htmlFor="Shared" class="_required">Shared</label>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex w-full align-items-center">
                                                    <div class="col-2 pl-0">
                                                        <p-checkbox formControlName="enableReview" id="enableReview" binary="true" (onChange)="onReviewedChange($event)"></p-checkbox>
                                                        <label for="enableReview" class="mb-0 ml-2">Review <i class="pi pi-question-circle" pTooltip="Submissions needs to be reviewed"></i></label>
                                                    </div>
                                                    <div class="col-2 my-auto">
                                                        <!-- <span class="p-float-label">
                                                            <p-dropdown id="Reviewer" [formControl]="selectedReviewerType" optionLabel="label" required="true"
                                                                optionValue="key" [options]="reviewArr" class="p-inputtext-sm" styleClass="w-full">
                                                            </p-dropdown>
                                                            <label htmlFor="Reviewer" class="_required">Reviewer Type</label>
                                                        </span> -->
                                                        <div class="flex flex-row align-items-center">
                                                            <p-inputSwitch id="Reviewer" [formControl]="isReviewer" (onChange)="onReviewerChange($event)"></p-inputSwitch>
                                                            <label htmlFor="Reviewer" class="ml-2 mb-1">{{ selectedReviewerType | titlecase }}</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-8 pr-0">
                                                        <span class="p-float-label">
                                                            <p-dropdown id="assign" appendTo="body" [formControl]="selectedReviewer" required="true" [autoDisplayFirst]="false"
                                                                 [options]="reviewersarr" class="p-inputtext-sm" styleClass="w-full">
                                                            </p-dropdown>
                                                            <label htmlFor="assign" class="_required">Assign Reviewer</label>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex flex-row align-items-center">
                                                    <div class="col-6 pl-0">
                                                        <p-checkbox formControlName="enableShiftHandoff" [disabled]="!enableShared" id="enableShiftHandoff" binary="true"></p-checkbox>
                                                        <label for="enableShiftHandoff" class="mb-0 ml-2">Shift Handoff <i class="pi pi-question-circle" pTooltip="Teams work in shifts on forms"></i></label>
                                                    </div>
                                                    <div class="col-6 pr-0 text-right">
                                                        <button class="p-button-sm p-button-danger mr-2" style="outline: none !important;" pButton type="button" label="Cancel" (click)="resetAssignmentform(element.assignmentId)"></button>
                                                        <button class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateAssignments()" [disabled]="!assignTeamToForm.valid"></button>
                                                    </div>
                                                </div>
                    
                                            </div>
                                        </form>
                                    </div>
                                
                                </td>
                            </tr>
                        </ng-template>
                
                <!-- No data -->
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td class="text-center" style="font-weight: bold;" colspan="5">No Assignments Found.</td>
                            </tr>
                        </ng-template>
                    </p-table>

                    <!-- <div class="flex justify-content-between mb-2">
                        <button pButton (click)="removeTeamsAssignments($event)" [disabled]="selectedTeamsArr.length === 0"
                            icon="pi pi-fw pi-trash" pTooltip="Remove Assignments" tooltipPosition="left" class="p-button-sm bg-blue">
                        </button>
                        <button pButton (click)="assignTeamPanel.toggle($event);getAllTeams()" label="Assign" class="mr-2 p-button-sm bg-blue">
                        </button>
                    </div>
                    <div *ngIf="assignmentsArr?.length > 0">
                        <div *ngFor="let data of assignmentsArr" class="col-12 form-list" (click)="updateAssignment.toggle($event);showUpdateAssignmentDialog(data.assignmentId, data.teamId)">
                            <div class="flex flex-row align-items-center gap-3">
                                <p-checkbox [value]="data" [(ngModel)]="selectedTeamsArr" [inputId]="data.teamId" (click)="$event.stopPropagation()"></p-checkbox>
                                <div class="flex flex-column align-items-start gap-1">
                                    <div class="main-title">{{ data.teamId }}</div>
                                    <div class="description">{{ data.description }}</div>
                                </div>
                                <div class="flex flex-row justify-content-end flex-1 gap-2">
                                    <div class="flex flex-column align-items-start gap-1">
                                        <div class="description"><b class="mr-2">{{ data.members }}</b>Members</div>
                                        <div class="description"><b class="mr-2">{{ data.forms }}</b>Forms</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!assignmentsArr?.length > 0" class="col text-center">
                        <span>No Assignments!</span>
                    </div> -->
                </div>

                <!-- Schedules Start -->
                <div *ngIf="selectedOption === 'showSchedules'" (click)="$event.stopPropagation()">
                    <p-table #schedules 
                        [value]="schedulesArr" dataKey="id"
                        responsiveLayout="scroll" 
                        [scrollable]="true" 
                        styleClass="p-datatable-sm p-datatable-gridlines"
                        [tableStyle]="{ 'min-width': '50rem' }"
                        selectionMode="multiple" 
                        [(selection)]="selectedScheArr"
                        [paginator]="true"
                        [rows]="5"
                        [rowsPerPageOptions]="[5, 10, 25]"
                        [globalFilterFields]="['scheduleText', 'teams']">
                
                        <ng-template pTemplate="caption">
                            <div class="flex flex-row align-items-center justify-content-between">
                                <div>
                                    <span class="p-input-icon-left">
                                        <i class="pi pi-search"></i>
                                        <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                                            (input)="schedules.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                                    </span>
                                    <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                                        (click)="clearAllFilter(schedules)" pTooltip="Clear all filters" tooltipPosition="top">
                                    </button>
                                </div>
                                <div>
                                    <button pButton (click)="removeSchedules($event)" [disabled]="selectedScheArr.length === 0"
                                        icon="pi pi-fw pi-trash" pTooltip="Remove Schedules" tooltipPosition="left" class="p-button-sm p-button-danger mr-2 bg-blue">
                                    </button>
                                    <button pButton (click)="createSchedule.toggle($event);showCreateScheduleDialog()" pTooltip="Create Schedule" label="Schedule" class="p-button-sm bg-blue">
                                    </button>
                                </div>
                            </div>
                        </ng-template>
                
                <!-- Header -->
                        <ng-template pTemplate="header" let-columns>
                            <tr>
                                <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                                <th style="min-width: 10rem">Recurrence </th>
                                <th style="min-width: 10rem">Team </th>
                            </tr>
                        </ng-template>
                
                <!-- Table data -->
                        <ng-template pTemplate="body" let-sche let-columns="columns">
                            <tr>
                                <td class="text-center">
                                    <p-tableCheckbox [value]="sche" (click)="$event.stopPropagation()"></p-tableCheckbox>
                                </td>
                        
                                <td>
                                    {{sche.scheduleText}}
                                </td>
                        
                                <td>
                                    {{sche.teams}}
                                </td>
                            </tr>
                        </ng-template>
                
                <!-- No data -->
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td class="text-center" style="font-weight: bold;" colspan="3">Not yet scheduled!</td>
                            </tr>
                        </ng-template>
                    
                    </p-table>



                    <!-- <div class="flex justify-content-between mb-2">
                        <button pButton (click)="removeSchedules($event)" [disabled]="selectedScheArr.length === 0"
                            icon="pi pi-fw pi-trash" pTooltip="Remove Schedules" tooltipPosition="left" class="p-button-sm p-button-danger bg-blue">
                        </button>
                        <button pButton (click)="createSchedule.toggle($event);showCreateScheduleDialog()" label="Create Schedule" class="mr-2 p-button-sm bg-blue">
                        </button>
                    </div>
                    <div *ngIf="schedulesArr?.length > 0">
                        <div *ngFor="let sche of schedulesArr" class="col-12 schedule-list">
                            <div class="flex justify-content-between align-items-center">
                                <div class="flex gap-3">
                                    <p-checkbox [value]="sche" [(ngModel)]="selectedScheArr" [inputId]="sche.scheduleText" (click)="$event.stopPropagation()"></p-checkbox>
                                    <div class="main-title">{{ sche.scheduleText }}</div>
                                </div>
                                <div class="description">{{ sche.teams }}</div>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="!schedulesArr?.length > 0" class="col text-center">
                        <span>Not Scheduled yet!!!</span>
                    </div> -->
                </div>

            </ng-template>
        </p-accordionTab>
    </p-accordion>
    
</div>

<div *ngIf="!(formsets?.length > 0)" class="no-data">
    <span>No Formset Found!</span>
</div>


<!-- Assign Formsets -->
<p-overlayPanel #assignFormsets [style]="{'max-width': '400px'}">
    <div class="grid set">
        <div class="col-12 my-3">
          <span class="p-float-label w-full">
            <p-multiSelect [style]="{'min-width': '200px','max-width': '300px'}" id="Formsets" [options]="filteredFormsets" [(ngModel)]="SelectedFormsets" optionLabel="name" display="chip"></p-multiSelect>
            <label htmlFor="Formsets">Formsets</label>
          </span>
          <div class="col-12 text-right pr-0">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetAssignFormset(assignFormsets)"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Assign" (click)="assignFormsetsToForm(assignFormsets)" [disabled]="SelectedFormsets.length === 0"></button>
          </div>
        </div>
    </div>
</p-overlayPanel>


<!-- Create Formset -->
<p-overlayPanel #newFormset styleClass="p-0" [style]="{'max-width': '400px'}">
    <form [formGroup]="createFormset" novalidate>
        <div class="grid">
            <div class="col-12 mt-3">
                <!-- <span class="p-float-label">
                    <input id="title" pInputText formControlName="title" type="text" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="title" class="_required">Title</label>
                </span> -->
                <div class="p-inputgroup">
                    <button class="p-button-sm bg-blue" pButton type="button" (click)="iconPicker.show($event)" pTooltip="Choose Icon">
                      <span class="material-icons">{{selectedIcon}}</span>
                    </button>
                    <span class="p-float-label">
                        <input id="title" pInputText formControlName="title" type="text" class="w-full p-inputtext-sm" required="true">
                        <label htmlFor="title" class="_required">Title</label>
                    </span>
                </div>
            </div>
            <div class="col-12">
                <span class="p-float-label">
                    <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="description" class="w-full"></textarea>
                    <label htmlFor="description">Description</label>
                </span>
                <span *ngIf="createFormset.get('description').hasError('maxlength')" class="error" style="margin-top: 0px !important;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 100 characters.</small></span>
            </div>
            <!-- <div class="col-12">
                <span class="p-input-icon-left w-full">
                    <span class="material-icons custom-icon">{{selectedIcon}}</span>
                    <input pInputText (click)="iconPicker.show($event)" class="w-full p-inputtext-sm" placeholder="Icon" readonly="true" required="true">
                </span>
            </div> -->
            <div class="col-12 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetFormset(newFormset)"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Create" (click)="CreateFormset(newFormset)" [disabled]="!createFormset.valid"></button>
            </div>
        </div>
    </form>
</p-overlayPanel>
<p-overlayPanel #iconPicker [style]="{'width': '300px','height': '380px'}">
    <div class="col w-full">
      <input pInputText #search type="search" class="w-full p-inputtext-sm" placeholder="Search..." (click)="$event.stopPropagation()">
    </div>
    <div class="scrolling">
      <button pButton *ngFor="let icon of iconsArr | filter : search.value" (click)="selectIcon(icon);$event.stopPropagation()" class="p-button-rounded p-button-text">
        <span class="material-icons">{{icon}}</span>
      </button>
    </div>
</p-overlayPanel>


<!-- Assign Forms -->
<p-overlayPanel #assignForms [style]="{'max-width': '400px'}">
    <div class="grid set">
        <div class="col-12 mt-3">
          <span class="p-float-label w-full">
            <p-multiSelect [style]="{'min-width': '200px','max-width': '300px'}" id="forms" [options]="filteredForms" [(ngModel)]="selectedForms" optionLabel="name" optionValue="formName" display="chip"></p-multiSelect>
            <label htmlFor="forms">Assign Forms</label>
          </span>
          <div class="col-12 text-right pr-0">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetAssignForm(assignForms)"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Assign" (click)="assignFormsToFormset(assignForms)" [disabled]="selectedForms.length === 0"></button>
          </div>
        </div>
    </div>
</p-overlayPanel>


<!-- Assign Teams -->
<p-overlayPanel #assignTeamPanel [style]="{'max-width': '400px'}">
    <div class="grid set">
        <div class="col-12 mt-3">
          <span class="p-float-label w-full">
            <p-multiSelect [style]="{'min-width': '200px','max-width': '300px'}" id="teams" [options]="filteredTeams" [(ngModel)]="selectedTeams" optionLabel="teamId" optionValue="teamId" display="chip"></p-multiSelect>
            <label htmlFor="teams">Teams</label>
          </span>
          <div class="col-12 mt-3 text-right pr-0">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetAssignTeam(assignTeamPanel)"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Assign" (click)="assignTeamsToForm(assignTeamPanel)" [disabled]="selectedTeams.length === 0"></button>
          </div>
        </div>
      </div>
</p-overlayPanel>

<!-- Create Schedule -->
<p-overlayPanel #createSchedule [style]="{'max-width': '800px'}">
    
    <form [formGroup]="scheduleform" novalidate>
        <div class="formgrid grid sch">
          <!-- <div class="col-12 p-0"> -->
            <div class="col-4 my-3">
                <span class="p-float-label">
                    <p-dropdown id="team" formControlName="teamCtrl" [autoDisplayFirst]="false"
                         [options]="allTeams" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="team" class="_required">Team</label>
                </span>
            </div>
            <div class="col-4 my-3">
                <span class="p-float-label">
                    <p-dropdown id="priority" formControlName="taskpriority" [autoDisplayFirst]="false"
                         [options]="taskpriorities" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="priority">Priority</label>
                </span>
            </div>
            <div class="col-4 my-3">
              <span class="p-float-label">
                <input id="due" pInputText formControlName="dueindays" type="number" (change)="disableReminder()" class="w-full p-inputtext-sm">
                <label htmlFor="due">Due days</label>
              </span>
            </div>
<!-- Reminder -->
            <div class="col-4 my-3">
                <span class="p-float-label">
                    <p-dropdown id="Reminder" formControlName="selectedReminder" [autoDisplayFirst]="false"
                         [options]="Reminders" styleClass="w-full p-inputtext-sm">
                    </p-dropdown>
                    <label htmlFor="Reminder">Reminder</label>
                </span>
            </div>
<!-- End date -->
            <div class="col-4 my-3">
                <span class="p-float-label">
                    <p-calendar id="date" formControlName="pickervalue" [minDate]="minDate" styleClass="w-full p-inputtext-sm" (onSelect)="setValidatorsfordate($event)"></p-calendar>
                    <label for="date" class="mb-0 ml-2">End Date</label>
                </span>
            </div>
<!-- Type -->
            <div class="col-12 set mt-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        <span class="p-float-label">
                            <p-dropdown id="schedule" formControlName="scheduletype" [autoDisplayFirst]="false" (onChange)="pickedScheduleType($event)"
                                [options]="CreateSchedules" optionLabel="key" optionValue="value" styleClass="w-full p-inputtext-sm">
                            </p-dropdown>
                            <label htmlFor="schedule">Type</label>
                        </span>
                    </ng-template>
                    <ng-template pTemplate="content">
<!-- Minute -->         <div class="grid">
                        <div class="col-6 my-3" *ngIf="selectedschedule === 'Minute'">
                            <span class="p-float-label">
                                <p-dropdown id="Minute" formControlName="selectedMinute" [autoDisplayFirst]="false"
                                    [options]="MinuteArr" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Minute">Minute</label>
                            </span>
                        </div>
<!-- Day & Hour -->
                        <div class="col-12 my-3" *ngIf="selectedschedule === 'Daily' || selectedschedule === 'Hourly'">
                            <!-- <div *ngFor="let dailyoption of dailyscheduleoptions">
                                <p-radioButton [inputId]="dailyoption.name" [value]="dailyoption.value" formControlName="selectedtype"></p-radioButton>
                                <label [for]="dailyoption.name" class="ml-2">{{ dailyoption.name }}</label>
                            </div> -->
                            <span class="p-float-label">
                                <p-dropdown id="Occurred" formControlName="selectedtype" [autoDisplayFirst]="false" appendTo="body"
                                    [options]="scheduleOptions" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm">
                                </p-dropdown>
                                <label htmlFor="Occurred">Occurred On</label>
                            </span>
                        </div>
<!-- Week -->
                        <div [ngClass]="selectedschedule === 'Weekly' ? 'col-12 my-3' : 'col-6 my-3'" *ngIf="selectedschedule === 'Weekly' || selectedschedule === 'Minute'">
                            <!-- <div *ngFor="let availableDay of days" formGroupName="days">
                                <p-checkbox [inputId]="availableDay" [value]="availableDay" formControlName="{{availableDay}}" [binary]="true"></p-checkbox>
                                <label [for]="availableDay" class="ml-2 mb-0">{{ availableDay }}</label>
                            </div> -->
                            
                            <span class="p-float-label">
                                <p-multiSelect id="Weekdays" [options]="days" formControlName="weekDays" display="chip" styleClass="w-full p-inputtext-sm" appendTo="body"></p-multiSelect>
                                <label htmlFor="Weekdays">Weekdays</label>
                            </span>
                        </div>
<!-- Month -->
                        <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly'">
                            <span class="p-float-label">
                                <p-dropdown id="Choose" formControlName="chooseMonth" [autoDisplayFirst]="false" (onChange)="chooseMonthFun(chooseMonth)"
                                    [options]="chooseMonthArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Choose">Choose day of the month</label>
                            </span>
                        </div>
                        <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'manual'">
                            <span class="p-float-label">
                                <p-inputNumber id="day" formControlName="day" class="w-full" styleClass="w-full p-inputtext-sm" (onFocus)="setValidatorsMonthly('repeateverymonth')" [useGrouping]="false"></p-inputNumber>
                                <label htmlFor="day">Repeat on which day of every month?</label>
                            </span>
                        </div>
                        <!-- <div *ngIf="selectedschedule === 'Monthly'" class="col-12">
                            <p-divider align="center" styleClass="my-2">
                                <span>or</span>
                            </p-divider>
                        </div> -->
                        <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                            <span class="p-float-label">
                                <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                    [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="on">Repeat On</label>
                            </span>
                        </div>
                        <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                            <span class="p-float-label">
                                <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                    [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Day">Day</label>
                            </span>
                        </div>
<!-- Year -->
                        <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly'">
                            <span class="p-float-label">
                                <p-dropdown id="Choose" formControlName="chooseYear" [autoDisplayFirst]="false"
                                    [options]="chooseYearArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Choose">Choose day of the year</label>
                            </span>
                        </div>
                        <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'manual'">
                            <span class="p-float-label">
                                <p-inputNumber id="day" formControlName="day" (onFocus)="setValidatorsYearly('repeateveryyear')" class="w-full" styleClass="w-full p-inputtext-sm" [useGrouping]="false"></p-inputNumber>
                                <label htmlFor="day">Repeat on which day of every year?</label>
                            </span>
                        </div>
                        <!-- <div *ngIf="selectedschedule === 'Yearly'" class="col-12">
                            <p-divider align="center" styleClass="my-2">
                                <span>or</span>
                            </p-divider>
                        </div> -->
                        <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                            <span class="p-float-label">
                                <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                    [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="on">Repeat</label>
                            </span>
                        </div>
                        <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                            <span class="p-float-label">
                                <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                    [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Day">Day</label>
                            </span>
                        </div>
                        <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                            <span class="p-float-label">
                                <p-dropdown id="Month" formControlName="selectedmonth" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                    [options]="months" styleClass="w-full p-inputtext-sm" appendTo="body">
                                </p-dropdown>
                                <label htmlFor="Month">Month</label>
                            </span>
                        </div>
                    </div>
                    </ng-template>
                </p-fieldset>
            </div>

            <!-- <div class="col-6 my-auto">
                <p-checkbox formControlName="noenddate" id="enableReview" binary="true" (onChange)="setValidatorsfordate(scheduleform.get('noenddate').value)"></p-checkbox>
                <label for="enableReview" class="mb-0 ml-2">No end date</label>
            </div> -->
            
           
            <div class="col-4 text-right mt-3 ml-auto">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetScheduleForm(createSchedule)"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Create" [disabled]="!scheduleform.valid" (click)="createScheduleForm(createSchedule)"></button>
            </div>
          <!-- </div> -->
        </div>
    </form>
</p-overlayPanel>