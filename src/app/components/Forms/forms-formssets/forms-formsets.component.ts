import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Table } from 'primeng/table';
import { FormsService } from 'src/app/services/forms.service';
import { UsersTeamsService } from 'src/app/services/users-teams.service';
import { UtilsService } from 'src/app/services/utils.service';

interface FormsetForms {
  avatar: string;
  formName: string;
  category: string;
  description: string;
  mandatory: boolean;
}

@Component({
  selector: 'app-forms-formsets',
  templateUrl: './forms-formsets.component.html',
  styleUrls: ['./forms-formsets.component.scss']
})
export class FormsFormsetsComponent implements OnInit {
  public form: any;
  public formid: string;
  public formsets: any[] = [];
  public errmsg: string;
  filteredFormsets: any[] = [];
  SelectedFormsets: string[] = [];
  SelectedFormset: any;
  createFormset: FormGroup;
  selectedIcon: string = 'filter_9_plus';
  iconsArr: any;
  formsetForms: FormsetForms[] = [];
  expand: boolean = false;
  showForms: boolean = true;
  showAssignments: boolean = false;
  showSchedules: boolean = false;
  selectedOption: any = 'showForms';
  filteredForms: any = [];
  selectedForms: any[] = [];
  deleteFormsArr: any = [];

  assignmentsArr: any[] = [];
  selectedTeamsArr: any = [];
  filteredTeams: any = [];
  selectedTeams: any[] = [];
  assignTeamToForm: FormGroup;
  sharedUsers = new FormControl({ value: '', disabled: true });
  isReviewer = new FormControl({ value: false, disabled: true });
  selectedReviewer = new FormControl({ value: '', disabled: true });
  selectedReviewerType: string = 'team';
  assignmentFormData: any
  reviewersarr: string[] = [];
  reviewuser: string;
  reviewteam: string;
  reviewArr =  [{ label: 'User', key: 'user' },{ label: 'Team', key: 'team' }];
  sharedArr =  [{ label: 'Only one user can fill the form till released', key: 'singleuser' },{ label: 'All users can fill concurrently', key: 'allusers' }];
  isRowExpand: boolean = false;
  isDeveloperAndProd: boolean = false;

  schedulesArr: any[] = [];
  selectedScheArr: any = [];
  scheduleform: FormGroup;
  allTeams: string[] = [];
  workflows: any = [];
  selectedschedule: string;
  minDate = new Date();
  scheduletypes = ['Create', 'Others'];
  taskpriorities = ['High', 'Medium', 'Low'];
  Reminders = ['None', 'Daily', 'Before Expiry'];
  CreateSchedules: any[] = [
    {key:'Minute', value: 'Minute'},
    {key:'Hour', value: 'Hourly'},
    {key:'Day', value: 'Daily'},
    {key:'Week', value: 'Weekly'},
    {key:'Month', value: 'Monthly'},
    {key:'Year', value: 'Yearly'}
  ];
  MinuteArr: string[] = ['10', '15', '20', '30', '40', '45', '50'];
  scheduleOptions = [
    { name: 'Everyday', value: 'everyday'},
    { name: 'Weekdays', value: 'weekdays'},
    { name: 'Weekends', value: 'weekends' }
  ];
  days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  dayoccurences = ['First', 'Second', 'Third', 'Fourth', 'Last' ];
  months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  chooseMonthArr = [{ name: 'Repeat on which day of every month?', value: 'manual'}, { name: 'Repeat on specific day of the month', value: 'automatic'}];
  chooseYearArr = [{ name: 'Repeat on which day of every year?', value: 'manual'},{ name:'Repeat on specific day of the year', value: 'automatic'}];
  enddate = '';
  selectedMonthlySchedule: string;
  selectedYearlySchedule: string;
  weeklyValidationStatus = true;

  constructor(
    private formsService: FormsService,
    private usersTeamsService: UsersTeamsService,
    public route: ActivatedRoute,
    public data: DynamicDialogConfig,
    public ref: DynamicDialogRef,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private router: Router,
    private fb: FormBuilder,
    private utilservice: UtilsService,
  ) { }

  ngOnInit() {
    // this.formid = this.route.parent.snapshot.paramMap.get('formid');
    this.formid = this.data.data.formId;
    this.getAllAsignedFormsets(this.formid);
    this.getAllFormsets();
    this.createFormset = this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(100)]],
      icon: [{ value: '', disabled: true }, Validators.required]
    });
    this.formsService.getAllIcons().subscribe(data => {this.iconsArr = data});
    this.assignTeamToForm = this.fb.group({
      team: [{ value: null}, Validators.required],
      enableShared: [false, Validators.required],
      enableReview: [false, Validators.required],
      enableShiftHandoff: [false, Validators.required]
    });
    this.scheduleform = this.fb.group({
      scheduletype: ['Minute', Validators.required],
      noenddate: [false],
      pickervalue: [null],
      selectedReminder: [{value:'None', disabled: true}],
      teamCtrl: [null, Validators.required],
      taskpriority: [null],
      dueindays: [null],
      weekDays: [null],
      selectedtype: [null, Validators.required],
      day: [null, Validators.required],
      dayoccurence: [null, Validators.required],
      selectedday: [null, Validators.required],
      selectedmonth: [null, Validators.required],
      selectedMinute: [null, Validators.required],
      chooseMonth: [null],
      chooseYear: [null]
    });
    this.pickedScheduleType({value: "Minute"});
    this.checkDeveloperAccess();
  }

  getAllAsignedFormsets(formid: string) {
    this.formsService.getform(formid, 'formsets').subscribe((form: any) => {
      this.form = form;
      this.formsets = form.formsets;
    });
  }

  public deleteFormset(formsetId, event: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove formset?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formsService.removeFormfromFormset(this.formid, formsetId)
          .subscribe((res: any) => {
          const response = res;
          if (response.error === '') {
            if (response.status === 'Success') {
              this.getAllAsignedFormsets(this.formid);
              this.getAllFormsets();
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully removed!' });
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
      },
      reject: () => {}
    });
    // event.stopPropagation();
    // const dialogData = new ConfirmDialogModel('Confirm Action', 'Are you sure you want to remove this formset?');
    // const dialogRef = this.matDialog.open(ConfirmDialogComponent, {
    //   maxWidth: '400px',
    //   data: dialogData,
    //   panelClass: 'custom-dialog'
    // });
    // dialogRef.afterClosed().subscribe(confirmationres => {
    //   // console.log('dialogResult', dialogResult);
    //   if (confirmationres) {
    //     this.formsService.removeFormfromFormset(this.formid, formsetId)
    //     .subscribe((res: any) => {
    //       const response = res;
    //       if (response.error === '') {
    //         if (response.status === 'Success') {
    //           this.getformformsets(this.formid);
    //       }
    //     } else {
    //       this.errmsg = response.error;
    //       // console.log(this.errmsg);
    //     }
    //   });
    //   }
    // });
  }

  getAllFormsets() {
    this.formsService.getformsets('', 0, 100, this.formid, null, '')
      .subscribe((response: any) => {
        this.filteredFormsets = [];
        if (response.status.toLowerCase() === 'success') {
          if (response.formSets?.length) {
            response.formSets.forEach(sets => {
              this.filteredFormsets.push({'name': sets.name});
            });
          } else {
            this.filteredFormsets = [];
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  assignFormsetsToForm(element: any) {
    const formset = [];
    this.SelectedFormsets.forEach((sets: any) => {
      formset.push(sets.name);
    });
    this.formsService
      .assignFormToformset(this.form.formName, formset)
      .subscribe(assignFormToformsetAPIres => {
        const response = assignFormToformsetAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.getAllAsignedFormsets(this.formid);
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully assigned!' });
            element.hide();
            this.getAllFormsets();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetAssignFormset(element?: any) {
    if (element) {
      element.hide();
    }
    this.SelectedFormsets = [];
  }

  resetFormset(element: any) {
    this.createFormset.reset();
    this.selectedIcon = 'filter_9_plus';
    element.hide();
  }

  goToFormsets(element: any) {
    this.ref.close();
    this.router.navigate(['/formsets',element.id,{name:element.name}]);
  }

  CreateFormset(element: any) {
    // console.log(this.createformset.value);
    this.formsService
      .createformset(this.createFormset.value, this.selectedIcon)
      .subscribe(createformsetAPIres => {
        const response = createformsetAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.getAllFormsets();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Formset created!' });
            element.hide();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }
  selectIcon(icon: string) {
    this.selectedIcon = icon;
    this.createFormset.get('icon').setValue(this.selectedIcon);
  }

  public goToPreview(form: any) {
    let data = {
      submission:{},
      formId: form.formId,
      type: 'form',
      isPreview: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    // this.router.navigate([`/test-form/${routerData}`]);
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  selectedFormsetData(formset: any) {
    this.SelectedFormset = formset;
    this.getFormsetData(this.SelectedFormset.id);
  }

  getFormsetData(formsetid: string) {
    
    this.formsService.getformset(formsetid).subscribe((res: any) => {
      const response = res;
      if (response.status.toLowerCase() === 'success') {
        this.formsetForms = response?.form.formHeaders ? response?.form.formHeaders : [];
        this.assignmentsArr = response?.assignments ? response?.assignments : [];
        this.schedulesArr = response?.schedules ? response?.schedules : [];
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        this.formsetForms = [];
        this.assignmentsArr = [];
        this.schedulesArr = [];
      }
    });
  }

  IsFormMandatory(form: any) {
    this.formsService.updateFormofFormset(form.formId, this.SelectedFormset.id, form.mandatory).subscribe((res: any) => {
      const response = res;
      if (response.error === '') {
        if (response.status === 'Success') {
          this.getFormsetData(this.SelectedFormset.id);
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  removeMultipleForms(event: any) {
    let completedCount = 0;
    let totalCount = this.deleteFormsArr.length;
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove forms?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        if (this.deleteFormsArr.length > 0) {
          for (let i=0; this.deleteFormsArr.length > i; i++) {
            this.formsService.removeFormfromFormset(this.deleteFormsArr[i].formId, this.SelectedFormset.id).subscribe((response: any) => {
              if (response.status.toLowerCase() === "success") {
                completedCount += 1;
                if (completedCount === totalCount) {
                  this.deleteFormsArr = [];
                  this.getFormsetData(this.SelectedFormset.id)
                  completedCount = 0;
                }
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
            });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please slect forms.' });
        }
      },
      reject: () => {}
    });
  }

  getAllForms() {
    this.selectedForms = [];
    const excludeids = [];
    this.formsetForms.forEach(form => {
      excludeids.push(form.formName);
    });
    const data = {
      formsetId: this.SelectedFormset.id,
      formsetname: this.SelectedFormset.name,
      excludeForm: excludeids.toString(),
      teamId: null
    }
    this.formsService.getforms('', data, 0, 1000, []).subscribe((formdata: any) => {
      // UMP responce is type of string
      const formdataobj = formdata;
      this.filteredForms = [];
      formdataobj.formHeaders.forEach((formnameval: any) => {
        this.filteredForms.push({'name': formnameval.formTitle, 'formName': formnameval.formName});
      });
    });
  }

  assignFormsToFormset(element: any) {
    this.formsService
      .assignFormToformset(this.selectedForms, this.SelectedFormset.name)
      .subscribe(assignFormToformsetAPIres => {
        const response = assignFormToformsetAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            // window.location.reload();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully assigned!' });
            element.hide();
            this.getFormsetData(this.SelectedFormset.id);
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetAssignForm(element: any) {
    element.hide();
    this.selectedForms = [];
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.getFormsetData(this.SelectedFormset.id);
  }


  // Assignment Start

  checkDeveloperAccess() {    // developer can not access Assignment in prodction landscape
    const landsacpe = localStorage.getItem('landscape');
    const role = localStorage.getItem('Role');
    if (role.toLowerCase() === 'developer' && landsacpe.toLowerCase() === 'production') {
      this.isDeveloperAndProd = true;
    } else {
      this.isDeveloperAndProd = false
    }
  }

  getAllTeams() {
    this.filteredTeams = [];
    this.selectedTeams = [];
    this.selectedTeamsArr = [];
    const data = {
      formId: null,
      type: 'formset',
      formsetId: this.SelectedFormset.id
    }
    this.usersTeamsService.getteams('', '', 'asc', 0, 100, data, null)
      .subscribe((teamdata: any) => {
        const response = teamdata;
        this.filteredTeams = [];
        if (response.status === 'Success') {
          if (response.teams.length) {
            response.teams.forEach(teamnameval => {
              this.filteredTeams.push({'teamId': teamnameval.name});
            });
          } else {
            this.filteredTeams = [];
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  assignTeamsToForm(element: any) {
    const data = {
      formId: null,
      type: 'formset',
      formsetId: this.SelectedFormset.id
    }
    const form = {
      enableReview: false,
      enableShared: false,
      enableShiftHandoff: false
    }
    this.formsService
      .assignTeamsToform(data, this.selectedTeams, form, '', null, null)
      .subscribe(res => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully assigned!' });
            this.getFormsetData(this.SelectedFormset.id);
            element.hide();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetAssignTeam(element: any) {
    element.hide();
    this.selectedTeams = [];
  }

  removeTeamsAssignments(event: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove assignments?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        const assignmentids = [];
        this.selectedTeamsArr.forEach(assignments => {
          assignmentids.push(assignments.id);
        });
        this.formsService.deteteformsetassignments(this.SelectedFormset.id, assignmentids).subscribe(res => {
          const apires = res;
          if (apires.error === '') {
            if (apires.status === 'Success') {
              this.getFormsetData(this.SelectedFormset.id);
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully removed!' });
              this.selectedTeamsArr = [];
            }
          } else {
            this.errmsg = apires.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
          }
        });
      },
      reject: () => {}
    });
  }

  showUpdateAssignmentDialog(id, team) {
    this.assignTeamToForm.reset();
    this.sharedUsers.reset();
    this.isReviewer.reset();
    this.selectedReviewer.reset();
    this.formsService.getassignmentdetails(id).subscribe(getassignmentdetailsAPIres => {
      this.assignmentFormData = getassignmentdetailsAPIres;
      // this.showAssignmentDialog = true;
      this.assignTeamToForm.patchValue({
        team: team,
        enableShared: this.assignmentFormData.shared,
        enableReview: this.assignmentFormData.reviewReqd,
        enableShiftHandoff: this.assignmentFormData.handoff,
      });
      if (this.enableShared) {
        this.sharedUsers.enable();
        this.sharedUsers.patchValue(this.assignmentFormData.sharedusers);
      } else {
        this.sharedUsers.disable();
      }
      if (this.enableReview) {
        this.isReviewer.enable();
        this.selectedReviewer.enable();
      } else {
        this.isReviewer.disable();
        this.selectedReviewer.disable();
      }
      if (this.assignmentFormData.reviewUser) {
        this.isReviewer.enable();
        this.isReviewer.setValue(true);
        this.selectedReviewerType = 'user';
        this.pickedReviewer();
        this.selectedReviewer.setValue(this.assignmentFormData.reviewUser);
      } else {
        this.isReviewer.enable();
        this.isReviewer.setValue(false);
        this.selectedReviewerType = 'team';
        this.pickedReviewer();
        this.selectedReviewer.setValue(this.assignmentFormData.reviewTeam);
      }
    });
  }

  onUserChange(e: any) {
    if (e.checked) {
      this.sharedUsers.enable();
    } else {
      this.sharedUsers.disable();
    }
  }

  onReviewedChange(e: any) {
    if (e.checked) {
      this.isReviewer.enable();
      this.selectedReviewer.enable();
    } else {
      this.isReviewer.disable();
      this.selectedReviewer.disable();
    }
  }

  onReviewerChange(e: any) {
    if (e.checked) {
      this.selectedReviewerType = 'user';
      this.pickedReviewer();
    } else {
      this.selectedReviewerType = 'team';
      this.pickedReviewer();
    }
  }

  get enableShared() {
    return this.assignTeamToForm.get('enableShared').value;
  }
  get enableReview() {
    return this.assignTeamToForm.get('enableReview').value;
  }
  get team() {
    return this.assignTeamToForm.get('team');
  }

  pickedReviewer() {
    let id;
    if (this.assignmentFormData.formId) {
      id = this.assignmentFormData.formId;
    } else {
      id = this.assignmentFormData.formsetId;
    }
    this.formsService.getreviewers(id, 'formset', false, null, this.selectedReviewerType)
      .subscribe(res => {
        if (this.selectedReviewerType === 'user') {
          this.reviewersarr = [];
          res.reviewUser.forEach(element => {
            this.reviewersarr.push(element.email);
          });
        } else {
          this.reviewersarr = [];
          res.reviewTeam.forEach(element => {
            this.reviewersarr.push(element.teamId);
          });
        }
      });
  }

  updateAssignments() {
    if (this.enableReview) {
      if (this.selectedReviewerType === 'user') {
        this.reviewuser = this.selectedReviewer.value;
        this.reviewteam = null;
      } else {
        this.reviewteam = this.selectedReviewer.value;
        this.reviewuser = null;
      }
    }
    this.formsService
      // tslint:disable-next-line: max-line-length
      .updateassignmentdetails(this.assignmentFormData.assignmentId, this.assignTeamToForm.value, this.sharedUsers.value, this.reviewuser, this.reviewteam)
      .subscribe(updateassignmentsAPIres => {
        const response = updateassignmentsAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.getFormsetData(this.SelectedFormset.id);
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
            this.resetAssignmentform(this.assignmentFormData.assignmentId);
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
        }
      });
  }

  onRowCollapse() {
    this.isRowExpand = false;
  }

  onRowExpand() {
    this.isRowExpand = true;
  }

  resetAssignmentform(id: any) {
    if(this.isRowExpand) {
      document.getElementById('rowExpansion'+id).click();
    }
    this.assignTeamToForm.reset();
  }

  // Schedules Start

  showCreateScheduleDialog() {
    this.formsService.getteams_for_forms(null, this.SelectedFormset.id)
    .subscribe((res: any) => {
        this.allTeams = res.teams;
    });
    this.scheduleform.controls['scheduletype'].patchValue("Minute");
    this.scheduleform.controls['scheduletype'].updateValueAndValidity();
    this.pickedScheduleType({value: "Minute"});
  }

  disableReminder() {
    if (this.scheduleform.get('dueindays').value >= 1) {
      this.scheduleform.get('selectedReminder').enable();
      // this.scheduleform.get('selectedReminder').updateValueAndValidity();
    } else {
      this.scheduleform.get('selectedReminder').disable();
    }
  }

  setValidatorsfordate(endDate: any) {
    if (endDate) {
      this.scheduleform.get('noenddate').patchValue(true);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
    if (!endDate) {
      this.scheduleform.get('noenddate').patchValue(false);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
  }

  pickedScheduleType(event: any) {
    const schedule = event?.value;
    this.selectedschedule = schedule;
    
    if (schedule === 'Minute') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].setValidators([Validators.required]);
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Daily') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
    
    if (schedule === 'Hourly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();

      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue('');
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Weekly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);

    } if (schedule === 'Monthly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['chooseMonth'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseMonth'].updateValueAndValidity();

      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();

    } if (schedule === 'Yearly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['chooseYear'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseYear'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
  }

  setValidatorsMonthly(monthlyscheduletype: string) {
    this.selectedMonthlySchedule = monthlyscheduletype;
    if (monthlyscheduletype === 'repeateverymonth') {
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(0), Validators.max(31)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (monthlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
  }

  setValidatorsYearly(yearlyscheduletype: string) {
    this.selectedYearlySchedule = yearlyscheduletype;
    if (yearlyscheduletype === 'repeateveryyear') {
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(366)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (yearlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
   // });
  }

  createScheduleForm(element: any) {
    const teams = [];
    const ids = { formid: null, formsetid: this.SelectedFormset.id}
    teams.push(this.scheduleform.value.teamCtrl);
    let data = {};
    if (this.selectedschedule === 'Minute') {
      data = { 
        'selectedMinute': this.scheduleform.value.selectedMinute,
        'days' : {
          'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
          'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
          'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
          'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
          'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
          'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
          'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
        }
      }
    }
    if (this.selectedschedule === 'Daily' || this.selectedschedule === 'Hourly') {
      data = {'selectedtype':this.scheduleform.value.selectedtype}
    }
    if (this.selectedschedule === 'Weekly') {
      data = { 'days' : {
        'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
        'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
        'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
        'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
        'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
        'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
        'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
      }}
    }
    if (this.selectedschedule === 'Monthly') {
      data = 
        {
          'selectedmonthlyschedule': this.selectedMonthlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
        }
    }
    if (this.selectedschedule === 'Yearly') {
      data = 
        {
          'selectedyearlyschedule': this.selectedYearlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
          'selectedmonth': this.scheduleform.value.selectedmonth,
        }
      }
    var form = {
      'scheduletype': this.scheduleform.value.scheduletype,
      'noenddate': this.scheduleform.value.noenddate,
      'pickervalue': this.scheduleform.value.pickervalue,
      'selectedReminder': this.scheduleform.value.selectedReminder,
      [this.selectedschedule]: data,
    }
    if (this.scheduleform.get('noenddate').value === 'true') {
      this.enddate = null;
     } else {
       this.enddate = this.scheduleform.get('pickervalue').value;
     }
     if (this.scheduleform.get('scheduletype').value === 'Weekly' || this.scheduleform.get('scheduletype').value === 'Minute') {
      this.weeklyValidationStatus = this.utilservice.checkboxvalidity(data);
    }
    if (this.weeklyValidationStatus) {
      this.formsService
        .createschedule(ids, teams, this.selectedschedule, this.enddate, form, this.scheduleform.value.taskpriority, this.scheduleform.value.dueindays, 'Create', this.scheduleform.value.selectedWorkflow)
        .subscribe((response: any) => {
            if (response.error === '') {
              if (response.status === 'Success') {
                this.getFormsetData(this.SelectedFormset.id);
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!'});
                element.hide();
                this.scheduleform.reset();
                this.selectedschedule = '';
              }
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
      });
    } else {
       this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select days for schedule'});
    }
  }

  resetScheduleForm(element: any) {
    element.hide();
    this.scheduleform.reset();
    this.selectedschedule = '';
  }

  removeSchedules(event: any) {
    const scheduleids = [];
    this.selectedScheArr.forEach(schedule => {
      scheduleids.push(schedule.id);
    });
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove schedules?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formsService.deteteschedule(scheduleids).subscribe(res => {
          const apires = res;
          if (apires.error === '') {
            if (apires.status === 'Success') {
              this.getFormsetData(this.SelectedFormset.id);
              this.selectedScheArr = [];
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully deleted!'});
            }
          } else {
            this.errmsg = apires.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
          }
        });
      },
      reject: () => {}
    });
  }

}
