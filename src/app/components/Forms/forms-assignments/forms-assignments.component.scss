.team-list {
    border: none !important;
  }
  .team-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  .team-name {
    font-size: 14px;
    font-weight: 700;
  }
  .description {
    font-size: 14px;
    font-weight: 400;
  }
  ::ng-deep .doc p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 100px;
  }
  ::ng-deep .doc p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }
  
//   ::ng-deep .doc p-dataview .p-dataview-header {
//     margin-bottom: 12px !important;
//   }
  
  ::ng-deep .doc p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 10px 15px 10px 15px !important;
  }
  
  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }