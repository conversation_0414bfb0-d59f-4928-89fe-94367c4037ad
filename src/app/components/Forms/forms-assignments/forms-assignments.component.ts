import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { Table } from 'primeng/table';
import { FormsService } from 'src/app/services/forms.service';
import { UsersTeamsService } from 'src/app/services/users-teams.service';

@Component({
  selector: 'app-forms-assignments',
  templateUrl: './forms-assignments.component.html',
  styleUrls: ['./forms-assignments.component.scss']
})
export class FormsAssignmentsComponent implements OnInit {
  public formid: string;
  public dataSourceAssignments: any[] = [];
  public errmsg: string;
  selectedTeams: any[] = [];
  filteredTeams = [];
  assignTeam: any[] = [];
  
  showAssignmentDialog: boolean = false;
  sharedArr =  [{ label: 'Only one user can fill the form till released', key: 'singleuser' },{ label: 'All users can fill concurrently', key: 'allusers' }];
  reviewArr =  [{ label: 'User', key: 'user' },{ label: 'Team', key: 'team' }];
  reviewersarr: string[] = [];
  assignTeamToForm: FormGroup;
  sharedUsers = new FormControl({ value: '', disabled: true });
  isReviewer = new FormControl({ value: false, disabled: true });
  selectedReviewer = new FormControl({ value: '', disabled: true });
  assignmentFormData: any
  reviewuser: string;
  reviewteam: string;
  isRowExpand: boolean = false;
  selectedReviewerType: string = 'team';
  searchTerm: string;

  constructor(
    private formsService: FormsService,
    private usersTeamsService: UsersTeamsService,
    public route: ActivatedRoute,
    private messageService: MessageService,
    private fb: FormBuilder,
    public data: DynamicDialogConfig,
    private confirmationService: ConfirmationService,
  ) { }

  ngOnInit() {
    // this.formid = this.route.parent.snapshot.paramMap.get('formid');
    this.formid = this.data.data.formId;
    this.getFormAssignments(this.formid);
    this.getAllTeams();
    this.assignTeamToForm = this.fb.group({
      team: [{ value: null}, Validators.required],
      enableShared: [false, Validators.required],
      enableReview: [false, Validators.required],
      enableShiftHandoff: [false, Validators.required]
    });
    this.isReviewer.disable();
  }

  public getFormAssignments(formid: string) {
    this.formsService.getform(formid, 'assignments').subscribe((form: any) => {
      this.dataSourceAssignments = form.assignments ? form.assignments : [];
    });
  }
  // public assignteamstoform() {
  //   const dialogRef = this.matDialog.open(AssignTeamsToFormComponent, {
  //     minWidth: '40%',
  //     panelClass: 'custom-dialog',
  //     data: {
  //       formId: this.formid,
  //       type: 'form',
  //       formsetId: null
  //     }
  //   });
  //   dialogRef.afterClosed().subscribe(res => {
  //     if (res) {
  //       this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Form has been assigned, you can now configure additional workflow options by selecting the assignment.'});
  //       this.getFormAssignments(this.formid);
  //       this.selectionassignment.clear();
  //       this.deleteassignments = false;
  //     }
  //   });
  // }

  assignTeamsToForm(element: any) {
    const data = {
      formId: this.formid,
      type: 'form',
      formsetId: null
    }
    const form = {
      enableReview: false,
      enableShared: false,
      enableShiftHandoff: false
    }
    this.formsService
      .assignTeamsToform(data, this.assignTeam, form, '', null, null)
      .subscribe(assignTeamsToformAPIres => {
        const response = assignTeamsToformAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.getFormAssignments(this.formid);
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully assigned!' });
            element.hide();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  getAllTeams() {
    const data = {
      formId: this.formid,
      type: 'form',
      formsetId: null
    }
    this.usersTeamsService.getteams('', '', 'asc', 0, 0, data, null)
      .subscribe((teamdata: any) => {
        const response = teamdata;
        this.filteredTeams = [];
        if (response.status === 'Success') {
          if (response.teams.length) {
            response.teams.forEach(teamnameval => {
              this.filteredTeams.push({'teamName': teamnameval.name, 'teamId': teamnameval.name});
            });
          } else {
            this.filteredTeams = [];
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetTeamsForm(element?: any) {
    if (element) {
      element.hide();
    }
    this.assignTeam = [];
  }

  public showUpdateAssignmentDialog(id, team) {
    this.assignTeamToForm.reset();
    this.sharedUsers.reset();
    this.isReviewer.reset();
    this.selectedReviewer.reset();

    this.formsService.getassignmentdetails(id).subscribe(getassignmentdetailsAPIres => {
        this.assignmentFormData = getassignmentdetailsAPIres;
        // this.showAssignmentDialog = true;
        this.assignTeamToForm.patchValue({
          team: team,
          enableShared: this.assignmentFormData.shared,
          enableReview: this.assignmentFormData.reviewReqd,
          enableShiftHandoff: this.assignmentFormData.handoff,
        });
        if (this.enableShared) {
          this.sharedUsers.enable();
          this.sharedUsers.patchValue(this.assignmentFormData.sharedusers);
        } else {
          this.sharedUsers.disable();
        }
        if (this.enableReview) {
          this.isReviewer.enable();
          this.selectedReviewer.enable();
        } else {
          this.isReviewer.disable();
          this.selectedReviewer.disable();
        }
        if (this.assignmentFormData.reviewUser) {
          this.isReviewer.enable();
          this.isReviewer.setValue(true);
          this.selectedReviewerType = 'user';
          this.pickedReviewer();
          this.selectedReviewer.setValue(this.assignmentFormData.reviewUser);
        } else {
          this.isReviewer.enable();
          this.isReviewer.setValue(false);
          this.selectedReviewerType = 'team';
          this.pickedReviewer();
          this.selectedReviewer.setValue(this.assignmentFormData.reviewTeam);
        }
    });
  }

  onUserChange(e: any) {
    if (e.checked) {
      this.sharedUsers.enable();
    } else {
      this.sharedUsers.disable();
    }
  }

  onReviewedChange(e: any) {
    if (e.checked) {
      this.isReviewer.enable();
      this.selectedReviewer.enable();
    } else {
      this.isReviewer.disable();
      this.selectedReviewer.disable();
    }
  }

  onReviewerChange(e: any) {
    if (e.checked) {
      this.selectedReviewerType = 'user';
      this.pickedReviewer();
    } else {
      this.selectedReviewerType = 'team';
      this.pickedReviewer();
    }
  }

  public removeTeamsAssignments(event: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: `Remove assignments?`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        const assignmentids = [];
        this.selectedTeams.forEach(assignments => {
          assignmentids.push(assignments.id);
        });
        this.formsService.deteteformassignments(this.formid, assignmentids).subscribe(apires => {
          if (apires.error === '') {
            if (apires.status === 'Success') {
              this.getFormAssignments(this.formid);
              this.selectedTeams = [];
            }
          } else {
            this.errmsg = apires.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
          }
        });
      },
      reject: () => {
        this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Action aborted!' });
      }
    });
  }

  get enableShared() {
    return this.assignTeamToForm.get('enableShared').value;
  }
  get enableReview() {
    return this.assignTeamToForm.get('enableReview').value;
  }
  get team() {
    return this.assignTeamToForm.get('team');
  }

  public pickedReviewer() {
    let id;
    if (this.assignmentFormData.formId) {
      id = this.assignmentFormData.formId;
    } else {
      id = this.assignmentFormData.formsetId;
    }
    this.formsService.getreviewers(id, 'form', false, null, this.selectedReviewerType)
      .subscribe(res => {
        if (this.selectedReviewerType === 'user') {
          this.reviewersarr = [];
          res.reviewUser.forEach(element => {
            this.reviewersarr.push(element.email);
          });
        } else {
          this.reviewersarr = [];
          res.reviewTeam.forEach(element => {
            this.reviewersarr.push(element.teamId);
          });
        }
      });
  }

  updateAssignments() {
    if (this.enableReview) {
      if (this.selectedReviewerType === 'user') {
        this.reviewuser = this.selectedReviewer.value;
        this.reviewteam = null;
      } else {
        this.reviewteam = this.selectedReviewer.value;
        this.reviewuser = null;
      }
    }
    this.formsService
      // tslint:disable-next-line: max-line-length
      .updateassignmentdetails(this.assignmentFormData.assignmentId, this.assignTeamToForm.value, this.sharedUsers.value, this.reviewuser, this.reviewteam)
      .subscribe(updateassignmentsAPIres => {
        const response = updateassignmentsAPIres;
        if (response.status === 'Success') {
          this.getFormAssignments(this.formid);
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!'});
          this.resetAssignmentform(this.assignmentFormData.assignmentId);
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
        }
      });
  }

  resetAssignmentform(id: any) {
    if(this.isRowExpand) {
      document.getElementById('rowExpansion'+id).click();
    }
    this.assignTeamToForm.reset();
  }

  onRowCollapse() {
    this.isRowExpand = false;
  }

  onRowExpand() {
    this.isRowExpand = true;
  }

  clearAllFilter(table: Table) {
    this.searchTerm = null;
    table.clear();
    this.getFormAssignments(this.formid);
  }

}
