<a href="https://docs.unvired.com/builder/admin/forms/#forms-assignment" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="doc">
    <!-- <div class="col-12 mb-2 px-0">
        <div class="flex justify-content-between">
            <button pButton (click)="removeTeamsAssignments($event)" [disabled]="selectedTeams.length === 0"
                icon="pi pi-fw pi-trash" pTooltip="Remove Team Assignments" tooltipPosition="left" class="p-button-sm bg-blue">
            </button>
            <button pButton (click)="assignTeamPanel.toggle($event);resetForm()" label="Assign" class="mr-2 p-button-sm bg-blue">
            </button>
        </div>
    </div> -->


    <p-table #dt 
        [value]="dataSourceAssignments" dataKey="id"
        responsiveLayout="scroll" 
        [scrollable]="true" 
        selectionMode="multiple" 
        styleClass="p-datatable-sm p-datatable-gridlines"
        [tableStyle]="{ 'min-width': '50rem' }"
        [(selection)]="selectedTeams"
        rowExpandMode="single"
        (onRowCollapse)="onRowCollapse()" 
        (onRowExpand)="onRowExpand()" 
        [paginator]="true"
        [rows]="5"
        [rowsPerPageOptions]="[5, 10, 25]"
        paginatorDropdownAppendTo="body"
        [globalFilterFields]="['teamId', 'description', 'members', 'forms']">

        <ng-template pTemplate="caption">
            <div class="flex flex-row align-items-center justify-content-between">
                <div>
                      <span class="p-input-icon-left">
                          <i class="pi pi-search"></i>
                          <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchTerm"
                          (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                      </span>
                      <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                          (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                      </button>
                </div>
                <div>
                    <button pButton (click)="removeTeamsAssignments($event)" [disabled]="selectedTeams.length === 0"
                        icon="pi pi-fw pi-trash" pTooltip="Remove Team Assignments" tooltipPosition="left" class="p-button-sm p-button-danger mr-2">
                    </button>
                    <button pButton (click)="assignTeamPanel.toggle($event);resetTeamsForm()" label="Assign" class="p-button-sm bg-blue">
                    </button>
                </div>
            </div>
        </ng-template>

<!-- Header -->
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                <th style="min-width: 10rem">Team</th>
                <th style="min-width: 10rem">Description</th>
                <th>Members</th>
                <th>Forms</th>
            </tr>
        </ng-template>

<!-- Table data -->
        <ng-template pTemplate="body" let-item let-columns="columns" let-expanded="expanded">
            <tr (click)="dt.toggleRow(item, $event);showUpdateAssignmentDialog(item.assignmentId,item.teamId)" class="cursor-pointer">
                <td class="text-center" id="{{'rowExpansion'+ item.assignmentId}}">
                    <p-tableCheckbox [value]="item" (click)="$event.stopPropagation()"></p-tableCheckbox>
                </td>
                <td>
                    {{item.teamId}}
                </td>
        
                <td>
                    {{item.description}}
                </td>
        
                <td>
                    {{item.members}}
                </td>
        
                <td>
                    {{item.forms}}
                </td>
            </tr>
        </ng-template>
<!-- Row Expand -->
        <ng-template pTemplate="rowexpansion" let-element>
            <tr>
              <td colspan="12">
                <div class="card">
                    <form [formGroup]="assignTeamToForm" class="pt-3" novalidate>
                        <div class="col-12 p-0">
                            <div hidden>
                              <span class="p-float-label">
                                <input id="teamid" pInputText formControlName="team" class="w-full p-inputtext-sm" readonly="true">
                                <label htmlFor="teamid" class="_required">Teams</label>
                              </span>
                            </div>
                            <div class="flex w-full align-items-center">
                                <div class="col-4 p-0">
                                    <p-checkbox formControlName="enableShared" id="enableShared" binary="true" (onChange)="onUserChange($event)"></p-checkbox>
                                    <label for="enableShared" class="mb-0 ml-2">Sharing <i class="pi pi-question-circle" pTooltip="Everyone works on same data"></i></label>
                                </div>
                                <div class="col-8 pr-0">
                                    <span class="p-float-label">
                                        <p-dropdown id="Shared" [formControl]="sharedUsers" optionLabel="label" required="true" [autoDisplayFirst]="false"
                                            optionValue="key" [options]="sharedArr" class="p-inputtext-sm" styleClass="w-full">
                                        </p-dropdown>
                                        <label htmlFor="Shared" class="_required">Shared</label>
                                    </span>
                                </div>
                            </div>
                            <div class="flex w-full align-items-center">
                                <div class="col-2 pl-0">
                                    <p-checkbox formControlName="enableReview" id="enableReview" binary="true" (onChange)="onReviewedChange($event)"></p-checkbox>
                                    <label for="enableReview" class="mb-0 ml-2">Review <i class="pi pi-question-circle" pTooltip="Submissions needs to be reviewed"></i></label>
                                </div>
                                <div class="col-2 my-auto">
                                    <!-- <span class="p-float-label">
                                        <p-dropdown id="Reviewer" [formControl]="selectedReviewerType" optionLabel="label" required="true"
                                            optionValue="key" [options]="reviewArr" class="p-inputtext-sm" styleClass="w-full">
                                        </p-dropdown>
                                        <label htmlFor="Reviewer" class="_required">Reviewer Type</label>
                                    </span> -->
                                    <div class="flex flex-row align-items-center">
                                        <p-inputSwitch id="Reviewer" [formControl]="isReviewer" (onChange)="onReviewerChange($event)"></p-inputSwitch>
                                        <label htmlFor="Reviewer" class="ml-2 mb-1">{{ selectedReviewerType | titlecase }}</label>
                                    </div>
                                </div>
                                <div class="col-8 pr-0">
                                    <span class="p-float-label">
                                        <p-dropdown id="assign" appendTo="body" [formControl]="selectedReviewer" required="true" [autoDisplayFirst]="false"
                                             [options]="reviewersarr" class="p-inputtext-sm" styleClass="w-full">
                                        </p-dropdown>
                                        <label htmlFor="assign" class="_required">Assign Reviewer</label>
                                    </span>
                                </div>
                            </div>
                            <div class="flex flex-row align-items-center">
                                <div class="col-6 pl-0">
                                    <p-checkbox formControlName="enableShiftHandoff" [disabled]="!enableShared" id="enableShiftHandoff" binary="true"></p-checkbox>
                                    <label for="enableShiftHandoff" class="mb-0 ml-2">Shift Handoff <i class="pi pi-question-circle" pTooltip="Teams work in shifts on forms"></i></label>
                                </div>
                                <div class="col-6 pr-0 text-right">
                                    <button class="p-button-sm p-button-danger mr-2" style="outline: none !important;" pButton type="button" label="Cancel" (click)="resetAssignmentform(element.assignmentId)"></button>
                                    <button class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateAssignments()" [disabled]="!assignTeamToForm.valid"></button>
                                </div>
                            </div>

                        </div>
                    </form>
                </div>
                
              </td>
            </tr>
        </ng-template>

<!-- No data -->
        <ng-template pTemplate="emptymessage">
            <tr>
              <td class="text-center" style="font-weight: bold;" colspan="5">No Assignments Found.</td>
            </tr>
        </ng-template>
    
    </p-table>

    <!-- <p-accordion *ngIf="dataSourceAssignments">
        <p-accordionTab *ngFor="let element of dataSourceAssignments;let i=index" (click)="showUpdateAssignmentDialog(element.assignmentId,element.teamId)">
            <ng-template pTemplate="header">
                <div class="flex flex-row align-items-center gap-3 ml-2 w-full">
                    <p-checkbox [value]="element" [(ngModel)]="selectedTeams" [inputId]="element.teamId" (click)="$event.stopPropagation()"></p-checkbox>
                    <div class="flex flex-column align-items-start gap-1">
                        <div class="team-name">{{ element.teamId }}</div>
                        <div class="description">{{ element.description }}</div>
                    </div>
                    <div class="flex flex-row justify-content-end flex-1 gap-2">
                        <div class="flex flex-column align-items-start gap-1">
                            <div class="description"><b class="mr-2">{{ element.members }}</b>Members</div>
                            <div class="description"><b class="mr-2">{{ element.forms }}</b>Forms</div>
                        </div>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <form [formGroup]="assignTeamToForm" class="pt-3" novalidate (click)="$event.stopPropagation()">
                    <div class="col-12 p-0">
                        <div class="w-full my-3">
                          <span class="p-float-label">
                            <input id="teamid" pInputText formControlName="team" class="w-full p-inputtext-sm" readonly="true">
                            <label htmlFor="teamid" class="_required">Teams</label>
                          </span>
                        </div>
                        <div class="flex w-full mt-3 align-items-center">
                            <div class="col-6 p-0">
                                <p-checkbox formControlName="enableShared" id="enableShared" binary="true"></p-checkbox>
                                <label for="enableShared" class="mb-0 ml-2">Enable Shared mode (Everyone works on same data)</label>
                            </div>
                            <div class="col-6 pr-0" *ngIf="enableShared">
                                <span class="p-float-label">
                                    <p-dropdown id="Shared" [formControl]="sharedUsers" optionLabel="label" required="true" [autoDisplayFirst]="false"
                                        optionValue="key" [options]="sharedArr" class="p-inputtext-sm" styleClass="w-full">
                                    </p-dropdown>
                                    <label htmlFor="Shared" class="_required">Shared</label>
                                </span>
                            </div>
                        </div>
                        <div class="w-full my-3">
                            <p-checkbox formControlName="enableReview" id="enableReview" binary="true"></p-checkbox>
                            <label for="enableReview" class="mb-0 ml-2">Enable Review (Submissions needs to be reviewed)</label>
                        </div>
                        <div class="flex w-full mt-2" *ngIf="enableReview">
                            <div class="col-6 pl-0">
                                <span class="p-float-label">
                                    <p-dropdown id="Reviewer" [formControl]="selectedReviewerType" optionLabel="label" required="true"
                                        optionValue="key" [options]="reviewArr" class="p-inputtext-sm" styleClass="w-full">
                                    </p-dropdown>
                                    <label htmlFor="Reviewer" class="_required">Reviewer Type</label>
                                </span>
                            </div>
                            <div class="col-6 pr-0">
                                <span class="p-float-label">
                                    <p-dropdown id="assign" [formControl]="selectedReviewer" required="true" [autoDisplayFirst]="false"
                                         [options]="reviewersarr" class="p-inputtext-sm" styleClass="w-full">
                                    </p-dropdown>
                                    <label htmlFor="assign" class="_required">Assign Reviewer</label>
                                </span>
                            </div>
                        </div>
                        <div class="w-full my-3">
                            <p-checkbox formControlName="enableShiftHandoff" [disabled]="!enableShared" id="enableShiftHandoff" binary="true"></p-checkbox>
                            <label for="enableShiftHandoff" class="mb-0 ml-2">Enable Shift Handoff (Teams work in shifts on forms)</label>
                        </div>
                        <div class="col-12 p-0 text-right">
                          <button class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateAssignments()" [disabled]="!assignTeamToForm.valid"></button>
                        </div>
                      </div>
                </form>
            </ng-template>
        </p-accordionTab>
    </p-accordion> -->

    
<!-- </div> -->

<div *ngIf="!(dataSourceAssignments?.length > 0)" class="no-data">
    <span>No assignees yet!!!</span>
</div>


<!-- Assign Teams -->
<p-overlayPanel #assignTeamPanel styleClass="p-0" [style]="{'max-width': '400px'}" id="closeAssignTeam">
    <div class="grid">
        <div class="col-12 doc">
          <span class="p-float-label w-full my-3">
            <p-multiSelect [style]="{'min-width': '200px'}" id="teams" [options]="filteredTeams" [(ngModel)]="assignTeam" optionLabel="teamId" optionValue="teamId" display="chip"></p-multiSelect>
            <label htmlFor="teams">Teams</label>
          </span>
          <div class="col-12 text-right pr-0">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetTeamsForm(assignTeamPanel)"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Assign" (click)="assignTeamsToForm(assignTeamPanel)" [disabled]="assignTeam.length === 0"></button>
          </div>
        </div>
      </div>
</p-overlayPanel>