<div class="card">
    <form [formGroup]="createform" novalidate>
      <div class="grid">
          <div class="col-12 mt-3">
              <div class="p-inputgroup">
                <button class="p-button-sm bg-blue" pButton type="button" (click)="iconPicker.show($event)" pTooltip="Choose Icon">
                  <span class="material-icons">{{selectedIcon}}</span>
                </button>
                <span class="p-float-label">
                  <input id="title" pInputText pAutoFocus formControlName="title" type="text" class="w-full" required="true" [autofocus]="true">
                  <label htmlFor="title" class="_required">Title</label>
                </span>
              </div>
              <span *ngIf="createform.get('title').hasError('maxlength')" class="error" style="margin-top: 0px !important;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 100 characters.</small></span>
          </div>
          <!-- <div class="col-12 mt-3">
              <span class="p-float-label">
                  <input id="shortname" pInputText appBlockCopyPaste formControlName="shortname" (keydown.space)="$event.preventDefault()" type="text" class="w-full p-inputtext-sm" required="true">
                  <label htmlFor="shortname" class="_required">Short Name</label>
              </span>
          </div> -->
          <div class="col-12 mt-3 pb-0">
              <span class="p-float-label">
                  <textarea id="description" rows="2" pInputTextarea formControlName="description" class="w-full"></textarea>
                  <label htmlFor="description">Description</label>
              </span>
              <span *ngIf="createform.get('description').hasError('maxlength')" class="error" style="margin-top: 0px !important;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 200 characters.</small></span>
          </div>
          <div *ngIf="data.data?.formType !== 'Templates' && data.data?.form?.formType !== 'Templates'" class="col-12 mt-3">
            <div class="p-inputgroup cat">
              <span class="p-float-label">
                <p-dropdown id="category" formControlName="formCategory" [filter]="true" class="p-inputtext-sm" styleClass="w-full" [autoDisplayFirst]="false"
                  optionLabel="category" optionValue="category" [options]="categoryDisplay" required="true" appendTo="body">
                </p-dropdown>
                <label htmlFor="category" class="_required">Category</label>
              </span>
              <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" (click)="AddCategoryPanel.show($event);resetCatergory()"></button>
            </div>
  
            <!-- <span class="p-input-icon-left p-float-label w-full">
              <i class="pi pi-plus" (click)="addCategoryPannel.show($event)"></i>
              <p-dropdown id="category" formControlName="formCategory" class="p-inputtext-sm" styleClass="w-full"
                optionLabel="category" optionValue="category" [options]="categoryDisplay" required="true">
              </p-dropdown>
              <label htmlFor="category" class="_required">Category</label>
            </span> -->
          </div>
          <!-- <div class="col-12 mt-3" *ngIf="!isMasterform || !data.data.isaddform || !data.data.isupdateform">
              <span class="p-float-label">
                  <p-dropdown id="type" formControlName="formtype" class="p-inputtext-sm" styleClass="w-full"
                    optionLabel="label" optionValue="key" [options]="formTypeArr" required="true">
                  </p-dropdown>
                  <label htmlFor="type" class="_required">Type</label>
              </span>
          </div> -->
          <!-- <div class="col-12 mt-3">
              <span class="p-input-icon-left w-full">
                  <span class="material-icons custom-icon">{{selectedIcon}}</span>
                  <input pInputText (click)="iconPicker.show($event)" class="w-full p-inputtext-sm" placeholder="Icon" readonly="true" required="true">
              </span>
          </div> -->
          <div *ngIf="data.data.isNewForm && !(data.data.formType === 'nestedForm' || data.data.formType === 'masterdata')" class="col my-auto">
            <p-checkbox id="template" binary="true" formControlName="isTemplate"></p-checkbox>
            <label htmlFor="template" class="mb-0 ml-2">Save As Template</label>
          </div>
          <div class="col text-right">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetForm()"></button>
            <!-- <button *ngIf="data.data.isNewForm" class="p-button-sm bg-blue mr-2" pButton type="button" label="Save as Template" (click)="CreateForm()" [disabled]="!createform.valid"></button> -->
            <button *ngIf="data.data.isNewForm" class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="CreateForm()" [disabled]="!createform.valid"></button>
            <button *ngIf="data.data.isCopyForm" class="p-button-sm bg-blue" pButton type="button" label="Save Copy" (click)="CreateForm()" [disabled]="!createform.valid"></button>
            <button *ngIf="data.data.isUpdateForm" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="UpdateForm()" [disabled]="!createform.valid"></button>
            <!-- <button *ngIf="data.data.isupdatemasterdate" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="UpdateMaterData()" [disabled]="!createform.valid"></button> -->
          </div>
      </div>
    </form>
  </div>
  
  
  <!-- Icon picker -->
  <p-overlayPanel #iconPicker [style]="{'width': '300px','height': '380px'}">
    <div class="col w-full">
      <input pInputText #search type="search" class="w-full p-inputtext-sm" placeholder="Search...">
    </div>
    <div class="scrolling">
      <button pButton *ngFor="let icon of iconsArr | filter : search.value" (click)="selectIcon(icon, iconPicker)" class="p-button-rounded p-button-text">
        <span class="material-icons">{{icon}}</span>
      </button>
    </div>
  </p-overlayPanel>
  
  
  <!-- Create Category -->
  <p-overlayPanel #AddCategoryPanel styleClass="p-0" [style]="{'max-width': '400px'}">
    <div class="grid">
        <div class="col-12 w-full">
            <span class="p-float-label custom-width mt-2">
                <input id="category" pInputText [(ngModel)]="newCategory" class="w-full p-inputtext-sm" required="true">
                <label htmlFor="category" class="_required">Category</label>
            </span>
            <div class="col-12 mt-2 pr-0 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetCatergory(AddCategoryPanel)"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="createCategory(AddCategoryPanel)" [disabled]="!newCategory"></button>
            </div>
        </div>
    </div>    
  </p-overlayPanel>