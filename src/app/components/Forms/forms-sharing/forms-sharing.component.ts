import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { UtilsService } from 'src/app/services/utils.service';
import * as dayjs from 'dayjs';
import * as relativeTime from 'dayjs/plugin/relativeTime';
dayjs.extend(relativeTime);


@Component({
  selector: 'app-forms-sharing',
  templateUrl: './forms-sharing.component.html',
  styleUrls: ['./forms-sharing.component.scss']
})
export class FormsSharingComponent implements OnInit {
  sharedForms: any = [];
  formid: string;
  pageSize: number = 0;  //offset
  pageIndex: number = 10;  //limit
  pagination: any;
  searchquery: string = '';
  filled = false;
  displayedColumnsSharing: string[] = [
    'actions',
    'name',
    'url',
    'expires',
    'status',
    'sharedWith'
  ];
  createShareForm: FormGroup;
  externalUsersArr: any = [];
  externalEditArr: any = [];
  externalUsers = new FormControl();
  externalUserForm: FormGroup;
  isCreateContact: boolean = false;
  isEditContact: boolean = false;
  minDate = new Date();
  publicIp: string;
  errmsg: string;
  filterOptions: any = ['All','Active','Expired'];
  isCreate: boolean = false;
  totalRecords: number = 0;
  rangeDates: any = [];
  searchPublicForms: FormGroup;
  dataSourcePublicForms: any = [];
  subDataPageSize: number = 0;  //offset
  subDataPageIndex: number = 5;  //limit
  subDataPagination: any;
  subDataLength: number = 0;
  shareId: any;
  maxDate = new Date();
  validityDate: any;
  currentExpiry: Date;
  shareStatus: string = 'All';
  editUserId: string;

  constructor(
    private formsservice: FormsService,
    private router: Router,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
    private fb: FormBuilder,
    private utilService: UtilsService,
  ) { }

  ngOnInit(): void {
    // this.formid = this.route.parent.snapshot.paramMap.get('formid');
    this.formid = this.data.data.formId;
    // this.getsharingdata(this.formid, this.limit, this.offset, this.query);
    this.getSharingData(null, '', this.shareStatus);
    this.createShareForm = this.fb.group({
      name: ["",[Validators.required, Validators.maxLength(100)]],
      private: [true],
      // email: [''],
      permission: [true],
      expiry: ["", Validators.required],
    });
    this.externalUserForm = this.fb.group({
      firstName: ["", Validators.required],
      lastName: ["", Validators.required],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: ["", Validators.maxLength(10)],
      private: [false],
    });
    this.searchPublicForms = this.fb.group({
      startDate: [new Date(), Validators.required],
      endDate: [new Date(), Validators.required]
    });
  }

  getSharingData(page: any, searchquery: string, status: string, shareId?: any) {
    this.pagination = page;
    this.pageSize = this.pagination ? this.pagination.page * this.pagination.rows : 0;
    this.pageIndex = this.pagination ? this.pagination.rows : 10;
    this.shareStatus = status;
    this.formsservice.getformsharingdata(this.formid, this.pageIndex, this.pageSize, searchquery, this.shareStatus).subscribe((res: any) => {
      if (res.status.toLowerCase() === 'success') {
        this.sharedForms = res.data;
        this.totalRecords = res.totalRecords;
        if (shareId) {
          const obj = this.sharedForms.filter(data => data.shareID === shareId);
          this.copyUrl(obj[0]?.url);
        }
      } else {
        this.sharedForms = [];
        this.totalRecords = 0;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: res.error });
      }
    });
  }

  copyUrl(url: any, event?: any) {
    event?.stopPropagation();
    document.addEventListener('copy', (e: ClipboardEvent) => {
      e.clipboardData.setData('text/plain', (url));
      e.preventDefault();
      document.removeEventListener('copy', null);
    });
    document.execCommand('copy');
    this.messageService.add({ severity: 'info', summary: 'Info', detail: 'URL is copied!' });
    // const message = 'Url is copied';
    // const action = '';
    // this.openSnackBar(message,action);
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  showCreateSharePannel() {
    this.createShareForm.reset();
    this.createShareForm.get('private').setValue(true);
    this.createShareForm.updateValueAndValidity();
    this.externalUsers.reset();
    this.getAllExternalUsers();
    this.isCreate = true;
  }

  getIp() {
    this.formsservice.getpublicIp().subscribe((res: any) => {
      this.publicIp = res.ip;
    });
  }

  createShareURL() {
    console.log('create share caled')
    let formValue = {
      name: this.createShareForm.get("name").value,
      email: "",
      externalShareId: this.externalUsers?.value ? this.externalUsers.value : [],
      private: this.createShareForm.get("private").value ? this.createShareForm.get("private").value : false,
      permission: this.createShareForm.get("permission").value ? this.createShareForm.get("permission").value : false,
      expiry: this.createShareForm.get("expiry").value,
    };
    this.formsservice.createformsharingdata(this.formid, formValue, this.publicIp)
      .subscribe((createShareFormAPIres: any) => {
    console.log('API success')

        const response = createShareFormAPIres;
        if (response.status.toLowerCase() === "success") {
          this.getSharingData(null, '', this.shareStatus, response.shareId);
          this.isCreate = false;
          this.createShareForm.reset();
          // this.messageService.add({ severity: 'success', summary: 'Success', detail: 'URL created!' });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  cancelShareForm() {
    this.createShareForm.reset();
    this.isCreate = false;
  }

  showCreateContact() {
    this.resetExternalUserForm();
    this.isCreateContact = true;
  }

  getAllExternalUsers() {
    this.externalUsersArr = [];
    this.formsservice.getExternalUsers(' ', []).subscribe((response) => {
      if (response.status === 'Success') {
        const arr = response.externalUsers;
        this.externalEditArr = arr;
        for (let i=0; i < arr.length; i++) {
          this.externalUsersArr.push(
            {
              "userName": arr[i].firstName + ' ' + arr[i].lastName, 
              "userId": arr[i].id
            });
        }
      } else {
        this.externalUsersArr = [];
        this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
      }
    });
  }

  createExternalUser() {
    let userId = this.utilService.generateRandomString(32);
    let body = {
      firstName: this.externalUserForm.get("firstName").value,
      lastName: this.externalUserForm.get("lastName").value,
      email: this.externalUserForm.get("email").value,
      phoneNumber: this.externalUserForm.get("phoneNumber").value,
      private: this.externalUserForm.get("private").value,
      id: userId,
    };
    this.formsservice.createExternalUser(body)
      .subscribe((response) => {
        if (response.error) {
          const serverError = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: serverError });
        } else if (response.status.toLowerCase() === "success") {
          // element.hide();
          let obj = {};
          this.formsservice.getExternaluserDEtails(userId).subscribe((res) => {
            let response = res;
            obj["userName"] = response.firstName + ' ' + response.lastName;
            obj["userId"] = response.id;
            this.externalUsersArr.push(obj);
            this.externalUsers.setValue([{"userName": response.firstName + ' ' + response.lastName, "userId": response.id}]);
            this.externalUsers.updateValueAndValidity();
          });
          this.resetExternalUserForm();
        }
      });
  }

  editExternalUser() {
    let body = {
      firstName: this.externalUserForm.get("firstName").value,
      lastName: this.externalUserForm.get("lastName").value,
      email: this.externalUserForm.get("email").value,
      phoneNumber: this.externalUserForm.get("phoneNumber").value,
      private: this.externalUserForm.get("private").value,
      id: this.editUserId,
    };
    this.formsservice.editExternalUser(body).subscribe((response) => {
        if (response.error) {
          const serverError = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: serverError });
        } else if (response.status.toLowerCase() === "success") {
          // element.hide();
          let obj = {};
          this.formsservice.getExternaluserDEtails(this.editUserId).subscribe((res) => {
            let response = res;
            obj["userName"] = response.firstName + ' ' + response.lastName;
            obj["userId"] = response.id;
            this.externalUsersArr.push(obj);
            this.externalUsers.setValue([{"userName": response.firstName + ' ' + response.lastName, "userId": response.id}]);
            this.externalUsers.updateValueAndValidity();
          });
          this.resetExternalUserForm();
        }
      });
  }

  resetExternalUserForm() {
    // if (element) {
    //   element.hide();
    // }
    this.externalUserForm.reset();
    this.isCreateContact = false;
    this.isEditContact = false;
    this.editUserId = '';
  }

  goToPreview(share: any) {
    let data = {
      name: share.name,
      shareId: share.shareID,
      formId: share.formId,
      isPrivate: share.private,
      isFormSubmitted: share.completed
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    window.open(`/${share.private ? 'preview' : 'public-shared-forms'}/${routerData}`, '_blank');
  }

  getPublicSharedFormsInfo(page: any) {
    this.subDataPagination = page;
    this.subDataPageSize = this.subDataPagination ? this.subDataPagination.page * this.subDataPagination.rows : 0;
    this.subDataPageIndex = this.subDataPagination ? this.subDataPagination.rows : 10;

    const serachobj = {};
    const startDate = new Date(this.rangeDates[0]);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(this.rangeDates[1]);
    endDate.setHours(23, 59, 59, 999);
    serachobj['startDate'] = startDate;
    this.searchPublicForms.get('startDate').setValue(startDate);
    serachobj['endDate'] = endDate;
    this.searchPublicForms.get('endDate').setValue(endDate);    
    this.formsservice.getpublicsharedformsinfo(this.formid, this.shareId, this.subDataPageSize, this.subDataPageIndex, serachobj)
    .subscribe( res => {
      const publicforms = res;
      this.subDataLength = publicforms.totalRecords;
      this.subDataPageIndex = publicforms.nextOffSet;
      this.dataSourcePublicForms = publicforms.data;
    });
  }

  getShareId(data: any, event: any) {
    event.stopPropagation();
    this.shareId = data.shareID;
    this.currentExpiry = new Date(data.expiry);
  }

  extendValidityFunction(element: any) {
    this.formsservice
      .extendvalidityofsharelink(this.shareId, this.validityDate)
      .subscribe((response: any) => {
        if (response.status.toLowerCase() === "success") {
          this.validityDate = '';
          this.shareId = null;
          element.hide();
          this.getSharingData(null, '', this.shareStatus);
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Validity Extended!' });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  cancelValidityForm(element: any) {
    this.validityDate = '';
    element.hide();
    this.shareId = null;
  }

  shortenString(str) {
    const firstPart = str.substring(0, 20);
    const lastPart = str.substring(str.length - 20);
    return `${firstPart}......${lastPart}`;
  }

  onPublicPrivateChange(event: any) {
    if (!event.checked) {
      this.externalUserForm.reset();
      this.isCreateContact = false;
    }
  }

  showEditContact() {
    this.resetExternalUserForm();
    this.isEditContact = true;
  }

  onSelecteContact(event: any) {
    const data = this.externalEditArr.filter((item: any) => {return event.value?.userId === item.id});
    this.editUserId = data[0].id;
    this.externalUserForm.setValue({
      firstName: data[0].firstName,
      lastName: data[0].lastName,
      email: data[0].email,
      phoneNumber: data[0].phoneNo,
      private: data[0].private,
    });
  }

}
