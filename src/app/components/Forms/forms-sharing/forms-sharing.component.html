<a href="https://docs.unvired.com/builder/admin/forms/#sharing" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="share" >
    <!-- <div class="col-12 px-0">
      <div class="flex justify-content-between align-items-center cust-button">
        <div class="flex flex-row align-items-center gap-2">
          <span class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <input style="width: 21rem;" class="p-inputtext-sm" pInputText type="text"
            [(ngModel)]="searchquery" (keyup)="getSharingData(pagination,searchquery)" placeholder="Search..." />
          </span>
          <p-selectButton [options]="filterOptions" [(ngModel)]="status" (onOptionClick)="getSharingData(pagination,searchquery)" [disabled]="!(sharedForms?.length > 0)"></p-selectButton>
        </div>
        <button pButton type="button" (click)="showCreateSharePannel()" label="Create" class="p-button-sm bg-blue"></button>
      </div>
    </div> -->

<!--  Create share -->
    <div class="col-12 p-0 my-1 external" *ngIf="isCreate">
        <p-accordion>
            <p-accordionTab [selected]="true">
                <ng-template pTemplate="header">
                    <span>Create Share</span>
                </ng-template>
                <ng-template pTemplate="content">
                    <form [formGroup]="createShareForm" novalidate>
                        <div class="grid formgrid">
                          <div class="col-6 my-3"> <!-- Name -->
                            <span class="p-float-label">
                              <input id="comment" pInputText formControlName="name" class="w-full" required="true">
                              <label htmlFor="comment" class="_required">Comment</label>
                            </span>
                          </div>
                          <div class="col-6 my-3"> <!-- Expiry Date -->
                            <span class="p-float-label">
                              <p-calendar id="Expiry" formControlName="expiry" [minDate]="minDate" [readonlyInput]="true" styleClass="w-full" required="true" appendTo="body"></p-calendar>
                              <label for="Expiry" class="mb-0 _required">Expiry Date</label>
                            </span>
                          </div>
                          <div class="col-6 my-auto"> <!-- Check box -->
                            <div class="flex align-items-center gap-3">
                              <div class="flex align-items-center">
                                <p-inputSwitch inputId="private" formControlName="private" styleClass="my-auto" (onChange)="onPublicPrivateChange($event)"></p-inputSwitch>
                                <label for="private" class="ml-2 mb-1">{{createShareForm.get('private').value === true ? 'Private' : 'Public'}}</label>
                              </div>
                              <div *ngIf="createShareForm.get('private').value === true" class="flex align-items-center">
                                <p-checkbox value="Save Incomplete Form" formControlName="permission" inputId="Draft" [binary]="true"></p-checkbox>
                                <label for="Draft" class="mb-0 ml-2">Save Incomplete Form</label>
                              </div>
                            </div>
                          </div>
                          
                          <div class="col-6 my-3"> <!-- External Users -->
                            <div class="p-inputgroup cat" *ngIf="createShareForm.get('private').value === true">
                              <span class="p-float-label">
                                <p-multiSelect id="Contacts" [options]="externalUsersArr" [formControl]="externalUsers" optionLabel="userName" display="chip" styleClass="w-full" appendTo="body"></p-multiSelect>
                                <label htmlFor="Contacts">Contacts</label>
                              </span>
                              <button class="p-button-sm" pButton type="button" icon="pi pi-plus" [disabled]="isCreateContact || isEditContact" (click)="showCreateContact()" pTooltip="Create Contact"></button>
                              <button class="p-button-sm" pButton type="button" icon="pi pi-pencil" [disabled]="isCreateContact || isEditContact" (click)="showEditContact()" pTooltip="Edit Contact"></button>
                            </div>
                          </div>
                          <div *ngIf="!isCreateContact" class="col-12 mt-3 text-right">
                            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancelShareForm()"></button>
                            <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="createShareURL()" [disabled]="createShareForm.invalid"></button>
                          </div>
                        </div>
                    </form>
                    <p-fieldset *ngIf="isCreateContact || isEditContact">
                      <ng-template pTemplate="header">
                        <span *ngIf="isCreateContact">Create Contact</span>
                        <p-dropdown *ngIf="isEditContact" [autoDisplayFirst]="false" optionLabel="userName" (onChange)="onSelecteContact($event)"
                            [options]="externalUsersArr" styleClass="w-15rem p-inputtext-sm" appendTo="body" placeholder="Choose Contact" pAutoFocus [autofocus]="true">
                        </p-dropdown>
                      </ng-template>
                      <ng-template pTemplate="content">
                        <form [formGroup]="externalUserForm" novalidate>
                          <div class="grid formgrid">
                            <div class="col-6">
                              <span class="p-float-label">
                                <input type="text" id="First" pInputText formControlName="firstName" class="w-full p-inputtext-sm" required="true">
                                <label htmlFor="First" class="_required">First Name</label>
                              </span>
                            </div>
                            <div class="col-6">
                              <span class="p-float-label">
                                <input type="text" #lastName1 id="Last" pInputText formControlName="lastName" class="w-full p-inputtext-sm" required="true">
                                <label htmlFor="Last" class="_required">Last Name</label>
                              </span>
                            </div>
                            <div class="col-6 my-4">
                              <span class="p-float-label">
                                <input type="email" id="Email" pInputText formControlName="email" class="w-full p-inputtext-sm" required="true">
                                <label htmlFor="Email" class="_required">Email</label>
                              </span>
                            </div>
                            <div class="col-4 my-4">
                              <span class="p-float-label w-full">
                                <input type="text" id="Phone" pInputText formControlName="phoneNumber" class="w-full p-inputtext-sm">
                                <!-- <p-inputNumber inputId="Phone" formControlName="phoneNumber" [useGrouping]="false" [min]="10" [max]="10" styleClass="w-full" class="w-full"></p-inputNumber> -->
                                <label htmlFor="Phone">Phone</label>
                              </span>
                              <span *ngIf="externalUserForm.get('phoneNumber').hasError('maxlength')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>A maximum of 10 digits is required.</small></span>
                              <span *ngIf="externalUserForm.get('phoneNumber').hasError('minlength')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>A minimum of 10 digits is required.</small></span>
                            </div>
                            <div class="col-2 my-auto">
                              <div class="flex flex-row align-items-center">
                                <p-inputSwitch id="private" formControlName="private"></p-inputSwitch>
                                <label htmlFor="private" class="ml-2 mb-1">{{externalUserForm.get('private').value === true ? 'Private' : 'Public'}}</label>
                              </div>
                            </div>
                            <div class="col-12 mt-3 text-right">
                              <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetExternalUserForm()"></button>
                              <button class="p-button-sm" pButton type="button" [label]="isEditContact ? 'Update' : 'Create'" (click)="isEditContact ? editExternalUser() : createExternalUser()" [disabled]="externalUserForm.invalid"></button>
                            </div>
                          </div>
                        </form>
                      </ng-template>
                  </p-fieldset>
                </ng-template>
            </p-accordionTab>
        </p-accordion>
    </div>
   
  
    <!-- <div *ngIf="isCreate" class="col-12 bordar p-0 mb-2">     
     
     <hr class="mt-0">
      <form [formGroup]="createShareForm" class="p-3" novalidate>
        <div class="grid">
          <div class="col-6">
            <div class="w-full mb-4 mt-2">
              <span class="p-float-label">
                <input id="Name" pInputText formControlName="name" class="w-full p-inputtext-sm" required="true">
                <label htmlFor="Name" class="_required">Share Name</label>
              </span>
            </div>
            <div class="w-full my-4">
              <span class="p-float-label">
                <p-calendar id="Expiry" formControlName="expiry" [minDate]="minDate" [readonlyInput]="true" styleClass="w-full" required="true"></p-calendar>
                <label for="Expiry" class="mb-0 ml-2 _required">Expiry Date</label>
              </span>
            </div>
          </div>
  
          <div class="col-6">
            <div class="flex align-items-center justify-content-between w-full mt-3">
              <div class="flex align-items-center">
                <p-inputSwitch id="private" formControlName="private" styleClass="my-auto"></p-inputSwitch>
                <label htmlFor="private" class="ml-2 mb-1">{{createShareForm.get('private').value === true ? 'Private' : 'Public'}}</label>
              </div>
              <div *ngIf="createShareForm.get('private').value === true" class="flex align-items-center">
                <p-checkbox value="Enable Draft" formControlName="permission" inputId="Draft" [binary]="true"></p-checkbox>
                <label for="Draft" class="mb-0 ml-2">Enable Draft</label>
              </div>
            </div>
            <div *ngIf="createShareForm.get('private').value === true" class="w-full my-4">
              <div class="p-inputgroup cat">
                <span class="p-float-label">
                  <p-multiSelect id="External" [options]="externalUsersArr" [formControl]="externalUsers" optionLabel="userName" display="chip" styleClass="w-full"></p-multiSelect>
                  <label htmlFor="External">External Users</label>
                </span>
                <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" (click)="createExternalUserPannel.show($event);resetExternalUserForm()"></button>
              </div>
            </div>
          </div>
  
          <div class="col-12 mt-3 text-right">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancelShareForm()"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Create" (click)="CreateShareForm()" [disabled]="createShareForm.invalid"></button>
          </div>
        </div>
      </form>
    </div> -->
  
    <!-- <p-accordion *ngIf="isCreate">    
      <p-accordionTab [selected]="true">
          <ng-template pTemplate="header">
              <span>Create Share</span>
          </ng-template>
          <ng-template pTemplate="content">
            <form [formGroup]="createShareForm" novalidate>
              <div class="grid">
                <div class="col-6">
                  <div class="w-full mb-4 mt-2">
                    <span class="p-float-label">
                      <input id="Name" pInputText formControlName="name" class="w-full p-inputtext-sm" required="true">
                      <label htmlFor="Name" class="_required">Share Name</label>
                    </span>
                  </div>
                  <div class="w-full my-4">
                    <span class="p-float-label">
                      <p-calendar id="Expiry" formControlName="expiry" [minDate]="minDate" [readonlyInput]="true" styleClass="w-full" required="true"></p-calendar>
                      <label for="Expiry" class="mb-0 ml-2 _required">Expiry Date</label>
                    </span>
                  </div>
                </div>
        
                <div class="col-6">
                  <div class="flex align-items-center justify-content-between w-full mt-3">
                    <div class="flex align-items-center">
                      <p-inputSwitch id="private" formControlName="private" styleClass="my-auto"></p-inputSwitch>
                      <label htmlFor="private" class="ml-2 mb-1">{{createShareForm.get('private').value === true ? 'Private' : 'Public'}}</label>
                    </div>
                    <div *ngIf="createShareForm.get('private').value === true" class="flex align-items-center">
                      <p-checkbox value="Enable Draft" formControlName="permission" inputId="Draft" [binary]="true"></p-checkbox>
                      <label for="Draft" class="mb-0 ml-2">Enable Draft</label>
                    </div>
                  </div>
                  <div *ngIf="createShareForm.get('private').value === true" class="w-full my-4">
                    <div class="p-inputgroup cat">
                      <span class="p-float-label">
                        <p-multiSelect id="External" [options]="externalUsersArr" [formControl]="externalUsers" optionLabel="userName" display="chip" styleClass="w-full"></p-multiSelect>
                        <label htmlFor="External">External Users</label>
                      </span>
                      <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" (click)="createExternalUserPannel.show($event);resetExternalUserForm()"></button>
                    </div>
                  </div>
                 
                </div>
                <div class="col-12 mt-3 text-right">
                  <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancelShareForm()"></button>
                  <button class="p-button-sm bg-blue" pButton type="button" label="Create" (click)="CreateShareForm()" [disabled]="createShareForm.invalid"></button>
                </div>
              </div>
            </form>
          </ng-template>
      </p-accordionTab>
    </p-accordion>
   
    <p-accordion *ngIf="sharedForms?.length > 0">
      <p-accordionTab *ngFor="let share of sharedForms;let i=index" (click)="share.private ? goToFormPreview(share.name, share.shareID) : showSubDataPannel(share)">
          <ng-template pTemplate="header">
              <div class="flex flex-row align-items-center gap-3 ml-2 w-full">
                  <i [class]="share.private ? 'pi pi-user' : 'pi pi-users'" style="font-size: 20px"></i>
                  <div class="flex flex-column align-items-start gap-1">
                      <div class="main-title">{{ share.name }}</div>
                      <div class="description copy-url" pTooltip="Click To Copy" (click)="copyUrl($event, share.url)">{{ share.url }}</div>
                  </div>
                  <div class="flex flex-row justify-content-end flex-1 gap-2">
                    <span [ngClass]="share.shareStatus === 'Active' ? 'active' : 'in-active'" class="description"><span>{{share.shareStatus === 'Active' ? 'Expires ' : 'Expired '}}</span>{{ formatDate(share.expiry | date) }} ({{ share.expiry | date }})</span>
                  </div>
              </div>
          </ng-template>
      </p-accordionTab>
    </p-accordion>
  
    <div *ngIf="totalRecords > 0" class="col-12 text-center">
      <p-paginator class="p-0" [rows]="pageIndex" [totalRecords]="totalRecords" [showJumpToPageDropdown]="false"
        (onPageChange)="getSharingData($event,searchquery)" [showPageLinks]="true">
      </p-paginator>
    </div>
  
    <div *ngIf="!(sharedForms?.length > 0)" class="no-data">
      <span>No results found.</span>
    </div> -->
  

    <p-table *ngIf="!isCreate" #dt
      [value]="sharedForms" dataKey="shareID" 
      responsiveLayout="scroll" 
      [scrollable]="true" 
      styleClass="p-datatable-sm p-datatable-gridlines"
      [tableStyle]="{ 'min-width': '50rem' }">

      <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-between">
          <div>
              <span class="p-input-icon-left">
                <i class="pi pi-search"></i>
                <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                [(ngModel)]="searchquery" (keyup)="getSharingData(pagination,searchquery,shareStatus)" placeholder="Search..." />
              </span>
              <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                  (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
              </button>
          </div>
          <div class="flex flex-row cust-button">
              <p-selectButton [options]="filterOptions" styleClass="mr-2" [(ngModel)]="shareStatus" (onOptionClick)="getSharingData(pagination,searchquery,shareStatus)"></p-selectButton>
              <button pButton type="button" (click)="showCreateSharePannel()" label="Create" icon="pi pi-plus" class="p-button-sm bg-blue"></button>
          </div>
        </div>
      </ng-template>

<!-- Header -->
      <ng-template pTemplate="header" let-columns>
          <tr>
            <th style="min-width: 10rem">Name</th>
            <th style="min-width: 10rem">Link </th>
            <th style="min-width: 10rem">Validity </th>
            <th style="min-width: 3rem" class="text-center"></th>
          </tr>
      </ng-template>

<!-- Table data -->
      <ng-template pTemplate="body" let-share let-columns="columns">
        <!-- <tr (click)="goToPreview(share)" class="cursor-pointer"> -->
        <tr>

          <td>
            <!-- <button pButton class="p-button-text p-button-sm text-left"> -->
              <div class="flex flex-row align-items-center gap-2">
                <i [class]="share.private ? 'pi pi-lock' : 'pi pi-globe'" [pTooltip]="share.private ? 'Private' : 'Public'" style="font-size: 18px;"></i>
                <span>{{share.name}} <span *ngIf="share.private && share.completed" class="submitted-private-form">submitted</span></span>
              </div>
            <!-- </button> -->
          </td>
  
          <td>
            <div class="flex flex-row">
              <span class="copy-url" (click)="copyUrl(share.url, $event)" pTooltip="Click To Copy">{{shortenString(share.url)}}</span>
            </div>
          </td>
  
          <td>
            <div class="flex flex-row w-50 align-items-center">
              <div [ngClass]="share.shareStatus === 'Active' ? 'active' : 'in-active'" class="description"><span>{{share.shareStatus === 'Active' ? 'Expires ' : 'Expired '}}</span>{{ formatDate(share.expiry | date: 'MM/dd/yyyy h:mm a') }}</div> 
              <!-- ({{ share.expiry | date }}) -->
            </div>
          </td>

          <td class="text-center my-auto">
            <button pButton type="button" (click)="goToPreview(share)" class="p-button-sm p-button-text mr-2" icon="pi pi-eye" pTooltip="View Form"></button>
            <button pButton type="button" (click)="extendValidityPannel.show($event);getShareId(share, $event)" label="Extend" class="p-button-sm p-button-outlined bg-blue"></button>
          </td>

        </tr>
      </ng-template>

<!-- Paginator -->
      <ng-template pTemplate="footergrouped">
        <tr>
            <td colspan="4">
              <p-paginator class="p-0" [rows]="10" [totalRecords]="totalRecords" [showJumpToPageDropdown]="false" dropdownAppendTo="body"
                (onPageChange)="getSharingData($event,searchquery,shareStatus)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
              </p-paginator>
            </td>
        </tr>
       
      </ng-template>

<!-- No data -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-center" style="font-weight: bold;" colspan="4">No share URL found.</td>
        </tr>
      </ng-template>
    </p-table>
  

  <!-- Create external user -->
  <!-- <p-overlayPanel #createExternalUserPannel [style]="{'max-width': '400px'}">
    <form [formGroup]="externalUserForm" novalidate>
      <div class="grid formgrid">
        <div class="col-12 mt-4">
          <span class="p-float-label">
            <input type="text" id="First" pInputText formControlName="firstName" class="w-full p-inputtext-sm" required="true" (keydown)="handleTab($event)" tabindex="1">
            <label htmlFor="First" class="_required">First Name</label>
          </span>
        </div>
        <div class="col-12 mt-4">
          <span class="p-float-label">
            <input type="text" #lastName1 id="Last" pInputText formControlName="lastName" class="w-full p-inputtext-sm" required="true" tabindex="2">
            <label htmlFor="Last" class="_required">Last Name</label>
          </span>
        </div>
        <div class="col-12 my-4">
          <span class="p-float-label">
            <input type="email" id="Email" pInputText formControlName="email" class="w-full p-inputtext-sm" required="true" tabindex="3">
            <label htmlFor="Email" class="_required">Email</label>
          </span>
        </div>
        <div class="col-8">
          <span class="p-float-label w-full">
            <input type="text" id="Phone" pInputText formControlName="phoneNumber" class="w-full p-inputtext-sm">
            <label htmlFor="Phone">Phone</label>
          </span>
          <small class="error" *ngIf="externalUserForm.get('phoneNumber').hasError('maxlength')">Maximum of 10 numbers allowed.</small>
          <small class="error" *ngIf="externalUserForm.get('phoneNumber').hasError('minlength')">Maximum of 10 numbers allowed.</small>
        </div>
        <div class="col-4 my-auto">
          <div class="flex flex-row align-items-center">
            <p-inputSwitch id="private" formControlName="private"></p-inputSwitch>
            <label htmlFor="private" class="ml-2 mb-1">{{externalUserForm.get('private').value === true ? 'Private' : 'Public'}}</label>
          </div>
        </div>
       
        <div class="col-12 mt-2 text-right">
          <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetExternalUserForm(createExternalUserPannel)"></button>
          <button class="p-button-sm bg-blue" pButton type="button" label="Create" (click)="createExternalUser()" [disabled]="externalUserForm.invalid"></button>
        </div>
      </div>
    </form>
  </p-overlayPanel> -->

  <p-overlayPanel #extendValidityPannel [style]="{'max-width': '400px'}">
    <div class="col-12 px-0"> <!-- Expiry Date -->
      <span class="p-float-label">
        <p-calendar id="Expiry" [(ngModel)]="validityDate" [minDate]="currentExpiry" [readonlyInput]="true" styleClass="w-full p-inputtext-sm" required="true" appendTo="body"></p-calendar>
        <label for="Expiry" class="mb-0 _required">Expiry Date</label>
      </span>
    </div>
    <div class="col-12 text-right pr-0">
      <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancelValidityForm(extendValidityPannel)"></button>
      <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="extendValidityFunction(extendValidityPannel)" [disabled]="!validityDate"></button>
    </div>
  </p-overlayPanel>
  
  
  <!-- Submission data -->
  <!-- <p-overlayPanel #submissionDataPannel [style]="{'width': '600px'}">
    <div class="flex flex-row justify-content-between mb-2">
        <p-calendar [(ngModel)]="rangeDates" selectionMode="range" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" class="w-full"></p-calendar>
        <button pButton type="button" (click)="getPublicSharedFormsInfo(subDataPagination)" icon="pi pi-search" class="p-button-sm bg-blue ml-2"></button>
    </div>
  
    <p-table [value]="dataSourcePublicForms" responsiveLayout="scroll" scrollHeight="40vh" styleClass="p-datatable-gridlines p-datatable-sm">
      <ng-template pTemplate="header">
          <tr>
              <th class="text-align-center">Submission Date</th>
              <th class="">Submission ID</th>
          </tr>
      </ng-template>
      <ng-template pTemplate="body" let-item>
          <tr>
              Date
              <td>
                  <span>{{ item.submissionDate | date: "MMM d, y, h:mm:ss a" }}</span>
              </td>
              Id
              <td>
                  <span>{{ item.submissionId }}</span>
              </td>
          </tr>
      </ng-template>
      <ng-template *ngIf="dataSourcePublicForms.length > 0" pTemplate="summary">
        <p-paginator class="p-0" [rows]="subDataPageIndex" [totalRecords]="subDataLength" [showJumpToPageDropdown]="false"
          (onPageChange)="getPublicSharedFormsInfo($event,searchquery)" [showPageLinks]="true">
        </p-paginator>
      </ng-template>
      <ng-template *ngIf="!(dataSourcePublicForms.length > 0)" pTemplate="emptymessage">
        <tr class="text-center">
          <td colspan="2" class="text-center">No Data found.</td>
        </tr>
      </ng-template>
    </p-table>
  </p-overlayPanel> -->