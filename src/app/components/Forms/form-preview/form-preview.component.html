<p-toast></p-toast>
<div *ngIf="showSpinner" class="loader-overlay">
  <p-progressSpinner></p-progressSpinner>
</div>
<div *ngIf="form?.components" class="flex flex-row justify-content-between align-items-center custom-toolbar" styleClass="shadow-5">
    <div class="p-toolbar-group-start">
      <div class="flex flex-column">
        <h4 *ngIf="formTitle" class="mb-2">{{formTitle}}</h4>
        <h5 *ngIf="!(routerData.wfId || routerData.submissionId || routerData.auditId)" class="m-0">{{ device + ' ' }}Preview</h5>
        <div *ngIf="routerData.wfId || routerData.submissionId || routerData.auditId" class="flex flex-row">
          <span *ngIf="lastUpdatedBy">{{lastUpdatedBy}} </span>
          <!-- <p-divider *ngIf="lastUpdatedBy" styleClass="mx-2" layout="vertical"></p-divider> -->
          <span *ngIf="lastUpdated"><span class="mx-2">|</span>Last Updated on {{lastUpdated | date}}</span>
        </div>
      </div>
    </div>
    <div class="p-toolbar-group-center">
      <div *ngIf="!(routerData.wfId || routerData.submissionId || routerData.auditId)">
        <button *ngIf="!hideDeviceIcons" pButton type="button" label="Mobile" icon="pi pi-mobile" class="p-button-sm mx-2 bg-blue"
          (click)="selectDevice('Mobile')"></button>
        <button *ngIf="!hideDeviceIcons" pButton type="button" label="Tablet" icon="pi pi-tablet" class="p-button-sm mx-2 bg-blue"
          (click)="selectDevice('Tablet')"></button>
        <button *ngIf="!hideDeviceIcons" pButton type="button" label="Custom" icon="pi pi-window-maximize" class="p-button-sm mx-2 bg-blue"
          (click)="showCustomDevice()"></button>
        <button *ngIf="hideDeviceIcons" pButton type="button" label="Back" icon="pi pi-arrow-left" class="p-button-sm mr-2 bg-blue"
          (click)="goBackToDeviceIcons()"></button>
        <button *ngIf="!(device === 'Desktop' || isCustomDevice)" pButton type="button" icon="pi pi-tablet" class="p-button-sm bg-blue mr-2"
          label="Portrait" (click)="landscapePotraitMode('portrait')"></button> <!-- portrait -->
        <button pButton *ngIf="!(device === 'Desktop' || isCustomDevice)" class="bg-blue p-button-sm" (click)="landscapePotraitMode('landscape')">
          <i class="pi pi-tablet landscape"></i>
          <span class="ml-2" style="font-weight: 700;">Landscape</span>
        </button>
        <p-inputNumber *ngIf="isCustomDevice" class="p-inputtext-sm mx-2" placeholder="Height" inputId="integeronly" [(ngModel)]="customHeight" (onInput)="customDevice()"> </p-inputNumber>
        <i *ngIf="isCustomDevice" style="color: #aaadaf;" class="pi pi-times"></i>
        <p-inputNumber *ngIf="isCustomDevice" class="p-inputtext-sm mx-2" placeholder="Width" inputId="integeronly" [(ngModel)]="customWidth" (onInput)="customDevice()"> </p-inputNumber>
      </div>
    </div>
    <div #end class="p-toolbar-group-end flex flex-row align-items-center">
        <div *ngIf="versionArr?.length === 0" class="flex flex-row align-items-center">
          <span class="mr-1">Version: {{ formVersion }}</span>
          <p-divider *ngIf="formVersionComment" styleClass="mx-2" layout="vertical"></p-divider>
          <div class="flex flex-column justify-content-start">
            <span *ngIf="formVersionComment">Comment: {{formVersionComment}}</span>
          </div>
        </div>
        <!-- <div *ngIf="versionArr?.length === 0" class="flex flex-row align-items-center">
          <label *ngIf="landsacpe?.toLowerCase() !== 'production' && formVersion">Version: {{ formVersion }}</label>
          <label *ngIf="landsacpe?.toLowerCase() === 'production' && formVersion">Version: {{ formVersion }}</label>
          <p-divider *ngIf="formVersionComment" styleClass="mx-2" layout="vertical"></p-divider>
          <div *ngIf="formVersionComment" class="flex flex-column justify-content-start mr-2">
            <span>Comment: {{ formVersionComment }}</span>
          </div>
      </div> -->
        <div *ngIf="versionArr?.length > 0" class="flex flex-row align-items-center">
          <div class="flex align-items-center">
            <span class="mr-1">Version: </span>
            <p-dropdown [(ngModel)]="currentVersion" (onChange)="getFormDataOnVersion(currentVersion)"
              [options]="versionArr" class="p-inputtext-sm" styleClass="w-full">
            </p-dropdown>
          </div>
          <button *ngIf="formVersion" pButton type="button" icon="pi pi-download" class="ml-2" (click)="downloadForm()" pTooltip="Download PDF"></button>
          <p-divider *ngIf="formVersionComment || formVersionDate" styleClass="mx-2" layout="vertical"></p-divider>
          <div class="flex flex-column justify-content-start">
            <span *ngIf="formVersionComment">Comment: {{formVersionComment}}</span>
            <span *ngIf="formVersionDate">Last Updated on {{formVersionDate | date}}</span>
          </div>
        </div>
        <!-- <button *ngIf="routerData.iswftestdata" pButton type="button" [disabled]="displayIcon" icon="pi pi-check" 
          class="p-button-sm mx-2 bg-blue" pTooltip="Submission Data" (click)="displaySubmissionData()"></button>
        <button *ngIf="routerData.iswftestdata" pButton type="button" icon="pi pi-link" 
          class="p-button-sm bg-blue" pTooltip="Flow Test Data" (click)="openFormUsersDialog()"></button> -->
        <p-divider *ngIf="languages?.length > 0" styleClass="mx-2" layout="vertical"></p-divider>
        <p-dropdown *ngIf="languages?.length > 0" [(ngModel)]="currentLanguage" (onChange)="getAllTranslations()"
          optionLabel="display" optionValue="key" [options]="languages" class="p-inputtext-sm custom-width" styleClass="w-full" [appendTo]="end">
        </p-dropdown>
        <button *ngIf="routerData.wfId || routerData.submissionId" pButton type="button" [disabled]="readonly" class="p-button-sm mx-2 bg-blue" (click)="submitData()">
          <b style="font-size: 12px;">Submit</b>
          <i *ngIf="isSubmitting" class="pi pi-spin pi-spinner ml-2"></i>
        </button>
    </div>
</div>

<p-scrollTop></p-scrollTop>
<div class="sdc-spinner">
  <div class="sk-chase">
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
    <div class="sk-chase-dot"></div>
  </div>
</div>
<!-- Desktop -->
<div id="divSize" *ngIf="device === 'Desktop' && form?.components" class="surface-overlay component-wrapper" style="margin-top: 100px;">
  <div [ngClass]="[isCard ? 'form-with-card' : 'form-without-card']">
    <div class="form-renderer-container formio-wrapper">
      <formio [form]='form' [refresh]="refreshForm" [renderOptions]="seletedLanguage" [submission]='{ "data": submissiondata }'
        (customEvent)="handleCustomEvent($event)" (submit)="onSubmit($event)" [readOnly]="readonly" #formio>
      </formio>
    </div>
  </div>
  
</div>

<!-- Mobile/Tablet -->
<div *ngIf="device != 'Desktop'" id="divSize"
  [ngClass]="[mode === 'landscape' && device === 'Tablet' ? 'device-view-landscape round-button-landscape-tablet': '',
  mode === 'portrait' && device === 'Tablet' ? 'device-view-portrait': '',
  mode === 'landscape' && device === 'Mobile' ? 'device-view-landscape round-button-landscape-mobile': '',
  mode === 'portrait' && device === 'Mobile' ? 'device-view-portrait mobile-responsive': '',
  device === 'Custom' ? 'custom-device': '']">
  <div class="form-renderer-container" id="render_form" [ngStyle]="{'height': customHeight + 'px', 'width': customWidth + 'px'}">
    <!-- <iframe [srcdoc]="dataPreview" height="500px" width="300px" id="aa"></iframe> -->
    <!-- <formio [form]='form' [refresh]="refreshForm" [renderOptions]="seletedLanguage" [submission]='{ "data": submissiondata }'
      (customEvent)="handleCustomEvent($event)" (submit)="onSubmit($event)" [readOnly]="readonly" #formio>
    </formio> -->
  </div>
  <div *ngIf="mode === 'portrait'" [ngClass]="[mode === 'portrait' && !(device === 'Desktop' || device === 'Custom') ? 'round-button-portrait' : '']"></div>
</div>