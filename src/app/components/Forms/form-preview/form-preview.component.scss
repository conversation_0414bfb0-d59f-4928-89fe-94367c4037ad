.sdc-spinner {
    display: none;
    pointer-events: all;
    z-index: 99999;
    border: none;
    margin-top: 80px;
    padding: 0px;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    cursor: wait;
    position: fixed;
    /* opacity: 0.3;
    background-color: gray; */
    }
    .sk-chase {
    width: 40px;
    height: 40px;
    text-align: center;
    position: relative;
    animation: sk-chase 2.5s infinite linear both;
}
    
.sk-chase-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: sk-chase-dot 2.0s infinite ease-in-out both;
}
    
.sk-chase-dot:before {
    content: '';
    /* display: block; */
    width: 25%;
    height: 25%;
    background-color: #000;
    border-radius: 100%;
    animation: sk-chase-dot-before 2.0s infinite ease-in-out both;
}
    
.sk-chase-dot:nth-child(1) { animation-delay: -1.1s; }
.sk-chase-dot:nth-child(2) { animation-delay: -1.0s; }
.sk-chase-dot:nth-child(3) { animation-delay: -0.9s; }
.sk-chase-dot:nth-child(4) { animation-delay: -0.8s; }
.sk-chase-dot:nth-child(5) { animation-delay: -0.7s; }
.sk-chase-dot:nth-child(6) { animation-delay: -0.6s; }
.sk-chase-dot:nth-child(1):before { animation-delay: -1.1s; }
.sk-chase-dot:nth-child(2):before { animation-delay: -1.0s; }
.sk-chase-dot:nth-child(3):before { animation-delay: -0.9s; }
.sk-chase-dot:nth-child(4):before { animation-delay: -0.8s; }
.sk-chase-dot:nth-child(5):before { animation-delay: -0.7s; }
.sk-chase-dot:nth-child(6):before { animation-delay: -0.6s; }
    
@keyframes sk-chase {
    100% { transform: rotate(360deg); }
}
    
@keyframes sk-chase-dot {
    80%, 100% { transform: rotate(360deg); }
}
    
@keyframes sk-chase-dot-before {
    50% {
        transform: scale(0.4);
    } 100%, 0% {
        transform: scale(1.0);
    }
}
.form-renderer-container {
    /* margin: 24px auto; */
    width: 100%;
    height: 100%;
    // overflow-y: scroll;
    // overflow-x: scroll;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;
    border: 1px solid #e2dfdf;
    border-radius: 5px;
    // background-color: white;
    transition: height 0.3s ease-in-out, width 0.3s ease-in-out;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .form-renderer-container::-webkit-scrollbar {
    display: none;
  }
  .custom-width {
    width: 14rem;
  }
  .landscape {
    rotate: -90deg !important;
  }
  
  /* .round-button-landscape {
    border: 1px solid #e2dfdf;
    border-radius: 50%;
    height: 40px;
    width: 40px;
    margin: 10px auto;
    background-color: white;
  } */
  .device-view-portrait {
    margin: 130px auto 24px auto;
    padding: 60px 12px;
    border: 1px solid #e2dfdf;
    border-radius: 34px;
    // background-color: #F0F4FB;
    background-color: var(--surface-b);
    transition: all 0.3s ease-in-out;
  }
  .device-view-landscape {
    margin: 130px auto 24px auto;
    padding: 12px 60px;
    position: relative;
    transform-origin: top;
    border: 1px solid #e2dfdf;
    border-radius: 34px;
    background-color: var(--surface-b);
    transition: all 0.3s ease-in-out;
  }
  .round-button-portrait {
    border: 1px solid #e2dfdf;
    border-radius: 50%;
    height: 40px;
    width: 40px;
    margin: 8px auto;
    background-color: white;
  }
  .round-button-landscape-tablet:before {
    border: 1px solid #e2dfdf;
    border-radius: 50%;
    content: "";
    /* display: inline-block; */
    height: 40px;
    width: 40px;
    position: absolute;
    right: 8px;
    top: 379px;
    background-color: white;
  }
  .round-button-landscape-mobile:before {
    border: 1px solid #e2dfdf;
    border-radius: 50%;
    content: "";
    /* display: inline-block; */
    height: 40px;
    width: 40px;
    position: absolute;
    right: 10px;
    top: 182px;
    background-color: white;
  }
  .round-button-portrait-mobile:before {
    border: 1px solid #e2dfdf;
    border-radius: 50%;
    content: "";
    /* display: inline-block; */
    height: 40px;
    width: 40px;
    position: absolute;
    right: 13px;
    top: 140px;
    background-color: white;
  }
  .custom-device {
    margin: 130px auto 24px auto;
    padding: 5px;
  }
  /* .orintation-button .p-button-icon {
    font-size: 18px;
  } */
  .orintation-button {
    font-size: 22px;
    margin: 4px 8px;
    color: #818181;
    cursor: pointer;
    font-weight: 500;
  }
  .mobile-responsive {
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
      flex: none !important;
      max-width: none !important;
    }
  } 
  .custom-toolbar {
    position: fixed;
    top: 0;
    z-index: 9999;
    width: 100%;
    box-shadow: 0px 1px 7px rgba(0, 0, 0, 0.1), 0px 4px 5px -2px rgba(0, 0, 0, 0.12), 0px 10px 15px -5px rgba(0, 0, 0, 0.2) !important;
    background: var(--headerBackground) !important;
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
    padding: 1.25rem;
  }

  .p-toolbar-group-end span {
    color: var(--surface-900) !important;
  }

  #formio {
    padding: 10px;
  }

  .ui.selection.dropdown { min-width: auto !important;}

  :host {
    --fontName: 'Inter';
  }

  .formio-wrapper {
    font-family: var(--fontName) !important;
  }

.form-with-card {
  padding: 15px;
  border-radius: 5px;
  background-color: var(--cardBbackground) !important;
}

.form-without-card {
  padding: 0px 5px;
}

::ng-deep p-toolbar .p-toolbar {
  background: var(--headerBackground) !important;
  border: none !important;
  border-top-left-radius: 0px !important;
  border-top-right-radius: 0px !important;
}