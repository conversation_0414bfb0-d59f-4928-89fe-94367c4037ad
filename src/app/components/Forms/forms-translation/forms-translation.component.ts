import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import JSONEditor from 'jsoneditor';

@Component({
  selector: 'app-forms-translation',
  templateUrl: './forms-translation.component.html',
  styleUrls: ['./forms-translation.component.scss']
})
export class FormsTranslationComponent implements OnInit, AfterViewInit {
  public formid: string;
  public form: any;
  public errmsg: string;
  public editor;
  public container;
  public options = {
    modes: ['text', 'code', 'tree', 'form', 'view'],
    mode: 'code'
  };
  constructor(private formsservice: FormsService, public route: ActivatedRoute, public data: DynamicDialogConfig,private messageService: MessageService) { }

  ngOnInit() {
    // this.formid = this.route.parent.snapshot.paramMap.get('formid');
    this.formid = this.data.data.formId;
  }
  public gettranslations() {
    this.formsservice.getform(this.formid, 'translations').subscribe((form: any) => {
      // this.form = JSON.parse(form);
      // console.log(form, typeof form);
      this.editor.set(form.translations);
    });
  }
  ngAfterViewInit() {
    this.container = document.getElementById('jsoneditor');
    this.editor = new JSONEditor(this.container, this.options);
    this.gettranslations();
  }
  saveTranslations() {
    this.formsservice.savetranslations(this.formid, JSON.stringify(this.editor.get())).subscribe(
      (res) => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.gettranslations();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully saved!' });
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      }
    );
  }
}
