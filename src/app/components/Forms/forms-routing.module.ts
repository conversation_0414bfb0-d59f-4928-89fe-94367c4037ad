import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormListComponent } from './form-list/form-list.component';
import { FormioBuilderComponent } from './form-builder/form-builder.component';
import { roleGuard } from 'src/app/auth/guards/role.guard';

const routes: Routes = [
  {
    path: '',
    component: FormListComponent,
    data: { role: ['Admin', 'Developer'] },
    canActivate: [roleGuard],
  },
  {
    path: 'form-builder/:data',
    component: FormioBuilderComponent,
    data: { role: ['Admin', 'Developer'] },
    canActivate: [roleGuard],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FormsRoutingModule { }
