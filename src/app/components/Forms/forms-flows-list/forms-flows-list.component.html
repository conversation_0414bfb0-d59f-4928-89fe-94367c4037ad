<p-confirmPopup></p-confirmPopup>
<a href="https://docs.unvired.com/builder/admin/forms/#flows" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="flow">

  <div *ngIf="isDraft" class="col-12 p-0">
    <p-messages [(value)]="draftMessage" [enableService]="false" [closable]="false"></p-messages>
  </div>

<!-- Create Flow -->
  <p-accordion styleClass="mb-3" *ngIf="isCreate">
    <p-accordionTab [selected]="true" >
      <ng-template pTemplate="header">
          <span>{{actionName}}</span>
      </ng-template>
      <ng-template pTemplate="content">
        <app-create-flow [childData]="createFlowData" (eventEmitter)="emittedEvent($event)"></app-create-flow>
      </ng-template>
    </p-accordionTab>
  </p-accordion>

<!-- Flow Table -->
  <p-table *ngIf="!isCreate" #dt
    [value]="flowsArr" dataKey="wfId"
    responsiveLayout="scroll" 
    [scrollable]="true" 
    selectionMode="multiple" 
    [(selection)]="selectedFlows"
    styleClass="p-datatable-sm p-datatable-gridlines"
    [paginator]="true"
    [rows]="5"
    (onRowCollapse)="onRowCollapse()" 
    (onRowExpand)="onRowExpand()" 
    rowExpandMode="single"
    [tableStyle]="{ 'min-width': '50rem' }"
    [rowsPerPageOptions]="[5, 10, 25]"
    paginatorDropdownAppendTo="body"
    [globalFilterFields]="['wfTitle','wfDesc','status']">

    <ng-template pTemplate="caption">
      <div class="flex flex-row align-items-center justify-content-between">
        <div>
              <span class="p-input-icon-left">
                  <i class="pi pi-search"></i>
                  <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="search" [(ngModel)]="searchQuery"
                  (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
              </span>
              <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                  (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
              </button>
        </div>
        <div>
          <button pButton (click)="removeMultipleFlows($event)" icon="pi pi-trash" class="p-button-sm p-button-danger mr-2" [disabled]="selectedFlows?.length === 0"></button>
          <button pButton (click)="showFlowMenu(FlowMenu,$event)" [disabled]="isCreate" label="Create" icon="pi pi-angle-down" iconPos="right" class="p-button-sm bg-blue"></button>
        </div>
      </div>
    </ng-template>

<!-- Header -->
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
            <th style="min-width: 10rem">Title</th>
            <th style="min-width: 10rem">Description </th>
            <th>Status </th>
            <th>Type </th>
            <th style="min-width: 10rem">Last Updated </th>
            <th style="min-width: 10rem" class="text-center">Actions</th>
        </tr>
    </ng-template>

<!-- Table data -->
    <ng-template pTemplate="body" let-flow let-columns="columns" let-expanded="expanded">
        <tr class="cursor-pointer" (click)="goToFlow(flow)">
            <td class="text-center">
                <p-tableCheckbox [value]="flow" (click)="$event.stopPropagation()"></p-tableCheckbox>
            </td>
            <td>
              <button pButton class="p-button-text p-button-sm text-left" [ngClass]="[!flow.disabled && flow.published ? 'p-button-success' : '',flow.disabled ? 'p-button-danger' : '', !flow.disabled && !flow.published ? 'p-button-help' : '']">
                <span class="px-1">{{flow.wfTitle}}</span>
              </button>
            </td>
    
            <td>
                {{flow.wfDesc}}
            </td>
    
            <td>
                {{flow.published ? 'Deployed' : 'Not Deployed'}} {{flow.disabled ? ' - Disabled' : ' - Enabled'}}
            </td>

            <td>
                {{flow.actionFlowType === "Form Action Flow" ? flow.actionFlowType + ' - ' + flow.deploymentType : flow.actionFlowType}}
            </td>
    
            <td>
              <div class="flex flex-row justify-content-start">
                <span>{{flow.updateBy}}, <span *ngIf="flow?.lastUpdated" class="ml-1">{{formatDate(flow.lastUpdated | date: 'MM/dd/yyyy h:mm a')}}</span></span> 
              </div>
            </td>
    
            <td>
                <div class="flex flex-row justify-content-center">
                    <button pButton (click)="updateFlow(flow, dt, $event);$event.stopPropagation();dt.toggleRow(flow, $event)" [disabled]="isDraft" class="p-button-rounded p-button-text" icon="pi pi-pencil" pTooltip="Update Flow" id="{{'rowExpansion'+ flow.wfId}}"></button>
                    <button pButton (click)="copyFlow(flow);$event.stopPropagation()" class="p-button-rounded p-button-text mr-2" [disabled]="isCreate" icon="pi pi-copy" pTooltip="Copy Flow"></button>
                </div>
            </td>
        </tr>
    </ng-template>

<!-- Row Expand -->
    <ng-template pTemplate="rowexpansion" let-element>
      <tr>
        <td colspan="12">
          <app-create-flow [childData]="createFlowData" (eventEmitter)="emittedEvent($event)"></app-create-flow>
        </td>
      </tr>
    </ng-template>

<!-- No data -->
    <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-center" style="font-weight: bold;" colspan="7">No Flows Created.</td>
        </tr>
    </ng-template>

  </p-table>



    <!-- <p-dataView #dv [value]="flowsArr" [rows]="5" [paginator]="true" filterBy="wfTitle,wfDesc,status">
      <ng-template pTemplate="header">
          <div class="flex flex-row justify-content-between">
            <div>
              <span class="p-input-icon-left">
                <i class="pi pi-search"></i>
                <input style="width: 21rem;" class="p-inputtext-sm" pInputText type="search" [disabled]="flowsArr?.length === 0"
                (input)="dv.filter($event.target.value)" placeholder="Search..." />
              </span>
              <button pButton (click)="removeMultipleFlows($event)" icon="pi pi-trash" class="p-button-sm p-button-danger ml-2" [disabled]="selectedFlows?.length === 0"></button>
            </div>
            <button pButton (click)="FlowMenu.show($event)" label="Create" icon="pi pi-angle-down" iconPos="right" class="p-button-sm bg-blue"></button>
          </div>
          <div *ngIf="isCreate" class="col-12 bordar p-0 my-3">
            <h5 class="m-2">{{actionName}} Flow</h5>
            <hr class="m-0">
            <div class="col-12 py-0">
              <app-create-workflow-dialog [childData]="createFlowData" (eventEmitter)="emittedEvent($event)"></app-create-workflow-dialog>
  
            </div>
          </div>
      </ng-template>
      <ng-template let-flow pTemplate="listItem">
          <div class="col-12 flow-list" (click)="goToFlow(flow)">
              <div class="flex flex-row align-items-center p-2 gap-3">
                  <p-checkbox [value]="flow" [(ngModel)]="selectedFlows" [inputId]="flow.wfId" (click)="$event.stopPropagation()"></p-checkbox>
                  <i class="pi pi-circle-fill" [ngClass]="[flow.status === 'Deployed' ? 'status-deployed' : '']"></i>
                  <span class="material-icons">{{flow.avatar}}</span>
                  <div class="flex flex-row justify-content-between align-items-center flex-1">
                      <div class="flex flex-column align-items-start gap-1"> 
                          <div class="flow-Title">{{ flow.wfTitle }}</div>
                          <div class="flow-subtext">{{ flow.wfDesc }}</div>
                      </div>
                      <div class="flex flex-column align-items-end gap-1">
                          <span>{{ flow.status }}</span>
                          <div *ngIf="flow?.lastUpdated" class="flex align-items-center gap-1">
                              <i class="pi pi-clock text-400"></i>
                              <span class="flow-subtext">Last updated on {{ formatDate(flow.lastUpdated) }}</span>
                          </div>
                      </div>
                  </div>
                  <button pButton icon="pi pi-ellipsis-v" class="p-button-text p-button-rounded p-button-plain p-button-sm" (click)="ActionMenu.show($event);$event.stopPropagation();getFlowDetails(flow)"></button>
              </div>
          </div>
      </ng-template>
      <ng-template pTemplate="empty">
          <div class="col-12 text-center">
              <span>No results found.</span>
          </div>
      </ng-template>
    </p-dataView> -->
  </div>
  
  
  <!-- Action menu -->
  <!-- <p-overlayPanel #ActionMenu styleClass="p-0" [style]="{'width': '150px'}">
    <ul class="custom-ul">
      <li class="custom-li" (click)="copyFlow(ActionMenu)"><i class="pi pi-copy menu-icons"></i>Copy</li>
      <li class="custom-li" (click)="updateFlow(ActionMenu)"><i class="pi pi-pencil menu-icons"></i>Update</li>
    </ul>
  </p-overlayPanel> -->
  
  <!-- Flow create menu -->
  <p-overlayPanel #FlowMenu [style]="{'max-width': '400px'}">
    <ul class="custom-ul">
      <li class="custom-li" (click)="createNewFlow(FlowMenu,'Manual Flow')"><i class="pi pi-bolt menu-icons"></i>Manual Flow</li>
      <li class="custom-li" (click)="createNewFlow(FlowMenu,'Webhook Flow')"><i class="pi pi-external-link menu-icons"></i>Webhook Flow</li>
      <li class="custom-li" (click)="createNewFlow(FlowMenu,'Scheduled Flow')"><i class="pi pi-clock menu-icons"></i>Scheduled Flow</li>
      
      <li *ngIf="formType.toLowerCase() !== 'masterdata'" class="custom-li" (click)="createNewFlow(FlowMenu,'Form Action Flow')"><i class="pi pi-file menu-icons"></i>Form Action Flow</li>
      <li *ngIf="formType.toLowerCase() !== 'masterdata'" class="custom-li" (click)="createNewFlow(FlowMenu,'Sub Flow')"><i class="pi pi-sitemap menu-icons"></i>Sub Flow</li>
      <li *ngIf="formType.toLowerCase() !== 'masterdata'" class="custom-li" (click)="createNewFlow(FlowMenu,'Smart Data Flow')"><i class="pi pi-sync menu-icons"></i>Smart Data Flow</li>
    </ul>
  </p-overlayPanel>