.flow-list {
    /* margin-bottom: 8px; */
    border: none !important;
  }
  .flow-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  .flow-Title {
    font-size: 16px;
    font-weight: 600;
  }
  .flow-subtext {
    font-size: 14px;
    font-weight: 400;
  }
  .custom-ul {
    list-style-type: none !important;
    padding-left: 0px !important;
    margin-bottom: 5px;
    margin-top: 5px;
  }
  .custom-li {
    display: flex;
    align-items: center;
    cursor: pointer !important;
    padding: 8px;
  }
  .menu-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  .custom-li:hover {
    background-color: #e9ecef;
    border-radius: 5px;
  }
  .status-deployed {
    color: lightgreen;
  }
  ::ng-deep .flow p-dataview .p-dataview-header {
    padding: 0px !important;
    background: none !important;
    border: none !important;
    margin-bottom: 12px !important;
  }

  ::ng-deep .flow p-dataview .p-paginator-bottom {
    border: none !important;
  }

  .bordar {
    border: 1px solid #dee2e6!important;
    border-radius: 5px;
  }

  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }