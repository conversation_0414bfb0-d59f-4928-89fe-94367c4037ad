import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, Message, MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { ReteService } from 'src/app/services/rete.service';
import * as dayjs from 'dayjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-forms-flows-list',
  templateUrl: './forms-flows-list.component.html',
  styleUrls: ['./forms-flows-list.component.scss']
})
export class FormsFlowsListComponent implements OnInit {
  formid: string;
  formType: string;
  errmsg: string;
  newFormType:string='form';
  flowsArr: any[] = [];
  selectedFlows: any = [];
  selectedSinglFlow: any;
  isCreate: boolean = false;
  createFlowData: any;
  actionName: string;
  isRowExpand: boolean = false;
  flowId: number;
  isDraft: boolean;
  draftMessage: Message[];
  formTitle: any;
  searchQuery: string;
  
  constructor(
    private formsservice: FormsService,
    public route: ActivatedRoute,
    private router: Router,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private reteservice: ReteService,
    private ref: DynamicDialogRef,
    public layoutService: LayoutService,
  ) {}

  ngOnInit(): void {
    this.formid = this.data.data.formId;
    this.formType = this.data.data.formType;
    this.isDraft = this.data.data.isDraft;
    this.formTitle = this.data.data.formTitle;
    this.getworkflow();
    if (this.isDraft) {
      this.draftMessage = [{ severity: 'warn', summary: 'Waning: ', detail: 'Publish the form to perform any action.' }];
    }
  }

  getworkflow() {
    this.formsservice.getAllFlows(this.formid, this.formType).subscribe(response => {
      if (response.error === '') {
        if (response.status.toLowerCase() === 'success') {
          this.flowsArr = response.workflows;
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  // createworkflow() {
  //   const formData={
  //     formId:this.formid,
  //     formType:this.newFormType
  //   }
  //   const dialogRef = this.matDialog.open(CreateWorkflowDialogComponent, {
  //     minWidth: '40%',
  //     panelClass: 'custom-dialog',
  //     data: {
  //       type:'workflow',
  //       iscopy: false,
  //       isupdate: false,
  //       create:false,
  //       add:true,
  //       formData:formData  
  //     },
  //   });
  //   dialogRef.afterClosed().subscribe((response) => {
  //     if (response) {
  //       this.router.navigate(['forms/',this.formid,`workflows`]);//{wfTitle:response.wfTitle,wfId:response.wfId, formType:response.formType, formId:response.formId}
  //       this.getworkflow();
  //     }
  //   });
  // }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  getFlowDetails(flow: any) {
    this.selectedSinglFlow = flow;
  }

  createNewFlow(element: any, type: string) {
    this.createFlowData = {
      type:'workflow',
      iscopy: false,
      isupdate: false,
      create: false,
      add: true,
      formData: { formId: this.formid, formType: this.formType === 'Masterdata' ? 'masterdata' : 'form' },
      displayType: 'accordian',
      flowType: type,
    }
    element.hide();
    this.isCreate = true;
    this.actionName = 'Create ' + type;
  }

  copyFlow(flowData: any) {
    this.createFlowData = {
      type: "workflow",
      iscopy: true,
      isupdate: false,
      create: false,
      add: false,
      wfdata: flowData,
      flowType: flowData.actionFlowType,
      displayType: 'accordian',
      scheduleId: flowData.scheduleId
    }
    this.actionName = 'Copy Flow';
    this.isCreate = true;
  }

  updateFlow(flowData: any) {
    this.flowId = flowData.wfId;
    this.createFlowData = {
      type: "workflow",
      isupdate: true,
      iscopy: false,
      create: false,
      add: false,
      wfdata: flowData,
      flowType: flowData.actionFlowType,
      displayType: 'accordian',
      scheduleId: flowData.scheduleId
    }
    this.actionName = 'Update Flow';
  }

  goToFlow(data: any) {
    if (!this.isDraft) {
      if (this.layoutService.state.staticMenuDesktopInactive === false) {
        this.layoutService.state.staticMenuDesktopInactive = true;
      }
      let obj = {
        wfTitle: data.wfTitle,
        wfName: data.wfName,
        wfId: data.wfId,
        formType: this.formType,
        formId: this.formid,
        flowType: data.actionFlowType,
        formTitle: this.formTitle
      }
      // this.layoutService.onMenuToggle();
      this.ref.close();
      const routerData = encodeURIComponent(JSON.stringify(obj));
      this.router.navigate([`flows/flow-editor/${routerData}`]);
      // window.open(location.protocol + '//' + location.host + `/workflow-editor/${routerData}`, '_blank');
    }
  }

  removeMultipleFlows(event?: Event) {
    let completedCount = 0;
    let totalCount = this.selectedFlows.length;
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove flows?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        if (this.selectedFlows.length > 0) {
          for (let i=0; this.selectedFlows.length > i; i++) {
            this.reteservice.archiveworkflow(this.selectedFlows[i].wfId).subscribe((response: any) => {
              if (response.error == "") {
                completedCount += 1;
                if (completedCount === totalCount) {
                  this.selectedFlows = [];
                  this.getworkflow();
                  completedCount = 0;
                }
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
            });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please slect forms.' });
        }
      },
      reject: () => {}
    });
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.getworkflow();
    this.searchQuery = null;
  }

  getHelpText(str: string): string {
    let text: string;
    if (str === 'Manual Flow') {
      return text = 'Trigger the flow in the builder via the Run button.'
    } else if (str === 'Form Action Flow') {
      return text = 'Trigger the flow on creation of the form (used to prefill data), on partial save of the form data or on completion.'
    } else if (str === 'Sub Flow') {
      return text = 'Trigger the flow as part of another flow.'
    } else if (str === 'Smart Data Flow') {
      return text = "Trigger the flow on user's action in a form."
    } else if (str === 'Scheduled Flow') {
      return text = 'Trigger the flow on a preset schedule.'
    } else if (str === 'Webhook Flow') {
      return text = 'Trigger the flow by posting a request to the webhook URL.'
    } else if (str === 'Manual Flow') {
      return text = 'Trigger the flow when master data needs to be returned to the app.'
    } else {
      return text = '';
    }
  }

  emittedEvent(event: any) {
    if (event?.wfId) {
      // need to redirect to work flow screen.
      this.goToFlow(event);
      this.closeExpandedRow(event.wfId);
    }
    this.getworkflow();
    this.isCreate = false;
    this.closeExpandedRow(this.flowId);
  }

  onRowCollapse() {
    this.isRowExpand = false;
  }

  onRowExpand() {
    this.isRowExpand = true;
  }

  closeExpandedRow(id: any) {
    if (this.isRowExpand) {
      document.getElementById('rowExpansion'+id).click();
    }
    this.flowId = null;
  }

  showFlowMenu(element: any, event: any) {
    if (this.isDraft) {
      this.messageService.add({ severity: 'warn', summary: 'Warning', detail: 'Publish the form to perform any action.' });
    } else {
      element.show(event);
    }
  }
  
}
