import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsRoutingModule } from './forms-routing.module';
import { FormListComponent } from './form-list/form-list.component';
import { CreateFormComponent } from './create-form/create-form.component';
import { FormsVersionComponent } from './forms-version/forms-version.component';
import { FormsAssignmentsComponent } from './forms-assignments/forms-assignments.component';
import { FormsFormsetsComponent } from './forms-formssets/forms-formsets.component';
import { FormsDocumentsComponent } from './forms-documents/forms-documents.component';
import { FormsEnvironmentComponent } from './forms-environment/forms-environment.component';
import { FormsTranslationComponent } from './forms-translation/forms-translation.component';
import { FormsSchedulesComponent } from './forms-schedules/forms-schedules.component';
import { FormsSharingComponent } from './forms-sharing/forms-sharing.component';
import { FormsMasterdataComponent } from './forms-masterdata/forms-masterdata.component';
import { CreateFlowComponent } from './create-flow/create-flow.component';
import { FormsFlowsListComponent } from './forms-flows-list/forms-flows-list.component';
import { NestedFormsListComponent } from './nested-forms-list/nested-forms-list.component';
import { FormPreviewComponent } from './form-preview/form-preview.component';
import { PublicSharedFormDataComponent } from './public-shared-form-data/public-shared-form-data.component';
import { FormioBuilderComponent } from './form-builder/form-builder.component';
import { SharedModule } from '../../shared/shared.module';
import { FormioModule } from '@formio/angular';
import {SidebarModule} from 'primeng/sidebar';
import { BarcodeDialogComponent } from './barcode-dialog/barcode-dialog.component';
import { AngularSplitModule } from 'angular-split';
import { LayoutsAndTemplatesComponent } from './layouts-and-templates/layouts-and-templates.component';
import { FormTemplatesComponent } from './form-templates/form-templates.component';
import { FormControlComponent } from './form-control/form-control.component';
import { FormPdfSettingsComponent } from './form-pdf-settings/form-pdf-settings.component';

@NgModule({
  declarations: [
    FormListComponent,
    CreateFormComponent,
    FormsVersionComponent,
    FormsAssignmentsComponent,
    FormsFormsetsComponent,
    FormsDocumentsComponent,
    FormsEnvironmentComponent,
    FormsTranslationComponent,
    FormsSchedulesComponent,
    FormsSharingComponent,
    FormsMasterdataComponent,
    CreateFlowComponent,
    FormsFlowsListComponent,
    NestedFormsListComponent,
    FormPreviewComponent,
    PublicSharedFormDataComponent,
    FormioBuilderComponent,
    BarcodeDialogComponent,
    LayoutsAndTemplatesComponent,
    FormTemplatesComponent,
    FormControlComponent,
    FormPdfSettingsComponent
  ],
  imports: [
    CommonModule,
    FormsRoutingModule,
    SharedModule,
    FormioModule,
    SidebarModule,
    AngularSplitModule
  ]
})
export class FormsModule { }
