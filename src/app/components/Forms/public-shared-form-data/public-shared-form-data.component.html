<p-toolbar *ngIf="sharedName" class="sticky top-0 z-5" styleClass="shadow-5">
    <div class="p-toolbar-group-start">
      <div class="flex flex-column">
        <h4 class="mb-1">Submision report</h4>
        <h5 class="m-0">{{ sharedName }}</h5>
      </div>
    </div>
</p-toolbar>
  
  
  <div class="m-3" #paginator>
    <div class="flex flex-row justify-content-between w-25 my-4">
      <p-calendar [(ngModel)]="rangeDates" selectionMode="range" [maxDate]="maxDate" [readonlyInput]="true" styleClass="w-full" class="w-full"></p-calendar>
      <button pButton type="button" (click)="getPublicSharedFormsInfo(subDataPagination)" label="Search" class="p-button-sm bg-blue ml-2"></button>
    </div>
  
    <p-table [value]="dataSourcePublicForms" selectionMode="single" responsiveLayout="scroll" scrollHeight="50vh" styleClass="p-datatable-gridlines p-datatable-sm">
      <ng-template pTemplate="header">
          <tr>
              <th class="text-align-center">Submission Date</th>
              <th class="">Submission ID</th>
          </tr>
      </ng-template>
      <ng-template pTemplate="body" let-item>
          <tr class="preview" (click)="goToFormPreview(item)">
              <!-- Date -->
              <td><span>{{ item.submissionDate | date: "MMM d, y, h:mm:ss a" }}</span></td>
              <!-- Id -->
              <td><span>{{ item.submissionId }}</span></td>
          </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <p-paginator class="p-0" [rows]="10" [totalRecords]="subDataLength" [showJumpToPageDropdown]="false" [dropdownAppendTo]="paginator"
          (onPageChange)="getPublicSharedFormsInfo($event)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
        </p-paginator>
      </ng-template>
      <ng-template *ngIf="!(dataSourcePublicForms.length > 0)" pTemplate="emptymessage">
        <tr class="text-center">
          <td colspan="2" class="text-center">No Data found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>