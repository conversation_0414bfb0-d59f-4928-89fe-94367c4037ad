import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { FormsService } from 'src/app/services/forms.service';
import { Location } from '@angular/common';
import * as dayjs from 'dayjs';
import * as relativeTime from 'dayjs/plugin/relativeTime';
import { MessageService } from 'primeng/api';
dayjs.extend(relativeTime);

@Component({
  selector: 'app-public-shared-form-data',
  templateUrl: './public-shared-form-data.component.html',
  styleUrls: ['./public-shared-form-data.component.scss']
})
export class PublicSharedFormDataComponent implements OnInit {

  public searchpublicforms: FormGroup;
  public formid: string;
  public shareid: string;
  public maxDate = new Date();
  public errmsg: string;
  form: any;
  shareDesription: string;
  public dataSourcepublicforms = [];
  public displayedColumns: string[] = [
    'submissionDate',
    'submissionId',
  ];
  // MatPaginator Inputs
  length: any;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];
  pageIndex = 0;
  // MatPaginator Output

  routerData: any;
  dataSourcePublicForms: any = [];
  subDataPageSize: number = 0;  //offset
  subDataPageIndex: number = 5;  //limit
  subDataPagination: any;
  subDataLength: number = 0;
  sharedName: string;
  rangeDates: any = [];

  constructor(
    private fb: FormBuilder,
    private formservice: FormsService,
    private router: Router,
    private route: ActivatedRoute,
    private _location: Location,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.rangeDates = [new Date(dayjs().startOf('month').format('MM/DD/YYYY')),this.maxDate];
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.routerData = decodeURIComponent(params.get('data') || "");
      this.routerData = JSON.parse(this.routerData);
      this.formid = this.routerData.formId;
      this.shareid = this.routerData.shareId;
      this.sharedName = this.routerData.name;
    });
    this.getPublicSharedFormsInfo(null);
    this.searchpublicforms = this.fb.group({
      startDate: [new Date(), Validators.required],
      endDate: [new Date(), Validators.required]
    });
  }

  goBack() {
    this._location.back();
  }

  getPublicSharedFormsInfo(page: any) {
    this.subDataPagination = page;
    this.subDataPageSize = this.subDataPagination ? this.subDataPagination.page * this.subDataPagination.rows : 0;
    this.subDataPageIndex = this.subDataPagination ? this.subDataPagination.rows : 5;
    const serachobj = {};
    const startDate = new Date(this.rangeDates[0]);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(this.rangeDates[1]);
    endDate.setHours(23, 59, 59, 999);

    serachobj['startDate'] = startDate;
    // this.searchpublicforms.get('startDate').setValue(startDate);
    serachobj['endDate'] = endDate;
    // this.searchpublicforms.get('endDate').setValue(endDate);
    
    this.formservice.getpublicsharedformsinfo(this.formid, this.shareid, this.subDataPageSize, this.subDataPageIndex, serachobj)
    .subscribe(res => {
      if (res.status.toLowerCase() === 'success') {
        this.subDataLength = res.totalRecords;
        this.subDataPageIndex = res.nextOffSet;
        this.dataSourcePublicForms = res.data;
      } else {
        this.errmsg = res.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: res.error });
      }
    });
  }

  goToFormPreview(shareData: any) {
    if (shareData?.shareID && shareData.submissionId) {
      console.log(shareData)
      let data = {
        name: shareData.name,
        submissionId: shareData.submissionId,
        shareId: shareData.shareID,
        readOnly: true
      }
      const routerData = encodeURIComponent(JSON.stringify(data));
      // window.open(location.protocol + '//' + location.host + `/test-form/${routerData}`, '_blank');
      let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
      let baseUrl = window.location.href.replace(this.router.url, '');
      window.open(baseUrl + newRelativeUrl, '_blank');
    } else {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: "Missing submission ID or share ID." });
    }
  }


}
