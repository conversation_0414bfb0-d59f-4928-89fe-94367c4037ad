import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { ConfirmationService, MenuItem, Message, MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { Subject, debounceTime } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { whiteSpaceValidator } from 'src/app/shared/directives/white-space-validator.directive';
import { CreateFormComponent } from '../create-form/create-form.component';
import { FormsVersionComponent } from '../forms-version/forms-version.component';
import { FormsAssignmentsComponent } from '../forms-assignments/forms-assignments.component';
import { FormsFormsetsComponent } from '../forms-formssets/forms-formsets.component';
import { FormsDocumentsComponent } from '../forms-documents/forms-documents.component';
import { FormsEnvironmentComponent } from '../forms-environment/forms-environment.component';
import { FormsTranslationComponent } from '../forms-translation/forms-translation.component';
import { FormsSchedulesComponent } from '../forms-schedules/forms-schedules.component';
import { FormsSharingComponent } from '../forms-sharing/forms-sharing.component';
import { FormsMasterdataComponent } from '../forms-masterdata/forms-masterdata.component';
import { FormsFlowsListComponent } from '../forms-flows-list/forms-flows-list.component';
import { Table } from 'primeng/table';
import { NestedFormsListComponent } from '../nested-forms-list/nested-forms-list.component';
import * as dayjs from 'dayjs';
import { UsersTeamsService } from 'src/app/services/users-teams.service';
dayjs.extend(relativeTime);
import * as relativeTime from 'dayjs/plugin/relativeTime';
import { FormTemplatesComponent } from '../form-templates/form-templates.component';
import { FormControlComponent } from '../form-control/form-control.component';
import { FormPdfSettingsComponent } from '../form-pdf-settings/form-pdf-settings.component';

@Component({
  selector: 'app-form-list',
  templateUrl: './form-list.component.html', 
  styleUrls: ['./form-list.component.scss'],
  providers: [DialogService]
})
export class FormListComponent implements OnInit, OnDestroy {
  public isClicked = false;
  public filled = false;
  public categories: any;
  public errmsg: string;
  public iscategoryformsempty: boolean;
  formCategory: string = 'All';
  sharedData: string;
  listactive = true;
  forms: any[];
  formActions =  [
    {
      label: 'Copy',
      key: 'copy',
      icon: 'pi pi-copy'
    },
    {
      label: 'Update',
      key: 'update',
      icon: 'pi pi-pencil'
    },
    {
      label: 'Delete',
      key: 'delete',
      icon: 'pi pi-trash'
    },
    {
      label: 'Configuration',
      key: 'configuration',
      icon: 'pi pi-cog'
    },
  ];
  // formMenu =  [
  //   {
  //     label: 'Form',
  //     key: 'form',
  //     icon: 'pi pi-file-o'
  //   },
  //   {
  //     label: 'Nested Form',
  //     key: 'nestedForm',
  //     icon: 'pi pi-clone'
  //   },
  //   {
  //     label: 'Wizard',
  //     key: 'wizard',
  //     icon: 'pi pi-th-large'
  //   },
  //   {
  //     label: 'Masterdata',
  //     key: 'masterdata',
  //     icon: 'pi pi-server'
  //   }
  // ];
  categoryMenu =  [
    {
      label: 'Create',
      key: 'AddCategoryPanel',
      icon: 'pi pi-plus'
    },
    {
      label: 'Apps',
      key: 'AssociatedAppsPanel',
      icon: 'pi pi-th-large'
    }
  ];
  
  breadcrumbItems : MenuItem[];
  currentCategory: any;
  selectedForms: any;
  newCategory: any;
  published: boolean = true;
  searchForm; any;
  associatedApps: string[] = [];
  displayCategory: string;
  showImportByFile: boolean = false;
  showImportByRemote: boolean = false;
  autoPublish: boolean = false;
  overwriteDraft: boolean = false;
  file: File;
  FileJsonObj = null;
  showPassword: boolean = false;
  remoteSystemForm: FormGroup;
  remoteFormList: any[] = [];
  selectedForm: any;
  masterDataArr: any;
  assignMultiCategory: any;
  multiCategoriesArr: any = [];
  isLoading:boolean = false;
  showCreateCategory: boolean = false;
  showAssociatedApps: boolean = false;
  importFileText: Message[];
  selectedFormsForImport: any;
  pageSize: number = 10;
  pageIndex: number = 0;
  sortField: string;
  sortOrder: string;
  tablePagination: any;
  sort: any;
  formLength: number = 0;
  userArr: any;
  filteredUID: any;
  selectedUser: any;//for filter table
  selectedCategory: any; //for filter table
  searchTerm: string;
  first: number = 0;
  menuItems: MenuItem[] | undefined;
  formNavigationData: any = {page:'', sort:'', uid: '', category: '', searchTerm: '', formId: '', formType: '', formTitle: '', isFlow: false, isNestedForm: false}; //need to store filtered items in from the form-list
  menuType: string;
  navEnd: any;
  searchSubject: Subject<string> = new Subject();
  showEmptyForm: boolean = false;
  templateList: any[] = [];
  includeFlow = false;

  constructor(
    private route: ActivatedRoute,
    private formservice: FormsService,
    private router: Router,
    private fb: FormBuilder,
    private messageService: MessageService,
    public dialogService: DialogService,
    private confirmationService: ConfirmationService,
    public layoutService: LayoutService,
    private usersService: UsersTeamsService,
  ) {}

  ngOnInit() {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.navEnd = event;
      }
    });
    this.formNavigationData = this.formservice.getNavigationData();
    if (this.formNavigationData) {
      if (this.formNavigationData.page || this.formNavigationData.sort ||
        this.formNavigationData.uid || this.formNavigationData.category || 
        this.formNavigationData.searchTerm) {
  
        this.getAllFormsAndMasterdata(this.formNavigationData?.page, 
          this.formNavigationData?.sort, 
          this.formNavigationData?.uid, 
          this.formNavigationData?.category,
          this.formNavigationData.searchTerm);
  
        // if (this.formNavigationData.isFlow) {   Removed isFlow flag from if condition above
        //   const ref = this.dialogService.open(FormsFlowsListComponent,{
        //     header: `Flows (${this.formNavigationData?.formTitle})`,
        //     width: '60%',
        //     contentStyle: { 'overflow-y': 'scroll' },
        //     data: {
        //       formId: this.formNavigationData.formId,
        //       formType: this.formNavigationData.formType
        //     }
        //   });
        //   this.formNavigationData.isFlow = false;
        //   this.formNavigationData.formId = '';
        //   this.formNavigationData.formTitle = '';
        //   this.formservice.setNavigationData(this.formNavigationData)
        // }

        // if (this.formNavigationData.isNestedForm) {
        //   const ref = this.dialogService.open(NestedFormsListComponent,{
        //     header: `Form list (${this.formNavigationData?.formTitle})`,
        //     width: '60%',
        //     // height: '65%',
        //     contentStyle: { 'overflow-y': 'scroll' },
        //     data: {
        //       formId: this.formNavigationData.formId,
        //     }
        //   });
        //   this.formNavigationData.isNestedForm = false;
        //   this.formNavigationData.formId = '';
        //   this.formNavigationData.formTitle = '';
        //   this.formservice.setNavigationData(this.formNavigationData)
        // }
      } else {
        this.getAllFormsAndMasterdata(null, null, null, null, null);
      }
    } else {
      this.getAllFormsAndMasterdata(null, null, null, null, null);
    }
    
    // this.formservice.sharedData$.subscribe(
    //   (sharedData) => (this.sharedData = sharedData)
    // );
   
    // if (this.formcategory === 'Masterdata') {
    //   await this.getMasterData();
    // } else {
      
    // }
    this.remoteSystemForm = this.fb.group({
      url: ['', Validators.required],
      emailId: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, whiteSpaceValidator()]]
    });

    // this.formCategory = this.route.snapshot.paramMap.get('formcategory');    // note: enable this code if category required from url
    // this.route.paramMap.subscribe((params: ParamMap) => {  
    //   this.formCategory = params.get('formcategory');
    //   if(!this.formCategory) {
    //     this.formCategory = 'All';
    //     console.log('calledd')
    //     this.navigateToCategory(this.formCategory);
    //   }
    // });
  
    // await this.getforms(this.formCategory);
    this.breadcrumbItems = [
      {label:'Home', routerLink: '/home'},
      {label:'Forms'}
    ];
    this.getFormsCategory();
    this.searchSubject.pipe( // only for search bar
      debounceTime(500) 
    ).subscribe(searchText => {
      const page = {
        first: 0,
        page: 0,
        rows: this.tablePagination && typeof this.tablePagination.rows === 'number' ? this.tablePagination?.rows : 10
      }
      this.getAllFormsAndMasterdata(page, null, null, null, this.searchTerm);
    });
  }

  checkDeveloperAccess(): boolean {    // developer can not access Assignment in production landscape
    const landsacpe = localStorage.getItem('landscape');
    const role = localStorage.getItem('Role');
    if (role.toLowerCase() === 'developer' && landsacpe.toLowerCase() === 'production') {
      return false
    } else {
      return true
    }
  }

  showFormActionMenu(form: any) {
    this.selectedForm = form;
    // setTimeout(() => {
      this.menuItems = [
        {
          label: 'Versions',
          icon: 'pi pi-undo',
          visible: true,
          command: () => {
           this.showRevisionDialog();
          }
        },
        // {
        //   label: 'Assignments',
        //   icon: 'pi pi-file-o',
        //   visible: ((this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard') && this.checkDeveloperAccess()) ? true : false,
        //   command: () => {
        //    this.showAssignmentDialog();
        //   }
        // },
        {
          label: 'Formsets',
          icon: 'pi pi-folder-open',
          visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard') ? true : false,
          command: () => {
           this.showFormsetsDialog();
          }
        },
        // {
        //   label: 'Flows',
        //   icon: 'pi pi-link',
        //   visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard' || this.selectedForm?.formType.toLowerCase() === 'masterdata') ? true : false,
        //   command: () => {
        //    this.showFlow();
        //   }
        // },
        {
          label: 'Documents',
          icon: 'pi pi-file-pdf',
          visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard') ? true : false,
          command: () => {
           this.showDocumentDialog();
          }
        },
        {
          label: 'Environment',
          icon: 'pi pi-globe',
          visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard') ? true : false,
          command: () => {
           this.showEnvironmentDialog();
          }
        },
        // {
        //   label: 'Share',
        //   icon: 'pi pi-share-alt',
        //   visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard') ? true : false,
        //   command: () => {
        //    this.showSharingDialog();
        //   }
        // },
        // {
        //   label: 'Data',
        //   icon: 'pi pi-database',
        //   visible: this.selectedForm?.formType.toLowerCase() === 'masterdata' ? true : false,
        //   command: () => {
        //    this.showMasterdataDialog();
        //   }
        // },
        {
          label: 'Schedules',
          icon: 'pi pi-clock',
          visible: (this.selectedForm?.formType.toLowerCase() === 'form' || this.selectedForm?.formType.toLowerCase() === 'wizard' || this.selectedForm?.formType.toLowerCase() === 'masterdata') ? true : false,
          command: () => {
           this.showSchedulesDialog();
          }
        },
        // {
        //   label: 'Forms',
        //   icon: 'pi pi-file-o',
        //   visible: this.selectedForm?.formType === 'Nested Form' ? true : false,
        //   command: () => {
        //    this.nestedFormsList();
        //   }
        // },
        {
          label: 'Translations',
          icon: 'pi pi-language',
          visible: (this.selectedForm?.formType === 'Form' || this.selectedForm?.formType === 'Wizard' || this.selectedForm?.formType === 'Nested Form') ? true : false,
          command: () => {
           this.showTranslationDialog();
          }
        },
        {
          separator: true
        },
        {
          label: 'Update',
          icon: 'pi pi-pencil',
          visible: true,
          command: () => {
            this.selectedForm?.formType.toLowerCase() === 'masterdata' ? this.updateMasterData() : this.updateForm();
          }
        },
        {
          label: 'Control',
          icon: 'pi pi-cog',
          visible: (this.selectedForm?.formType === 'Form' || this.selectedForm?.formType === 'Wizard') ? true : false,
          command: () => {
            this.showFormConfigurations();
          }
        },
        {
          label: 'PDF',
          icon: 'pi pi-file-pdf',
          visible: (this.selectedForm?.formType === 'Form' || this.selectedForm?.formType === 'Wizard') ? true : false,
          command: () => {
            this.showPDFSettings();
          }
        },
        {
          label: 'Copy',
          icon: 'pi pi-copy',
          visible: true,
          command: () => {
            this.selectedForm?.formType.toLowerCase() === 'masterdata' ? this.copyMasterData() : this.copyForm();
          }
        },
      ];
    // }, 400);
    
  }

  getAllFormsAndMasterdata(pagination: any, sort: any, userId: any, category: string, searchTerm: string) {
    this.sort = sort;
    this.filteredUID = userId;
    this.selectedCategory = category;
    this.searchTerm = searchTerm;

    this.sortField = sort?.field;
    this.sortOrder = sort?.order;

    this.tablePagination = pagination;
    this.pageIndex = this.tablePagination ? this.tablePagination?.page * this.tablePagination?.rows : 0;
    this.pageSize = this.tablePagination ? this.tablePagination?.rows : 10;
    this.first = this.tablePagination?.first ? this.tablePagination?.first : 0;

    this.formNavigationData.page = this.tablePagination;
    this.formNavigationData.sort = this.sort;
    this.formNavigationData.uid = this.filteredUID;
    this.formNavigationData.category = this.selectedCategory;
    this.formNavigationData.searchTerm = this.searchTerm;

    this.formservice.setNavigationData(this.formNavigationData)


    this.formservice.getallformsandmasterdata(this.sortField, this.sortOrder, this.pageSize, this.pageIndex, this.filteredUID, this.selectedCategory, this.searchTerm, null, this.includeFlow).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.forms =  response.forms;
        this.formLength = response.totalRecords || response.forms?.length;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  // async getforms(category: string) {
  //   this.forms = [];
  //   this.getformsbycategory(category)
  //   .subscribe({
  //     next: (responseobj: any)=>{
  //       this.categories = responseobj.categories;
  //       this.categories = this.categories.filter(item=> item.category !== 'All');

  //       if (this.formCategory !== 'Masterdata') {
  //         this.forms = responseobj.forms;

  //         if (this.forms.length > 0 && this.forms[0] !== undefined) {
  //           this.iscategoryformsempty = false;
  //         } else {
  //           this.iscategoryformsempty = true;
  //         }
  //       }
  //     }, 
  //     error: (error: any) => {
  //       this.iscategoryformsempty = true;
  //       this.forms = [];
  //     }
  //   });
  // }
  // async getMasterData() {
  //   this.masterDataArr = [];
  //   this.formservice.getMasterData('').subscribe(getmasterdataAPIres => {
  //     const response =  getmasterdataAPIres;
  //     if (response.error === '') {
  //       if (response.status === 'Success') {
  //         this.masterDataArr =  response.masterdataHeaders;
  //       }
  //     }
  //     if (this.masterDataArr !== undefined) {
  //       this.mergeFormsAndMasterdata();
  //     }
  //   });
  // }
  // mergeFormsAndMasterdata() {
  //   this.forms = [...this.forms, ...this.masterDataArr];    
  //   this.forms.sort((a,b) => (a.formName > b.formName) ? 1 : ((b.formName > a.formName) ? -1 : 0));
  // }
  // public getformsbycategory(category: string) {
  //   const subject = new Subject<any[]>();
  //   this.formservice
  //     .getallforms(category)
  //     .subscribe(res => {
    
  //       const response = res;
  //       // console.log("this is for perticular/all cat",response.forms[0].category);
  //       // for(var i in response.forms){
  //       //   console.log(response.forms[i].category);
          
  //       // }
  //       if (response.error === '') {
  //         if (response.status === 'Success') {
  //           subject.next(response);
  //         }
  //       } else {
  //         this.errmsg = response.error;
  //         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          
  //       }
  //     });
  //     return subject.asObservable();
  // }
  // navigateToCategory(category: string) {
  //   // if(category === 'Masterdata') {
  //   //   this.forms = [];
  //   //   this.getMasterData();
  //   // } else {
  //     this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
  //   // }
  //   this.router.navigate(['./',{formcategory:category}]);
  // }

  clearAllFilter(table: Table) {
    table.clear();
    const page = {
      first: 0,
      page: 0,
      rows: 10
    }
    this.getAllFormsAndMasterdata(page, null, null, null, null);
    this.selectedUser = null;
  }

  getFormDetails(form: any) {
    this.selectedForm = form;
  }
 
  copyForm() {
    const ref = this.dialogService.open(CreateFormComponent, {
      header: `Copy Form`,
      width: '50%',
      data: {
        type: 'form',
        isCopyForm: true,
        form: this.selectedForm,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  public updateForm() {
    const ref = this.dialogService.open(CreateFormComponent, {
      header: `Update Form`,
      width: '50%',
      data: {
        type: 'form',
        isUpdateForm: true,
        form: this.selectedForm,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  archiveForm(formId: string, event?: Event, element?: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove form?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formservice.deleteform(formId)
          .subscribe((res: any) => {
            const response = res;
            if (response.error === '') {
              if (response.status === 'Success') {
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Deleted.' });
                element.hide();
                // this.navigateToCategory(response.category);
                this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
      },
      reject: () => {
        element.hide();
      }
    });
  }

  onSelectionChange(e: any) {
    this.selectedForms = e;
  }

  removeMultipleForms(event?: Event) {
    let completedCount = 0;
    let totalCount = this.selectedForms.length;
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove forms?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        if (this.selectedForms.length > 0) {
          for (let i=0; this.selectedForms.length > i; i++) {
            this.formservice.deleteform(this.selectedForms[i].formId)
              .subscribe((response: any) => {
                if (response.status.toLowerCase() === 'success') {
                  completedCount += 1;
                  if (completedCount === totalCount) {
                    this.selectedForms = [];
                    this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
                    completedCount = 0;
                    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully removed.' });
                  }
                } else {
                  this.errmsg = response.error;
                  this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
                }
              });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please slect forms.' });
        }
      },
      reject: () => {}
    });
  }

  getFormsCategory() {
    this.assignMultiCategory = null;
    this.formservice.getFormsCategories().subscribe((res: any) => {
      if (res.status === "Success") {
        this.multiCategoriesArr = res.category;
        this.multiCategoriesArr = this.multiCategoriesArr.filter(e => e.category !== "Default")
        this.categories = res.category; // need for filter table
      }
    })
  }

  getAssociatedApps(event: any) {
    const findCategory = this.multiCategoriesArr.find(e => e.category === event.value);
    this.associatedApps = findCategory?.associatedApps?.length ? findCategory.associatedApps : ['DIGITAL_FORMS'];
  }

  showCategoryPanel(str: string, element: any) {
    element.hide();
    if (str === 'category') {
      this.showCreateCategory = true;
    } else if (str === 'apps') {
      this.showAssociatedApps = true;
    } else {
      this.showCreateCategory = false;
      this.showAssociatedApps = false;
    }
    this.getFormsCategory();
  }

  createCategory() {
    this.formservice.createCategory(this.newCategory,null).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.showCreateCategory = false;
        this.formCategory = response.category;
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Category created successfully!' });
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      } else {
         this.errmsg = response.error;
         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  filterCommonCategories() {
    var userteams = [];
    var onlyTeamsArr = [];
    this.selectedForm.forEach(selectedUsers => {
      if(selectedUsers.teams && selectedUsers.teams.length > 0) {
        userteams.push(selectedUsers.teams);
      } else {
        userteams.push([]);
      }
    });
    userteams.forEach(teamName => {
      onlyTeamsArr.push(teamName.map(teams => {return teams.teamId}));
    });
    const result = onlyTeamsArr.reduce((a, b) => a.filter(c => b.includes(c)));
    // this.teams = result;
    // this.assignTeamForm.patchValue({
    //   team: result.map(res => {return {'teamName': res, 'teamId': res}}),
    // });
  }

  assignCategoryToForms(element: any) {
    let completedCount = 0;
    let totalCount = this.selectedForms.length;
    if (this.selectedForms.length > 0) {
      for (let i=0; this.selectedForms.length > i; i++) {
        const form = {
          title: this.selectedForms[i].formTitle,
          shortname: this.selectedForms[i].formName,
          name: this.selectedForms[i].formName,
          description: this.selectedForms[i].description,
          usage: this.selectedForms[i].usage,
          formtype: this.selectedForms[i].formType,
          formCategory: this.assignMultiCategory,
        }
        const data = {
          form : {
            formId: this.selectedForms[i].formId,
          },
          type: 'form'
        }
        this.formservice.updateform(form, this.selectedForms[i].avatar, data)
        .subscribe(CreateformAPIres => {
          const response = CreateformAPIres;
          if (response.error === '') {
            completedCount += 1;
            if (completedCount === totalCount) {
              element.hide();
              this.selectedForms = [];
              this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
              this.assignMultiCategory = '';
              completedCount = 0;
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
      }
    } else {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please slect forms.' });
    }
  }

  resetAssignCategory(element: any) {
    element.hide();
    this.assignMultiCategory = '';
  }

  resetCategory() {
    this.displayCategory = '';
    this.newCategory = null;
    this.associatedApps = [];
    this.showAssociatedApps = false;
    this.showCreateCategory = false;
  }

  saveAssociatedApps() {
   this.formservice.updateFormCategory(this.displayCategory,this.associatedApps).subscribe((response: any) => {
      if (response.status.toLowerCase() === 'success') {
        this.displayCategory = '';
        this.associatedApps = [];
        this.showAssociatedApps = false;
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Apps associated successfully!' });
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        this.displayCategory = '';
        this.associatedApps = [];
      }
   });
 }

  getSeverity(formType: string) {
    switch (formType) {
      case 'form':
          return 'success';
      case 'nestedForm':
          return 'warning';
      case 'wizard':
          return 'info';
      default:
          return null;
    }
  }

  camelCaseString(str: string) {
    return str.replace(/([A-Z])/g, (match) => ` ${match}`).replace(/^./, (match) => match.toUpperCase());
  }

  // Import Export form start

  showImportDialog(str: string, element: any) {
    element.hide();
    if (str === 'file') {
      this.importFileText = [{ severity: 'warn', summary: 'Warning: ', detail: 'Existing draft will be discarded.' }];
      this.showImportByFile = true;
    } else if (str === 'remote') {
      this.showImportByRemote = true;
      this.remoteSystemForm.patchValue({
        url: localStorage.getItem('remote_url') ? localStorage.getItem('remote_url') : null,
        emailId: localStorage.getItem('remote_email') ? localStorage.getItem('remote_email') : null
      });
    } else {
      this.showImportByFile = false;
      this.showImportByRemote = false;
    }
  }

  resetImportForms() {
    this.file = null;
    this.autoPublish = false;
    this.overwriteDraft = false;
    this.remoteSystemForm.reset();
    this.showImportByFile = false;
    this.showImportByRemote = false;
  }

  processFile(fileInput: any) {
    this.file = fileInput.files[0];
    const reader = new FileReader();
    reader.addEventListener('load', (event: any) => {
      this.FileJsonObj = JSON.parse(event.target.result);
    });
    reader.readAsText(this.file);
  }

  b64EncodeUnicode(str: any) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
    return String.fromCharCode(("0x" + p1) as any);
    }));
   }

  importFromFile() {
    this.formservice.importformsfromfile(
      'file',
      'form',
      this.autoPublish,
      true,
      this.b64EncodeUnicode(JSON.stringify(this.FileJsonObj))
    ).subscribe(response => {
    if (response.error === '') {
      if (response.status === 'Success') {
        this.messageService.add({ severity: 'success', summary: 'Success', detail: `skipped:${response.skipped},imported ${response.imported} of all selected form}` });
        this.resetImportForms();
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    } else {
      this.errmsg = response.error;
      this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
    }
    });
  }

  exportSelectedForms() {
    this.formservice
      .exportforms('form',this.selectedForms.map(data => data.formId))
      .subscribe({
        next: (data: any) => { this.downloadFile(data, 'exportdata.json')}, // console.log(data),
        error: (error) => { console.log('Error downloading the file.') },
        // () => {
        //   console.log('OK');
        // }
      });
  }

  downloadFile(data: any, filename: string) {
    const blob = new Blob([JSON.stringify(data)], { type: 'octet/stream' });
   // const url = window.URL.createObjectURL(blob);
   // window.open(url);
   const a = document.createElement('a');
   document.body.appendChild(a);
   const url = window.URL.createObjectURL(blob);
   a.href = url;
   a.download = filename;
   a.click();
   setTimeout(() => {
     window.URL.revokeObjectURL(url);
     document.body.removeChild(a);
   }, 0);
   this.selectedForms = [];
 }

  downloadForms() {
    const obj = {
      email: this.remoteSystemForm.value.emailId,
      password: this.remoteSystemForm.value.password,
      url: this.remoteSystemForm.value.url,
      domain: localStorage.getItem('domain')
    }
    this.formservice.downloadremoteforms('remote', 'form', obj)
      .subscribe((response: any) => {
        if (response.error === '') {
          if (response.status === 'Success') {
            localStorage.setItem('remote_url',this.remoteSystemForm.value.url);
            localStorage.setItem('remote_email',this.remoteSystemForm.value.emailId);
            this.remoteFormList = response.data;
            this.remoteSystemForm.reset();
           // console.log('Downloaded forms successfully!!');
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  importselectedForms() {
    // console.log('Forms to export', this.selectedformitems.selectedOptions.selected.map(o => o.value));
    console.log(this.selectedFormsForImport);
    this.formservice.importremoteforms(
        'remote',
        'form',
        this.remoteSystemForm.value,
        this.selectedFormsForImport,
        this.autoPublish,
        true
      )
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          this.resetImportForms();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: `skipped:${response.skipped}, imported ${response.imported} of all selected form` });
          this.showImportByRemote = false;
          this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  goToFormDesign(form: any) {
    const obj = {
      type: form.formType.toLowerCase() === 'masterdata' ? 'masterdata' : 'form',
      formType: form.formType,
      formId: form.formId,
      category: form?.category ? form?.category : this.formCategory,
      formVersion: form?.recentVersion,
      isDraft: form?.isDraft,
      formVersionComment: form?.versionComments
    }
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
  }

  newFormDesign(formType: string) {
    const obj = {
      type: formType.toLowerCase() === 'masterdata' ? 'masterdata' : 'form',
      formType: formType,
      formId: '1',
      isCreateForm: true,
    }
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
    // if (formType === 'masterdata') {
    //   if (formId === '1') {
    //     console.log('call',formId)
        
    //   } else {
    //     const routerData = btoa(JSON.stringify(obj));
    //     this.router.navigate([`/formio/formbuilder/${routerData}`]);
    //     console.log('call',formId)
    //   }
    // } else {
    //   console.log('call',formId)
    //   const routerData = btoa(JSON.stringify(obj));
    //   this.router.navigate([`/formio/formbuilder/${routerData}`]);
    // }
  }

  goToForms() {
    this.router.navigate(['./nestedForms'],{ relativeTo: this.route })
  }

  // showFormMenu(element: any, event: any) {
  //   if (this.formCategory === 'All') {
  //     this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Please select another category.' });
  //     console.log('message',this.formCategory)
  //   } else {
  //     element.show(event);
  //   }
  // }

  showRevisionDialog() {
    const ref = this.dialogService.open(FormsVersionComponent, {
      header: `Versions (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
        formType: this.selectedForm?.formType
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showAssignmentDialog(form: any) {
    this.selectedForm = form;
    const ref = this.dialogService.open(FormsAssignmentsComponent, {
      header: `Assignments (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showFormsetsDialog() {
    const ref = this.dialogService.open(FormsFormsetsComponent, {
      header: `Formsets (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showDocumentDialog() {
    const ref = this.dialogService.open(FormsDocumentsComponent, {
      header: `Documents (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showEnvironmentDialog() {
    const ref = this.dialogService.open(FormsEnvironmentComponent, {
      header: `Environment (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showTranslationDialog() {
    const ref = this.dialogService.open(FormsTranslationComponent, {
      header: `Translation (${this.selectedForm?.formTitle})`,
      width: '60%',
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showSchedulesDialog() {
    const ref = this.dialogService.open(FormsSchedulesComponent, {
      header: `Schedule (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
        formType: this.selectedForm?.formType
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showSharingDialog(form: any) {
    this.selectedForm = form;
    const ref = this.dialogService.open(FormsSharingComponent, {
      header: `Share (${this.selectedForm?.formTitle ? this.selectedForm?.formTitle : ''})`,
      width: '60%',
      // height: '65%',
      // contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showMasterdataDialog(form: any) {
    this.selectedForm = form;
    const ref = this.dialogService.open(FormsMasterdataComponent, {
      header: `Masterdata (${this.selectedForm?.formTitle})`,
      width: '70%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        name: this.selectedForm?.formName,
        description: this.selectedForm?.description,
        formId: this.selectedForm?.formId,
        formVersion: form?.recentVersion,
        isDraft: form?.isDraft,
        formVersionComment: form?.versionComments
      }
    });
    // element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.navigateToCategory(data.category);
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  copyMasterData() {
    const ref = this.dialogService.open(CreateFormComponent, {
      header: `Copy Masterdata`,
      width: '50%',
      data: {
        type: 'masterdata',
        isCopyForm: true,
        form: this.selectedForm,
        category: this.formCategory,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  updateMasterData() {
    const ref = this.dialogService.open(CreateFormComponent,{
      header: `Update Masterdata`,
      width: '50%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        type: 'masterdata',
        form: this.selectedForm,
        isUpdateForm: true,
      } 
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  deleteMasterdata(formId: string, event?: Event, element?: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Delete masterdata?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formservice.deleteresource(formId)
          .subscribe((res: any) => {
            const response = res;
            if (response.error === '') {
              if (response.status === 'Success') {
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully Deleted!' });
                element.hide();
                this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
      },
      reject: () => {
        element.hide();
      }
    });
  }

  showCreateWFdialog(element: any) {
    const ref = this.dialogService.open(CreateFormComponent,{
      header: 'Create Flow For ' + this.selectedForm.formTitle,
      width: '50%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        type:'workflow',
        row: this.selectedForm,
        create:true,
        isupdate: false,
        iscopy:false,
        add:false,
        displayType: 'dialog'
      } 
    });
    element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  updateSchedule() {
    let status;
    let msg;
    if(this.selectedForm.scheduleRunStatus === '0') {
      status = '1';
      msg = 'Resumed';
    } else {
      status = '0';
      msg = 'Paused';
    }
    this.formservice.updateScheduleRunStatus(this.selectedForm.formId, status)
    .subscribe((response: any) => {
      if (response.status.toLowerCase() === 'success') {
        // element.hide();
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
        this.messageService.add({ severity: 'success', summary: 'Success', detail: `Schedule ${msg} Successfully!` });
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  showFlow(form: any) {
    this.selectedForm = form;
    const ref = this.dialogService.open(FormsFlowsListComponent,{
      header: `Flows (${this.selectedForm?.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      // closeOnEscape: false,
      data: {
        formId: this.selectedForm.formId,
        formType: this.selectedForm.formType,
        isDraft: (this.selectedForm.isDraft && this.selectedForm.recentVersion === "1") ? true : false,
        formTitle: this.selectedForm?.formTitle
      }
    });
    this.formNavigationData.formId = this.selectedForm.formId;
    this.formNavigationData.formType = this.selectedForm.formType;
    this.formNavigationData.formTitle = this.selectedForm.formTitle;
    this.formservice.setNavigationData(this.formNavigationData);

    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  nestedFormsList(form: any) {
    this.selectedForm = form;
    const ref = this.dialogService.open(NestedFormsListComponent,{
      header: `Form list (${this.selectedForm.formTitle})`,
      width: '60%',
      // height: '65%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm.formId,
        formType: this.selectedForm.formType,
        formTitle: this.selectedForm?.formTitle
      }
    });
    // element.hide();
    this.formNavigationData.formId = this.selectedForm.formId;
    this.formNavigationData.formTitle = this.selectedForm.formTitle;
    this.formservice.setNavigationData(this.formNavigationData);
    ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }
  
  navigateToMasterData(event: any, workflow: any, formId: string) { // if we use this fun then open in new tab using 'window'
    event.stopPropagation();
    this.router.navigate(['/workflows/workflow-editor',{wfTitle:workflow.wfTitle,wfId:workflow.wfId, formType:'masterdata', formId:formId}]);
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  onUserChange(e: any) {
    this.usersService
      .getusers(e.query, 
        '',
        0,
        3,
        [], 
        '',
        false, 
        '', 
        '').subscribe((res: any) => {
        this.userArr = res.formUsers;
      });
  }

  onSelectUser(data: any) {
    this.getAllFormsAndMasterdata(null, this.sort, data.id, this.selectedCategory, this.searchTerm);
  }

  onSelectCategory(data: any) {
    this.getAllFormsAndMasterdata(null, this.sort, this.filteredUID, data.value, this.searchTerm);
  }

  onSearch() {
    this.searchSubject.next(this.searchTerm);
  }

  sorting(str: string, order: string) {
    this.sortField = str;
    this.sortOrder = order;
    let sort = {field: this.sortField, order: this.sortOrder};
    this.getAllFormsAndMasterdata(null, sort, this.filteredUID, this.selectedCategory, this.searchTerm);
  }

  onClickSidebar() {
    this.layoutService.config.menuMode = 'static';
    this.layoutService.state.staticMenuDesktopInactive = !this.layoutService.state.staticMenuDesktopInactive;
  }

  showEmptyFormDialog(element: any) {
    this.getAllTemplates(null, null);
    this.showEmptyForm = true;
    element.hide();
  }

  cancelEmptyForm() {
    this.showEmptyForm = false;
  }

  showTemplatePreview() {
    let data = {
      submission: {},
      // formId: this.formId,
      // type: this.type,
      isPreview: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');

    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  getAllForms() {
    this.formservice.getallforms('layout').subscribe({
      next: (res: any)=>{
        if (res.status.toLowerCase() === 'success') {
          this.templateList = res.forms;
        } else {
          this.templateList = [];
        }
      }, 
      error: (error: any) => {
        this.templateList = [];
      }
    });
  }

  helpURL() {
    window.open("https://docs.unvired.com/builder/admin/forms/", '_blank');
  }

  getAllTemplates(pagination: any, searchTerm: string) {
    // this.searchTerm = searchTerm;

    // this.tablePagination = pagination;
    // this.pageIndex = this.tablePagination ? this.tablePagination.page * this.tablePagination.rows : 0;
    // this.pageSize = this.tablePagination ? this.tablePagination.rows : 10;

    this.formservice.getallformsandmasterdata(null, null, 10, 0, null, null, null, 'templates', false).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.templateList =  response.forms;
        // this.formLength = response.totalRecords || response.forms?.length;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        this.templateList = [];
      }
    });
  }

  showTemplateDialog(formType: string, element: any) {
    const ref = this.dialogService.open(FormTemplatesComponent,{
      header: `Create ${formType === 'form' ? 'Form' : 'Wizard'}`,
      width: '60%',
      height: '100%',
      data: {
        formType: formType
      }
    });
    element.hide();
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showFormConfigurations() {
    const ref = this.dialogService.open(FormControlComponent, {
      header: `Form Control (${this.selectedForm?.formTitle})`,
      width: '50%',
      data: {
        formId: this.selectedForm.formId,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showPDFSettings() {
    const ref = this.dialogService.open(FormPdfSettingsComponent, {
      header: `PDF Settings (${this.selectedForm?.formTitle})`,
      width: '60%',
      height: '50%',
      data: {
        formId: this.selectedForm.formId,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllFormsAndMasterdata(this.tablePagination, this.sort, this.filteredUID, this.selectedCategory, this.searchTerm);
      }
    });
  }

  ngOnDestroy(): void {
    if (!(this.navEnd.url.includes('/form-builder') || this.navEnd.url.includes('/flow-editor'))) {
      this.formNavigationData.sort = '';
      this.formNavigationData.uid = '';
      this.formNavigationData.category = '';
      this.formNavigationData.searchTerm = '';
      this.formservice.setNavigationData(this.formNavigationData)
    }
  }

  onIncludeFlowChange(inputElement: HTMLInputElement) {
    if (this.includeFlow) {
      setTimeout(() => inputElement.focus(), 0);
    }
  }
}
