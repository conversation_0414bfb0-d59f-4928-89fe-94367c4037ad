.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}
/* .create-form-button {
  background-color: #00b8ff;
  color: white;
}
.create-form-button::before {
  background: repeating-linear-gradient(60deg, 
              transparent, 
              transparent 0.75rem, 
              var(--first-color-alt) 0.75rem, 
              var(--first-color-alt) 1.5rem);
  animation: load 1s infinite linear;
} */
@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}
  .highlight {
    cursor: pointer;
    color: var(--primary-color) !important;
  }
  .custom-width {
    width: 21rem !important;
  }
  .assign-category {
    width: 16rem;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }
  .custom-ul {
    list-style-type: none !important;
    padding-left: 0px !important;
    margin-bottom: 5px;
    margin-top: 5px;
  }
  .custom-li {
    display: flex;
    align-items: center;
    cursor: pointer !important;
    padding: 8px;
  }
  .menu-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  .custom-li:hover {
    background-color: var(--highlight-bg);
    border-radius: 5px;
  }
  .form-type {
    background-color: #d0e1fd;
    width: 50px;
    height: 25px;
    span {
      color: #3d82f6;
    }
  }
  .nested-form-type {
    background-color: #A5D6A7;
    width: 100px;
    height: 25px;
    span {
      color: #2E7D32;
      text-overflow: ellipsis;
    }
  }
  .wizard-type {
    background-color: #ead6fd;
    width: 60px;
    height: 25px;
    span {
      color: #a855f7;
    }
  }
  .masterData-type {
    background-color: #c3edf5;
    width: 90px;
    height: 25px;
    span {
      color: #06b6d4;
    }
  }
  .templates-type {
    background-color: #fecdd3;
    width: 90px;
    height: 25px;
    span {
      color: #fb7185;
    }
  }
  .badge-style {
    padding: 0.20rem 0.45rem;
    font-size: 0.75rem;
    border-radius: 6px;
    margin-left: 8px;
  }
  .draft-badge {
    /* background: #818a98; */
    border: 0.5px solid lightgray;
    /* color: white; */
  }
  .publish-badge {
    /* background: #2586c7; */
    border: 0.5px solid lightgray;
    /* color: white; */
  }
  .draft-published-badge {
    /* background: #f17b42; */
    border: 0.5px solid lightgray;
    /* color: white; */
  }

  ::ng-deep p-chips .p-chips-multiple-container {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    /* Overflow-x: hidden; */
    max-height: 100px;
    width: 100%;
  }
  
  ::ng-deep p-chips .p-chips-input-token {
    margin-bottom: .5rem;
  }

  ::ng-deep p-dataview .p-dataview-header {
    margin-bottom: 12px !important;
  }

.file-drop-area {
    border: 1px dashed #b2b8be;
    border-radius: 3px;
    position: relative;
    max-width: 100%;
    padding: 20px;
    -webkit-transition: 0.2s;
    transition: 0.2s;
}
.file-msg {
    font-size: medium;
    font-weight: 300;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 10px;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: calc(100% - 130px);
    padding-bottom: 2px;
}

.file-input {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0;
}
.wf-icons {
  font-size: 18px;
  margin-right: 10px;
  color: #2586c7;
  cursor: pointer;
}
::ng-deep .remote p-multiselect .p-multiselect-label {
  display: flex;
  flex-wrap: wrap;
  overflow-y: scroll;
  max-height: 100px;
}
::ng-deep .remote p-multiselect .p-multiselect-token {
  margin-bottom: .5rem;
}
svg {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.sparkles path {
  transform-origin: 50% 50%;
  transform-box: fill-box;
  animation: sparkle var(--duration) var(--delay) infinite ease-in-out;
}

@keyframes sparkle {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(0);
  }
  70% {
    transform: scale(-1, 0);
  }
  80% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}

.image-container {
  position: relative;
  display: inline-block;
}

.hover-image {
  display: block;
  width: 100%;
  height: 100%;
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 3px;
}

.hover-text {
  color: #dadada; /* Text color */
  font-size: 1.5rem; /* Adjust as needed */
}

.image-container:hover .hover-overlay {
  opacity: 1;
}

::ng-deep .template-cards p-card .p-card-content {
  padding: 0px;
}

::ng-deep .template-cards p-card .p-card-body {
  padding: 14px;
}

::ng-deep .template-cards p-card .p-card.p-component:hover {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
}