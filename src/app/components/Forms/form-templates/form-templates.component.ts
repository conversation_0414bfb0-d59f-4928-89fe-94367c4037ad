import { Component, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { debounceTime, Subject } from 'rxjs';
import { FormsService } from 'src/app/services/forms.service';

@Component({
  selector: 'app-form-templates',
  templateUrl: './form-templates.component.html',
  styleUrls: ['./form-templates.component.scss']
})
export class FormTemplatesComponent {
  private errmsg: string;
  private categoryType: string = 'templates';
  private searchSubject: Subject<string> = new Subject();
  showPreview: boolean = false;
  forms: any[] = [];
  searchTerm: string;
  formData: any = {
    components: []
  };
  formVersion: any;
  formVersionComment: string;
  formId: string;

  constructor(
    private formservice: FormsService,
    private messageService: MessageService,
    private router: Router,
    public data: DynamicDialogConfig,
    private ref: DynamicDialogRef,
  ) {}

  ngOnInit(): void {
      this.getAllTemplatesAndLayouts(null);
      this.searchSubject.pipe( // only for search bar
        debounceTime(500) 
      ).subscribe(searchText => {
        this.getAllTemplatesAndLayouts(this.searchTerm);
      });
  }

  onSearch() {
    this.searchSubject.next(this.searchTerm);
  }

  clearFilter() {
    this.getAllTemplatesAndLayouts(null);
  }

  private getAllTemplatesAndLayouts(searchTerm: string) {
    this.searchTerm = searchTerm;

    // this.tablePagination = pagination;
    // this.pageIndex = this.tablePagination ? this.tablePagination.page * this.tablePagination.rows : 0;
    // this.pageSize = this.tablePagination ? this.tablePagination.rows : 10;
    // this.first = this.tablePagination?.first ? this.tablePagination?.first : 0;
    
    this.formservice.getallformsandmasterdata(null, null, null, null, null, null, this.searchTerm, this.categoryType, false).subscribe(response => {
      if (response?.status?.toLowerCase() === 'success') {
        const forms =  response.forms;
        this.forms = forms?.length > 0 ? forms.filter((form: any) => form.usage.toLowerCase() === this.data.data.formType.toLowerCase()) : [];
        const blankForm = {
          formId: '1',
          formTitle: `Blank ${this.data.data.formType.toLowerCase() === 'form' ? 'Form' : 'Wizard'}`,
          description: `An empty  ${this.data.data.formType} with no pre-filled fields or structure, allowing users to design from scratch.`,
          formType: this.data.data.formType,
        }
        if (this.forms) {
          this.forms.unshift(blankForm);
        } else {
          this.forms = [blankForm];
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  openFormPreview(form: any) {
    if (form.formId === '1') return; // Blank form is not allowed to preview
    this.formservice.getformDataWithMasterData(form.formId, 'form', true)
    .subscribe({ 
      next:(response) =>
      {
        if (response?.status?.toLowerCase() === 'success') {
          if (response?.formData) {
            this.showPreview = true;
            this.formData.components = response.formData.components.filter(data => data.action !== 'submit' && data.key !== 'submit');
            this.formVersion = response.version;
            this.formVersionComment = response.versionComments;
            this.formId = form.formId;
          } else {
            this.showPreview = false;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: "No form found" });
          }
        } else {
          this.showPreview = false;
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
        }
      },
      error: (error) => {
        this.showPreview = false;
        this.errmsg = error.error?.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  goBack() {
    this.showPreview = false;
  }

  goToFormBuilder(form: any) {
    const obj = {
      type: 'form',
      formType: this.data.data.formType,
      formId: form.formId,
      isCreateForm: true,
    }
    this.ref.close();
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
    this.formId = null;
  }

}
