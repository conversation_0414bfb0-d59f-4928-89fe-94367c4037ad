<div class="col-12 p-0">
    <div *ngIf="!showPreview" class="mb-3 mt-1">
        <span class="p-input-icon-right w-full">
            <i *ngIf="searchTerm" class="pi pi-times cursor-pointer" (click)="clearFilter()"></i>
            <input id="search" class="p-inputtext-sm w-full" pInputText type="text" [(ngModel)]="searchTerm" (keyup)="onSearch()" placeholder="Search..." />
        </span>
    </div>
    
    <div *ngIf="!showPreview" class="grid">
        <div class="col-3" *ngFor="let form of forms">
          <div class="card flex flex-column mr-3" style="width: 100%; height: 100%;">
            <div class="image-hover" (click)="openFormPreview(form)">
                <img [src]="form?.thumbnail ? form.thumbnail : 'assets/images/dummy.jpg'" alt="Form preview" class="w-full"/>
                <span *ngIf="form?.formId !== '1'" class="image-hover-text"><i class="pi pi-eye text-lg"></i> Preview</span>
            </div>

            <div class="mb-3">
              <h5 class="form-title">{{ form.formTitle }}</h5>
              <p class="form-description" [pTooltip]="form?.description">{{ form.description }}</p>
            </div>

            <div class="mt-auto">
              <button pButton type="button" label="Use Template" class="p-button-outlined w-full" (click)="goToFormBuilder(form)"></button>
            </div>
          </div>
        </div>
    </div>

    <div *ngIf="showPreview" class="m-3">
        <div class="flex justify-content-between align-items-center mb-3">
            <button pButton (click)="goBack()" icon="pi pi-arrow-left" class="p-button-sm p-button-rounded" pTooltip="Go Back"></button>
            <div class="flex flex-row align-items-center gap-2">
                <div *ngIf="formVersion" class="flex flex-row align-items-center"><span>Version: {{formVersion}}</span></div>
                <p-divider *ngIf="formVersion && formVersionComment" styleClass="mx-0" layout="vertical"></p-divider>
                <div *ngIf="formVersionComment" class="flex flex-row align-items-center">Comment: {{formVersionComment}}</div>
                <button pButton type="button" label="Use Template" class="p-button-outlined" (click)="goToFormBuilder({formId: formId})"></button>
            </div>
        </div>
        <hr *ngIf="formData">
        <div>
            <formio [form]='formData' readOnly="true"></formio>
        </div>
      </div>
</div>