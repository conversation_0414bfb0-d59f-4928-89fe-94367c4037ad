<a href="https://docs.unvired.com/builder/admin/forms/#schedules" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<p-accordion styleClass="mb-3" *ngIf="isCreate">
    <p-accordionTab [selected]="true" >
        <ng-template pTemplate="header">
            <span>Create Schedule</span>
        </ng-template>
        <ng-template pTemplate="content">
            <form [formGroup]="scheduleform" novalidate>
                <div class="formgrid grid sch">
                  <!-- <div class="col-12 p-0"> -->
                    <div *ngIf="data.data.formType.toLowerCase() !== 'masterdata'" class="col-4 my-3">
                        <span class="p-float-label">
                            <p-dropdown id="team" formControlName="teamCtrl" [autoDisplayFirst]="false"
                                 [options]="allTeams" class="p-inputtext-sm" styleClass="w-full">
                            </p-dropdown>
                            <label htmlFor="team" class="_required">Team</label>
                        </span>
                    </div>
                    <div *ngIf="data.data.formType.toLowerCase() !== 'masterdata'" class="col-4 my-3">
                        <span class="p-float-label">
                            <p-dropdown id="priority" formControlName="taskpriority" [autoDisplayFirst]="false"
                                 [options]="taskpriorities" class="p-inputtext-sm" styleClass="w-full">
                            </p-dropdown>
                            <label htmlFor="priority">Priority</label>
                        </span>
                    </div>
                    <div *ngIf="data.data.formType.toLowerCase() !== 'masterdata'" class="col-4 my-3">
                      <span class="p-float-label">
                        <input id="due" pInputText formControlName="dueindays" type="number" (change)="disableReminder()" class="w-full p-inputtext-sm">
                        <label htmlFor="due">Due days</label>
                      </span>
                    </div>
        <!-- Reminder -->
                    <div *ngIf="data.data.formType.toLowerCase() !== 'masterdata'" class="col-4 my-3">
                        <span class="p-float-label">
                            <p-dropdown id="Reminder" formControlName="selectedReminder" [autoDisplayFirst]="false"
                                 [options]="Reminders" styleClass="w-full p-inputtext-sm">
                            </p-dropdown>
                            <label htmlFor="Reminder">Reminder</label>
                        </span>
                    </div>
        <!-- End date -->
                    <div class="col-4 my-3">
                        <span class="p-float-label">
                            <p-calendar id="date" formControlName="pickervalue" [minDate]="minDate" styleClass="w-full p-inputtext-sm" (onSelect)="setValidatorsfordate($event)" appendTo="body"></p-calendar>
                            <label for="date" class="mb-0 ml-2">End Date</label>
                        </span>
                    </div>
        <!-- Type -->
                    <div class="col-12 set mt-3">
                        <p-fieldset>
                            <ng-template pTemplate="header">
                                <span class="p-float-label">
                                    <p-dropdown id="schedule" formControlName="scheduletype" [autoDisplayFirst]="false" (onChange)="pickedScheduleType($event)"
                                        [options]="CreateSchedules" optionLabel="key" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="schedule">Type</label>
                                </span>
                            </ng-template>
                            <ng-template pTemplate="content">
        <!-- Minute -->         <div class="grid">
                                <div class="col-6 my-3" *ngIf="selectedschedule === 'Minute'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Minute" formControlName="selectedMinute" [autoDisplayFirst]="false"
                                            [options]="MinuteArr" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Minute">Minute</label>
                                    </span>
                                </div>
        <!-- Day & Hour -->
                                <div class="col-12 my-3" *ngIf="selectedschedule === 'Daily' || selectedschedule === 'Hourly'">
                                    <!-- <div *ngFor="let dailyoption of dailyscheduleoptions">
                                        <p-radioButton [inputId]="dailyoption.name" [value]="dailyoption.value" formControlName="selectedtype"></p-radioButton>
                                        <label [for]="dailyoption.name" class="ml-2">{{ dailyoption.name }}</label>
                                    </div> -->
                                    <span class="p-float-label">
                                        <p-dropdown id="Occurred" formControlName="selectedtype" [autoDisplayFirst]="false" appendTo="body"
                                            [options]="scheduleOptions" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm">
                                        </p-dropdown>
                                        <label htmlFor="Occurred">Occurred On</label>
                                    </span>
                                </div>
        <!-- Week -->
                                <div [ngClass]="selectedschedule === 'Weekly' ? 'col-12 my-3' : 'col-6 my-3'" *ngIf="selectedschedule === 'Weekly' || selectedschedule === 'Minute'">
                                    <!-- <div *ngFor="let availableDay of days" formGroupName="days">
                                        <p-checkbox [inputId]="availableDay" [value]="availableDay" formControlName="{{availableDay}}" [binary]="true"></p-checkbox>
                                        <label [for]="availableDay" class="ml-2 mb-0">{{ availableDay }}</label>
                                    </div> -->
                                    
                                    <span class="p-float-label">
                                        <p-multiSelect id="Weekdays" [options]="days" formControlName="weekDays" display="chip" styleClass="w-full p-inputtext-sm" appendTo="body"></p-multiSelect>
                                        <label htmlFor="Weekdays">Weekdays</label>
                                    </span>
                                </div>
        <!-- Month -->
                                <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Choose" formControlName="chooseMonth" [autoDisplayFirst]="false" 
                                            [options]="chooseMonthArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Choose">Choose day of the month</label>
                                    </span>
                                </div>
                                <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'manual'">
                                    <span class="p-float-label">
                                        <p-inputNumber id="day" formControlName="day" class="w-full" styleClass="w-full p-inputtext-sm" (onFocus)="setValidatorsMonthly('repeateverymonth')" [useGrouping]="false"></p-inputNumber>
                                        <label htmlFor="day">Repeat on which day of every month?</label>
                                    </span>
                                </div>
                                <!-- <div *ngIf="selectedschedule === 'Monthly'" class="col-12">
                                    <p-divider align="center" styleClass="my-2">
                                        <span>or</span>
                                    </p-divider>
                                </div> -->
                                <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                                    <span class="p-float-label">
                                        <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                            [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="on">Repeat On</label>
                                    </span>
                                </div>
                                <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                            [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Day">Day</label>
                                    </span>
                                </div>
        <!-- Year -->
                                <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Choose" formControlName="chooseYear" [autoDisplayFirst]="false"
                                            [options]="chooseYearArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Choose">Choose day of the year</label>
                                    </span>
                                </div>
                                <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'manual'">
                                    <span class="p-float-label">
                                        <p-inputNumber id="day" formControlName="day" (onFocus)="setValidatorsYearly('repeateveryyear')" class="w-full" styleClass="w-full p-inputtext-sm" [useGrouping]="false"></p-inputNumber>
                                        <label htmlFor="day">Repeat on which day of every year?</label>
                                    </span>
                                </div>
                                <!-- <div *ngIf="selectedschedule === 'Yearly'" class="col-12">
                                    <p-divider align="center" styleClass="my-2">
                                        <span>or</span>
                                    </p-divider>
                                </div> -->
                                <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                    <span class="p-float-label">
                                        <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                            [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="on">Repeat</label>
                                    </span>
                                </div>
                                <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                            [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Day">Day</label>
                                    </span>
                                </div>
                                <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                    <span class="p-float-label">
                                        <p-dropdown id="Month" formControlName="selectedmonth" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                            [options]="months" styleClass="w-full p-inputtext-sm" appendTo="body">
                                        </p-dropdown>
                                        <label htmlFor="Month">Month</label>
                                    </span>
                                </div>
                            </div>
                            </ng-template>
                        </p-fieldset>
                    </div>
        
                    <!-- <div class="col-6 my-auto">
                        <p-checkbox formControlName="noenddate" id="enableReview" binary="true" (onChange)="setValidatorsfordate(scheduleform.get('noenddate').value)"></p-checkbox>
                        <label for="enableReview" class="mb-0 ml-2">No end date</label>
                    </div> -->
                    
                   
                    <div class="col-4 text-right mt-3 ml-auto">
                        <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancleForm()"></button>
                        <button class="p-button-sm bg-blue" pButton type="button" label="Create" [disabled]="!scheduleform.valid" (click)="createScheduleForm()"></button>
                    </div>
                  <!-- </div> -->
                </div>
            </form>
        </ng-template>
    </p-accordionTab>
</p-accordion>

<p-table *ngIf="!isCreate" #schedules 
    [value]="dataSourceSchedules" dataKey="id"
    responsiveLayout="scroll" 
    [scrollable]="true" 
    styleClass="p-datatable-sm p-datatable-gridlines"
    [tableStyle]="{ 'min-width': '50rem' }"
    selectionMode="multiple" 
    [(selection)]="selectedScheArr"
    [paginator]="true"
    [rows]="5"
    paginatorDropdownAppendTo="body"
    [rowsPerPageOptions]="[5, 10, 25]"
    [globalFilterFields]="['scheduleText', 'teams']">

    <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-between">
            <div>
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                        (input)="schedules.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(schedules)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
            </div>
            <div>
                <button pButton (click)="removeSchedules($event)" [disabled]="selectedScheArr.length === 0"
                    icon="pi pi-fw pi-trash" pTooltip="Remove Schedules" tooltipPosition="left" class="p-button-sm p-button-danger bg-blue mr-2">
                </button>
                <button pButton (click)="showCreateScheduleDialog()" label="Create" icon="pi pi-plus" class="p-button-sm bg-blue">
                </button>
            </div>
        </div>
    </ng-template>

<!-- Header -->
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
            <th style="min-width: 10rem">Recurrence </th>
            <th style="min-width: 10rem">Team </th>
            <th style="width: 10rem" class="text-center">Action </th>
        </tr>
    </ng-template>

<!-- Table data -->
    <ng-template pTemplate="body" let-sche let-columns="columns">
        <tr>
            <td class="text-center">
                <p-tableCheckbox [value]="sche" (click)="$event.stopPropagation()"></p-tableCheckbox>
            </td>
    
            <td>
                {{sche.scheduleText}}
            </td>
    
            <td>
                {{sche.teams ? sche.teams : ''}}
            </td>
            <td class="text-center">
                <button pButton (click)="updateScheduleStatus(sche.id,sche.runStatus === '1' ? '0' : '1')"
                    [icon]="sche.runStatus === '1' ? 'pi pi-fw pi-pause' : 'pi pi-fw pi-play'" [pTooltip]="sche.runStatus === '1' ? 'Pause' : 'Play'" tooltipPosition="left" class="p-button-text p-button-rounded">
                </button>
                <button pButton (click)="runImmediately(sche.id)"
                    icon="pi pi-angle-double-right" pTooltip="Run Now" tooltipPosition="left" class="p-button-text p-button-rounded">
                </button>
            </td>
        </tr>
    </ng-template>

<!-- No data -->
    <ng-template pTemplate="emptymessage">
        <tr>
            <td class="text-center" style="font-weight: bold;" colspan="4">Not yet scheduled!</td>
        </tr>
    </ng-template>
</p-table>


<!-- Old Create Schedule -->
<!-- <p-overlayPanel #createSchedule [style]="{'max-width': '700px'}">
    <form [formGroup]="scheduleform" class="pt-3" novalidate>
        <div class="formgrid grid">
            <div class="col-6 my-2 px-2">
                <span class="p-float-label">
                    <p-dropdown id="team" formControlName="teamCtrl" [autoDisplayFirst]="false"
                         [options]="allTeams" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="team">Team</label>
                </span>
            </div>
            <div class="col-6 my-2 px-2">
                <span class="p-float-label">
                    <p-dropdown id="priority" formControlName="taskpriority" [autoDisplayFirst]="false"
                         [options]="taskpriorities" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="priority">Priority</label>
                </span>
            </div>
            <div class="col-6 my-2 px-2">
              <span class="p-float-label">
                <input id="due" pInputText formControlName="dueindays" class="w-full p-inputtext-sm">
                <label htmlFor="due">Due days</label>
              </span>
            </div>
            <div class="col-12 px-2 my-3" *ngIf="scheduleform.get('dueindays').value">
                <span class="p-float-label">
                    <p-dropdown id="Reminder" formControlName="selectedReminder" [autoDisplayFirst]="false"
                         [options]="Reminders" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="Reminder">Reminder</label>
                </span>
            </div>
            <div class="flex flex-wrap gap-2 w-full my-3 px-2">
                <div *ngFor="let schedule of OtherSchedules" class="flex-auto">
                    <p-radioButton [inputId]="schedule" [value]="schedule.value" formControlName="scheduletype" (click)="pickedScheduleType(schedule.value)"></p-radioButton>
                    <label [for]="schedule" class="ml-2 mb-0">{{ schedule.key }}</label>
                </div>
            </div>
Minute
            <div class="col-12 px-2 my-3" *ngIf="selectedschedule === 'Minute'">
                <span class="p-float-label">
                    <p-dropdown id="Minute" formControlName="selectedReminder" [autoDisplayFirst]="false"
                         [options]="MinuteArr" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="Minute">Minute</label>
                </span>
            </div>
Day
            <div class="flex flex-wrap align-items-center gap-2 w-full my-2 px-2" *ngIf="selectedschedule === 'Daily'">
                <div *ngFor="let dailyoption of dailyscheduleoptions" class="flex-auto">
                    <p-radioButton [inputId]="dailyoption.name" [value]="dailyoption.value" formControlName="selectedtype"></p-radioButton>
                    <label [for]="dailyoption.name" class="ml-1 mb-0">{{ dailyoption.name }}</label>
                </div>
            </div>
Hour
            <div class="flex flex-wrap align-items-center gap-2 w-full my-2 px-2" *ngIf="selectedschedule === 'Hourly'">
                <div *ngFor="let dailyoption of dailyscheduleoptions" class="flex-auto">
                    <p-radioButton [inputId]="dailyoption.name" [value]="dailyoption.value" formControlName="selectedtype"></p-radioButton>
                    <label [for]="dailyoption.name" class="ml-1 mb-0">{{ dailyoption.name }}</label>
                </div>
            </div>
Week
            <div class="flex flex-wrap align-items-center gap-2 w-full my-2 px-2" *ngIf="selectedschedule === 'Weekly' || selectedschedule === 'Minute'">
                <div *ngFor="let availableDay of days" class="flex-auto" formGroupName="days">
                    <p-checkbox [inputId]="availableDay" [value]="availableDay" formControlName="{{availableDay}}" [binary]="true"></p-checkbox>
                    <label [for]="availableDay" class="ml-2 mb-0">{{ availableDay }}</label>
                </div>
            </div>
Month
            <div class="col-12 mt-4 px-2" *ngIf="selectedschedule === 'Monthly'">
                <span class="p-float-label">
                    <p-inputNumber id="day" formControlName="day" class="w-full" styleClass="w-full p-inputtext-sm" (onFocus)="setValidatorsMonthly('repeateverymonth')" [useGrouping]="false"></p-inputNumber>
                    <label htmlFor="day">Repeat on which day of every month?</label>
                </span>
            </div>
            <div *ngIf="selectedschedule === 'Monthly'" class="col-12 px-2">
                <p-divider align="center">
                    <span>or</span>
                </p-divider>
            </div>
            <div class="col-6 px-2" *ngIf="selectedschedule === 'Monthly'">
                <span class="p-float-label">
                    <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                         [options]="dayoccurences" class="p-inputtext-sm" class="w-full" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="on">Repeat On</label>
                </span>
            </div>
            <div class="col-6 px-2" *ngIf="selectedschedule === 'Monthly'">
                <span class="p-float-label">
                    <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                         [options]="days" class="p-inputtext-sm" class="w-full" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="Day">Day</label>
                </span>
            </div>
Year
            <div class="col-12 mt-4 px-2" *ngIf="selectedschedule === 'Yearly'">
                <span class="p-float-label">
                    <p-inputNumber id="day" formControlName="day" (onFocus)="setValidatorsYearly('repeateveryyear')" class="w-full" styleClass="w-full p-inputtext-sm" [useGrouping]="false"></p-inputNumber>
                    <label htmlFor="day">Repeat on which day of every year?</label>
                </span>
            </div>
            <div *ngIf="selectedschedule === 'Yearly'" class="col-12 px-2">
                <p-divider  align="center">
                    <span>or</span>
                </p-divider>
            </div>
            <div class="col-4 px-2 mb-4" *ngIf="selectedschedule === 'Yearly'">
                <span class="p-float-label">
                    <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                         [options]="dayoccurences" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="on">Repeat</label>
                </span>
            </div>
            <div class="col-4 px-2 mb-4" *ngIf="selectedschedule === 'Yearly'">
                <span class="p-float-label">
                    <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                         [options]="days" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="Day">Day</label>
                </span>
            </div>
            <div class="col-4 px-2 mb-4" *ngIf="selectedschedule === 'Yearly'">
                <span class="p-float-label">
                    <p-dropdown id="Month" formControlName="selectedmonth" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                         [options]="months" class="p-inputtext-sm" styleClass="w-full">
                    </p-dropdown>
                    <label htmlFor="Month">Month</label>
                </span>
            </div>
            <div class="col-6 px-2 my-auto">
                <p-checkbox formControlName="noenddate" id="enableReview" binary="true" (onChange)="setValidatorsfordate(scheduleform.get('noenddate').value)"></p-checkbox>
                <label for="enableReview" class="mb-0 ml-2">No end date</label>
            </div>
            <div class="col-6 px-2 my-auto" *ngIf="!scheduleform.get('noenddate').value">
                <span class="p-float-label">
                    <p-calendar id="date" formControlName="pickervalue" [minDate]="minDate" styleClass="w-full"></p-calendar>
                    <label for="date" class="mb-0 ml-2">Choose a date</label>
                </span>
            </div>
           
            <div class="col-12 px-2 text-right mt-3">
              <button class="p-button-sm bg-blue" pButton type="button" label="Create" [disabled]="!scheduleform.valid" (click)="createScheduleForm(createSchedule)"></button>
            </div>
        </div>
    </form>
</p-overlayPanel> -->