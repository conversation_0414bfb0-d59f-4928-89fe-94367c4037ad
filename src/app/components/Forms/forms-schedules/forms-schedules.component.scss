.main-title {
    font-size: 14px;
    font-weight: 700;
  }
  .sub-text {
    font-size: 14px;
    font-weight: 400;
  }
  .schedule-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
  }
  .schedule-list {
    border: none !important;
  }
  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  ::ng-deep .sch p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 40px;
  }
  
  ::ng-deep .sch p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }

  ::ng-deep .set p-fieldset .p-fieldset-legend {
    padding: 0px !important;
    border: none !important;
    font-weight: normal !important;
  }
  .help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }