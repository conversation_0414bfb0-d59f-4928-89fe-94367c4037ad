import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { UtilsService } from 'src/app/services/utils.service';

@Component({
  selector: 'app-forms-schedules',
  templateUrl: './forms-schedules.component.html',
  styleUrls: ['./forms-schedules.component.scss']
})
export class FormsSchedulesComponent implements OnInit {
  public formid: string;
  public form: any;
  public dataSourceSchedules: any[] = [];
  public errmsg: string;
  public deleteschedules: boolean;
  displayedColumnsSchedules: string[] = [
    'select',
    'scheduleMode',
    'workflowTitle',
    'scheduleText',
    'team',
    'status'
  ];
  public runimmediatelymsg = false;
  scheduleform: FormGroup;
  allTeams: string[] = [];
  selectedScheArr: any = [];
  selectedschedule: string;
  minDate = new Date();
  scheduletypes = ['Create', 'Others'];
  taskpriorities = ['High', 'Medium', 'Low'];
  Reminders = ['None', 'Daily', 'Before Expiry'];
  // CreateSchedules: string[] = ['Daily', 'Weekly', 'Monthly', 'Yearly'];
  CreateSchedules: any[] = [
    {key:'Minute', value: 'Minute'},
    {key:'Hour', value: 'Hourly'},
    {key:'Day', value: 'Daily'},
    {key:'Week', value: 'Weekly'},
    {key:'Month', value: 'Monthly'},
    {key:'Year', value: 'Yearly'}
  ];
  MinuteArr: string[] = ['10', '15', '20', '30', '40', '45', '50'];
  scheduleOptions = [
    { name: 'Everyday', value: 'everyday'},
    { name: 'Weekdays', value: 'weekdays'},
    { name: 'Weekends', value: 'weekends' }
  ];
  days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  dayoccurences = ['First', 'Second', 'Third', 'Fourth', 'Last' ];
  months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  chooseMonthArr = [{ name: 'Repeat on which day of every month?', value: 'manual'}, { name: 'Repeat on specific day of the month', value: 'automatic'}];
  chooseYearArr = [{ name: 'Repeat on which day of every year?', value: 'manual'},{ name:'Repeat on specific day of the year', value: 'automatic'}];
  enddate = '';
  selectedMonthlySchedule: string;
  selectedYearlySchedule: string;
  weeklyValidationStatus = true;
  isCreate: boolean = false;

  constructor(
    private formsservice: FormsService,
    public  route: ActivatedRoute,
    private messageService: MessageService,
    public data: DynamicDialogConfig,
    private confirmationService: ConfirmationService,
    private fb: FormBuilder,
    private utilservice: UtilsService,
  ) { }

  ngOnInit() {
    // this.formid = this.route.parent.snapshot.paramMap.get('formid');
    this.formid = this.data.data.formId;
    this.getformschedules(this.formid);
    this.scheduleform = this.fb.group({
      scheduletype: ['Minute', Validators.required],
      noenddate: [false],
      pickervalue: [null],
      selectedReminder: [{value:'None', disabled: true}],
      teamCtrl: [null, Validators.required],
      taskpriority: [null],
      dueindays: [null],
      weekDays: [null],
      selectedtype: [null, Validators.required],
      day: [null, Validators.required],
      dayoccurence: [null, Validators.required],
      selectedday: [null, Validators.required],
      selectedmonth: [null, Validators.required],
      selectedMinute: [null, Validators.required],
      chooseMonth: [null],
      chooseYear: [null]
    });
    if (this.data.data?.formType.toLowerCase() === 'masterdata') {
      this.scheduleform.controls['teamCtrl'].clearValidators();
      this.scheduleform.controls['teamCtrl'].updateValueAndValidity();
    }
  }

  public getformschedules(formid: string) {
    this.formsservice.getform(formid, 'schedules').subscribe((form: any) => {
      this.form = form;
      this.dataSourceSchedules = form.schedules;
    });
  }

  showCreateScheduleDialog() {
    this.formsservice.getteams_for_forms(this.formid, null)
    .subscribe((res: any) => {
        this.allTeams = res.teams;
    });
    this.scheduleform.controls['scheduletype'].patchValue("Minute");
    this.scheduleform.controls['scheduletype'].updateValueAndValidity();
    this.pickedScheduleType({value: "Minute"});
    this.isCreate = true;
  }

  // changeScheduleType(type) {
  //   if (type === 'Create') {
  //     this.scheduleform.removeControl('selectedWorkflow');
  //   } else if (type === 'Others') {
  //     this.scheduleform.addControl('selectedWorkflow', new FormControl('', Validators.required));
  //   }
  // }

  disableReminder() {
    if (this.scheduleform.get('dueindays').value >= 1) {
      this.scheduleform.get('selectedReminder').enable();
      // this.scheduleform.get('selectedReminder').updateValueAndValidity();
    } else {
      this.scheduleform.get('selectedReminder').disable();
    }
  }

  setValidatorsfordate(endDate: any) {
    if (endDate) {
      this.scheduleform.get('noenddate').patchValue(true);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
    if (!endDate) {
      this.scheduleform.get('noenddate').patchValue(false);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
  }

  pickedScheduleType(event: any) {
    const schedule = event?.value;
    this.selectedschedule = schedule;
    
    if (schedule === 'Minute') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].setValidators([Validators.required]);
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Daily') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
    
    if (schedule === 'Hourly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();

      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue('');
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Weekly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);

    } if (schedule === 'Monthly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['chooseMonth'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseMonth'].updateValueAndValidity();

      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();

    } if (schedule === 'Yearly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['chooseYear'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseYear'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
  }

  // chooseMonthFun() {
  //   console.log('choose month',this.scheduleform.get('selectedReminder').value)
  // }

  setValidatorsMonthly(monthlyscheduletype: string) {
    this.selectedMonthlySchedule = monthlyscheduletype;
    if (monthlyscheduletype === 'repeateverymonth') {
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(0), Validators.max(31)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (monthlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
   // });
  }

  setValidatorsYearly(yearlyscheduletype: string) {
    this.selectedYearlySchedule = yearlyscheduletype;

    if (yearlyscheduletype === 'repeateveryyear') {
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(366)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (yearlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
   // });
  }

  createScheduleForm() {
    const teams = [];
    const ids = { formid: this.formid, formsetid: null}
    teams.push(this.scheduleform.value.teamCtrl);
    let data = {};
    if (this.selectedschedule === 'Minute') {
      data = { 
        'selectedMinute': this.scheduleform.value.selectedMinute,
        'days' : {
          'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
          'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
          'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
          'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
          'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
          'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
          'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
        }
      }
    }
    if (this.selectedschedule === 'Daily' || this.selectedschedule === 'Hourly') {
      data = {'selectedtype':this.scheduleform.value.selectedtype}
    }
    if (this.selectedschedule === 'Weekly') {
      data = { 'days' : {
        'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
        'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
        'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
        'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
        'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
        'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
        'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
      }}
    }
    if (this.selectedschedule === 'Monthly') {
      data = 
        {
          'selectedmonthlyschedule': this.selectedMonthlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
        }
    }
    if (this.selectedschedule === 'Yearly') {
      data = 
        {
          'selectedyearlyschedule': this.selectedYearlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
          'selectedmonth': this.scheduleform.value.selectedmonth,
        }
      }
    var form = {
      'scheduletype': this.scheduleform.value.scheduletype,
      'noenddate': this.scheduleform.value.noenddate,
      'pickervalue': this.scheduleform.value.pickervalue,
      'selectedReminder': this.scheduleform.value.selectedReminder,
      [this.selectedschedule]: data,
    }
    if (this.scheduleform.get('noenddate').value === 'true') {
      this.enddate = null;
     } else {
       this.enddate = this.scheduleform.get('pickervalue').value;
     }
    if (this.scheduleform.get('scheduletype').value === 'Weekly' || this.scheduleform.get('scheduletype').value === 'Minute') {
      this.weeklyValidationStatus = this.utilservice.checkboxvalidity(data);
    }
    if (this.weeklyValidationStatus) {
      // console.log('form',)
      this.formsservice
        .createschedule(ids, teams, this.selectedschedule, this.enddate, form, this.scheduleform.value.taskpriority, this.scheduleform.value.dueindays, 'Create', this.scheduleform.value.selectedWorkflow)
        .subscribe((response: any) => {
            if (response.error === '') {
              if (response.status === 'Success') {
                this.isCreate = false;
                this.getformschedules(this.formid);
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!'});
                this.scheduleform.reset();
              }
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
      });
    } else {
       this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select days for schedule'});
    }
    
  }

  removeSchedules(event: any) {
    const scheduleids = [];
    this.selectedScheArr.forEach(schedule => {
      scheduleids.push(schedule.id);
    });
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove schedules?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formsservice.deteteschedule(scheduleids).subscribe(res => {
          const apires = res;
          if (apires.error === '') {
            if (apires.status === 'Success') {
              this.getformschedules(this.formid);
              this.selectedScheArr = [];
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully deleted!'});
            }
          } else {
            this.errmsg = apires.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg});
          }
        });
      },
      reject: () => {}
    });
  }

  public updateScheduleStatus(scheduleid: string, status: string) {
    this.formsservice.updateschedulerunstatus(scheduleid, status).subscribe(res => {
      const apires = res;
      if (apires.error === '') {
        if (apires.status === 'Success') {
          this.getformschedules(this.formid);
        }
      } else {
        this.errmsg = apires.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  public runImmediately(scheduleid: string) {
    // console.log(event, status);
    this.formsservice.runtaskimmediately(scheduleid).subscribe(
      (res) => {
        const apires = res;
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Schedule Task Queued!' });
        /** No responce expected
        if (apires.error === '') {
          if (apires.status === 'Success') {
            setTimeout(() => {
              this.runimmediatelymsg = true;
              console.log('task created');
            }, 2000);
            this.getform(this.formid);
            // this.table.renderRows();
          }
        } else {
          this.errmsg = apires.error;
        }
        */
    });
  }

  cancleForm() {
    this.isCreate = false;
    this.selectedschedule = '';
    this.scheduleform.reset();
  }



}
