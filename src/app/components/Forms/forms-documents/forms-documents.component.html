<a href="https://docs.unvired.com/builder/admin/forms/#documents" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="doc">

  <!-- Create Document -->
  <p-accordion styleClass="mb-3" *ngIf="isCreate">
      <p-accordionTab [selected]="true" >
        <ng-template pTemplate="header">
            <span *ngIf="firstPart">Upload Document</span>
            <span *ngIf="secondPart">URL Document</span>
            <span *ngIf="thirdPart">Associated Document</span>
        </ng-template>
        <ng-template pTemplate="content">
          <div class="card">
            <form [formGroup]="createDocumentform" novalidate>
              <!-- First Page -->
              <div *ngIf="firstPart" class="grid formgrid">
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="teamid" class="_required">Document Name</label>
                  </span>
                </div>
                <div class="col-6 mt-4">
                  <div class="flex flex-row align-items-center">
                    <p-inputSwitch id="Download" formControlName="autoDownload"></p-inputSwitch>
                    <label htmlFor="Download" class="ml-2 mb-1">Auto Download</label>
                  </div>
                </div>
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" required="true" class="w-full"></textarea>
                    <label htmlFor="description" class="_required">Description</label>
                  </span>
                </div>
                <div class="col-6 mt-4">
                  <div class="file-drop-area">
                    <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" label="Upload Document"></button>
                    <span class="file-msg"> {{file ? file.name : 'Drag and drop here' }} </span>
                    <input class="file-input" pInputText #fileInput type="file" (change)="processFile(fileInput)">
                  </div>
                </div>
                
                <div class="col-12 text-right mt-2">
                  <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeAccordian()"></button>
                  <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="addDocument()" [disabled]="checkFileValidation()"></button>
                </div>
              </div>
        
              <!-- second page -->
              <div *ngIf="secondPart" class="grid formgrid">
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="teamid" class="_required">Name</label>
                  </span>
                </div>
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <p-dropdown id="Content" [options]="typeOfdocumentContent" formControlName="urlType" [autoDisplayFirst]="false" class="p-inputtext-sm" styleClass="w-full" required="true"></p-dropdown>
                    <label htmlFor="Content" class="_required">Content Type</label>
                  </span>
                </div>
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" required="true" class="w-full"></textarea>
                    <label htmlFor="description" class="_required">Description</label>
                  </span>
                </div>
                <div class="col-6 mt-4">
                  <span class="p-float-label">
                    <input id="url" pInputText formControlName="docUrl" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="url" class="_required">URL</label>
                  </span>
                </div>
                <div class="col-12 mt-2 text-right">
                  <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeAccordian()"></button>
                  <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="addDocument()" [disabled]="!createDocumentform.valid"></button>
                </div>
              </div>
            </form>

            <!-- third page -->
            <div *ngIf="thirdPart">
              <div class="col-12 mt-1">
                  <span class="p-float-label">
                      <p-multiSelect [style]="{'min-width': '100%'}" id="Associate" [options]="docList" [(ngModel)]="selectedDocument" optionLabel="docName" optionValue="docId" display="chip"></p-multiSelect>
                      <label htmlFor="Associate">Associate Document</label>
                  </span>
              </div>
              <div class="col-12 text-right">
                  <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeAccordian()"></button>
                  <button class="p-button-sm bg-blue" pButton type="button" label="Link" (click)="AssociateDocument()" [disabled]="!selectedDocument"></button>
              </div>
            </div>
          </div>
        </ng-template>
      </p-accordionTab>
  </p-accordion>

  <!-- Table Data -->
  <p-table *ngIf="!isCreate" #dt #paginator
      [value]="documentsArr" dataKey="docId"
      responsiveLayout="scroll" 
      [scrollable]="true" 
      selectionMode="multiple" 
      styleClass="p-datatable-sm p-datatable-gridlines"
      [tableStyle]="{ 'min-width': '50rem' }"
      [(selection)]="selectedDocs"
      (onRowCollapse)="onRowCollapse()" 
      (onRowExpand)="onRowExpand()" 
      rowExpandMode="single"
      [globalFilterFields]="['docName', 'docDesc', 'lastUpdatedBy']">

    <ng-template pTemplate="caption">
      <div class="flex flex-row align-items-center justify-content-between">
          <div>
                <span class="p-input-icon-left">
                  <i class="pi pi-search"></i>
                  <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                    [(ngModel)]="searchquery" (keyup)="onSearch()" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
          </div>
          <div>
              <button pButton (click)="showDeleteDialog($event)" icon="pi pi-trash" class="p-button-sm p-button-danger mr-2" [disabled]="selectedDocs.length === 0" pTooltip="Remove">
              </button>
              <button pButton (click)="ActionMenu.show($event)" label="Document" icon="pi pi-angle-down" iconPos="right" class="p-button-sm bg-blue">
              </button>
              <!-- <button pButton (click)="createDoc.toggle($event);showAddURLPannel()" label="Add URL" class="p-button-sm bg-blue mr-2">
              </button>
              <button pButton (click)="createDoc.toggle($event);showAssociatedDocPannel()" label="Associate Document" class="p-button-sm bg-blue">
              </button> -->
          </div>
      </div>
    </ng-template>

  <!-- Header -->
      <ng-template pTemplate="header" let-columns>
          <tr>
              <th style="width: 3rem" class="text-center"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
              <th style="min-width: 10rem">Name </th>
              <th style="min-width: 10rem">Description </th>
              <th style="min-width: 10rem">Updated By </th>
              <!-- <th style="min-width: 10rem">Updated On </th> -->
              <th style="width: 3rem" class="text-center">Type </th>
          </tr>
      </ng-template>

  <!-- Table data -->
      <ng-template pTemplate="body" let-doc let-columns="columns" let-expanded="expanded">
          <tr (click)="dt.toggleRow(doc, $event);showEditDocument(doc)" class="cursor-pointer">
            <td class="text-center" id="{{'rowExpansion'+ doc.docId}}">
              <p-tableCheckbox [value]="doc" (click)="$event.stopPropagation()"></p-tableCheckbox>
            </td>
              <td>
                  {{doc.docName}}
              </td>
      
              <td>
                  {{doc.docDesc}}
              </td>
      
              <!-- <td>
                  {{doc.lastUpdatedBy}}
              </td> -->
      
              <td>
                <div class="flex flex-row justify-content-start">
                  <span>{{doc.lastUpdatedBy}}, <span *ngIf="doc?.lastUpdated" class="ml-1">{{formatDate(doc.lastUpdatedAt | date: 'MM/dd/yyyy h:mm a')}}</span></span> 
                </div>
              </td>
      
              <td>
                <span class="material-icons cursor-pointer" (click)="viewDocument(doc);$event.stopPropagation()" style="color: var(--primary-color)">{{doc.attachIcon}}</span>
                <!-- <button pButton icon="doc.attachIcon" class="p-button-text p-button-rounded p-button-primary material-icons" (click)="viewDocument(doc);$event.stopPropagation()"></button> -->
              </td>
          </tr>
      </ng-template>
      
<!-- Row expand -->
      <ng-template pTemplate="rowexpansion" let-element>
        <tr>
          <td colspan="12">
            <div class="card">
              <form [formGroup]="createDocumentform" novalidate>
                <!-- First Page -->
                <div *ngIf="firstPart" class="grid formgrid">
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
                      <label htmlFor="teamid" class="_required">Document Name</label>
                    </span>
                  </div>
                  <div class="col-6 mt-4">
                    <div class="flex flex-row align-items-center">
                      <p-inputSwitch id="Download" formControlName="autoDownload"></p-inputSwitch>
                      <label htmlFor="Download" class="ml-2 mb-1">Auto Download</label>
                    </div>
                  </div>
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" required="true" class="w-full"></textarea>
                      <label htmlFor="description" class="_required">Description</label>
                    </span>
                  </div>
                  <div class="col-6 mt-4">
                    <div class="file-drop-area">
                      <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" label="Upload Document"></button>
                      <span class="file-msg"> {{file ? file.name : 'Drag and drop here' }} </span>
                      <input class="file-input" pInputText #fileInput type="file" (change)="processFile(fileInput)">
                    </div>
                  </div>
                  
                  <div class="col-12 text-right mt-2">
                    <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeExpandedRow(element.docId)"></button>
                    <button *ngIf="isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateDocument()" [disabled]="checkFileValidation()"></button>
                  </div>
                </div>
          
                <!-- second page -->
                <div *ngIf="secondPart" class="grid formgrid">
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
                      <label htmlFor="teamid" class="_required">Name</label>
                    </span>
                  </div>
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <p-dropdown id="Content" [options]="typeOfdocumentContent" formControlName="urlType" [autoDisplayFirst]="false" class="p-inputtext-sm" styleClass="w-full" required="true"></p-dropdown>
                      <label htmlFor="Content" class="_required">Content Type</label>
                    </span>
                  </div>
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" required="true" class="w-full"></textarea>
                      <label htmlFor="description" class="_required">Description</label>
                    </span>
                  </div>
                  <div class="col-6 mt-4">
                    <span class="p-float-label">
                      <input id="url" pInputText formControlName="docUrl" class="w-full p-inputtext-sm" required="true">
                      <label htmlFor="url" class="_required">URL</label>
                    </span>
                  </div>
                  <div class="col-12 mt-2 text-right">
                    <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="closeExpandedRow(element.docId)"></button>
                    <button *ngIf="isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateDocument()" [disabled]="!createDocumentform.valid"></button>
                  </div>
                </div>
              </form>
              <!-- third page will not be here because no update -->
            </div>
          </td>
        </tr>
      </ng-template>

<!-- Paginator -->
      <ng-template pTemplate="footergrouped">
        <tr>
            <td colspan="5">
              <p-paginator class="p-0" [rows]="pageSize" [first]="first" [totalRecords]="dataLength" [showJumpToPageDropdown]="false" dropdownAppendTo="body"
                (onPageChange)="getDocuments($event,searchquery)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
              </p-paginator>
            </td>
        </tr>
       
      </ng-template>

  <!-- No data -->
      <ng-template pTemplate="emptymessage">
          <tr>
            <td class="text-center" style="font-weight: bold;" colspan="5">No Documents Found.</td>
          </tr>
      </ng-template>

  </p-table>

</div>
  
  
  <!-- Create Document -->
  <!-- <p-overlayPanel #createDoc styleClass="p-0" [style]="{'max-width': '400px'}">
    <form [formGroup]="createDocumentform" novalidate>
      <div class="grid">
        <div *ngIf="firstPart">
          <div class="col-12 mt-3">
            <span class="p-float-label">
              <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
              <label htmlFor="teamid" class="_required">Document Name</label>
            </span>
          </div>
          <div class="col-12">
            <span class="p-float-label">
              <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" class="w-full"></textarea>
              <label htmlFor="description">Description</label>
            </span>
          </div>
          <div class="col-12">
            <div class="file-drop-area">
              <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" label="Upload Document"></button>
              <span class="file-msg"> {{file ? file.name : 'Drag and drop file here' }} </span>
              <input class="file-input" pInputText #fileInput type="file" (change)="processFile(fileInput)">
            </div>
          </div>
          <div class="flex flex-row mt-3 px-3 align-items-center">
            <p-inputSwitch id="Download" formControlName="autoDownload"></p-inputSwitch>
            <label htmlFor="Download" class="ml-2">Auto Download</label>
          </div>
          <div class="col-12 text-right">
            <button *ngIf="!isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="addDocument(createDoc)" [disabled]="checkFileValidation()"></button>
            <button *ngIf="isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateDocument(createDoc)" [disabled]="checkFileValidation()"></button>
          </div>
        </div>
  
        <div *ngIf="secondPart">
          <div class="col-12 mt-1">
            <span class="p-float-label">
              <input id="teamid" pInputText formControlName="docName" class="w-full p-inputtext-sm" required="true">
              <label htmlFor="teamid" class="_required">Name</label>
            </span>
          </div>
          <div class="col-12">
            <span class="p-float-label">
              <textarea id="description" rows="2" cols="30" pInputTextarea formControlName="docDesc" required="true" class="w-full"></textarea>
              <label htmlFor="description" class="_required">Description</label>
            </span>
          </div>
          <div class="col-12">
            <span class="p-float-label">
              <p-dropdown id="Content" [options]="typeOfdocumentContent" formControlName="urlType" [autoDisplayFirst]="false" class="p-inputtext-sm" styleClass="w-full" required="true"></p-dropdown>
              <label htmlFor="Content" class="_required">Content Type</label>
            </span>
          </div>
          <div class="col-12">
            <span class="p-float-label">
              <input id="url" pInputText formControlName="docUrl" class="w-full p-inputtext-sm" required="true">
              <label htmlFor="url" class="_required">URL</label>
            </span>
          </div>
          <div class="col-12 text-right">
            <button *ngIf="!isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="addDocument(createDoc)" [disabled]="!createDocumentform.valid"></button>
            <button *ngIf="isUpdate" class="p-button-sm bg-blue" pButton type="button" label="Update" (click)="updateDocument(createDoc)" [disabled]="!createDocumentform.valid"></button>
          </div>
        </div>
      </div>
    </form>

    <div *ngIf="thirdPart">
      <div class="col-12 mt-1">
          <span class="p-float-label">
              <p-multiSelect [style]="{'min-width': '300px'}" id="Associate" [options]="docList" [(ngModel)]="selectedDocument" optionLabel="docName" optionValue="docId" display="chip"></p-multiSelect>
              <label htmlFor="Associate">Associate Document</label>
          </span>
      </div>
      <div class="col-12 text-right">
          <button class="p-button-sm bg-blue" pButton type="button" label="Link" (click)="AssociateDocument(createDoc)" [disabled]="!selectedDocument"></button>
      </div>
    </div>
    
  </p-overlayPanel> -->

<p-overlayPanel #ActionMenu [style]="{'max-width': '400px'}">
    <div class="col p-0">
        <ul class="custom-ul">
            <li class="custom-li" (click)="showUploadDocumentPannel(ActionMenu)"><i class="pi pi-link menu-icons"></i>Upload</li>
            <li class="custom-li" (click)="showAddURLPannel(ActionMenu)"><i class="pi pi-database menu-icons"></i>URL</li>
            <li class="custom-li" (click)="showAssociatedDocPannel(ActionMenu)"><i class="pi pi-clock menu-icons"></i>Associated</li>
        </ul>
    </div>
</p-overlayPanel>