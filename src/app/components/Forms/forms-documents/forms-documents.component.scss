.main-title {
    font-size: 14px;
    font-weight: 700;
  }
  .description {
    font-size: 14px;
    font-weight: 400;
  }
  .doc-list {
    border: none !important;
  }
  .doc-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  ::ng-deep .doc p-dataview .p-dataview-header {
    padding: 0px !important;
    background: none !important;
    border: none !important;
    margin-bottom: 12px !important;
  }
  ::ng-deep .doc p-dataview .p-dataview-footer {
    padding: 0px !important;
    background: none !important;
    border: none !important;
  }
  
  .file-drop-area {
    border: 1px dashed #b2b8be;
    border-radius: 3px;
    position: relative;
    max-width: 100%;
    padding: 18px;
    -webkit-transition: 0.2s;
    transition: 0.2s;
  }
  .file-msg {
    font-size: medium;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 10px;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: calc(100% - 130px);
    padding-bottom: 2px;
  }
  
  .file-input {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0;
  }
  
  ::ng-deep .doc p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 100px;
  }
  
  ::ng-deep .doc p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }

//   ::ng-deep .doc p-dropdown .p-dropdown-panel {
//     position: unset !important;
//     top: unset !important;
//     left: unset !important;
// }

.custom-ul {
  list-style-type: none !important;
  padding-left: 0px !important;
  margin-bottom: 5px;
  margin-top: 5px;
}
.custom-li {
  display: flex;
  align-items: center;
  cursor: pointer !important;
  padding: 8px;
}
.menu-icons {
  font-size: 18px;
  margin-right: 8px;
}
.custom-li:hover {
  background-color: #e9ecef;
  border-radius: 5px;
}
.help-icon {
  position: absolute;
  top: 22px;
  right: 55px;
}