import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import * as dayjs from 'dayjs';
import * as relativeTime from 'dayjs/plugin/relativeTime';
import { Table } from 'primeng/table';
import { debounceTime, Subject } from 'rxjs';
dayjs.extend(relativeTime);

interface Document {
  docId: string;
  docDesc: string;
  attachId: string;
  lastUpdatedBy: string;
  lastUpdatedAt: string;
  docName: string;
  docType: string;
  docUrl: string;
  formId: string;
  assignment: boolean;
  updateDoc: boolean;
  urlType: string;
  autoDownload: boolean;
}

@Component({
  selector: 'app-forms-documents',
  templateUrl: './forms-documents.component.html',
  styleUrls: ['./forms-documents.component.scss']
})
export class FormsDocumentsComponent implements OnInit {
  title: string;
  message: string;

  // dataSource: FormDocumentDataSource;
  formDocumentDataSubject: any;
  formId: string;
  pageSize: number = 0;
  pageIndex: number = 10;
  first: number = 0;
  query = '';
  filled = false;
  actionButtons = false;
  searchquery: string = '';
  docObject = new Document();
  deleteresponse: string;
  docTobeEdited: Document;
  dataLength: number = 0;
  isRowExpand: boolean = false;

  /** Columns displayed in the table. Columns IDs can be added, removed, or reordered. */
  displayedColumns = [
    'select',
    'docName',
    'description',
    'lastUpdatedOn',
    'lastUpdatedBy',
    'docType',
  ];
  durationInSeconds = 5;

  selectedDocs: any = [];
  pageIndexDocs: any;
  documentsArr: any = [];
  pagination: any;

  firstPart: boolean = false;
  secondPart: boolean = false;
  thirdPart: boolean = false;
  createDocumentform: FormGroup;
  typeOfdocumentContent: any[] = [ 'Others', 'Audio', 'Video'];
  docList: Document[] = [];
  attachid: string;
  public file: File;
  selectedDocument: any;
  updateData: Document;
  isUpdate: boolean = false;
  isCreate: boolean = false;
  errmsg: string;
  searchSubject: Subject<string> = new Subject();

  constructor(
    private formsservice: FormsService,
    private router: Router,
    public data: DynamicDialogConfig,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private fb: FormBuilder
  ) {}

  ngOnInit() {
    this.formId = this.data.data.formId;
    // this.dataSource = new FormDocumentDataSource(this.formsservice);
    // console.log(FormDocumentDataSource.length);
    this.getDocuments(null, '');
    this.createDocumentform = this.fb.group({
      docName: [
        '',
        [Validators.required, Validators.maxLength(100)],
      ],
      docDesc: ['', Validators.required],
      docFile: [''],
      docUrl: [''],
      autoDownload: [false],
      urlType: ['']
    });
    this.searchSubject.pipe( // only for search bar
      debounceTime(500) 
    ).subscribe(searchText => {
      const page = {
        first: 0,
        page: 0,
        rows: this.pagination && typeof this.pagination.rows === 'number' ? this.pagination?.rows : 10
      }
      this.getDocuments(page, this.searchquery);
    });
  }

  getDocuments(page: any, filter: string) {
    this.pagination = page;
    this.pageSize = this.pagination ? this.pagination?.rows : 10;
    this.pageIndex = this.pagination ? this.pagination?.page * this.pagination?.rows : 0;
    this.first = this.pagination?.first ? this.pagination?.first : 0;

    this.formsservice
      .getDocumentDataAsObservable(this.formId, this.pageSize, this.pageIndex, filter)
      .subscribe((ObservableResult: any) => {
        const parsedData = ObservableResult;
        this.documentsArr = parsedData.formDocuments;
        this.dataLength = parsedData.totalRecords;
        // this.pageIndexDocs = parsedData.nextOffSet;
        // stream values for user data table
        // this.formDocumentDataSubject.next(parsedData.formDocuments);
    });
  }

  onSearch() {
    this.searchSubject.next(this.searchquery);
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  // tslint:disable-next-line:no-shadowed-variable
  viewDocument(element) {
    if (element.docUrl) {
      this.router.navigate([]).then((result) => {
        window.open(element.docUrl, '_blank');
      });
    } else {
      // console.log(element.attachId);

      const attachid = element.attachId;
      this.formsservice.DownloadDocument(attachid).subscribe((res) => {
        // console.log(res);
        // const blob = new Blob([res], { type: 'application/octet-stream' });
        // console.log(res);
        const blob = new Blob([res], { type: 'octet/stream' });
        const a = document.createElement('a');
        document.body.appendChild(a);
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = element.fileName;
        a.click();
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        }, 0);
      });
    }
  }

  showDeleteDialog(event: any) {
    const docIds = [];
    this.selectedDocs.forEach((docs) => {
      docIds.push(docs.docId);
    });
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Delete document?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formsservice
      .deleteDocument(this.formId, docIds.toString(), true)
      .subscribe((data) => {
        const response = data;
        if (response.error) {
          this.deleteresponse = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.deleteresponse });
        }
        // method to update the table
        this.getDocuments(this.pagination, '')
      });
      },
      reject: () => {}
    });
  }

  showUploadDocumentPannel(element: any) {
    if (this.updateData?.docId) {
      this.closeExpandedRow(this.updateData.docId);
    }
    this.resetForm();
    this.firstPart = true;
    this.secondPart = false;
    this.thirdPart = false;
    this.isUpdate = false;
    this.isCreate = true;
    element.hide();
  }

  showAddURLPannel(element: any) {
    if (this.updateData?.docId) {
      this.closeExpandedRow(this.updateData.docId);
    }
    this.resetForm();
    this.firstPart = false;
    this.secondPart = true;
    this.thirdPart = false;
    this.isUpdate = false;
    this.isCreate = true;
    element.hide();
  }

  showAssociatedDocPannel(element: any) {
    if (this.updateData?.docId) {
      this.closeExpandedRow(this.updateData.docId);
    }
    this.resetForm();
    this.getDocumentList();
    this.isCreate = true;
    this.firstPart = false;
    this.secondPart = false;
    this.thirdPart = true;
    element.hide();
  }

  showEditDocument(data: any) {
    this.updateData = data;
    this.isUpdate = true;
    if (!data.docUrl) {
      // if attachment
      this.firstPart = true;
      this.secondPart = false;
      this.thirdPart = false;
      this.createDocumentform.patchValue({
        docName: data.docName,
        docDesc: data.docDesc,
        autoDownload: data.autoDownload
      });
    } else if (data.docUrl) {
      // If Url
      this.createDocumentform.patchValue({
        docName: data.docName,
        docDesc: data.docDesc,
        docUrl: data.docUrl,
        urlType: data.urlType
      });
      this.firstPart = false;
      this.secondPart = true;
      this.thirdPart = false;
    }
  }

  resetForm() {
    this.createDocumentform.reset();
    this.selectedDocument = [];
  }

  closeExpandedRow(id: any) {
    if (this.isRowExpand) {
      document.getElementById('rowExpansion'+id).click();
    }
  }

  closeAccordian() {
      this.firstPart = false;
      this.secondPart = false;
      this.thirdPart = false;
      this.isCreate = false;
      this.resetForm();
  }

  // get the documents avaialble for associate
  getDocumentList() {
    const form = {
      formId: this.formId,
    };
    return this.formsservice.getAllDocument(form).subscribe((data) => {
      // console.log(data.totalRecords);
      const usersJson = data;
      this.docList = usersJson.formDocuments;
    });
  }

  processFile(fileInput: any) {
    this.file = fileInput.files[0];
    this.formsservice.uploadDocument(this.file).subscribe((data) => {
      this.attachid = data.attachmentId;
    });
  }

  checkFileValidation(): boolean {
    if (this.createDocumentform.valid) {
      if (this.file?.name) {
        return false;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  addDocument() {
    this.formsservice.addDoctToServer(
        this.formId,
        this.createDocumentform.value,
        this.attachid
      ).subscribe((data) => {
          const parsedData = data;
          if (parsedData.error) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: '' });
          } else {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: '' });
            this.getDocuments(null, '');
            this.closeAccordian();
          }
        });
  }

  updateDocument() {
    this.updateData.docName = this.createDocumentform.get('docName').value;
      this.updateData.docDesc = this.createDocumentform.get('docDesc').value;
      this.updateData.docUrl = this.createDocumentform.get('docUrl').value;
      this.updateData.autoDownload = this.createDocumentform.get('autoDownload').value;
      this.updateData.urlType = this.createDocumentform.get('urlType').value;
      this.updateData.formId = this.formId;
      this.updateData.updateDoc = true;
  
      if (this.attachid) {
        this.updateData.attachId = this.attachid;
      } else {
        this.updateData.attachId = this.updateData.attachId;
      }
      this.formsservice.updateDocsInTheServer(this.updateData).subscribe(
        (parsedData) => {
          if (parsedData.error) {
            this.errmsg = parsedData.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          } else {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
            if (this.updateData?.docId) {
              this.closeExpandedRow(this.updateData.docId);
            }
            this.getDocuments(null, '');
            this.resetForm();
          }
        });
  }

  AssociateDocument(element: any) {
    const selectedDocs = {
      assignment: true,
      formId: this.formId,
      docId: this.selectedDocument.toString(),
    };
    return this.formsservice.AssociateDocument(selectedDocs).subscribe(
      (parsedData) => {
        if (parsedData.error) {
            this.errmsg = parsedData.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        } else {
          this.getDocuments(null, '');
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Document associated successfully!' });
          element.hide();
        }
      });
  }

  onRowCollapse() {
    this.isRowExpand = false;
  }

  onRowExpand() {
    this.isRowExpand = true;
  }

  clearAllFilter(table: Table) {
    table.clear();
    const page = {
      first: 0,
      page: 0,
      rows: 10
    }
    this.getDocuments(page, null);
    this.searchquery = null;
  }

}
