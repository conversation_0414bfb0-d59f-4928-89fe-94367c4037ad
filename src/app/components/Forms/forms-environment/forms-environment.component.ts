import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Table } from 'primeng/table';
import { FormsService } from 'src/app/services/forms.service';

@Component({
  selector: 'app-forms-environment',
  templateUrl: './forms-environment.component.html',
  styleUrls: ['./forms-environment.component.scss']
})
export class FormsEnvironmentComponent implements OnInit {
  public keyValueForm: FormGroup;
  public attributepairs: any[] = [];
  public formid: string;
  public errmsg: string;
  public attributesList = [];
  public defaultAttributesList = [];
  attributes: any[] = [];
  change: boolean = false;
  searchQuery: string;

  constructor(
    private formsservice: FormsService,
    private router: Router,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
    public ref: DynamicDialogRef
  ) {}
  ngOnInit() {
    // this.formid = this.route.parent.snapshot.paramMap.get("formid");
    this.formid = this.data.data.formId;
    // this.keyValueForm = this.fb.group({
    //   attributes: this.fb.array([]),
    // });
    this.getformattributes(this.formid);
  }
  public getformattributes(formid: string) {
    this.formsservice
      .getdefaultAttributes("USER")
      .subscribe((attributes: any) => {
        let attrs = attributes;
        console.log(attrs);
        
        if (attrs.length > 0) {
          this.defaultAttributesList = attrs;
          this.attributesList = attrs;
        }
    });

    this.formsservice.getformAttr(formid, "USER").subscribe((form: any) => {
      this.attributepairs = form.attributes;
      if (this.attributepairs.length > 0) {
        this.attributepairs.forEach((pair) => {
          var index = this.attributesList.findIndex((p) => p.key === pair.key);
          if (index > -1) {
            this.attributesList.splice(index, 1);
          }
          this.attributes.push({key: pair.key, value: pair.value});
          this.attributes = this.attributes.map(v => ({...v, readonly: true}));
        });
      }
    });
  }
  // get attributes() {
  //   return this.keyValueForm.get("attributes") as FormArray;
  // }

  // addCustomRow() {
  //   this.attributes.push(this.createItem("", "", false));
  // }
  // addAttrribute(list: any, index) {
  //   this.attributes.push(this.createItem(list.key, "", true));
  //   if (index > -1) {
  //     this.attributesList.splice(index, 1);
  //   }
  // }



  // createItem(key: string, value: string, readOnly: boolean) {
  //   return this.fb.group({
  //     key: [{ value: key, disabled: readOnly }, [Validators.required]],
  //     value: [{ value: value, disabled: false }, [Validators.required]],
  //   });
  // }
  saveattributespairs() {
    this.attributes = this.attributes.map(({ readonly, ...r }) => r);
    const valueArr = this.attributes.map(function (item) {
      console.log(item.key);
      return item.key;
    });
    const isDuplicate = valueArr.some(function (item, idx) {
      return valueArr.indexOf(item) !== idx;
    });
    if (isDuplicate) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Setting already exists' });
    } else {
      console.log('arrrrr',this.attributes)
      let obj = { attributes: this.attributes };
      this.formsservice
        .saveattributes(this.formid, obj, "USER")
        .subscribe((res) => {
          const apires = res;
          if (apires.error === "") {
            if (apires.status === "Success") {
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Attributr saved!' });
              this.ref.close();
            }
          } else {
            this.errmsg = apires.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
    }
  }

  pageReload() {
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    this.router.onSameUrlNavigation = "reload";
    this.router.navigate(["forms/", this.formid, `attributes`]);
  }

  addRow() {
    let arr: any = {
      key: '',
      value: '',
    }
    this.attributes.push(arr);
  }

  removeRow(index) {
    if (
      index > -1 &&
      this.attributes &&
      this.attributes.length > 0 &&
      this.attributes[index] &&
      this.attributes[index].key
    ) {
      let searchIndex = index;
      let ind = this.defaultAttributesList.findIndex(
        (p) => p.key === this.attributes[searchIndex].key
      );
      if (ind > -1) {
        let obj = {
          key: this.attributes[searchIndex].key,
          value: this.attributes[searchIndex].value,
          icon: this.attributes[searchIndex].icon,
        };
        this.attributesList.push(obj);
        this.change = true;
      }
    }
    this.attributes.splice(index, 1);
  }

  checkValidation(): boolean {
    let res = false;
    if (this.attributes?.length == 0) {
      res = true;
    } else if (this.attributes?.length > 0) {
      this.attributes.forEach(element => {
        if ((element.key == '' || element.key == null) ||
          (element.value == '' || element.value == null)) {
          res = true;
          return;
        }
      });
    }
    return res;
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.attributes = [];
    this.searchQuery = '';
    this.getformattributes(this.formid);
  }
  
}
