<a href="https://docs.unvired.com/builder/admin/forms/#environment" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<p-table #dt [value]="attributes" responsiveLayout="scroll" scrollHeight="45vh" 
    [globalFilterFields]="['key', 'value']" styleClass="p-datatable-gridlines p-datatable-sm">
    <ng-template pTemplate="caption">
        <div class="flex flex-row justify-content-between align-items-center">
            <div>
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchQuery"
                        (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
            </div>
            <div>
                <button class="p-button-sm bg-blue mr-2" pButton type="button" label="Add" icon="pi pi-plus" (click)="addRow()"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Save" [disabled]="(checkValidation() && !change)" (click)="saveattributespairs()"></button>    
            </div>
        </div>
      </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th class="required text-align-center">Key</th>
            <th class="_required">Value</th>
            <th class="text-center" style="width: 3rem">Action</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-item let-i="rowIndex">
        <tr>
            <td>
                <input [readonly]="item.readonly" placeholder="Add key" class="w-full p-inputtext-sm" type="text" pInputText [(ngModel)]="item.key" required="true"/>
            </td>
            <td>
                <input placeholder="Add key value" [(ngModel)]="item.value" class="w-full p-inputtext-sm" type="text" pInputText required="true"/>
            </td>
            <td class="text-center">
                <button pButton type="button" icon="pi pi-times" pTooltip="Remove This Row" tooltipPosition="top"
                    class="p-button-rounded p-button-text p-button-danger" style="outline: none !important;"(click)="removeRow(i)">
                </button>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-center" style="font-weight: bold;" colspan="3">No environment setup!</td>
        </tr>
    </ng-template>
</p-table>