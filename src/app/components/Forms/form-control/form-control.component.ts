import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';

@Component({
  selector: 'app-form-control',
  templateUrl: './form-control.component.html',
  styleUrls: ['./form-control.component.scss']
})
export class FormControlComponent implements OnInit {
  formConfigurations: FormGroup;
  errmsg: string;
  formFieldsArr: any = [];
  private formId: string;
  private showOnlyHiddenFields: boolean = false;

  constructor(
    private fb: FormBuilder,
    private formservice: FormsService,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
    private ref: DynamicDialogRef,
  ) {}

  ngOnInit() {
    this.formId = this.data.data.formId;
    this.formConfigurations = this.fb.group({
      isSaveForm: [false],
      saveMessage: [''],
      isCompleteForm: [false],
      completeMessage: [''],
      isCondition: [false],
      conditionFormfields: [''],
      isIdentifier: [false],
      identifierFormfields: [''],
    });
    this.getFormFields();
    this.getFormAttributes();
  }

  saveFormConfigurations() {
    const obj = {
      "attributes": [
        // {
        //   "label": "Disable Form Completion",
        //   "key": "disable-form-completion",
        //   "value": this.formConfigurations.get('fileName').value,
        //   "type": "Checkbox",
        //   "help": "If form is not used to submit any data, set this option to hide the Save and Complete buttons",
        //   "order": 0
        // },
        {
          "label": "Prompt On Form Save",
          "key": "prompt-on-form-save",
          "value": this.formConfigurations.get('isSaveForm').value,
          "type": "Checkbox",
          "help": "Displays a prompt when users save the form without filling it 100%",
          "order": 0
        },
        {
          "label": "Form Save Message",
          "key": "message-form-save",
          "value": this.formConfigurations.get('saveMessage').value,
          "type": "Text",
          "help": "Message to display when user saves the form",
          "order": 1
        },
        {
          "label": "Prompt On Form Complete",
          "key": "prompt-on-form-complete",
          "value": this.formConfigurations.get('isCompleteForm').value,
          "type": "Checkbox",
          "help": "Displays a prompt when users complete the form without filling it 100%",
          "order": 2
        },
        {
          "label": "Form Complete Message",
          "key": "message-form-complete",
          "value": this.formConfigurations.get('completeMessage').value,
          "type": "Text",
          "help": "Message to display when user completes the form",
          "order": 3
        },
        {
          "label": "Field To Enable Form Completion",
          "key": "client-complete-check-field",
          "value": this.formConfigurations.get('conditionFormfields').value,
          "type": "list",
          "help": "Allows form to be completed if this field is True and form is 100% filled in (Only hidden fields allowed)",
          "order": 4
        },
        {
          "label": "Turbo Forms Display Field",
          "key": "client-dashboard-display-field",
          "value": this.formConfigurations.get('identifierFormfields').value,
          "type": "list",
          "help": "Displays the selected field in the Turbo Forms Dashboard for easy identification",
          "order": 5
        }
      ]
    };
    this.formservice.saveattributes(this.formId, obj, 'SYSTEM')
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          
          this.ref.close();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!' });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetForm() {
    this.formConfigurations.reset();
    this.ref.close(null);
  }

  getFormFields() {
    this.formservice.getFormFieldsForAttr(this.formId, this.showOnlyHiddenFields).subscribe((response: any) => {
      if (response && response.status.toLowerCase() === 'success') {
        this.formFieldsArr = response?.formFields || [];
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  getFormAttributes() {
    this.formservice.getformAttr(this.formId, 'SYSTEM').subscribe((response: any) => {
      if (response && response.status.toLowerCase() === 'success') {
        if (response?.attributes?.length > 0) {
          for (let i = 0; i < response.attributes.length; i++) {
            if (response.attributes[i].key === 'prompt-on-form-save') {
              this.formConfigurations.patchValue({
                isSaveForm: response.attributes[i].value
              });
            }
            if (response.attributes[i].key === 'message-form-save') {
              this.formConfigurations.patchValue({
                saveMessage: response.attributes[i].value
              });
            }
            if (response.attributes[i].key === 'prompt-on-form-complete') {
              this.formConfigurations.patchValue({
                isCompleteForm: response.attributes[i].value
              });
            }
            if (response.attributes[i].key === 'message-form-complete') {
              this.formConfigurations.patchValue({
                completeMessage: response.attributes[i].value
              });
            }
            if (response.attributes[i].key === 'client-complete-check-field') {
              this.formConfigurations.patchValue({
                conditionFormfields: response.attributes[i].value,
                isCondition: response.attributes[i].value ? true : false
              });
            }
            if (response.attributes[i].key === 'client-dashboard-display-field') {
              this.formConfigurations.patchValue({
                identifierFormfields: response.attributes[i].value,
                isIdentifier: response.attributes[i].value ? true : false
              });
            }
          }
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  onSaveForm(event: any) {
    if (event.checked) {
      this.formConfigurations.controls['saveMessage'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.formConfigurations.controls['saveMessage'].patchValue(null);
      this.formConfigurations.controls['saveMessage'].updateValueAndValidity();
    } else {
      this.formConfigurations.controls['saveMessage'].clearValidators();
      this.formConfigurations.controls['saveMessage'].patchValue(null);
      this.formConfigurations.controls['saveMessage'].updateValueAndValidity();
    }
  }

  onCompleteForm(event: any) {
    if (event.checked) {
      this.formConfigurations.controls['completeMessage'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.formConfigurations.controls['completeMessage'].patchValue(null);
      this.formConfigurations.controls['completeMessage'].updateValueAndValidity();
    } else {
      this.formConfigurations.controls['completeMessage'].clearValidators();
      this.formConfigurations.controls['completeMessage'].patchValue(null);
      this.formConfigurations.controls['completeMessage'].updateValueAndValidity();
    }
  }

  onConditionForm(event: any) {
    if (event.checked) {
      this.formConfigurations.controls['conditionFormfields'].setValidators([Validators.required]);
      this.formConfigurations.controls['conditionFormfields'].patchValue(null);
      this.formConfigurations.controls['conditionFormfields'].updateValueAndValidity();
    } else {
      this.formConfigurations.controls['conditionFormfields'].clearValidators();
      this.formConfigurations.controls['conditionFormfields'].patchValue(null);
      this.formConfigurations.controls['conditionFormfields'].updateValueAndValidity();
    }
  }

  onIdentifierForm(event: any) {
    if (event.checked) {
      this.formConfigurations.controls['identifierFormfields'].setValidators([Validators.required]);
      this.formConfigurations.controls['identifierFormfields'].patchValue(null);
      this.formConfigurations.controls['identifierFormfields'].updateValueAndValidity();
    } else {
      this.formConfigurations.controls['identifierFormfields'].clearValidators();
      this.formConfigurations.controls['identifierFormfields'].patchValue(null);
      this.formConfigurations.controls['identifierFormfields'].updateValueAndValidity();
    }
  }

  setOrder(attrs) {
    if (attrs) {
      for (let key in attrs) {

        if (attrs[key].key === 'disable-form-completion') {
          attrs[key].order = 0;
        }
        if (attrs[key].key === 'prompt-on-form-save') {
          attrs[key].order = 1;
        }
        if (attrs[key].key === 'message-form-save') {
          attrs[key].order = 2;
        }
        if (attrs[key].key === 'prompt-on-form-complete') {
          attrs[key].order = 3;
        }
        if (attrs[key].key === 'message-form-complete') {
          attrs[key].order = 4;
        }
        if (attrs[key].key === 'client-complete-check-field') {
          attrs[key].order = 5;
        }
        if (attrs[key].key === 'client-dashboard-display-field') {
          attrs[key].order = 6;
        }
        if (attrs[key].key === 'pdf-file-name') {
          attrs[key].order = 7;
        }
        if (attrs[key].key === 'loglevel') {
          attrs[key].order = 8;
        }
      }
    }
  }

}
