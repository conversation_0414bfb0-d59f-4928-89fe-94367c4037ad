<div class="card">
    <form [formGroup]="formConfigurations" novalidate>
      <div class="col-12 p-0">
          
          <div class="flex flex-row align-items-center w-full mb-2 px-3">
            <p-inputSwitch id="save" formControlName="isSaveForm" (onChange)="onSaveForm($event)"></p-inputSwitch>
            <label htmlFor="save" class="mb-0 ml-2">Ask user before saving form? <i class="pi pi-exclamation-circle ml-1" pTooltip="Displays a confirmation dialog to the user before saving the form."></i></label>
          </div>
          <div class="col-12 mt-2 mb-3" *ngIf="formConfigurations.get('isSaveForm').value">
            <span class="p-float-label">
                <input id="smessage" pInputText formControlName="saveMessage" type="text" class="w-full" required="true">
                <label htmlFor="smessage" class="_required">Save Message</label>
            </span>    
          </div>
          <div class="flex flex-row align-items-center w-full mb-2 px-3">
            <p-inputSwitch id="complete" formControlName="isCompleteForm" (onChange)="onCompleteForm($event)"></p-inputSwitch>
            <label htmlFor="complete" class="mb-0 ml-2">Ask user before completing form? <i class="pi pi-exclamation-circle ml-1" pTooltip="Displays a confirmation dialog to the user before marking the form as complete."></i></label>
          </div>
          <div class="col-12 mt-2 mb-3" *ngIf="formConfigurations.get('isCompleteForm').value">
            <span class="p-float-label">
                <input id="cmessage" pInputText formControlName="completeMessage" type="text" class="w-full" required="true">
                <label htmlFor="cmessage" class="_required">Complete Message</label>
            </span>    
          </div>

          <div class="flex flex-row align-items-center w-full mb-2 px-3">
            <p-inputSwitch id="condition" formControlName="isCondition" (onChange)="onConditionForm($event)"></p-inputSwitch>
            <label htmlFor="condition" class="mb-0 ml-2">Allow form completion only if a condition is met? <i class="pi pi-exclamation-circle ml-1" pTooltip="Enable this if you want form completion to depend on a specific field being true. You’ll be able to choose which field."></i></label>
          </div>
          <div class="col-12 mt-2 mb-3" *ngIf="formConfigurations.get('isCondition').value">
            <span class="p-float-label">
                <p-dropdown id="conditionFields" formControlName="conditionFormfields" class="p-inputtext-sm" styleClass="w-full"
                  optionLabel="name" optionValue="key" [options]="formFieldsArr" required="true" appendTo="body">
                </p-dropdown>
                <label htmlFor="conditionFields" class="_required">Choose a Field to Set Condition</label>
            </span>
        </div>

        <div class="flex flex-row align-items-center w-full mb-2 px-3">
            <p-inputSwitch id="Identifier" formControlName="isIdentifier" (onChange)="onIdentifierForm($event)"></p-inputSwitch>
            <label htmlFor="Identifier" class="mb-0 ml-2">Enable TurboForms identifier label? <i class="pi pi-exclamation-circle ml-1" pTooltip="When enabled, you can select a field to display as the identifier label next to each form in the TurboForms list view."></i></label>
        </div>
        <div class="col-12 mt-2 mb-3" *ngIf="formConfigurations.get('isIdentifier').value">
            <span class="p-float-label">
                <p-dropdown id="IdentifierFields" formControlName="identifierFormfields" class="p-inputtext-sm" styleClass="w-full"
                  optionLabel="name" optionValue="key" [options]="formFieldsArr" required="true" appendTo="body">
                </p-dropdown>
                <label htmlFor="IdentifierFields" class="_required">Select TurboForms Identifier Field</label>
            </span>
        </div>
        
          <div class="col-12 py-0 text-right">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetForm()"></button>
            <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="saveFormConfigurations()" [disabled]="!formConfigurations.valid"></button>
          </div>
      </div>
    </form>
  </div>