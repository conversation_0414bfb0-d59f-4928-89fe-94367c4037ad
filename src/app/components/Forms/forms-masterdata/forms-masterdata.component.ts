import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { Location } from "@angular/common";
import { MessageService } from 'primeng/api';


@Component({
  selector: 'app-forms-masterdata',
  templateUrl: './forms-masterdata.component.html',
  styleUrls: ['./forms-masterdata.component.scss']
})
export class FormsMasterdataComponent implements OnInit {
  public resourceName: string;
  public resourceType: string;
  public resourceDesc: string;
  public resourceIcon: string;
  public resource: any;
  public errmsg: string;
  public datasourse: any;
  public displayedColumns: any = [];
  public isformavailable: boolean;
  public createButtonFlag: boolean;
  public pageSize = 5;
  public pageIndex = 0;
  public formDefined: boolean;
  loading: boolean = false;
  dataLength: number = 0;
  pagination: any;
  resourceData: any;
  isAddData: boolean = false;
  alertmsg = {
    alerts: { submitMessage: 'Submission has been queued.' },
    disableAlerts: true,
  };
  formId: any;
  readonly: boolean = false;
  submissiondata: any;
  masterdataResources: any;
  submissionId: any
  isUpdateData: boolean = false;
  formName: string;
  formVersion: any;
  formVersionComment: string;
  searchTerm: string;
  tablePagination: any;

  constructor(
    private formsservice: FormsService,
    private route: ActivatedRoute,
    private router: Router,
    private _location: Location,
    public data: DynamicDialogConfig,
    private messageService: MessageService,
  ) {}

  ngOnInit() {
    this.resourceName = this.data.data.name;
    this.resourceDesc = this.data.data.description;
    this.formId = this.data.data.formId;
    this.formVersion = this.data.data?.formVersion;
    this.formVersionComment = this.data.data?.formVersionComment;
    // this.resourceIcon = this.route.snapshot.paramMap.get('avatar');
    this.loadMasterdata(this.resourceName, null, null);
  }

  loadMasterdata(resource: string, page: any, searchTerm: string) {
    this.loading = true;
    this.tablePagination = page;
    this.pageIndex = page ? page.page * page.rows : 0;
    this.pageSize = page ? page.rows : 5;
    this.searchTerm = searchTerm;

    this.formsservice.getresourceDetails(resource, 'masterdata', this.pageSize, this.pageIndex, this.searchTerm).subscribe((resourceform: any) => {
        if (resourceform.status.toLowerCase() === 'success') {
          this.resource = resourceform;
          this.loading = false;
          this.formDefined = this.resource.resource.formAvailable;
          this.dataLength = this.resource.totalRecords;
          if (this.resource.worflowAvailable) {
            this.createButtonFlag = this.resource.worflowAvailable;
          } else {
            this.createButtonFlag = false;
          }
          this.isformavailable = this.resource.resource.formAvailable;
          this.formId = this.resource.submission[0].form;
          this.resourceData = this.resource.formComponents;
          // *******  Masterdata Formid **********
          if (this.isformavailable) {
            this.resource.submission.forEach((element) => {
              if (element.data) {
                this.displayedColumns = Object.keys(element.data);
              }
            });
          } 
        } else {
          this.errmsg = resourceform.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          this.loading = false;
        }
      });
  }
  public addmasterdata() {
    // this.formsservice.getmasterformcomponents(this.formId, "masterdata")
    //   .subscribe((resourcedata: any) => {
        this.isAddData = true;
        this.isUpdateData = false;
        this.submissiondata = null;
      //   this.resourceData = resourcedata.formData;
      //   console.log('resource data',this.resourceData)
      // });
  }

  submitNewData(submission: any) {
    this.formsservice.createsubmissiondata(this.formId, this.resourceName, submission)
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          this.loadMasterdata(this.resourceName, null, this.searchTerm);
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully data added!' });
          this.isAddData = false;
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  updateMasterdata(submission: any) {
    this.formsservice.updatesubmissiondata(this.formName, 'masterdata', this.submissionId, submission).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.loadMasterdata(this.resourceName, null, this.searchTerm);
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully data updated!' });
        this.isUpdateData = false;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  goBack() {
    this.isAddData = false;
    this.isUpdateData = false;
    this.loadMasterdata(this.resourceName, null, this.searchTerm);
  }

  goToFormDesign(data: any) {   // no need this function
    const obj = {
      submissionId: data._id,
      formId: data.form,
      name: data.formName,
      type:'masterdata'
    }
    const routerData = encodeURIComponent(JSON.stringify(obj));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  getDataOfMasterData(data: any) {
    this.isAddData = false;
    this.submissionId = data._id;
    this.formName = data.formName;
    const obj = {
      submissionId: data._id,
      formId: data.form,
      name: data.formName,
      type:'masterdata'
    }
    this.formsservice.getresourceformdata(obj).subscribe((response: any) => {
      if (response.status.toLowerCase() === 'success') {
        this.isUpdateData = true;
        this.setFormRenderer(response);
        // this.formVersion = response.formVersion;
        // this.formVersionComment = response.formVersionComments;
      } else {
      this.errmsg = response.error;
      this.messageService.add({ severity: 'error', summary: 'Error', detail: response.error });
    }
    });
  }

  setFormRenderer(formData: any) {
    this.masterdataResources = formData.masterdata;
    localStorage.setItem('resourceData', JSON.stringify(this.masterdataResources));
    const resdata = formData.template;
    if (!formData.managerCanEdit) {
      this.readonly = true;
    }
    let formwithselectlistdata = this.getObjects(resdata,'dataSrc','json');
    formwithselectlistdata = this.getMasterdDataresource(resdata, 'dataSrc','masterdata');
    if (formwithselectlistdata !== undefined && formwithselectlistdata !== '') {
      this.resourceData = formwithselectlistdata;
    } else {
      this.resourceData = formData.template;
    }
    // this.resourcedata.nosubmit = true;
    formData.submissionData.data.submit = false;
    this.submissiondata = formData.submissionData.data;
  }

  public getObjects(obj, key, val) {
    let objects = [];
    for (const i in obj) {
      if (typeof obj[i] === 'object') {
        objects = objects.concat(this.getObjects(obj[i], key, val));
      } else if (i === key && obj[key] === val) {
        const val1 = obj['valueProperty'];
        const val2 = val1.indexOf('.');
        const ress = val1.slice(0, val2);
        const obj1 = [];
        const resource = this.masterdataResources;
        if (resource !== undefined && resource !== '') {
          for (let x = 0; x < resource.length; x++) {
            const s = resource[x].resourceName;
            if (s === ress) {
              obj1.push(JSON.parse(resource[x].data));
            }
          }
        }
        if (obj.data && typeof obj.data === 'object' && obj.data !== undefined) {
          if (obj.data['json'] === '') {
            obj.data['json'] = obj1;
            const tmplt = obj['template'];
            if (tmplt !== '') {
              const str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
              const str2 = str1.substring(0, str1.indexOf(' '), str1.length);
              const str3 = '<span>{{ ' + 'item.' + str2 + '}}</span>';
              obj['template'] = str3;
            }
            const value = obj['valueProperty'];
            if (value !== '') {
              const valProp = value.substring(value.lastIndexOf('.') + 1, value.length);
              obj['valueProperty'] = valProp;
            }
          } else {
            obj.data['json'] = obj.data['json'];
          }
        } else if (obj.data === undefined) {
          obj['data'] = {
            'values': [],
            'json': '',
            'url': '',
            'resource': '',
            'custom': ''
          };
          obj.data['json'] = obj1;
          const tmplt = obj['template'];
          if (tmplt !== '') {
            const str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
            const str2 = str1.substring(0, str1.indexOf(' '), str1.length);
            const str3 = '<span>{{ ' + 'item.' + str2 + '}}</span>';
            obj['template'] = str3;
          }
          const value = obj['valueProperty'];
          if (value !== '') {
            const valProp = value.substring(value.lastIndexOf('.') + 1, value.length);
            obj['valueProperty'] = valProp;
          }
        }
      }
    }
    return obj;
  }

  // setting masterdata resource
  public getMasterdDataresource(obj: any, key: any, val: any) {
    let objects = [];
    for (let i in obj) {
      if (typeof obj[i] === "object") {
        objects = objects.concat(this.getMasterdDataresource(obj[i], key, val));
      } else if (i === key && obj[key] === val) {
        // let val1 = obj['valueProperty'];
        let ress;
        if (
          obj.data &&
          typeof obj.data === "object" &&
          obj.data !== undefined
        ) {
          if (obj.data["masterdata"]) {
            ress = obj.data["masterdata"];
          }
        }
        let obj1 = [];

        const resource = this.masterdataResources;
        if (
          resource !== undefined &&
          resource !== "" &&
          ress !== undefined &&
          ress !== ""
        ) {
          for (let x = 0; x < resource.length; x++) {
            const s = resource[x].resourceName;
            if (s === ress) {
              obj1.push(JSON.parse(resource[x].data).data);
            }
          }
        } else {
          // console.log("resource is empty");
          obj1 = [];
        }
        const tmplt = obj["template"];
        let str2 = "";
        if (tmplt !== "") {
          const str1 = tmplt.substring(
            tmplt.lastIndexOf(".") + 1,
            tmplt.length
          );
          str2 = str1.substring(0, str1.indexOf("}"), str1.length);
          const str3 = "<span>{{" + "item." + str2.trim() + "}}</span>";
          obj["template"] = str3;
        }
        // tslint:disable-next-line:no-shadowed-variable
        const val = obj["valueProperty"];
        let valProp = "";
        if (val !== "") {
          valProp = val.substring(val.lastIndexOf(".") + 1, val.length);
          obj["valueProperty"] = valProp;
        }
        str2 = str2.trim();
        obj1 = obj1.sort(function (a, b) {
          return a[str2] > b[str2] ? 1 : a[str2] < b[str2] ? -1 : 0;
        });
        obj.masterdata = obj1;
      }
    }
    return obj;
  }

  clearAllFilter() {
    this.searchTerm = null;
    this.loadMasterdata(this.resourceName, null, null);
  }

}
