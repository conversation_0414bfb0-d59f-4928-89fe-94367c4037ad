<div class="flex justify-content-end align-items-center mb-3">
  <button *ngIf="(!createButtonFlag) && (formDefined === true) && !isUpdateData && !isAddData" pButton (click)="addmasterdata()" pTooltip="Add Masterdata" label="Add" icon="pi pi-plus" class="p-button-sm bg-blue">
  </button>
</div>

<div *ngIf="!isAddData && !isUpdateData && !resource?.zeroDataMsg" class="md">
    <p-table [columns]="displayedColumns" [value]="resource?.submission" 
      [tableStyle]="{ 'min-width': '50rem' }" styleClass="p-datatable-sm p-datatable-gridlines" [loading]="loading">

      <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-start">
          <span class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <input style="width: 21rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchTerm"
                (input)="loadMasterdata(resourceName, tablePagination, searchTerm)" placeholder="Search..." />
          </span>
          <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
              (click)="clearAllFilter()" pTooltip="Clear all filters" tooltipPosition="top">
          </button>
        </div>
      </ng-template>
        
      <ng-template pTemplate="header" let-columns>
          <tr>
              <th *ngFor="let col of columns">{{ col }}</th>
          </tr>
      </ng-template>

      <ng-template pTemplate="body" let-rowData let-columns="columns">
          <tr class="cursor-pointer" (click)="getDataOfMasterData(rowData)">
              <td *ngFor="let col of columns">{{ rowData.data[col] }}</td>
          </tr>
      </ng-template>

      <ng-template pTemplate="summary">
        <p-paginator *ngIf="displayedColumns" class="p-0" [rows]="5" [totalRecords]="dataLength" [showJumpToPageDropdown]="false" dropdownAppendTo="body"
          (onPageChange)="loadMasterdata(resourceName, $event, searchTerm)" [showPageLinks]="true" [rowsPerPageOptions]="[5,10,25]">
        </p-paginator>
      </ng-template>

    </p-table>
</div>

<div *ngIf="displayedColumns.length === 0 && resource?.zeroDataMsg && !isAddData" class="no-data">
    <span>{{resource.zeroDataMsg}}</span>
</div>
  
<div *ngIf="isAddData || isUpdateData" class="m-3">
  <div class="flex justify-content-between align-items-center mb-3">
      <button pButton (click)="goBack()" icon="pi pi-arrow-left" class="p-button-sm p-button-rounded bg-blue" pTooltip="Go Back"></button>
      <div *ngIf="isAddData || isUpdateData" class="flex flex-row align-items-center">
        <label class="mr-1">Version: {{formVersion}}</label>
        <p-divider *ngIf="formVersionComment" styleClass="mx-2" layout="vertical"></p-divider>
        <div class="flex flex-column justify-content-start">
          <span *ngIf="formVersionComment">Comment: {{formVersionComment}}</span>
        </div>
      </div>
  </div>
  <hr *ngIf="resourceData">
  <div>
    <formio [form]='resourceData' (submit)="isUpdateData ? updateMasterdata($event) : submitNewData($event)" [options]="alertmsg" [readOnly]="readonly" [submission]='{ "data": submissiondata }'></formio>
    <!-- <formio *ngIf="isUpdateData" [form]='resourceData' (submit)="updateMasterdata($event)" [options]="alertmsg" [readOnly]="readonly" [submission]='{ "data": submissiondata }'></formio> -->
  </div>
</div>