<div class="card">
    <div *ngIf="helpText" class="col-12 py-0">
        <p-messages [value]="helpText" [enableService]="false" [closable]="false"></p-messages>
    </div>
    <div class="col-12 mt-3">
        <span class="p-float-label">
            <input id="scann" pInputText [(ngModel)]="scannedData" type="text" class="w-full p-inputtext-sm" cdkFocusInitial>
            <label htmlFor="scann">Test Data</label>
        </span>
    </div>
    <div class="col-12 text-right mt-2">
        <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancleForm()"></button>
        <button class="p-button-sm bg-blue" pButton type="button" label="Simulate Scan" (click)="simulate(scannedData)"></button>
    </div>
</div>
