import { Component, OnInit } from '@angular/core';
import { Message } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-barcode-dialog',
  templateUrl: './barcode-dialog.component.html',
  styleUrls: ['./barcode-dialog.component.scss']
})
export class BarcodeDialogComponent implements OnInit {
  helpText: Message[];
  scannedData: any;

  constructor(public data: DynamicDialogConfig, private ref: DynamicDialogRef) {}

  ngOnInit(): void {
    console.log(this.data);
    
    this.helpText = [{ severity: 'info', summary: 'Help: ', detail: 'Test the scanning by simulating the barcode or QRCode data.Enter your test data below.' }];
    this.scannedData = this.data.data.barcodedata;
  }

  simulate(scandata: any) {
    this.ref.close(scandata);
  }

  cancleForm() {
    this.ref.close();
  }

}
