import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, Message, MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import { ReteService } from 'src/app/services/rete.service';
import { UtilsService } from 'src/app/services/utils.service';
import * as moment from 'moment';

@Component({
  selector: 'app-create-flow',
  templateUrl: './create-flow.component.html',
  styleUrls: ['./create-flow.component.scss']
})
export class CreateFlowComponent implements OnInit {
  @Input() childData: any;
  @Output() eventEmitter: EventEmitter<any> = new EventEmitter<any>();
  
  createWorkflow: FormGroup;
  masterdataLower:any;
  errmsg: string;
  forms: any[] = [];
  selectedIcon: string = 'call_split';
  operationNamePattern = '[a-z0-9]+';
  flowTitlePattern = '[a-zA-Z0-9 ]+';
  form_id_workflow = localStorage.getItem("form_id_workflow")
  iconsArr: any;
  actionFlowArr: string[] = ['On Create', 'On Save', 'On Complete', 'On Approval'];
  helpText: Message[];

  scheduleform: FormGroup;
  OtherSchedules: any[] = [
    {key:'Minute', value: 'Minute'},
    {key:'Hour', value: 'Hourly'},
    {key:'Day', value: 'Daily'},
    {key:'Week', value: 'Weekly'},
    {key:'Month', value: 'Monthly'},
    {key:'Year', value: 'Yearly'}
  ];
  MinuteArr: string[] = ['10', '15', '20', '30', '40', '45', '50'];
  scheduleOptions = [
    { name: 'Everyday', value: 'everyday'},
    { name: 'Weekdays', value: 'weekdays'},
    { name: 'Weekends', value: 'weekends' }
  ];
  days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  dayoccurences = ['First', 'Second', 'Third', 'Fourth', 'Last' ];
  months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  chooseMonthArr = [{ name: 'Repeat on which day of every month?', value: 'manual'}, { name: 'Repeat on specific day of the month', value: 'automatic'}];
  chooseYearArr = [{ name: 'Repeat on which day of every year?', value: 'manual'},{ name:'Repeat on specific day of the year', value: 'automatic'}];
  masterdataOptions = ['Manual', 'Schedule'];
  masterdataType: string = 'Manual';
  enddate = '';
  selectedMonthlySchedule: string;
  selectedYearlySchedule: string;
  weeklyValidationStatus = true;
  selectedschedule: string;
  copyOptions: any = ['Manual Flow', 'Webhook Flow', 'Scheduled Flow', 'Form Action Flow', 'Sub Flow', 'Smart Data Flow'];

  isExtend: boolean = false;
  isRevoke: boolean = false;
  minDate: Date = new Date();
  // isRedirect: boolean = false;
  saveFlowResponse: any;

  constructor(
    public data: DynamicDialogConfig,
    private fb: FormBuilder,
    private reteservice: ReteService,
    private formservice: FormsService,
    private messageService: MessageService,
    private ref: DynamicDialogRef,
    private utilService: UtilsService,
    private confirmationService: ConfirmationService,
  ) {}
  ngOnInit(): void {
    if (this.childData?.displayType === 'accordian') {
      this.data.data = this.childData;
    }
    this.helpText = [{ severity: 'info', summary: 'Help: ', detail: this.getHelpText(this.data.data.flowType) }];
    this.createWorkflow = this.fb.group({
      wfTitle: ['', [Validators.required, Validators.maxLength(100), Validators.pattern(this.flowTitlePattern)]],
      wfName: ['', [Validators.required, Validators.pattern(this.operationNamePattern)]],
      formType: ['', Validators.required],
      formId: ['', Validators.required],//${this.form_id_workflow}
      wfDesc: ["", [Validators.required,Validators.maxLength(100)]],
      actionFlowType: [this.data.data.flowType],
      deploymentType: [''],
      webhookURL: [{value: '', disabled: true}],
      expiryDate: ['']
    });

    if(this.data.data.add) {
      if (this.data.data.formData) {
        this.createWorkflow.patchValue({
          formType: this.data.data.formData.formType,
          formId: this.data.data.formData.formId,
          wfDesc: this.data.data.formData.wfDesc,
        });
        // this.picked_icon = this.data.data.formData.avatar;
        this.createWorkflow.get('formType').disable();
        this.createWorkflow.get('formId').disable();
        if (this.data.data.flowType === 'Webhook Flow') {
          this.createWorkflow.controls['expiryDate'].setValidators(Validators.required)
          this.createWorkflow.controls['expiryDate'].updateValueAndValidity()
        }
      }
      // this.getAllFormsForWF(this.data.data.formData.formType);  note: no need to call this fun, formId is there.
    }
  
    if(this.data.data.create) {
      if (this.data.data.row) {
        this.masterdataLower = this.data.data.row.usage.toLowerCase();
        this.createWorkflow.patchValue({
          formType: `${this.masterdataLower}`,
          formId: this.data.data.row.formId,
          wfDesc: this.data.data.row.wfDesc,
        });
        // this.picked_icon = this.data.data.row.avatar;
        this.createWorkflow.get('formType').disable();
        this.createWorkflow.get('formId').disable();
        this.createWorkflow.get('webhookURL').disable();
      }
      // this.getAllFormsForWF(this.data.data.row.formType);
    }
    if(this.data.data.isupdate) {
      if (this.data.data.wfdata) {        
        this.createWorkflow.patchValue({
          wfTitle: `${this.data.data.wfdata.wfTitle}`,
          wfName: `${this.data.data.wfdata.wfName}`,
          formType: `${this.data.data.wfdata.formType}`,
          formId: this.data.data.wfdata.formId,
          wfDesc: this.data.data.wfdata.wfDesc,
          // icon: this.data.data.wfdata.avatar
        });
        this.selectedIcon = this.data.data.wfdata.avatar;
        this.createWorkflow.get('wfName').disable();
        this.createWorkflow.get('formType').disable();
        this.createWorkflow.get('formId').disable();
        if (this.data.data.flowType === 'Webhook Flow') {
          this.getWebhook();
          this.createWorkflow.controls['expiryDate'].setValidators(Validators.required)
          this.createWorkflow.controls['expiryDate'].updateValueAndValidity()
        }
        if (this.data.data.flowType === "Scheduled Flow") {
          this.getScheduleData(this.data.data.scheduleId);
        }
      }
      // this.getAllFormsForWF(this.data.data.wfdata.formType);
    }
    if (this.data.data.iscopy) {
      if (this.data.data.wfdata) {
        // console.log(this.data.data.wfdata);
        this.createWorkflow.patchValue({
          wfTitle: `${this.data.data.wfdata.wfTitle} Copy`,
          wfName: `${this.data.data.wfdata.wfName}copy`,
          formType: `${this.data.data.wfdata.formType}`,
          formId: this.data.data.wfdata.formId,
          wfDesc: this.data.data.wfdata.wfDesc,
          // icon: this.data.data.wfdata.avatar
        });
        this.selectedIcon = this.data.data.wfdata.avatar;
        if (this.data.data.flowType === 'Webhook Flow') {
          this.createWorkflow.controls['expiryDate'].setValidators(Validators.required);
          this.createWorkflow.controls['expiryDate'].updateValueAndValidity();
        }
        if (this.data.data.flowType === "Scheduled Flow") {
          this.getScheduleData(this.data.data.scheduleId);
        }
        // this.formId.valueChanges.subscribe(value => {
        //   if(this.data.data.wfdata.formId !== value) {
        //     this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Please check all workflow step inputs as the associated form has been changed' });
        //   }
        // });
      }
      this.getAllFormsForWF(this.data.data.wfdata.formType);
    }
    this.wfTitle.valueChanges.subscribe(value => {
      if (value) {
        this.createWorkflow.patchValue({
          wfName: value.split(' ').join('').toLowerCase()
        });
      }
    });
    this.formservice.getAllIcons().subscribe(data => {this.iconsArr = data});

    if (this.data.data.flowType === 'Scheduled Flow') {
      this.setScheduleForm();
    }

  }

  setScheduleForm() {
    this.scheduleform = this.fb.group({
      scheduletype: [null, Validators.required],
      noenddate: [false],
      pickervalue: [null],
      selectedtype: [null, Validators.required],
      weekDays: [null],
      day: [null, Validators.required],
      dayoccurence: [null, Validators.required],
      selectedday: [null, Validators.required],
      selectedmonth: [null, Validators.required],
      selectedMinute: [null, Validators.required],
      chooseMonth: [null],
      chooseYear: [null]
    });
    if (!this.data.data.isupdate) {
      this.scheduleform.controls['scheduletype'].patchValue("Minute");
      this.scheduleform.controls['scheduletype'].updateValueAndValidity();
      this.pickedScheduleType({value: "Minute"});  
    }
  }

  get wfTitle() {
    return this.createWorkflow.get('wfTitle');
  }
  
  get formId() {
    return this.createWorkflow.get('formId');
  }

  get icon() {
    return this.createWorkflow.get('icon');
  }
  
  getAllFormsForWF(type: string) {
    this.reteservice.getallformsforworkflow(type)
    .subscribe(res => {
      if (res && res?.formHeaders?.length > 0) {
        this.forms = res.formHeaders;
      }
    });
  }

  CreateWorkflow() {
     this.reteservice.createworkflow(this.createWorkflow.getRawValue(), this.selectedIcon)
       .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          // this.isRedirect = true;
          this.saveFlowResponse = response;
          if (this.data.data.flowType === 'Scheduled Flow') {
            this.createScheduleForm(response.wfId);
          } else if (this.data.data.flowType === 'Webhook Flow') {
            this.createWebhook();
          } else {
            this.goToFlowEditor();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!' });
          }
        } else {
         this.errmsg = response.error;
         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  CreateCopy() {
    this.reteservice.copyworkflow(this.createWorkflow.getRawValue(), this.selectedIcon, this.data.data.wfdata.wfId)
      .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          // this.isRedirect = true;
          this.saveFlowResponse = response;
          if (this.data.data.flowType === 'Scheduled Flow') {
            this.createScheduleForm(response.wfId);
          } else if (this.data.data.flowType === 'Webhook Flow') {
            this.createWebhook();
          } else {
            this.goToFlowEditor();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!' });
          }
        } else {
         this.errmsg = response.error;
         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  goToFlowEditor() {
    if (this.childData?.displayType === 'accordian') {
      this.eventEmitter.emit(this.saveFlowResponse);
    } else {
      this.ref.close(this.saveFlowResponse);
    }
    setTimeout(() => {
      // this.isRedirect = false;
      this.saveFlowResponse = null;
    }, 300);
  }

  updateWorkflow(event: any) {
    if (this.createWorkflow.getRawValue().wfTitle !== this.data.data.wfdata.wfTitle) {
      this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Flow name will be changed, if used as sub-flow needs to be manually corrected. Proceed anyway?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.reteservice.updateworkflow(this.createWorkflow.getRawValue(), this.selectedIcon, this.data.data.wfdata.wfId, true)
            .subscribe(response => {
              if (response.status.toLowerCase() === 'success') {
                this.eventEmitter.emit(response);
                if (this.data.data.flowType === 'Scheduled Flow') {
                  this.updateScheduleForm(this.data.data.wfdata.wfId);
                } else if (this.data.data.flowType === 'Webhook Flow') {
                  this.updateWebhook(null);
                } else {
                  this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
                }
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
          });
        },
        reject: () => {}
      });
    } else {
      this.reteservice.updateworkflow(this.createWorkflow.getRawValue(), this.selectedIcon, this.data.data.wfdata.wfId, false)
        .subscribe(response => {
          if (response.status.toLowerCase() === 'success') {
            this.eventEmitter.emit(response);
            if (this.data.data.flowType === 'Scheduled Flow') {
              this.updateScheduleForm(this.data.data.wfdata.wfId);
            }
            if (this.data.data.flowType === 'Webhook Flow') {
              this.updateWebhook(null);
            }
            if (this.data.data.flowType === 'Manual Flow') {
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
      });
    }
  }

  selectIcon(icon: string, element?: any) {
    this.selectedIcon = icon;
    this.createWorkflow.get('icon').setValue(this.selectedIcon);
    element.hide();
  }

  cancleForm() {
    this.createWorkflow.reset();
    if (this.data.data.flowType === 'Scheduled Flow') {
      this.scheduleform.reset();
    }
    if (this.childData?.displayType === 'accordian') {
      this.eventEmitter.emit();
    } else {
      this.ref.close();
    }
  }

// Schedule start

  pickedScheduleType(event: any) {
    const schedule = event?.value;
    this.selectedschedule = schedule;
    
    if (schedule === 'Minute') {
      
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].setValidators([Validators.required]);
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Daily') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
    
    if (schedule === 'Hourly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();

      this.scheduleform.controls['selectedtype'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedtype'].patchValue('');
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }

    if (schedule === 'Weekly') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['weekDays'].patchValue([
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday'
      ]);

    } if (schedule === 'Monthly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['chooseMonth'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseMonth'].updateValueAndValidity();

      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();

    } if (schedule === 'Yearly') {
      this.scheduleform.controls['selectedtype'].clearValidators();
      this.scheduleform.controls['selectedMinute'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['chooseYear'].patchValue(null);
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['selectedMinute'].clearValidators();
      
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(31)]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['selectedtype'].updateValueAndValidity();
      this.scheduleform.controls['selectedMinute'].updateValueAndValidity();
      this.scheduleform.controls['chooseYear'].updateValueAndValidity();

      this.scheduleform.controls['weekDays'].clearValidators();
      this.scheduleform.controls['weekDays'].patchValue(null);
      this.scheduleform.controls['weekDays'].updateValueAndValidity();
    }
  }

  setValidatorsMonthly(monthlyscheduletype: string) {
    this.selectedMonthlySchedule = monthlyscheduletype;
    if (monthlyscheduletype === 'repeateverymonth') {
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(0), Validators.max(31)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (monthlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
  // });
  }

  setValidatorsYearly(yearlyscheduletype: string) {
    this.selectedYearlySchedule = yearlyscheduletype;

    if (yearlyscheduletype === 'repeateveryyear') {
      this.scheduleform.controls['selectedmonth'].clearValidators();
      this.scheduleform.controls['dayoccurence'].clearValidators();
      this.scheduleform.controls['selectedday'].clearValidators();
      this.scheduleform.controls['selectedmonth'].patchValue(null);
      this.scheduleform.controls['dayoccurence'].patchValue(null);
      this.scheduleform.controls['selectedday'].patchValue(null);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
      this.scheduleform.controls['day'].setValidators([Validators.required, Validators.min(1), Validators.max(366)]);
      this.scheduleform.controls['day'].updateValueAndValidity();
    }
    if (yearlyscheduletype === 'ondayofmonth') {
      this.scheduleform.controls['day'].clearValidators();
      this.scheduleform.controls['day'].patchValue(null);
      this.scheduleform.controls['day'].updateValueAndValidity();
      this.scheduleform.controls['selectedmonth'].setValidators([Validators.required]);
      this.scheduleform.controls['dayoccurence'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedday'].setValidators([Validators.required]);
      this.scheduleform.controls['selectedmonth'].updateValueAndValidity();
      this.scheduleform.controls['dayoccurence'].updateValueAndValidity();
      this.scheduleform.controls['selectedday'].updateValueAndValidity();
    }
  // });
  }

  setValidatorsfordate(endDate: any) {
    if (endDate) {
      this.scheduleform.get('noenddate').patchValue(true);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
    if (!endDate) {
      this.scheduleform.get('noenddate').patchValue(false);
      this.scheduleform.get('noenddate').updateValueAndValidity();
    }
  }

  createScheduleForm(flowId: any) {
    const ids = { formid: this.data.data?.formData?.formId || this.data.data?.wfdata?.formId, formsetid: null}
    let data = {};
    if (this.selectedschedule === 'Minute') {
      data = { 
        'selectedMinute': this.scheduleform.value.selectedMinute,
        'days' : {
          'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
          'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
          'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
          'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
          'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
          'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
          'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
        }
      }
    }
    if (this.selectedschedule === 'Daily' || this.selectedschedule === 'Hourly') {
      data = {'selectedtype':this.scheduleform.value.selectedtype}
    }
    if (this.selectedschedule === 'Weekly') {
      data = { 'days' : {
        'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
        'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
        'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
        'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
        'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
        'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
        'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
      }}
    }
    if (this.selectedschedule === 'Monthly') {
      data = 
        {
          'selectedmonthlyschedule': this.selectedMonthlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
        }
    }
    if (this.selectedschedule === 'Yearly') {
      data = 
        {
          'selectedyearlyschedule': this.selectedYearlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
          'selectedmonth': this.scheduleform.value.selectedmonth,
        }
      }
    var form = {
      'scheduletype': this.scheduleform.value.scheduletype,
      'noenddate': this.scheduleform.value.noenddate,
      'pickervalue': this.scheduleform.value.pickervalue,
      [this.selectedschedule]: data,
    }
    if (this.scheduleform.get('noenddate').value === 'true') {
      this.enddate = null;
     } else {
       this.enddate = this.scheduleform.get('pickervalue').value;
     }
    if (this.scheduleform.get('scheduletype').value === 'Weekly' || this.scheduleform.get('scheduletype').value === 'Minute') {
      this.weeklyValidationStatus = this.utilService.checkboxvalidity(data);
    }
    if (this.weeklyValidationStatus) {
      // if (this.data.data.formData.formType.toLowerCase() === "masterdata") {
      //   console.log('masterID',this.data.data.formData.formId)
      //   console.log('flowId',flowId)
      //   console.log('this.selectedschedule',this.selectedschedule)
      //   this.formservice.createMasterDataSchedule(this.data.data.formData.formId, flowId, this.selectedschedule, this.enddate, this.scheduleform.value)
      //   .subscribe((response) => {
      //     if (response.status === "Success") {
      //       this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!'});
      //       this.scheduleform.reset();
      //     } else {
      //       this.errmsg = response.error;
      //       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      //     }
      //   });
      // } else {
        this.formservice.createschedule(ids, null, this.selectedschedule, this.enddate, form, this.scheduleform.value.taskpriority, this.scheduleform.value.dueindays, 'Others', flowId)
        .subscribe((response: any) => {
          if (response.status.toLowerCase() === 'success') {
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!'});
              this.goToFlowEditor();
              this.scheduleform.reset();
          } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
      // }
      
    } else {
       this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select days for schedule'});
    }
  }

  getScheduleData(id) {
    // onsole.log(id);
    this.formservice
      .getschedule(id)
      .subscribe(res => {
        if (res.status.toLowerCase() === 'success') {
          if (res.type === "Minute") { 
            this.selectedschedule = res.type;
            const wDays = Object.entries(res.scheduleData.Minute.days).reduce((acc, [key, val]) => {if (val === true) {acc.push(key)};return acc}, []);
            this.scheduleform = this.fb.group({
              scheduletype: [res.type, Validators.required],
              noenddate: [false],
              pickervalue: [null],
              selectedtype: [null],
              weekDays: [wDays],
              day: [null],
              dayoccurence: [null],
              selectedday: [null],
              selectedmonth: [null],
              selectedMinute: [res.scheduleData.Minute.selectedMinute, Validators.required], // need to add minute attribute
              chooseMonth: [null],
              chooseYear: [null]
            })
          } else if (res.type === "Daily" || res.type === "Hourly") {
            this.selectedschedule = res.type;
            this.scheduleform = this.fb.group({
              scheduletype: [res.type, Validators.required],
              noenddate: [false],
              pickervalue: [null],
              selectedtype: [res.scheduleData.Daily.selectedtype, Validators.required],
              weekDays: [null],
              day: [null],
              dayoccurence: [null],
              selectedday: [null],
              selectedmonth: [null],
              selectedMinute: [null],
              chooseMonth: [null],
              chooseYear: [null]
            })
          } else if (res.type === "Weekly") {
            this.selectedschedule = res.type;
            const wDays = Object.entries(res.scheduleData.Weekly.days).reduce((acc, [key, val]) => {if (val === true) {acc.push(key)};return acc}, []);
            this.scheduleform = this.fb.group({
              scheduletype: [res.type, Validators.required],
              noenddate: [false],
              pickervalue: [null],
              selectedtype: [null],
              weekDays: [wDays],
              day: [null],
              dayoccurence: [null],
              selectedday: [null],
              selectedmonth: [null],
              selectedMinute: [null],
              chooseMonth: [null],
              chooseYear: [null]
            })
          } else if (res.type === "Monthly") {
            this.selectedschedule = res.type;
            this.selectedMonthlySchedule = res.scheduleData.Monthly.selectedmonthlyschedule;
            if (this.selectedMonthlySchedule === "ondayofmonth") {
              this.scheduleform = this.fb.group({
                scheduletype: [res.type, Validators.required],
                noenddate: [false],
                pickervalue: [null],
                selectedtype: [null],
                weekDays: [null],
                day: [null],
                dayoccurence: [res.scheduleData.Monthly.dayoccurence, Validators.required],
                selectedday: [res.scheduleData.Monthly.selectedday, Validators.required],
                selectedmonth: [null],
                selectedMinute: [null],
                chooseMonth: ['automatic'],
                chooseYear: [null]
              })
            } else if (this.selectedMonthlySchedule === "repeateverymonth") {
              this.scheduleform = this.fb.group({
                scheduletype: [res.type, Validators.required],
                noenddate: [false],
                pickervalue: [null],
                selectedtype: [null],
                weekDays: [null],
                day: [res.scheduleData.Monthly.day, [Validators.required, Validators.min(0), Validators.max(31)]],
                dayoccurence: [null],
                selectedday: [null],
                selectedmonth: [null],
                selectedMinute: [null],
                chooseMonth: ['manual'],
                chooseYear: [null]
              })
            }
          } else if (res.type === "Yearly") {
            this.selectedschedule = res.type;
            this.selectedYearlySchedule = res.scheduleData.Yearly.selectedyearlyschedule;
            if (this.selectedYearlySchedule === "ondayofmonth") {
              this.scheduleform = this.fb.group({
                scheduletype: [res.type, Validators.required],
                noenddate: [false],
                pickervalue: [null],
                selectedtype: [null],
                weekDays: [null],
                day: [null],
                dayoccurence: [res.scheduleData.Yearly.dayoccurence, Validators.required],
                selectedday: [res.scheduleData.Yearly.selectedday, Validators.required],
                selectedmonth: [res.scheduleData.Yearly.selectedmonth, Validators.required],
                selectedMinute: [null],
                chooseMonth: [null],
                chooseYear: ['automatic']
              })
            } else if (this.selectedYearlySchedule === "repeateveryyear") {
              this.scheduleform = this.fb.group({
                scheduletype: [res.type, Validators.required],
                noenddate: [false],
                pickervalue: [null],
                selectedtype: [null],
                weekDays: [null],
                day: [res.scheduleData.Yearly.day, [Validators.required, Validators.min(1), Validators.max(366)]],
                dayoccurence: [null],
                selectedday: [null],
                selectedmonth: [null],
                selectedMinute: [null],
                chooseMonth: [null],
                chooseYear: ['manual']
              })
            }
          }
          // this.pickedScheduleType({value: res.type});          
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  updateScheduleForm(flowId: any) {
    const ids = { formid: this.data.data?.formData?.formId || this.data.data?.wfdata?.formId, formsetid: null}
    let data = {};
    if (this.selectedschedule === 'Minute') {
      data = { 
        'selectedMinute': this.scheduleform.value.selectedMinute,
        'days' : {
          'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
          'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
          'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
          'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
          'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
          'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
          'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
        }
      }
    }
    if (this.selectedschedule === 'Daily' || this.selectedschedule === 'Hourly') {
      data = {'selectedtype':this.scheduleform.value.selectedtype}
    }
    if (this.selectedschedule === 'Weekly') {
      data = { 'days' : {
        'Monday': this.scheduleform.value.weekDays.includes('Monday') ? true : false,
        'Tuesday':this.scheduleform.value.weekDays.includes('Tuesday') ? true : false,
        'Wednesday':this.scheduleform.value.weekDays.includes('Wednesday') ? true : false,
        'Thursday':this.scheduleform.value.weekDays.includes('Thursday') ? true : false,
        'Friday':this.scheduleform.value.weekDays.includes('Friday') ? true : false,
        'Saturday':this.scheduleform.value.weekDays.includes('Saturday') ? true : false,
        'Sunday':this.scheduleform.value.weekDays.includes('Sunday') ? true : false,
      }}
    }
    if (this.selectedschedule === 'Monthly') {
      data = 
        {
          'selectedmonthlyschedule': this.selectedMonthlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
        }
    }
    if (this.selectedschedule === 'Yearly') {
      data = 
        {
          'selectedyearlyschedule': this.selectedYearlySchedule,
          'day': this.scheduleform.value.day,
          'dayoccurence': this.scheduleform.value.dayoccurence,
          'selectedday': this.scheduleform.value.selectedday,
          'selectedmonth': this.scheduleform.value.selectedmonth,
        }
      }
    var form = {
      'scheduletype': this.scheduleform.value.scheduletype,
      'noenddate': this.scheduleform.value.noenddate,
      'pickervalue': this.scheduleform.value.pickervalue,
      [this.selectedschedule]: data,
    }
    if (this.scheduleform.get('noenddate').value === 'true') {
      this.enddate = null;
     } else {
       this.enddate = this.scheduleform.get('pickervalue').value;
     }
    if (this.scheduleform.get('scheduletype').value === 'Weekly' || this.scheduleform.get('scheduletype').value === 'Minute') {
      this.weeklyValidationStatus = this.utilService.checkboxvalidity(data);
    }
    if (this.weeklyValidationStatus) {
      this.formservice.updateschedule(this.data.data.scheduleId, ids, null, this.selectedschedule, this.enddate, form, this.scheduleform.value.taskpriority, this.scheduleform.value.dueindays, 'Others', flowId)
        .subscribe((response: any) => {
          if (response.status.toLowerCase() === 'success') {
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!'});
              this.scheduleform.reset();
          } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
      });
    } else {
       this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select days for schedule'});
    }
  }

  //Webhook start

  onExpiryChange(event) {
    if (this.data.data.isupdate) {
      this.isExtend = true;
      this.createWorkflow.controls['expiryDate'].patchValue(event);
    } else {
      this.isExtend = false;
    }
  }

  getWebhook() {
    this.reteservice.getwebhook(this.data.data.wfdata.wfName, this.data.data.wfdata.wfId)
     .subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.createWorkflow.patchValue({
          webhookURL: response.token,
          expiryDate: response.expiry
        })
      } else {
       this.errmsg = response.error;
       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  createWebhook() {
    if (this.createWorkflow.getRawValue().wfName) {
      const expiryDate = moment(this.createWorkflow.getRawValue().expiryDate).format('MM/DD/YYYY');
      this.reteservice.createwebhook(this.createWorkflow.getRawValue().wfName, expiryDate)
       .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          this.createWorkflow.controls['webhookURL'].patchValue(response.token);
          this.createWorkflow.controls['expiryDate'].patchValue(response.expiry);
          this.createWorkflow.controls['webhookURL'].updateValueAndValidity();
          this.createWorkflow.controls['expiryDate'].updateValueAndValidity();
          this.goToFlowEditor();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully created!' });
        } else {
         this.errmsg = response.error;
         this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
    } else {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Title is mandatory!' });
    }
  }

  updateWebhook(str: string) {
    if (str === 'revoke') {
      this.isRevoke = true;
    }
    const expiryDate = moment(this.createWorkflow.getRawValue().expiryDate).format('MM/DD/YYYY')
    this.reteservice.updatewebhook(this.createWorkflow.getRawValue().wfName, this.data.data.wfdata.wfId, expiryDate, this.isExtend, this.isRevoke)
     .subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
        this.isRevoke = false;
        this.createWorkflow.controls['webhookURL'].patchValue('');
        this.createWorkflow.controls['webhookURL'].updateValueAndValidity();
      } else {
       this.errmsg = response.error;
       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  copyUrl(url) {
    document.addEventListener('copy', (e: ClipboardEvent) => {
      e.clipboardData.setData('text/plain', (url));
      e.preventDefault();
      document.removeEventListener('copy', null);
    });
    document.execCommand('copy');
    this.messageService.add({ severity: 'info', summary: 'Info', detail: 'URL is copied!' });
  }

  getHelpText(str: string): string {
    let text: string;
    if (str === 'Manual Flow') {
      return text = 'Trigger the flow in the builder via the Run button.'
    } else if (str === 'Form Action Flow') {
      return text = 'Trigger the flow on creation of the form (used to prefill data), on partial save of the form data or on completion and form submitted for approval.'
    } else if (str === 'Sub Flow') {
      return text = 'Trigger the flow as part of another flow.'
    } else if (str === 'Smart Data Flow') {
      return text = "Trigger the flow on user's action in a form."
    } else if (str === 'Scheduled Flow') {
      return text = 'Trigger the flow on a preset schedule.'
    } else if (str === 'Webhook Flow') {
      return text = 'Trigger the flow by posting a request to the webhook URL.'
    } else if (str === 'Manual Flow') {
      return text = 'Trigger the flow when master data needs to be returned to the app.'
    } else {
      return text = '';
    }
  }

  onFlowTypeChange(e: any) {
    this.createWorkflow.controls['actionFlowType'].patchValue(e.value);
    this.createWorkflow.controls['actionFlowType'].setValidators(Validators.required);
    this.createWorkflow.controls['actionFlowType'].updateValueAndValidity();
    this.data.data.flowType = e.value;
    if (this.data.data.flowType === 'Scheduled Flow') {
      this.setScheduleForm();
    }
  }

  showFormChangeWarning() {
    this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Please check all workflow step inputs as the associated form has been changed' });
  }
}
