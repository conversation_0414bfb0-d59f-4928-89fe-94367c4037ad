.custom-icon {
    position: absolute;
    top: 18%;
    left: 1%;
}
.scrolling {
    display: block;
    overflow-x: hidden;
    overflow-y: scroll;
    height: 280px;
}

::ng-deep .set p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 40px;
  }
  
  ::ng-deep .set p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }

  ::ng-deep .set p-fieldset .p-fieldset-legend {
    padding: 0px !important;
    border: none !important;
    font-weight: normal !important;
  }

  .error {
    display: flex;
    flex-direction: row;
    color: #dc2626;
    border-radius: 5px;
    font-size: 14px;
    margin: 4px auto auto 2px;
    gap: 2px;
    align-items: center;
  }