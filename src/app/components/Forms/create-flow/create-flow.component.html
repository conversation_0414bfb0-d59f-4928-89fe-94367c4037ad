<p-confirmPopup></p-confirmPopup>
<div class="card my-2">
    <form [formGroup]="createWorkflow" novalidate>
        <div class="grid formgrid">
            <div *ngIf="helpText" class="col-12 py-0">
                <p-messages [value]="helpText" [enableService]="false" [closable]="false"></p-messages>
            </div>

            <div *ngIf="data.data.iscopy" class="flex flex-row justify-content-between my-3 px-3 w-full">
                <div *ngFor="let type of copyOptions" class="flex-auto">
                    <p-radioButton [inputId]="Ftype" [value]="type" formControlName="actionFlowType" (onClick)="onFlowTypeChange($event)"></p-radioButton>
                    <label [for]="Ftype" class="ml-1 mb-0">{{ type }}</label>
                </div>
            </div>

            <div class="col-6 mt-3">
                <span class="p-float-label">
                    <input id="Title" pInputText formControlName="wfTitle" type="text" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="Title" class="_required">Title</label>
                </span>
                <span *ngIf="createWorkflow.get('wfTitle').hasError('pattern')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Please use only English alphabets and numbers (A–Z, a–z, 0–9).</small></span>
            </div>
            <!-- <div class="col-6 mt-3">
                <span class="p-input-icon-left w-full">
                    <span class="material-icons custom-icon">{{selectedIcon}}</span>
                    <input pInputText (click)="iconPicker.show($event)" class="w-full p-inputtext-sm" placeholder="Icon" readonly="true" required="true">
                </span>
            </div> -->
            <div class="col-6 mt-3">
                <span class="p-float-label">
                  <textarea id="description" pInputTextarea formControlName="wfDesc" rows="1" cols="30" class="w-full p-inputtext-sm" required="true"></textarea>
                  <label htmlFor="description" class="_required">Description</label>
                </span>
                <span *ngIf="createWorkflow.get('wfDesc').hasError('maxlength')" class="error" style="margin-top: 0px !important;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 100 characters.</small></span>
            </div>

            <div *ngIf="data.data.iscopy" class="col-12 mt-4">
                <span class="p-float-label">
                    <p-dropdown id="form" formControlName="formId" [autoDisplayFirst]="false" (onChange)="showFormChangeWarning($event)" filter="true" filterBy="formName"
                        [options]="forms" optionLabel="formName" optionValue="formId" styleClass="w-full p-inputtext-sm" appendTo="body" required="true">
                    </p-dropdown>
                    <label htmlFor="form" class="_required">Select Form</label>
                </span>
            </div>

        <!-- Form Action Flow -->
            <div *ngIf="data.data.flowType === 'Form Action Flow' && !data.data.isupdate" class="flex flex-row gap-4 mt-4 px-3 w-full">
                <div *ngFor="let type of actionFlowArr" class="flex-auto">
                    <p-radioButton [inputId]="type" [value]="type" formControlName="deploymentType"></p-radioButton>
                    <label [for]="type" class="ml-1 mb-0">{{ type }}</label>
                </div>
            </div>

            <!-- <div *ngIf="data.data.flowType === 'Smart Data Flow'" class="col-6 mt-3">
                <span class="p-float-label">
                    <input id="sdc" pInputText formControlName="sdcName" type="text" class="w-full p-inputtext-sm">
                    <label htmlFor="sdc">Associate to SDC form component</label>
                </span>
            </div> -->

        <!-- Webhook -->
            <div *ngIf="data.data.flowType === 'Webhook Flow'" class="flex flex-row w-full">
                <div class="col-6 mt-3">
                    <div class="p-inputgroup">
                        <span class="p-float-label">
                            <input id="URL" pInputText formControlName="webhookURL" type="text" class="w-full p-inputtext-sm">
                            <label htmlFor="URL">URL</label>
                        </span>
                        <button *ngIf="createWorkflow.get('webhookURL').value" class="p-button-sm bg-blue" (click)="copyUrl(createWorkflow.get('webhookURL').value)" pButton type="button" icon="pi pi-clone" pTooltip="Copy Webhook"></button>
                        <button *ngIf="createWorkflow.get('webhookURL').value && !isRevoke" [disabled]="!createWorkflow.get('webhookURL').value" class="p-button-sm bg-blue" (click)="updateWebhook('revoke')" pButton type="button" icon="pi pi-ban" pTooltip="Revoke Webhook"></button>
                        <button *ngIf="!createWorkflow.get('webhookURL').value" [disabled]="data.data.add || !createWorkflow.get('expiryDate').value" class="p-button-sm bg-blue" (click)="createWebhook()" pButton type="button" icon="pi pi-plus" pTooltip="Regenerate Webhook"></button>
                    </div>
                </div>
                <div class="col-6 mt-3">
                    <span class="p-float-label">
                        <p-calendar id="date" formControlName="expiryDate" [minDate]="minDate" (onSelect)="onExpiryChange($event)" styleClass="w-full p-inputtext-sm" appendTo="body" [readonlyInput]="true"></p-calendar>
                        <label for="date" class="mb-0 _required">Expiry Date</label>
                    </span>
                </div>
            </div>

            <div *ngIf="data.data.flowType === 'Read Flow' || data.data.flowType === 'Update Flow'" class="flex flex-row gap-4 my-3 px-3">
                <div *ngFor="let type of masterdataOptions" class="flex-auto">
                    <p-radioButton [inputId]="Mtype" [value]="type" [(ngModel)]="masterdataType" [ngModelOptions]="{standalone: true}"></p-radioButton>
                    <label [for]="Mtype" class="ml-1 mb-0">{{ type }}</label>
                </div>
            </div>
            
            <div class="col-12 text-right mt-2" *ngIf="data.data.flowType !== 'Scheduled Flow' && masterdataType !== 'Schedule'">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancleForm()"></button>
                <button *ngIf="!data.data.iscopy && !data.data.isupdate" class="p-button-sm bg-blue" pButton type="button" label="Save" [disabled]="!createWorkflow.valid" (click)="CreateWorkflow()"></button>
                <button *ngIf="data.data.iscopy" class="p-button-sm bg-blue" pButton type="button" label="Create Copy" [disabled]="!createWorkflow.valid" (click)="CreateCopy()"></button>
                <button *ngIf="data.data.isupdate" class="p-button-sm bg-blue" pButton type="button" label="Update" [disabled]="!createWorkflow.valid" (click)="updateWorkflow($event)"></button>
                <!-- <button *ngIf="isRedirect" class="p-button-sm bg-blue" pButton type="button" label="Go To Editor" (click)="goToFlowEditor()"></button> -->
            </div>
        </div>
    </form>

<!-- Schedule Flow -->
    <form class="mt-3" [formGroup]="scheduleform" *ngIf="data.data.flowType === 'Scheduled Flow'" novalidate>
        <div class="grid formgrid sch">
            <div class="col-12 set">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        <span class="p-float-label">
                            <p-dropdown id="schedule" formControlName="scheduletype" [autoDisplayFirst]="false" (onChange)="pickedScheduleType($event)"
                                [options]="OtherSchedules" optionLabel="key" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                            </p-dropdown>
                            <label htmlFor="schedule">Type</label>
                        </span>
                    </ng-template>
                    <ng-template pTemplate="content">
<!-- Minute -->         <div class="grid">
                            <div class="col-6 my-3" *ngIf="selectedschedule === 'Minute'">
                                <span class="p-float-label">
                                    <p-dropdown id="Minute" formControlName="selectedMinute" [autoDisplayFirst]="false"
                                        [options]="MinuteArr" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Minute">Minute</label>
                                </span>
                            </div>
    <!-- Day & Hour -->
                            <div class="col-12 my-3" *ngIf="selectedschedule === 'Daily' || selectedschedule === 'Hourly'">
                                <!-- <div *ngFor="let dailyoption of dailyscheduleoptions">
                                    <p-radioButton [inputId]="dailyoption.name" [value]="dailyoption.value" formControlName="selectedtype"></p-radioButton>
                                    <label [for]="dailyoption.name" class="ml-2">{{ dailyoption.name }}</label>
                                </div> -->
                                <span class="p-float-label">
                                    <p-dropdown id="Occurred" formControlName="selectedtype" [autoDisplayFirst]="false" appendTo="body"
                                        [options]="scheduleOptions" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm">
                                    </p-dropdown>
                                    <label htmlFor="Occurred">Occurred On</label>
                                </span>
                            </div>
    <!-- Week -->
                            <div [ngClass]="selectedschedule === 'Weekly' ? 'col-12 my-3' : 'col-6 my-3'" *ngIf="selectedschedule === 'Weekly' || selectedschedule === 'Minute'">
                                <!-- <div *ngFor="let availableDay of days" formGroupName="days">
                                    <p-checkbox [inputId]="availableDay" [value]="availableDay" formControlName="{{availableDay}}" [binary]="true"></p-checkbox>
                                    <label [for]="availableDay" class="ml-2 mb-0">{{ availableDay }}</label>
                                </div> -->
                                
                                <span class="p-float-label">
                                    <p-multiSelect id="Weekdays" [options]="days" formControlName="weekDays" display="chip" styleClass="w-full p-inputtext-sm" appendTo="body"></p-multiSelect>
                                    <label htmlFor="Weekdays">Weekdays</label>
                                </span>
                            </div>
    <!-- Month -->
                            <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly'">
                                <span class="p-float-label">
                                    <p-dropdown id="Choose" formControlName="chooseMonth" [autoDisplayFirst]="false"
                                        [options]="chooseMonthArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Choose">Choose day of the month</label>
                                </span>
                            </div>
                            <div class="col-12 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'manual'">
                                <span class="p-float-label">
                                    <p-inputNumber id="day" formControlName="day" class="w-full" styleClass="w-full p-inputtext-sm" (onFocus)="setValidatorsMonthly('repeateverymonth')" [useGrouping]="false"></p-inputNumber>
                                    <label htmlFor="day">Repeat on which day of every month?</label>
                                </span>
                            </div>
                            <!-- <div *ngIf="selectedschedule === 'Monthly'" class="col-12">
                                <p-divider align="center" styleClass="my-2">
                                    <span>or</span>
                                </p-divider>
                            </div> -->
                            <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                                <span class="p-float-label">
                                    <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                        [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="on">Repeat On</label>
                                </span>
                            </div>
                            <div class="col-6 my-3" *ngIf="selectedschedule === 'Monthly' && scheduleform.get('chooseMonth').value === 'automatic'">
                                <span class="p-float-label">
                                    <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsMonthly('ondayofmonth')"
                                        [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Day">Day</label>
                                </span>
                            </div>
    <!-- Year -->
                            <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly'">
                                <span class="p-float-label">
                                    <p-dropdown id="Choose" formControlName="chooseYear" [autoDisplayFirst]="false"
                                        [options]="chooseYearArr" optionLabel="name" optionValue="value" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Choose">Choose day of the year</label>
                                </span>
                            </div>
                            <div class="col-12 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'manual'">
                                <span class="p-float-label">
                                    <p-inputNumber id="day" formControlName="day" (onFocus)="setValidatorsYearly('repeateveryyear')" class="w-full" styleClass="w-full p-inputtext-sm" [useGrouping]="false"></p-inputNumber>
                                    <label htmlFor="day">Repeat on which day of every year?</label>
                                </span>
                            </div>
                            <!-- <div *ngIf="selectedschedule === 'Yearly'" class="col-12">
                                <p-divider align="center" styleClass="my-2">
                                    <span>or</span>
                                </p-divider>
                            </div> -->
                            <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                <span class="p-float-label">
                                    <p-dropdown id="on" formControlName="dayoccurence" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                        [options]="dayoccurences" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="on">Repeat</label>
                                </span>
                            </div>
                            <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                <span class="p-float-label">
                                    <p-dropdown id="Day" formControlName="selectedday" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                        [options]="days" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Day">Day</label>
                                </span>
                            </div>
                            <div class="col-4 my-3" *ngIf="selectedschedule === 'Yearly' && scheduleform.get('chooseYear').value === 'automatic'">
                                <span class="p-float-label">
                                    <p-dropdown id="Month" formControlName="selectedmonth" [autoDisplayFirst]="false" (onChange)="setValidatorsYearly('ondayofmonth')"
                                        [options]="months" styleClass="w-full p-inputtext-sm" appendTo="body">
                                    </p-dropdown>
                                    <label htmlFor="Month">Month</label>
                                </span>
                            </div>
                        </div>
                    </ng-template>
                </p-fieldset>
            </div>
            <div class="col-4 text-right mt-3 ml-auto">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancleForm()"></button>
                <button *ngIf="!this.data.data.isupdate" class="p-button-sm bg-blue" pButton type="button" [label]="this.data.data.iscopy ? 'Create Copy' : 'Save'" [disabled]="scheduleform.invalid || createWorkflow.invalid" (click)="this.data.data.iscopy ? CreateCopy() : CreateWorkflow()"></button>
                <button *ngIf="this.data.data.isupdate" class="p-button-sm bg-blue" pButton type="button" label="Update" [disabled]="scheduleform.invalid || createWorkflow.invalid" (click)="updateWorkflow($event)"></button>
            </div>
        </div>
    </form>
</div>


<!-- Icon picker -->
<!-- <p-overlayPanel #iconPicker [style]="{'width': '300px','height': '380px'}">
    <div class="col w-full">
      <input pInputText #search type="search" class="w-full p-inputtext-sm" placeholder="Search...">
    </div>
    <div class="scrolling">
      <button pButton *ngFor="let icon of iconsArr | filter : search.value" (click)="selectIcon(icon, iconPicker)" class="p-button-rounded p-button-text">
        <span class="material-icons">{{icon}}</span>
      </button>
    </div>
</p-overlayPanel> -->
