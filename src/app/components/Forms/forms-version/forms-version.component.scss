.form-list {
    border: none !important;
    padding: 5px;
  }
  .form-list:hover, .draft-list:hover {
    background-color: #cdd5e44f;
    border-radius: 10px;
    cursor: pointer;
  }
  .description {
    font-size: 18px;
    font-weight: 500;
  }
  .form-subtext {
    font-size: 14px;
    font-weight: 400;
  }
  .no-data {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .status-label {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 0px;
    /* border-radius: 50%;
    padding: 18px; */
  }
  ::ng-deep .view p-dataview .p-dataview-header {
    padding: 0px !important;
    background: none !important;
    border: none !important;
  }
  .draft-form {
    background-color: #cdd5e44f;
    border-radius: 10px;
    border: none !important;
    padding: 5px;
    cursor: pointer;
    margin-bottom: 5px;
  }
  ::ng-deep .view p-dataview .p-dataview-content > .p-grid > div {
    border: none !important;
  }
  .help-icon {
    position: absolute;
    top: 22px;
    right: 55px;
  }
  ::ng-deep .publish p-messages .p-message {
    margin: 0rem 0rem 1rem 0rem !important;
    .p-message-wrapper {
        padding: 1rem !important;
    }
}