<p-confirmPopup></p-confirmPopup>
<a href="https://docs.unvired.com/builder/admin/forms/#versions" style="padding: 0.45625rem 0.45625rem;" target="_blank" rel="noopener noreferrer" class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a>
<div class="view">
    <p-dataView *ngIf="draftArr.length > 0" #dv [value]="draftArr">
        <ng-template pTemplate="header" let-rowIndex="rowIndex">
            <div class="flex flex-row">
                <div class="col-12 draft-list" style="border: none !important;" (click)="goToPreview(draftArr[0])">
                    <div class="flex flex-row align-items-center justify-content-between">
                        <label *ngIf="landsacpe?.toLowerCase() === 'production'" class="status-label">Draft</label>
                        <div *ngIf="landsacpe?.toLowerCase() !== 'production'" class="flex flex-column justify-content-center">
                            <label class="status-label">{{draftArr?.length > 0 && draftArr[0]?.releaseType === 'preRelease' ? 'Pre Released' : 'Draft'}}</label>
                            <span *ngIf="draftArr?.length > 0 && draftArr[0]?.comments" class="font-normal">Comment: {{ draftArr[0]?.comments }}</span>
                        </div>
                        <div class="flex align-items-center gap-1">
                            <i class="pi pi-clock text-400"></i>
                            <span class="form-subtext">Last updated by {{ draftArr[0].updatedBy }}, {{ formatDate(draftArr[0].updated | date: 'MM/dd/yyyy h:mm a') }}</span>
                        </div>
                        <div class="flex flex-row justify-content-end">
                            <button *ngIf="landsacpe?.toLowerCase() !== 'production' && draftArr?.length > 0 && draftArr[0]?.releaseType === 'preRelease'" pButton (click)="publishForm(publishPannel, true, $event);$event.stopPropagation()" class="p-button-sm p-button-danger mr-2" label="Unpublish"></button>
                            <button *ngIf="draftArr?.length > 0 && draftArr[0]?.releaseType !== 'preRelease'" pButton (click)="discardChanges($event);$event.stopPropagation()" class="p-button-sm p-button-danger mr-2" label="Discard"></button>
                            <button *ngIf="draftArr?.length > 0 && draftArr[0]?.releaseType !== 'preRelease'" pButton (click)="showPublishFormPannel(publishPannel, $event)" class="p-button-sm p-button-success" label="Publish"></button>
                        </div>
                    </div>
                </div>
            </div>
        </ng-template>
    </p-dataView>


    <p-table #dt 
      [value]="dataSourceRevisions" dataKey="id"
      responsiveLayout="scroll" 
      [scrollable]="true" 
      styleClass="p-datatable-sm p-datatable-gridlines"
      [tableStyle]="{ 'min-width': '50rem' }"
      [globalFilterFields]="['comments', 'description', 'category', 'formType', 'updatedBy']">

        <ng-template pTemplate="caption">
            <div class="flex flex-row align-items-center">
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                    (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
            </div>
        </ng-template>

<!-- Header -->
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th style="width: 4rem" class="text-center">No. </th>
                <th style="min-width: 10rem">Description </th>
                <th style="min-width: 10rem">Last Updated </th>
                <th style="min-width: 10rem" class="text-center">Actions </th>
            </tr>
        </ng-template>

<!-- Table data -->
        <ng-template pTemplate="body" let-ver let-columns="columns">
            <tr (click)="goToPreview(ver)" class="cursor-pointer">
                <td class="text-center">
                    {{ver.version}}
                </td>
        
                <td>
                    <button pButton class="p-button-text p-button-sm text-left">
                        <span>{{ver.comments}}</span>
                    </button>
                </td>
        
                <td>
                    <div class="flex flex-row justify-content-start">
                        <span>{{ver.updatedBy}}, <span *ngIf="ver?.updated" class="ml-1">{{ formatDate(ver.updated | date: 'MM/dd/yyyy h:mm a') }}</span></span> 
                    </div>
                </td>
        
                <td>
                    <div class="flex flex-row justify-content-center">
                        <button *ngIf="ver.isDraft" pButton (click)="discardChanges($event);$event.stopPropagation()" class="p-button-sm p-button-danger mr-2" label="Discard"></button>
                        <button *ngIf="ver.isDraft" pButton (click)="publishPannel.show($event);$event.stopPropagation()" class="p-button-sm p-button-success mr-2" label="Publish"></button>
                        <button *ngIf="!ver.isDraft" pButton class="p-button-sm p-button-outlined bg-blue" label="Revert" (click)="revertformversion(ver.version, $event);$event.stopPropagation()" pTooltip="Revert to version {{ver.version}}"></button>
                    </div>
                </td>
            </tr>
        </ng-template>

<!-- No data -->
        <ng-template pTemplate="emptymessage">
            <tr>
              <td class="text-center" style="font-weight: bold;" colspan="5">No Published Versions Found.</td>
            </tr>
        </ng-template>
    
    </p-table>



    <!-- <p-dataView #dv [value]="dataSourceRevisions" filterBy="comments,updatedBy">
        <ng-template pTemplate="header" let-rowIndex="rowIndex">
            <div class="flex flex-row">
                <div *ngIf="draftArr.length > 0" class="col-8 draft-list" style="border: none !important;" (click)="goToPreview(draftArr[0])">
                    <div class="flex flex-row align-items-center justify-content-between gap-3 w-full mr-2">
                        <div class="flex flex-row align-items-center gap-2">
                            <label class="status-label">Draft</label>
                            <div class="flex align-items-center gap-1">
                                <i class="pi pi-clock text-400"></i>
                                <span class="form-subtext">Last updated by {{ draftArr[0].updatedBy }}, {{ formatDate(draftArr[0].updated | date) }}</span>
                            </div>
                        </div>
                        <div class="flex flex-row justify-content-end flex-1">
                            <button pButton (click)="publishPannel.show($event);$event.stopPropagation()" class="p-button-sm p-button-success mr-2" label="Publish"></button>
                            <button pButton (click)="discardChanges($event);$event.stopPropagation()" class="p-button-sm p-button-danger" label="Discard"></button>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="p-input-icon-left w-full" (click)="$event.stopPropagation()">
                        <i class="pi pi-search"></i>
                        <input class="p-inputtext-sm w-full" pInputText type="search"
                        (input)="dv.filter($event.target.value)" placeholder="Search..." />
                    </div>
                </div>
            </div>
        </ng-template>
        
        <ng-template let-element pTemplate="listItem" >
            <div *ngIf="!element.isDraft" class="col-12 form-list" (click)="goToPreview(element)">
                <div class="flex flex-row align-items-center p-1 gap-3">
                    <div>
                        <label *ngIf="!element.isDraft" class="status-label">{{element.version}}</label>
                    </div>
                    <div [ngClass]="{ 'element.comments' : 'gap-1'}" class="flex flex-column align-items-start">
                        <div class="description">{{ element.comments }}</div>
                        <div class="flex align-items-center gap-1">
                            <i class="pi pi-clock text-400"></i>
                            <span class="form-subtext">Last updated by {{ element.updatedBy }}, {{ formatDate(element.updated | date) }}</span>
                        </div>
                    </div>
                    <div class="flex flex-row justify-content-end flex-1">
                        <button *ngIf="!element.isDraft" pButton class="p-button-sm bg-blue" label="Revert" (click)="revertformversion(element.version, $event);$event.stopPropagation()" pTooltip="Revert to version {{element.version}}"></button>
                    </div>
                </div>
            </div>
        </ng-template>
    </p-dataView> -->
</div>

<!-- Publish form -->
<p-overlayPanel #publishPannel styleClass="p-0" [style]="{'min-width': '300px'}" appendTo="body" (onHide)="resetPublishForm(publishPannel)">
    <form [formGroup]="publishform" novalidate>
        <div class="grid">
            <div class="col-12 w-full mt-3 publish">
                <p-messages *ngIf="landsacpe?.toLowerCase() !== 'production'" severity="info">
                    <ng-template pTemplate>
                        <i style="font-size: 1.5rem" class="pi pi-exclamation-circle mr-3"></i>
                        <div class="flex flex-column justify-content-center align-items-start gap-1">
                            <span><b>Pre Release : </b>Publish a pre-release for testing, further edits possible.</span>
                            <span><b>Final Release : </b>Publish a final release for users to fill, no more edits possible.</span>
                        </div>
                    </ng-template>
                </p-messages>
                <div *ngIf="landsacpe?.toLowerCase() !== 'production'" class="flex flex-row align-items-center mb-3">
                    <p-inputSwitch id="Relase" formControlName="formRelease" (onChange)="onReleaseChange($event)"></p-inputSwitch>
                    <label htmlFor="Relase" class="ml-2 mb-1">{{ formRelease === 'preRelease' ? 'Pre Release' : 'Final Release' }}</label>
                </div>
                <span class="p-float-label">
                    <textarea id="Comment" rows="2" pInputTextarea formControlName="comments" class="w-full" [required]="publishform.get('formRelease').value"></textarea>
                    <label htmlFor="Comment" class="_required" [ngClass]="{'_required': publishform.get('formRelease').value || landsacpe?.toLowerCase() === 'production'}">Comments</label>
                </span>
                <span *ngIf="publishform.get('comments').hasError('maxlength')" class="error"
                    style="margin-top: 0px;"><i class="pi pi-exclamation-circle mr-1"></i><small>Maximum of 100 characters.</small></span>
                <div class="col-12 pr-0 pb-0 text-right">
                    <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel"
                        (click)="resetPublishForm(publishPannel)"></button>
                    <button class="p-button-sm bg-blue" pButton type="button" label="Publish"
                        (click)="publishForm(publishPannel, false)" [disabled]="!publishform.valid"></button>
                </div>
            </div>
        </div>
    </form>
</p-overlayPanel>