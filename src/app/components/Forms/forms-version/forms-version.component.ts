import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import * as dayjs from 'dayjs';
import * as relativeTime from 'dayjs/plugin/relativeTime';
import { Table } from 'primeng/table';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
dayjs.extend(relativeTime);

@Component({
  selector: 'app-forms-version',
  templateUrl: './forms-version.component.html',
  styleUrls: ['./forms-version.component.scss']
})
export class FormsVersionComponent implements OnInit {
  public form: any;
  public formid: string;
  public confirmdialogresult: boolean;
  public dataSourceRevisions = [];
  public draftArr = [];
  public errmsg: string;
  comments: any;
  versionArr: any[];
  formType: string;
  publishform: FormGroup;
  formRelease: string = 'preRelease';
  landsacpe: string;

  constructor(
    private formsservice: FormsService,
    public route: ActivatedRoute,
    private router: Router,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    public data: DynamicDialogConfig,
    private fb: FormBuilder,
  ) { }

  ngOnInit() {
    this.landsacpe = localStorage.getItem('landscape');
    this.formid = this.data.data.formId;
    this.formType = this.data.data.formType;
    this.getformrevisions(this.formid);
    this.publishform = this.fb.group({
      comments: [''],
      formRelease: [false]
    });
  }
  public getformrevisions(formid: string) {
    this.formsservice.getform(formid, 'revisions').subscribe((form: any) => {
      this.form = form;
      const arr = form.revisions;
      this.versionArr = arr.map(item => {if (item.isDraft) {return 'Draft'} else {item.version} return item.version});
      this.dataSourceRevisions = arr ? arr : [];
      this.dataSourceRevisions = arr.filter(item => !item.isDraft);
      this.draftArr = arr.filter(item => item.isDraft);
    });
  }
  // public publishform() {
  //   const dialogRef = this.matDialog.open( PublishFormComponent, {
  //     minWidth: '40%',
  //     panelClass: 'custom-dialog',
  //     data: {
  //       formId: this.formid
  //     }
  //   });
  //   dialogRef.afterClosed().subscribe(res => {
  //     if (res) {
  //       this.getformrevisions(this.formid);
  //     }
  //   });
  // }

// Revert Version
  revertformversion(version, event) {
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: `Do you want to revert back to version ${version}?`,
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.formsservice.revertformversion(this.formid, version)
            .subscribe((archiveformAPIres: any) => {
              const response = archiveformAPIres;
              if (response.error === '') {
                if (response.status === 'Success') {
                  this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully reverted!' });
                  this.getformrevisions(this.formid);
              }
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
          });
        },
        reject: () => {
          this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Action aborted!' });
        }
    });
  }

// Discard Chnages
  discardChanges(event) {
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Discard draft?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.formsservice.discardformversion(this.formid)
            .subscribe((archiveformAPIres: any) => {
              const response = archiveformAPIres;
              if (response.error === '') {
                if (response.status === 'Success') {
                  this.getformrevisions(this.formid);
                  this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully removed!' });
              }
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
          });
        },
        reject: () => {
          this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Action aborted!' });
        }
    });
  }

  public goToPreview(form: any) {
    let data = {
      submission:{},
      formId: form.formId,
      type: this.formType.toLowerCase() === 'masterdata' ? 'masterdata' : 'form',
      version: form.version,
      versionArr: this.versionArr
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    // this.router.navigate([`/test-form/${routerData}`]);
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  publishForm(element: any, isUnpublished: boolean, event?: any) {
    if (this.landsacpe?.toLowerCase() !== 'production' && isUnpublished) {
      this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Unpublish this version?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          const data = {
            comments: this.publishform.value.comments,
            release: this.landsacpe?.toLowerCase() === 'production' ? 'finalRelease' : this.formRelease,
            unPublish: this.landsacpe?.toLowerCase() === 'production' ? false : isUnpublished
          }
          this.formsservice
            .publishform(this.formid, data)
            .subscribe(publishformAPIres => {
              const response = publishformAPIres;
              if (response.error === '') {
                if (response.status === 'Success') {
                  this.getformrevisions(this.formid);
                  this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully Unpublished!' });
                  element?.hide();
                }
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
            });
        },
        reject: () => {
          this.messageService.add({ severity: 'info', summary: 'Info', detail: 'Action aborted!' });
        }
      });
    } else {
      const data = {
        comments: this.publishform.value.comments,
        release: this.landsacpe?.toLowerCase() === 'production' ? 'finalRelease' : this.formRelease,
        unPublish: this.landsacpe?.toLowerCase() === 'production' ? false : isUnpublished
      }
      this.formsservice
        .publishform(this.formid, data)
        .subscribe(publishformAPIres => {
          const response = publishformAPIres;
          if (response.error === '') {
            if (response.status === 'Success') {
              this.getformrevisions(this.formid);
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully published!' });
              element?.hide();
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
        });
    }
  }

  showPublishFormPannel(element: any, event: any) {
    event.stopPropagation();
    if (this.landsacpe?.toLowerCase() === 'production') {
      this.publishform.controls['comments'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.publishform.controls['comments'].updateValueAndValidity();
    }
    this.publishform.reset();
    this.publishform.controls['formRelease'].setValue(false);
    this.publishform.controls['formRelease'].updateValueAndValidity();
    this.formRelease = this.landsacpe?.toLowerCase() === 'production' ? null : 'preRelease';
    element.show(event);
  }

  onReleaseChange(event: any) {
    if (event.checked) {
      this.formRelease = 'finalRelease';
      this.publishform.controls['comments'].setValue(this.publishform.controls['comments'].value);
      this.publishform.controls['comments'].setValidators([Validators.required, Validators.maxLength(100)]);
      this.publishform.controls['comments'].updateValueAndValidity();
    } else {
      this.formRelease = 'preRelease';
      this.publishform.controls['comments'].setValue(this.publishform.controls['comments'].value);
      this.publishform.controls['comments'].clearValidators();
      this.publishform.controls['comments'].updateValueAndValidity();
    }
  }

  resetPublishForm(element: any) {
    element.hide();
    this.publishform.controls['comments'].setValue(null);
    this.publishform.controls['comments'].clearValidators();
    this.publishform.controls['comments'].updateValueAndValidity();
    this.publishform.controls['formRelease'].setValue(false);
    this.publishform.controls['formRelease'].updateValueAndValidity();
    this.publishform.reset();
  }

  checkButtonsVisibility(type: string, releaseType: string): boolean {
    const isProduction = this.landsacpe?.toLowerCase() === 'production';
  
    if (!isProduction) {
      // In non-production, hide discard/publish buttons for preRelease
      return !(releaseType === 'preRelease' && (type === 'discard' || type === 'publish'));
    }
  
    // In production, always show discard/publish buttons
    if (type === 'discard' || type === 'publish') {
      return true;
    }
  
    return false; // Default: hide button
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.getformrevisions(this.formid);
  }

}
