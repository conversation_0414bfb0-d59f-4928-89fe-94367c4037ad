import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormsService } from 'src/app/services/forms.service';
import * as dayjs from 'dayjs';
dayjs.extend(relativeTime);
import * as relativeTime from 'dayjs/plugin/relativeTime';

@Component({
  selector: 'app-nested-forms-list',
  templateUrl: './nested-forms-list.component.html',
  styleUrls: ['./nested-forms-list.component.scss']
})
export class NestedFormsListComponent implements OnInit {
  formId:any;
  showActionButton:boolean = false;
  recentVersion:any;
  recentVersionComments:any;
  forms: any[] = [];
  formTitle: any;
  formType: any;

  constructor(
    private formservice: FormsService,
    private messageService: MessageService,
    public data: DynamicDialogConfig,
    private confirmationService: ConfirmationService,
    private router: Router,
    private ref: DynamicDialogRef,
  ) { }

  ngOnInit() {
    this.formId = this.data.data.formId;
    this.formTitle = this.data.data.formTitle;
    this.formType = this.data.data.formType;
    this.getNestedForms(this.formId);
  }

  getNestedForms(formId: any) {
    let query = {
      formId: formId,
      displayType:'nestedForms'
    }
    this.formservice.getNetsedFormsFromServer(query)
    .subscribe((res) =>{
      let response = res;
      if(response.nestedForms.totalForms >0){
        this.showActionButton = true;
        this.recentVersion = response.nestedForms.nestedFormRecentVersion;
        this.recentVersionComments = response.nestedForms.nestedFormRecentVersionComments;
      } else {
        this.showActionButton =false;
      }
      this.forms = response.nestedForms.formHeaders;
    });
  }

  updateAllVersions(event: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to update all?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formservice.updateNestedForms(this.formId).subscribe(res =>{
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Please reload page after sometime.' });
        })
      },
      reject: () => {}
    });
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  goToFormDesign(form: any) {
    let data = this.formservice.getNavigationData();
    const obj = {
      type: 'form',
      formType: this.formType,
      formId: form.formId,
      category: form?.category,
      formTitle: this.formTitle,
      nestedFormId: this.formId
    }
    this.ref.close();
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
    data.isNestedForm = true;
    this.formservice.setNavigationData(data);
  }

}
