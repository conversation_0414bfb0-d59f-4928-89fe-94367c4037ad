<p-table #dt 
    [value]="forms" dataKey="formId" 
    scrollHeight="45vh"
    responsiveLayout="scroll"
    [scrollable]="true"
    styleClass="p-datatable-sm p-datatable-gridlines"
    [tableStyle]="{ 'min-width': '50rem' }"
    [globalFilterFields]="['formName', 'description', 'usedVersion']">

    <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-between">
            <div>
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchQuery"
                        (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
            </div>
            <button *ngIf="showActionButton" pButton type="button" (click)="updateAllVersions($event)" label="Update All" class="p-button-sm bg-blue"></button>
        </div>
    </ng-template>

    <!-- Header -->
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th style="min-width: 10rem">Title</th>
            <th style="min-width: 10rem">Description</th>
            <th>Version</th>
            <th style="min-width: 10rem">Last Updated</th>
        </tr>
    </ng-template>

    <!-- table data -->
    <ng-template pTemplate="body" let-form let-columns="columns">
        <tr (click)="goToFormDesign(form)" class="cursor-pointer">
            <td>
                <button pButton class="p-button-text">
                    <span class="material-icons" style="color:var(--primary-color) !important;">{{form.avatar}}</span>
                    <span class="px-3">{{form.formTitle}}</span>
                </button>
            </td>
            <td>{{form.description}}</td>
            <td>{{form.usedVersion}}</td>
            <td><span>{{formatDate(form.lastUpdated | date: 'MM/dd/yyyy h:mm a')}}</span></td>
        </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
        <tr>
            <td class="text-center" style="font-weight: bold;" colspan="4">No forms designed yet!</td>
        </tr>
    </ng-template>
</p-table>