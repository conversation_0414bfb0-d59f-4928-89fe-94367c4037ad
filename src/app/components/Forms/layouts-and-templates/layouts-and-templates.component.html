<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <!-- <ng-template pTemplate="right"> 
            <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template> -->
    </p-toolbar>

    <p-table #dt 
        [value] = "forms" 
        dataKey = "formId" 
        sortMode = "single"
        [customSort] = "true"
        selectionMode = "multiple" 
        scrollHeight = "calc(100vh - 190px)"
        [(selection)] = "selectedForms"
        responsiveLayout = "scroll"
        [scrollable] = "true"
        styleClass = "p-datatable-sm p-datatable-gridlines"
        paginatorDropdownAppendTo = "body"
        [lazy] = "true">

      <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-between">
          <div>
            <!-- <label class="mb-0" style="font-weight: bold !important;">Forms List</label> -->
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 21rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchTerm"
                        (keyup)="onSearch()" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
          </div>
          <div>
            <button *ngIf="selectedForms?.length > 0" pButton pRipple (click)="exportSelectedForms()"
                icon="pi pi-fw pi-upload" pTooltip="Export Templates and Layouts" tooltipPosition="bottom" class="mx-1 p-button-sm">
            </button>
            <button *ngIf="selectedForms?.length > 0" pButton pRipple (click)="removeMultipleForms($event)" icon="pi pi-fw pi-trash"
                pTooltip="Remove Templates and Layouts" tooltipPosition="bottom" class="mx-1 p-button-sm p-button-danger">
            </button>
            <button *ngIf="!(selectedForms?.length > 0)" pButton pRipple (click)="CreateFormMenu.show($event)" label="Create" icon="pi pi-angle-down" iconPos="right" class="mx-1 p-button-sm">
                <svg viewBox="114.551 81.194 134.814 113.214">
                    <g class="sparkles">
                      <path style="--duration: 1.9s; --delay: 0s; fill: white;" d="M 134.089 134.136 C 134.218 134.007 140.632 79.696 146.299 133.622 C 146.427 133.493 204.495 138.919 147.969 145.188 C 148.098 145.188 142.057 194.411 134.218 145.574 C 95.547 143.619 104.86 135.576 134.218 134.007 Z"></path>
                      <path style="--duration: 3s; --delay: 0s; fill: white;" d="M 164.499 98.461 C 164.499 98.461 165.237 94.608 165.893 98.461 C 165.893 98.461 169.582 99.116 165.893 99.772 C 165.893 99.772 165.237 103.707 164.499 99.772 C 162.286 99.362 162.45 98.952 164.499 98.461 Z"></path>
                      <path style="--duration: 1.6s; --delay: 0s; fill: white;" d="M 245.112 190.116 C 245.112 190.116 246.146 184.713 247.066 190.116 C 247.066 190.116 252.239 191.035 247.066 191.955 C 247.066 191.955 246.146 197.473 245.112 191.955 C 242.008 191.38 242.238 190.806 245.112 190.116 Z"></path>
                      <path style="--duration: 3.4s; --delay: 0s; fill: white;" d="M 145.191 169.997 C 145.116 170.073 150.603 142.15 155.945 170.111 C 155.983 170.149 193.651 175.043 155.945 180.069 C 155.983 180.107 151.124 210.541 145.153 180.978 C 122.396 175.825 127.604 174.696 145.191 170.073 Z"></path>
                      <path style="--duration:2.5s; --delay: 0s; fill: white;" d="M 221.387 93.153 C 221.441 93.045 226.064 57.859 230.916 92.99 C 231.024 93.153 270.329 98.121 231.133 101.437 C 231.187 101.437 226.75 136.324 222.199 101.924 C 196.439 99.837 205.018 96.977 221.549 93.153 Z"></path>
                      <path style="--duration: 3.9s; --delay: 0s; fill: white;" d="M 200.728 154.009 C 200.657 154.045 203.439 114.882 205.935 153.973 C 206.042 154.009 245.419 156.669 205.864 159.145 C 205.864 159.181 203.379 195.591 201.049 159.074 C 171.905 157.152 191.541 155.218 200.692 154.009 Z"></path>
                    </g>
                </svg>
            </button>
          </div>
            
        </div>
      </ng-template>

<!-- Header -->
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th style="width: 3rem" class="text-center"></th>
                <th style="min-width: 10rem">
                    <div class="flex flex-row align-items-center gap-2">
                        <span>Title</span>
                        <i *ngIf="sortField !== 'formName'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('formName', 'ASC')"></i>
                        <i *ngIf="sortField === 'formName' && sortOrder === 'ASC'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('formName', 'DESC')" style="color: var(--primary-color);"></i>
                        <i *ngIf="sortField === 'formName' && sortOrder === 'DESC'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('formName', 'ASC')" style="color: var(--primary-color);"></i>
                    </div>
                </th>
                <th style="min-width: 10rem">Description</th>
                <th>
                    <div class="flex flex-row align-items-center gap-2">
                        <span>Category</span>
                        <!-- <p-sortIcon field="category" class="ml-auto"></p-sortIcon> -->
                        <i *ngIf="sortField !== 'category'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('category', 'ASC')"></i>
                        <i *ngIf="sortField === 'category' && sortOrder === 'ASC'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('category', 'DESC')" style="color: var(--primary-color);"></i>
                        <i *ngIf="sortField === 'category' && sortOrder === 'DESC'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('category', 'ASC')" style="color: var(--primary-color);"></i>
                        <p-columnFilter type="text" field="category" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false"  [showClearButton]="false" [showApplyButton]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown [(ngModel)]="selectedCategory"
                                    [options]="categories" 
                                    optionLabel="category" 
                                    optionValue="category"
                                    (onChange)="onSelectCategory($event)" 
                                    placeholder="Category"
                                    >
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </div>
                </th>
                <!-- <th>
                    <div class="flex flex-row align-items-center gap-2">
                        <span>Type</span>
                        <i *ngIf="sortField !== 'formType'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('formType', 'ASC')"></i>
                        <i *ngIf="sortField === 'formType' && sortOrder === 'ASC'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('formType', 'DESC')" style="color: var(--primary-color);"></i>
                        <i *ngIf="sortField === 'formType' && sortOrder === 'DESC'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('formType', 'ASC')" style="color: var(--primary-color);"></i>
                    </div>
                </th> -->
                <th style="min-width: 10rem">
                    <div class="flex flex-row align-items-center gap-2">
                        <span>Last Updated</span>
                        <i *ngIf="sortField !== 'lastUpdated'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('lastUpdated', 'ASC')"></i>
                        <i *ngIf="sortField === 'lastUpdated' && sortOrder === 'ASC'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('lastUpdated', 'DESC')" style="color: var(--primary-color);"></i>
                        <i *ngIf="sortField === 'lastUpdated' && sortOrder === 'DESC'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('lastUpdated', 'ASC')" style="color: var(--primary-color);"></i>
                        <!-- <p-columnFilter type="text" field="lastUdpatedBy" display="menu" [showMatchModes]="false" [showOperator]="false" 
                            [showAddButton]="false"  [showClearButton]="false" [showApplyButton]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-autoComplete 
                                    [(ngModel)]="selectedUser" [suggestions]="userArr" field="firstName" placeholder="Type Username..."
                                    (completeMethod)="onUserChange($event)" (onSelect)="onSelectUser($event)"
                                    >
                                </p-autoComplete>
                            </ng-template>
                        </p-columnFilter> -->
                    </div>
                </th>
                <th style="min-width: 10rem" class="text-center">Actions</th>
            </tr>
        </ng-template>

<!-- table data -->
        <ng-template pTemplate="body" let-form let-columns="columns">
            <tr (click)="goToFormDesign(form)" class="cursor-pointer">
                <td class="text-center" (click)="$event.stopPropagation()">
                    <p-tableCheckbox [value]="form"></p-tableCheckbox>
                </td>
                <!-- <td class="text-center">
                    <span class="material-icons" style="color:var(--primary-color) !important;">{{form.avatar}}</span>
                </td> -->
                <td>  
                    <button pButton class="p-button-text p-button-sm text-left">
                        <span class="material-icons" style="color:var(--primary-color) !important;">{{form.avatar}}</span>
                        <span class="px-2">{{form.formTitle}}</span>
                    </button>
                </td>
        
                <td>
                    {{form.description}}
                </td>
        
                <td>
                    {{form.category}}
                </td>
        
                <!-- <td>
                    <div class="flex flex-row align-items-center justify-content-center border-round" 
                        [ngClass]="[form.formType.toLowerCase() === 'layouts' ? 'layout-type' : '',form.formType.toLowerCase() === 'templates' ? 'template-type' : '']">
                        <span>{{camelCaseString(form.formType)}}</span>
                    </div>
                </td> -->
        
                <td>
                    <div class="flex flex-row justify-content-start">
                        <span>{{form.lastUdpatedBy}}, <span *ngIf="form?.lastUpdated" class="ml-1">{{formatDate(form.lastUpdated | date: 'MM/dd/yyyy h:mm a')}}</span></span> 
                    </div>
                </td>
        
                <td (click)="$event.stopPropagation();getFormDetails(form)">
                    <div class="flex flex-row justify-content-center">
                        <button pButton [pTooltip]="[form.formType.toLowerCase() === 'Layouts' ? 'Update Layout' : 'Update Template']"
                            icon="pi pi-fw pi-pencil" class="p-button-rounded p-button-text mx-1" (click)="updateForm(form)">
                        </button>
                        <button pButton [pTooltip]="[form.formType.toLowerCase() === 'Layouts' ? 'Copy Layout' : 'Copy Template']"
                            icon="pi pi-fw pi-copy" class="p-button-rounded p-button-text mx-1" (click)="copyForm(form)">
                        </button>
                        <button pButton pRipple (click)="menu.toggle($event);showFormActionMenu(form)" label="More" icon="pi pi-angle-down" iconPos="right" class="p-button-sm p-button-outlined"></button>

                    </div>
                </td>
            </tr>
        </ng-template>

<!-- Paginator -->
        <ng-template pTemplate="footergrouped">
            <tr>
                <td colspan="6">
                    <p-paginator [rows]="pageSize" [totalRecords]="formLength" [showJumpToPageDropdown]="false" dropdownAppendTo="body"
                        (onPageChange)="getAllTemplatesAndLayouts($event, sort, selectedCategory, searchTerm)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
                    </p-paginator>
                </td>
            </tr>   
        </ng-template>
        
        <ng-template pTemplate="emptymessage">
            <tr>
                <td class="text-center" style="font-weight: bold;" colspan="6">No layouts and templates created yet!</td>
            </tr>
        </ng-template>
    </p-table>

</div>

<p-menu #menu [model]="menuItems" [popup]="true" appendTo="body" [showTransitionOptions]="'150ms'"></p-menu>

<!-- Create Form -->
<p-overlayPanel #CreateFormMenu [style]="{'max-width': '400px'}">
    <ul class="custom-ul">
        <!-- <li class="custom-li my-auto" (click)="newLayoutOrTemplate('Theme')"><i class="pi pi-credit-card menu-icons"></i>Theme</li> -->
        <li class="custom-li my-auto" (click)="newLayoutOrTemplate()"><i class="pi pi-file-o menu-icons"></i>Template</li>
    </ul>
    <p-divider align="center" styleClass="my-2"><b>Import</b></p-divider>
    <ul class="custom-ul">
        <li class="custom-li my-auto" (click)="showImportDialog('file', CreateFormMenu)"><i class="pi pi-fw pi-copy menu-icons"></i> Files</li>
        <li class="custom-li my-auto" (click)="showImportDialog('remote', CreateFormMenu)"><i class="pi pi-fw pi-desktop menu-icons"></i> Remote System</li>
    </ul>
</p-overlayPanel>

<!-- Import Forms By Files -->
<p-dialog header="Import Forms From File" [(visible)]="showImportByFile" [modal]="true" [style]="{ width: 'calc(100vh - 40vh)' }" [draggable]="false" [resizable]="false" (onHide)="resetImportForms()">
    <div class="grid">
        <div class="col-12">
            <div class="flex file-drop-area align-items-center mb-2">
                <button class="p-button-sm" pButton type="button" icon="pi pi-plus" label="Choose File"></button>
                <span class="file-msg"><b class="cancel-button">{{file ? file.name : 'or drag and drop here' }} </b></span>
                <input class="file-input" pInputText #imageInput accept="application/json" type="file" (change)="processFile(imageInput)">
            </div>
            <p-messages [value]="importFileText" [enableService]="false" [closable]="false"></p-messages>
            <div>
                <p-checkbox id="Publish" [(ngModel)]="autoPublish" binary="true"></p-checkbox>
                <label for="Publish" class="mb-0 ml-2">Publish Immediately</label>
            </div>
        </div>
        <div class="col-12 text-right">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetImportForms()"></button>
            <button class="p-button-sm" pButton type="button" label="Import" (click)="importFromFile()"></button>
        </div>
    </div>
</p-dialog>

<!-- Import Forms By Remote -->
<p-dialog header="Import Forms Form Remote System" [(visible)]="showImportByRemote" [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" [resizable]="false" (onHide)="resetImportForms()">
    <form [formGroup]="remoteSystemForm" *ngIf="remoteFormList?.length === 0" novalidate>
        <div class="grid formgrid">
            <div class="col-12 mt-4">
                <span class="p-float-label">
                    <input id="url" pInputText formControlName="url" type="text" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="url" class="_required">URL</label>
                </span>
            </div>
            <div class="col-12 mt-4">
                <span class="p-float-label">
                    <input id="Email" pInputText formControlName="emailId" type="text" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="Email" class="_required">Email</label>
                </span>
                <span *ngIf="remoteSystemForm.get('emailId')?.hasError('email')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Enter valid email id.</small></span>
            </div>
            <div class="col-12 mt-4">
                <span class="p-input-icon-right p-float-label w-full">
                    <i [class]="showPassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showPassword=!showPassword"></i>
                    <input id="password" pInputText [formControlName]="'password'" class="w-full p-inputtext-sm" [type]="showPassword ? 'text' : 'password'" required="true">
                    <label htmlFor="password" class="_required">Password</label>
                </span>
            </div>
            <div class="col-12 mt-4 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetImportForms()"></button>
                <button class="p-button-sm" pButton type="button" label="Get Forms List" (click)="downloadForms()" [disabled]="!remoteSystemForm.valid"></button>
            </div>
        </div>
    </form>
    <form *ngIf="remoteFormList?.length > 0">
        <div class="flex flex-column w-full remote">
            <span class="p-float-label">
                <span class="p-float-label w-full mt-4">
                    <p-multiSelect [style]="{'min-width': '100%'}" id="Forms" [options]="remoteFormList" [(ngModel)]="selectedFormsForImport" optionLabel="name" optionValue="id" display="chip" dropdownAppendTo="body" appendTo="body"></p-multiSelect>
                    <label htmlFor="Forms">Select Forms</label>
                </span>
            </span>
            <div class="col-12 pr-0 mt-2 text-right">
                <button class="p-button-sm" pButton type="button" label="Import" (click)="importselectedForms()" [disabled]="selectedFormsForImport?.length === 0"></button>
            </div>
        </div>
    </form>
</p-dialog>

<!-- Theme Dialog -->
<p-dialog [(visible)]="showThemeDialog" [modal]="true" (onHide)="cancelEmptyForm()" [style]="{ width: '60vw', height: 'calc(100vh - 30vh)' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="header">  
        <span style="font-weight: 700;font-size: 1.25rem;">Themes</span>
    </ng-template>
    <div class="grid template-cards">
        <div class="col-3">     <!-- Add method for tablet also, hover problem -->
            <p-card>
                <ng-template pTemplate="header">
                    <div class="flex flex-column w-full">
                        <div class="image-container">
                            <img src="assets/images/bootstarp-dark-blue.png" alt="Hover Image" class="hover-image">
                            <div class="hover-overlay">
                                <button class="p-button-sm p-button-rounded p-button-help" pButton type="button" label="Preview" (click)="showTemplatePreview()"></button>
                                <button class="p-button-sm p-button-rounded" pButton type="button" label="Create" (click)="newFormDesign('form')"></button>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="flex flex-column">
                        <h5 class="mb-2" style="color: var(--text-color);">Empty</h5>
                        <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:95%">Create an empty form. </span>
                    </div>
                </ng-template>
            </p-card>
        </div>
        <div class="col-3">
            <p-card>
                <ng-template pTemplate="header">
                    <div class="flex flex-column w-full">
                        <div class="image-container">
                            <img src="assets/images/bootstarp-dark-blue.png" alt="Hover Image" class="hover-image">
                            <div class="hover-overlay">
                                <button class="p-button-sm p-button-rounded p-button-help" pButton type="button" label="Preview" (click)="showTemplatePreview()"></button>
                                <button class="p-button-sm p-button-rounded" pButton type="button" label="Create" (click)="newFormDesign('form')"></button>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="flex flex-column">
                        <h5 class="mb-2" style="color: var(--text-color);">Header Layout</h5>
                        <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:95%">This form has header with two columns. </span>
                    </div>
                </ng-template>
            </p-card>
        </div>
        <div class="col-3 text-center">
            <p-card>
                <ng-template pTemplate="header">
                    <div class="flex flex-column w-full">
                        <div class="image-container">
                            <img src="assets/images/bootstarp-dark-blue.png" alt="Hover Image" class="hover-image">
                            <div class="hover-overlay">
                                <button class="p-button-sm p-button-rounded p-button-help" pButton type="button" label="Preview" (click)="showTemplatePreview()"></button>
                                <button class="p-button-sm p-button-rounded" pButton type="button" label="Create" (click)="newFormDesign('form')"></button>
                            </div>
                        </div>
                    </div>
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="flex flex-column">
                        <h5 class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden mb-2" style="width:95%;color: var(--text-color);">Create Notification Template</h5>
                        <span class="text-overflow-ellipsis surface-overlay white-space-nowrap overflow-hidden" style="width:95%">This form will send notifications on approval. </span>
                    </div>
                </ng-template>
            </p-card>
        </div>

        <!-- <div class="col-12 mt-2 text-right">
            <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="cancelEmptyForm()"></button>
            <button class="p-button-sm" pButton type="button" label="Create" (click)="downloadForms()"></button>
        </div> -->
    </div>
</p-dialog>