import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MenuItem, Message, MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { Table } from 'primeng/table';
import { Subject, debounceTime } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { FormsVersionComponent } from '../forms-version/forms-version.component';
import { FormsTranslationComponent } from '../forms-translation/forms-translation.component';
import * as dayjs from 'dayjs';
dayjs.extend(relativeTime);
import * as relativeTime from 'dayjs/plugin/relativeTime';
import { CreateFormComponent } from '../create-form/create-form.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { whiteSpaceValidator } from 'src/app/shared/directives/white-space-validator.directive';

@Component({
  selector: 'app-layouts-and-templates',
  templateUrl: './layouts-and-templates.component.html',
  styleUrls: ['./layouts-and-templates.component.scss']
})
export class LayoutsAndTemplatesComponent implements OnInit {
  breadcrumbItems : MenuItem[];
  searchSubject: Subject<string> = new Subject();
  pageSize: number = 10;
  pageIndex: number = 0;
  sortField: string;
  sortOrder: string;
  tablePagination: any;
  sort: any;
  searchTerm: string;
  forms: any[];
  formLength: number = 0;
  errmsg: string;
  selectedCategory: any;
  selectedForm: any;
  menuItems: MenuItem[] | undefined;
  selectedForms: any;
  categoryType: string = 'templates';
  importFileText: Message[];
  showImportByFile: boolean = false;
  showImportByRemote: boolean = false;
  autoPublish: boolean = false;
  overwriteDraft: boolean = false;
  remoteSystemForm: FormGroup;
  file: File;
  FileJsonObj = null;
  showPassword: boolean = false;
  remoteFormList: any[] = [];
  selectedFormsForImport: any;
  showThemeDialog: boolean = false;

  constructor(private formservice: FormsService,
    private router: Router,
    private messageService: MessageService,
    public dialogService: DialogService,
    public layoutService: LayoutService,
    private confirmationService: ConfirmationService,
    private fb: FormBuilder,) {

  }

  ngOnInit(): void {
    this.getAllTemplatesAndLayouts(null, null, null, null);
    this.breadcrumbItems = [
      {label:'Home', routerLink: '/home'},
      {label:'Templates'}
    ];
    this.searchSubject.pipe( // only for search bar
      debounceTime(500) 
    ).subscribe(searchText => {
      this.getAllTemplatesAndLayouts(null, null, null, this.searchTerm);
    });
    this.remoteSystemForm = this.fb.group({
      url: ['', Validators.required],
      emailId: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, whiteSpaceValidator()]]
    });
  }

  showFormActionMenu(form: any) {
    this.selectedForm = form;
      this.menuItems = [
        {
          label: 'Versions',
          icon: 'pi pi-undo',
          visible: true,
          command: () => {
           this.showRevisionDialog();
          }
        },
        {
          label: 'Translations',
          icon: 'pi pi-language',
          visible: true,
          command: () => {
           this.showTranslationDialog();
          }
        }
      ];
  }

  getAllTemplatesAndLayouts(pagination: any, sort: any, category: string, searchTerm: string) {
    this.searchTerm = searchTerm;
    this.sortField = sort?.field;
    this.sortOrder = sort?.order;
    this.selectedCategory = category;

    this.tablePagination = pagination;
    this.pageIndex = this.tablePagination ? this.tablePagination.page * this.tablePagination.rows : 0;
    this.pageSize = this.tablePagination ? this.tablePagination.rows : 10;
    // this.first = this.tablePagination?.first ? this.tablePagination?.first : 0;

    this.formservice.getallformsandmasterdata(this.sortField, this.sortOrder, this.pageSize, this.pageIndex, null, this.selectedCategory, this.searchTerm, this.categoryType, false).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.forms =  response.forms;
        this.formLength = response.totalRecords || response.forms?.length;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  onSearch() {
    this.searchSubject.next(this.searchTerm);
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.getAllTemplatesAndLayouts(null, null, null, null);
  }

  sorting(str: string, order: string) {
    this.sortField = str;
    this.sortOrder = order;
    let sort = {field: this.sortField, order: this.sortOrder};
    this.getAllTemplatesAndLayouts(null, sort, this.selectedCategory, this.searchTerm);
  }

  onSelectCategory(data: any) {
    this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, data.value, this.searchTerm);
  }

  goToFormDesign(form: any) {
    if (form?.editable) {
      const obj = {
        type: 'form',
        formType: form?.formType ? form.formType : (form === 'Layouts' ? 'Layouts' : 'Templates'),
        formId: form?.formId ? form.formId : 1,
        category: form?.category
      }
      const routerData = encodeURIComponent(JSON.stringify(obj));
      this.router.navigate([`/forms/form-builder/${routerData}`]);
    } else {
      let data = {
        submission: {},
        formId: form?.formId,
        type: form?.formType,
        isPreview: true
      }
      const routerData = encodeURIComponent(JSON.stringify(data));
      let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
      let baseUrl = window.location.href.replace(this.router.url, '');
      window.open(baseUrl + newRelativeUrl, '_blank');
    }
  }

  newLayoutOrTemplate() {
    const obj = {
      type: 'form',
      formType: "Templates",
      formId: '1',
    }
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
  }

  getFormDetails(form: any) {
    this.selectedForm = form;
  }

  showRevisionDialog() {
    const ref = this.dialogService.open(FormsVersionComponent, {
      header: `Versions (${this.selectedForm?.formTitle})`,
      width: '60%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.selectedForm?.formId,
        formType: this.selectedForm?.formType
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
      }
    });
  }

  showTranslationDialog() {
    const ref = this.dialogService.open(FormsTranslationComponent, {
      header: `Translation (${this.selectedForm?.formTitle})`,
      width: '60%',
      data: {
        formId: this.selectedForm?.formId,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
      }
    });
  }

  camelCaseString(str: string) {
    return str.replace(/([A-Z])/g, (match) => ` ${match}`).replace(/^./, (match) => match.toUpperCase());
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  updateForm(form: any) {
    const ref = this.dialogService.open(CreateFormComponent, {
      header: `Update ${form.formType === 'Templates' ? 'Template' : 'Layout'}`,
      width: '50%',
      data: {
        type: 'form',
        isUpdateForm: true,
        form: form,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
      }
    });
  }

  copyForm(form: any) {
    const ref = this.dialogService.open(CreateFormComponent, {
      header: `Copy ${form.formType === 'Templates' ? 'Template' : 'Layout'}`,
      width: '50%',
      data: {
        type: 'form',
        isCopyForm: true,
        form: form,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
      }
    });
  }

  exportSelectedForms() {
    this.formservice
      .exportforms('form',this.selectedForms.map(data => data.formId))
      .subscribe({
        next: (data: any) => { this.downloadFile(data, 'exportdata.json')}, // console.log(data),
        error: (error) => { console.log('Error downloading the file.') },
        // () => {
        //   console.log('OK');
        // }
      });
  }

  downloadFile(data: any, filename: string) {
    const blob = new Blob([JSON.stringify(data)], { type: 'octet/stream' });
   // const url = window.URL.createObjectURL(blob);
   // window.open(url);
   const a = document.createElement('a');
   document.body.appendChild(a);
   const url = window.URL.createObjectURL(blob);
   a.href = url;
   a.download = filename;
   a.click();
   setTimeout(() => {
     window.URL.revokeObjectURL(url);
     document.body.removeChild(a);
   }, 0);
   this.selectedForms = [];
 }

  removeMultipleForms(event?: Event) {
    let completedCount = 0;
    let totalCount = this.selectedForms.length;
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove templates or layouts?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        if (this.selectedForms.length > 0) {
          for (let i=0; this.selectedForms.length > i; i++) {
            this.formservice.deleteform(this.selectedForms[i].formId)
              .subscribe((response: any) => {
                if (response.status.toLowerCase() === 'success') {
                  completedCount += 1;
                  if (completedCount === totalCount) {
                    this.selectedForms = [];
                    this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
                    completedCount = 0;
                    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully removed.' });
                  }
                } else {
                  this.errmsg = response.error;
                  this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
                }
              });
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please slect templates and layouts.' });
        }
      },
      reject: () => {}
    });
  }

  showImportDialog(str: string, element: any) {
    element.hide();
    if (str === 'file') {
      this.importFileText = [{ severity: 'warn', summary: 'Warning: ', detail: 'Existing draft will be discarded.' }];
      this.showImportByFile = true;
    } else if (str === 'remote') {
      this.showImportByRemote = true;
      this.remoteSystemForm.patchValue({
        url: localStorage.getItem('remote_url') ? localStorage.getItem('remote_url') : null,
        emailId: localStorage.getItem('remote_email') ? localStorage.getItem('remote_email') : null
      });
    } else {
      this.showImportByFile = false;
      this.showImportByRemote = false;
    }
  }

  importFromFile() {
    this.formservice.importformsfromfile(
      'file',
      'form',
      this.autoPublish,
      true,
      this.b64EncodeUnicode(JSON.stringify(this.FileJsonObj))
    ).subscribe(response => {
    if (response.error === '') {
      if (response.status === 'Success') {
        this.messageService.add({ severity: 'success', summary: 'Success', detail: `skipped:${response.skipped},imported ${response.imported} of all selected templates and layouts}` });
        this.resetImportForms();
        this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
      }
    } else {
      this.errmsg = response.error;
      this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
    }
    });
  }

  b64EncodeUnicode(str: any) {
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
    return String.fromCharCode(("0x" + p1) as any);
    }));
  }

  processFile(fileInput: any) {
    this.file = fileInput.files[0];
    const reader = new FileReader();
    reader.addEventListener('load', (event: any) => {
      this.FileJsonObj = JSON.parse(event.target.result);
    });
    reader.readAsText(this.file);
  }

  downloadForms() {
    const obj = {
      email: this.remoteSystemForm.value.emailId,
      password: this.remoteSystemForm.value.password,
      url: this.remoteSystemForm.value.url,
      domain: localStorage.getItem('domain')
    }
    this.formservice.downloadremoteforms('remote', 'form', obj)
      .subscribe((response: any) => {
        if (response.error === '') {
          if (response.status === 'Success') {
            localStorage.setItem('remote_url',this.remoteSystemForm.value.url);
            localStorage.setItem('remote_email',this.remoteSystemForm.value.emailId);
            this.remoteFormList = response.data;
            this.remoteSystemForm.reset();
           // console.log('Downloaded forms successfully!!');
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  importselectedForms() {
    // console.log('Forms to export', this.selectedformitems.selectedOptions.selected.map(o => o.value));
    console.log(this.selectedFormsForImport);
    this.formservice.importremoteforms(
        'remote',
        'form',
        this.remoteSystemForm.value,
        this.selectedFormsForImport,
        this.autoPublish,
        true
      )
      .subscribe(response => {
        if (response.error === '') {
          if (response.status.toLowerCase() === 'success') {
            this.resetImportForms();
            this.messageService.add({ severity: 'success', summary: 'Success', detail: `skipped:${response.skipped}, imported ${response.imported} of all selected form` });
            this.showImportByRemote = false;
            this.getAllTemplatesAndLayouts(this.tablePagination, this.sort, this.selectedCategory, this.searchTerm);
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  resetImportForms() {
    this.file = null;
    this.autoPublish = false;
    this.overwriteDraft = false;
    this.remoteSystemForm.reset();
    this.showImportByFile = false;
    this.showImportByRemote = false;
  }

  cancelEmptyForm() {
    this.showThemeDialog = false;
  }

  showTemplatePreview() {
    let data = {
      submission: {},
      isPreview: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');

    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  newFormDesign(formType: string) {
    const obj = {
      type: 'form',
      formType: formType,
      formId: '1',
    }
    const routerData = encodeURIComponent(JSON.stringify(obj));
    this.router.navigate([`/forms/form-builder/${routerData}`]);
  }

}
