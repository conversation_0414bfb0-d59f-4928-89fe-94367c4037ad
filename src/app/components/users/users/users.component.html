<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>
<div class="card mb-0" #paginator>
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                  <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <button pButton pRipple (click)="showCeateUserAccordian()"
                icon="pi pi-fw pi-user-plus" pTooltip="Create User" tooltipPosition="bottom" class="p-button-sm mr-2">
            </button>
            <button pButton pRipple (click)="teamPanel.toggle($event);createTeamForm.reset()"
                icon="pi pi-fw pi-users" pTooltip="Create Team" tooltipPosition="bottom" class="p-button-sm mr-2">
            </button>
            <button pButton pRipple (click)="showImportUser(importUser, $event)"
                icon="pi pi-fw pi-download" pTooltip="Import User" tooltipPosition="bottom" class="p-button-sm mr-2">
            </button>
            <!-- <a href="https://docs.unvired.com/builder/admin/users/" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
            <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        
          </ng-template>
    </p-toolbar>
  
    <!-- Create User -->
    <p-accordion styleClass="mb-3" *ngIf="isCreateUser">
        <p-accordionTab [selected]="true" >
        <ng-template pTemplate="header">
            <span>New User</span>
        </ng-template>
        <ng-template pTemplate="content">
            <form [formGroup]="userform" novalidate>
                <div class="formgrid grid usr">
                  <div class="col-3">
                    <div class="mt-4">
                      <span class="p-float-label">
                        <input id="firstname" pInputText formControlName="firstName" class="w-full p-inputtext-sm" [autofocus]="true" pAutoFocus required="true">
                        <label htmlFor="firstname" class="_required">First Name</label>
                      </span>
                      <span *ngIf="userform.get('firstName')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                    </div>
                    <div class="mt-4">
                      <span class="p-float-label">
                        <input id="email" pInputText formControlName="email" type="email" class="w-full " required="true">
                        <label htmlFor="email" class="_required">Email Id</label>
                      </span>
                      <span *ngIf="userform.get('email')?.hasError('email')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Enter valid email id.</small></span>
                    </div>
                    <div class="mt-4">
                      <span class="p-float-label">
                        <p-dropdown id="role" formControlName="role" optionLabel="label" required="true" [autoDisplayFirst]="false"
                            optionValue="value" [options]="Roles" class="" styleClass="w-full p-inputtext-sm">
                        </p-dropdown>
                        <label htmlFor="role" class="_required">Role</label>
                      </span>
                    </div>
                    <div class="mt-4">
                      <div class="w-full toggle-button">
                        <div *ngIf="loginType !== 'ads'" class="my-auto">
                            <label>Auto Generate</label>
                            <p-inputSwitch formControlName="letUserChoosePassword" (onChange)="addremovevalidators()" class="ml-2"></p-inputSwitch>
                        </div>
                        <div class="my-auto">
                            <label>Send A/c Info</label>
                            <p-inputSwitch formControlName="sendPasswordInEmail" class="ml-2"></p-inputSwitch>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-3">
                    <div class="mt-4">
                      <span class="p-float-label">
                        <input id="lastname" pInputText formControlName="lastName" class="w-full " required="true">
                        <label htmlFor="lastname" class="_required">Last Name</label>
                      </span>
                      <span *ngIf="userform.get('lastName')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                    </div>
                    <div class="mt-4">
                      <span class="p-float-label">
                        <input id="phonenumber" pInputText formControlName="phoneNumber" type="text" class="w-full " pattern="^[^0-9]*(?:(\d)[^0-9]*){10}$">
                        <label htmlFor="phonenumber">Phone Number</label>
                      </span>
                      <span *ngIf="userform.get('phoneNumber')?.hasError('pattern')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Please eneter 10 digits.</small></span>
                    </div>
                    <div class="mt-4">
                      <span class="p-input-icon-right p-float-label w-full">
                        <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                        <input id="password" pInputText formControlName="password" class="w-full" [type]="showpassword ? 'text' : 'password'" required="true">
                        <label htmlFor="password" class="_required">Password</label>
                      </span>
                      <span *ngIf="userform.get('password')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                      <span *ngIf="userform.get('password')?.hasError('minlength') && (userform.get('password')?.touched || userform.get('password')?.dirty)" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Minimum 3 characters are required.</small></span>
                    </div>
                    <div *ngIf="loginType === 'ads'" class="my-4">    <!-- ADS -->
                      <span class="p-float-label">
                          <input id="adsusername" pInputText formControlName="adsusername" type="text" class="w-full " required="true">
                          <label htmlFor="adsusername" class="_required">Active Directory User</label>
                        </span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="flex flex-column justify-content-center align-items-center ">
                      <image-cropper class="max-h-15rem max-w-15rem"
                          [imageChangedEvent]="imageChangedEvent"
                          format="png"
                          (imageCropped)="imageCropped($event)"
                          (imageLoaded)="imageLoaded($event)"
                          (cropperReady)="cropperReady()"
                          (loadImageFailed)="loadImageFailed()"
                          [style.display]="showCropper ? null : 'none'"
                          output="base64"
                          [settings]="cropperSettings"
                        ></image-cropper>
                        <button *ngIf="!imageChangedEvent" pButton class="p-button-sm" (click)="showUploadPopup()" label="Upload Profile Photo"></button>    
                        <input *ngIf="!imageChangedEvent" class="file-input" pInputText id="imageInput" accept="image/*" type="file" (change)="fileChangeEvent($event)" hidden>
                        <div *ngIf="imageChangedEvent" class="flex flex-row justify-content-center">
                            <button pButton class="p-button-sm p-button-danger" (click)="cancelProfilePhoto()" label="Remove"></button>
                            <!-- <button pButton class="p-button-sm" (click)="saveProfilePhoto()" label="Save"></button>     -->
                        </div>
                    </div>
                    <div class="mt-4">
                      <span class="p-float-label">
                        <p-multiSelect id="teams" [options]="filteredTeams" formControlName="team" optionLabel="teamId" display="chip" styleClass="w-full"></p-multiSelect>
                        <label htmlFor="teams">Teams</label>
                      </span>
                    </div>
                  </div>
                    <!-- <div class="col-3 mt-2 mb-3">
                        <span class="p-float-label">
                            <input id="firstname" pInputText formControlName="firstName" class="w-full p-inputtext-sm" [autofocus]="true" pAutoFocus required="true">
                            <label htmlFor="firstname" class="_required">First Name</label>
                        </span>
                        <small class="error" *ngIf="userform.get('firstName')?.hasError('whitespace')">Whitespaces are not allowed.</small>
                    </div>
                    <div class="col-3 mt-2 mb-3">
                        <span class="p-float-label">
                            <input id="lastname" pInputText formControlName="lastName" class="w-full " required="true">
                            <label htmlFor="lastname" class="_required">Last Name</label>
                        </span>
                        <small class="error" *ngIf="userform.get('lastName')?.hasError('whitespace')">Whitespaces are not allowed.</small>
                    </div>
                    <div class="col-6 text-center">  
                        <label class="hoverable" for="imageInput"> comment start
                            <span *ngIf="url" (click)="cacelUserProfile();event.stopPropagation()"><i class="pi pi-times closeIcon"></i></span>
                            <p-avatar [image]="url ? url : '/assets/images/dummy_user.png'" size="xlarge" shape="circle" pTooltip="Upload Photo"></p-avatar>
                        </label>
                        <input id="imageInput" type='file' accept="image/*" (change)="onSelectImage($event)"> comment end
                        <image-cropper
                          [imageChangedEvent]="imageChangedEvent"
                          [maintainAspectRatio]="true"
                          [aspectRatio]="4/3"
                          format="png"
                          (imageCropped)="imageCropped($event)"
                          (imageLoaded)="imageLoaded($event)"
                          (cropperReady)="cropperReady()"
                          (loadImageFailed)="loadImageFailed()"
                          [style.display]="showCropper ? null : 'none'"
                          output="base64"
                        ></image-cropper>
                        <button *ngIf="!imageChangedEvent" pButton class="p-button-sm" (click)="showUploadPopup()" label="Upload Profile Photo"></button>    
                        <input *ngIf="!imageChangedEvent" class="file-input" pInputText id="imageInput" accept="image/*" type="file" (change)="fileChangeEvent($event)" hidden>
                        <div *ngIf="imageChangedEvent" class="flex flex-row justify-content-center">
                            <button pButton class="p-button-sm p-button-danger mr-2" (click)="cancelProfilePhoto()" label="Cancel"></button>
                            <button pButton class="p-button-sm" (click)="saveProfilePhoto()" label="Save"></button>    
                        </div>
                    </div>
                    <div class="col-3 my-2">
                        <span class="p-float-label">
                            <input id="email" pInputText formControlName="email" type="email" class="w-full " required="true">
                            <label htmlFor="email" class="_required">Email Id</label>
                        </span>
                        <small class="error" *ngIf="userform.get('email')?.hasError('email')">Enter valid email id.</small>
                    </div>
                    <div class="col-3 my-2">
                        <span class="p-float-label">
                            <input id="phonenumber" pInputText formControlName="phoneNumber" type="text" class="w-full " pattern="^[^0-9]*(?:(\d)[^0-9]*){10}$">
                            <label htmlFor="phonenumber">Phone Number</label>
                        </span>
                        <small class="error" *ngIf="userform.get('phoneNumber')?.hasError('pattern')">Please eneter 10 digits.</small>
                    </div>
                    <div class="col-offset-6 my-3"></div>

                    <div class="col-3 my-3">
                        <span class="p-float-label">
                            <p-dropdown id="role" formControlName="role" optionLabel="label" required="true" [autoDisplayFirst]="false"
                                optionValue="value" [options]="Roles" class="" styleClass="w-full p-inputtext-sm">
                            </p-dropdown>
                            <label htmlFor="role" class="_required">Role</label>
                        </span>
                    </div>
                    <div *ngIf="loginType === 'ads'" class="col-3 my-3">
                        <span class="p-float-label">
                            <input id="adsusername" pInputText formControlName="adsusername" type="text" class="w-full " required="true">
                            <label htmlFor="adsusername" class="_required">Active Directory User</label>
                        </span>
                    </div>
                    <div *ngIf="loginType === 'ads'" class="col-offset-6 my-3"></div> comment
                    <div *ngIf="loginType === 'ads'" class="col-6 my-3">
                      <span class="p-float-label">
                          <p-multiSelect id="teams" [options]="filteredTeams" formControlName="team" optionLabel="teamId" display="chip" styleClass="w-full"></p-multiSelect>
                          <label htmlFor="teams">Teams</label>
                      </span>
                    </div>
                   
                    <div class="col-3 my-3">    
                        <span class="p-input-icon-right p-float-label w-full">
                          <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                          <input id="password" pInputText formControlName="password" class="w-full" [type]="showpassword ? 'text' : 'password'" required="true">
                          <label htmlFor="password" class="_required">Password</label>
                        </span>
                        <small class="error" *ngIf="userform.get('password')?.hasError('whitespace')">Whitespaces are not allowed.</small>
                        <small class="error" *ngIf="userform.get('password')?.hasError('minlength')">Minimum 3 letters are needed to set password.</small>
                    </div>

                    <div *ngIf="loginType != 'ads'" class="col-6 my-3">   
                      <span class="p-float-label">
                          <p-multiSelect id="teams" [options]="filteredTeams" formControlName="team" optionLabel="teamId" display="chip" styleClass="w-full"></p-multiSelect>
                          <label htmlFor="teams">Teams</label>
                      </span>
                    </div>

                    <div class="col-3 my-3">    
                        <div class="w-full toggle-button">
                            <div *ngIf="loginType !== 'ads'" class="my-auto">
                                <label>Auto Generate</label>
                                <p-inputSwitch formControlName="letUserChoosePassword" (onChange)="addremovevalidators()" class="ml-2"></p-inputSwitch>
                            </div>
                            <div class="my-auto">
                                <label>Send A/c Info</label>
                                <p-inputSwitch formControlName="sendPasswordInEmail" class="ml-2"></p-inputSwitch>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="loginType != 'ads'" class="col-offset-3 my-3"></div> -->

                    <div class="col-12 text-right mt-3">
                        <button class="p-button-sm p-button-danger mr-2" style="outline: none !important;" pButton type="button" label="Cancel" (click)="resetUserform()"></button>
                        <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="createUser($event)" [disabled]="!userform.valid"></button>
                    </div>
                    
                </div>
            </form>
        </ng-template>
        </p-accordionTab>
    </p-accordion>

    <!-- Users Table -->
    <p-table #dt 
      [value]="tableData" dataKey="id" 
      (onRowCollapse)="onUserRowCollapse()" 
      (onRowExpand)="onRowSelect($event)" 
      rowExpandMode="single"
      sortMode="single"
      selectionMode="multiple" 
      (selectionChange)="onSelectionChange($event)" 
      [customSort]="true"
      [lazy]="true" 
      responsiveLayout="scroll" 
      [scrollable]="true" 
      styleClass="p-datatable-sm p-datatable-gridlines"
      [tableStyle]="{ 'min-width': '50rem' }"
      scrollHeight="calc(100vh - 20vh)">

      <ng-template pTemplate="caption">
        <div class="flex flex-row align-items-center justify-content-between">
          <div>
                <span class="p-input-icon-left">
                    <i class="pi pi-search"></i>
                    <input style="width: 21rem;" class="p-inputtext-sm" pInputText type="text" [(ngModel)]="searchquery"
                      (keyup)="onSearch()" placeholder="Search..." />
                </span>
                <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                    (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                </button>
          </div>
          <div *ngIf="isMultiUserSelected">
              <button pButton (click)="assignTeamPanel.toggle($event);filterOnlyTeams()" icon="pi pi-qrcode" pTooltip="Assign" tooltipPosition="top" class="p-button-sm mr-2">
              </button>
              <button pButton (click)="showDisableUserDialog($event)" icon="pi pi-trash" pTooltip="Delete" tooltipPosition="top" class="p-button-sm p-button-danger">
              </button>
          </div>
        </div>
      </ng-template>

<!-- Header -->
      <ng-template pTemplate="header" let-columns>
          <tr>
            <th style="width: 3rem" class="text-center">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
            <th style="width: 3rem" class="text-center"></th>
            <th style="min-width: 10rem">
              <div class="flex flex-row align-items-center gap-2">
                <span>First Name</span>
                <i *ngIf="sortField !== 'firstName'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('firstName', 'asc')"></i>
                <i *ngIf="sortField === 'firstName' && sortOrder === 'asc'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('firstName', 'desc')" style="color: var(--primary-color);"></i>
                <i *ngIf="sortField === 'firstName' && sortOrder === 'desc'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('firstName', 'asc')" style="color: var(--primary-color);"></i>
              </div>
            </th>
            <th style="min-width: 10rem">
              <div class="flex flex-row align-items-center gap-2">
                <span>Last Name</span>
                <i *ngIf="sortField !== 'lastName'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('lastName', 'asc')"></i>
                <i *ngIf="sortField === 'lastName' && sortOrder === 'asc'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('lastName', 'desc')" style="color: var(--primary-color);"></i>
                <i *ngIf="sortField === 'lastName' && sortOrder === 'desc'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('lastName', 'asc')" style="color: var(--primary-color);"></i>
              </div>
            </th>
            <th>
              <div class="flex flex-row align-items-center gap-2">
                <span>Role</span>
                <i *ngIf="sortField !== 'role'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('role', 'asc')"></i>
                <i *ngIf="sortField === 'role' && sortOrder === 'asc'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('role', 'desc')" style="color: var(--primary-color);"></i>
                <i *ngIf="sortField === 'role' && sortOrder === 'desc'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('role', 'asc')" style="color: var(--primary-color);"></i>
              </div>
            </th>
            <th style="min-width: 10rem">
              <div class="flex flex-row align-items-center gap-2">
                <span>Email</span>
                <i *ngIf="sortField !== 'email'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('email', 'asc')"></i>
                <i *ngIf="sortField === 'email' && sortOrder === 'asc'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('email', 'desc')" style="color: var(--primary-color);"></i>
                <i *ngIf="sortField === 'email' && sortOrder === 'desc'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('email', 'asc')" style="color: var(--primary-color);"></i>
              </div>
            </th>
            <th style="min-width: 10rem">
              <div class="flex flex-row align-items-center gap-2">
                <span>Last Seen</span>
                <i *ngIf="sortField !== 'lastAccess'" class="pi pi-sort-alt ml-auto cursor-pointer" (click)="sorting('lastAccess', 'asc')"></i>
                <i *ngIf="sortField === 'lastAccess' && sortOrder === 'asc'" class="pi pi-sort-amount-up ml-auto cursor-pointer" (click)="sorting('lastAccess', 'desc')" style="color: var(--primary-color);"></i>
                <i *ngIf="sortField === 'lastAccess' && sortOrder === 'desc'" class="pi pi-sort-amount-down ml-auto cursor-pointer" (click)="sorting('lastAccess', 'asc')" style="color: var(--primary-color);"></i>
              </div>
            </th>
            <th style="min-width: 14rem">Teams</th>
            <!-- <th style="min-width: 14rem" class="text-center">Actions</th> -->
          </tr>
      </ng-template>

<!-- Row data -->
      <ng-template pTemplate="body" let-user let-columns="columns" let-expanded="expanded">
        <tr (click)="dt.toggleRow(user, $event);assignImageURL(user.avatar)" class="cursor-pointer">
          <td class="text-center" id="{{'rowExpansion'+ user.id}}">
            <p-tableCheckbox [value]="user" (click)="$event.stopPropagation()"></p-tableCheckbox>
          </td>
          <td class="text-center" id="rowExpansion">
            <p-avatar *ngIf="!user.avatar" [label]="convertNameToAvatar(user.firstName)" [style]="{ 'background-color': 'var(--primary-color)', color: '#ffffff' }" class="avatar-bg" shape="circle"></p-avatar>
            <p-avatar *ngIf="user.avatar" [image]="user.avatar" shape="circle"></p-avatar>
            <!-- <button type="button" id="rowExpansion" pButton pRipple [pRowToggler]="user" pTooltip="Edit" class="p-button-text p-button-sm p-button-rounded p-button-plain" [icon]="expanded ? 'pi pi-times' : 'pi pi-pencil'"></button> -->
          </td>
          <td>
            {{user.firstName}}
          </td>
  
          <td>
            {{user.lastName}}
          </td>
  
          <td>
            {{user.role}}
          </td>
  
          <td>
            {{user.email}}
          </td>
  
          <td>
            {{user.lastAccess}}
          </td>
  
          <td>
            <span *ngFor="let team of user.teams">
              <a id='teamOverlayClose' (click)="showUpdateTeam(team, updateTeamPanel, $event)">{{team.teamId}}</a>,
            </span>
          </td>
        </tr>
      </ng-template>

<!-- User form -->
      <ng-template pTemplate="rowexpansion" let-user>
        <tr>
          <td colspan="12">
            <div class="card usr">
              <form [formGroup]="userform" novalidate>
                <div class="formgrid grid pt-2">

                    <div class="col-3">
                      <div class="mt-4">
                        <span class="p-float-label">
                          <input id="firstname" pInputText formControlName="firstName" class="w-full p-inputtext-sm" [autofocus]="true" pAutoFocus required="true">
                          <label htmlFor="firstname" class="_required">First Name</label>
                        </span>
                        <span *ngIf="userform.get('firstName')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                      </div>
                      <div class="mt-4">
                        <span class="p-float-label">
                          <input id="email" pInputText formControlName="email" type="email" class="w-full " required="true">
                          <label htmlFor="email" class="_required">Email Id</label>
                        </span>
                        <span *ngIf="userform.get('email')?.hasError('email')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Enter valid email id.</small></span>
                      </div>
                      <div class="mt-4">
                        <span class="p-float-label">
                          <p-dropdown id="role" formControlName="role" optionLabel="label" required="true" [autoDisplayFirst]="false"
                              optionValue="value" [options]="Roles" class="" styleClass="w-full p-inputtext-sm">
                          </p-dropdown>
                          <label htmlFor="role" class="_required">Role</label>
                        </span>
                      </div>
                      <div class="mt-4">
                        <div class="w-full toggle-button">
                          <div *ngIf="loginType !== 'ads'" class="my-auto">
                              <label>Auto Generate</label>
                              <p-inputSwitch formControlName="letUserChoosePassword" (onChange)="addremovevalidators()" class="ml-2"></p-inputSwitch>
                          </div>
                          <div class="my-auto">
                              <label>Send A/c Info</label>
                              <p-inputSwitch formControlName="sendPasswordInEmail" class="ml-2"></p-inputSwitch>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-3">
                      <div class="mt-4">
                        <span class="p-float-label">
                          <input id="lastname" pInputText formControlName="lastName" class="w-full " required="true">
                          <label htmlFor="lastname" class="_required">Last Name</label>
                        </span>
                        <span *ngIf="userform.get('lastName')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                      </div>
                      <div class="mt-4">
                        <span class="p-float-label">
                          <input id="phonenumber" pInputText formControlName="phoneNumber" type="text" class="w-full " pattern="^[^0-9]*(?:(\d)[^0-9]*){10}$">
                          <label htmlFor="phonenumber">Phone Number</label>
                        </span>
                        <span *ngIf="userform.get('phoneNumber')?.hasError('pattern')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Please eneter 10 digits.</small></span>
                      </div>
                      <div class="mt-4">   <!-- Password -->
                        <div *ngIf="!selectedUser?.password" >
                            <span class="p-input-icon-right p-float-label w-full">
                                <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                                <input id="password" pInputText formControlName="password" class="w-full" [type]="showpassword ? 'text' : 'password'" required="true">
                                <label htmlFor="password" class="_required">Password</label>
                            </span>
                        </div>
                        <div *ngIf="selectedUser?.password">
                            <span class="p-float-label">
                                <input id="password" pInputText formControlName="password" class="w-full" type="password" required="true">
                                <label htmlFor="password" class="_required">Password</label>
                            </span>
                        </div>
                        <span *ngIf="userform.get('password')?.hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                        <span *ngIf="userform.get('password')?.hasError('minlength')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Minimum 3 letters are needed to set password.</small></span>
                      </div>
                      <div *ngIf="loginType === 'ads'" class="my-6">    <!-- ADS -->
                        <span class="p-float-label">
                            <input id="adsusername" pInputText formControlName="adsusername" type="text" class="w-full " required="true">
                            <label htmlFor="adsusername" class="_required">Active Directory User</label>
                          </span>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="flex flex-column justify-content-center align-items-center ">
                        <image-cropper #cropper class="max-h-15rem max-w-15rem"
                            [imageChangedEvent]="imageChangedEvent"
                            format="png"
                            (imageCropped)="imageCropped($event)"
                            (imageLoaded)="imageLoaded($event)"
                            (cropperReady)="cropperReady()"
                            (loadImageFailed)="loadImageFailed()"
                            [style.display]="showCropper ? null : 'none'"
                            output="base64"
                            [settings]="cropperSettings"
                          ></image-cropper>
                          <button *ngIf="!imageChangedEvent" pButton class="p-button-sm" (click)="showUploadPopup()" label="Upload Profile Photo"></button>    
                          <input *ngIf="!imageChangedEvent" class="file-input" pInputText id="imageInput" accept="image/*" type="file" (change)="fileChangeEvent($event)" hidden>
                          <div *ngIf="imageChangedEvent" class="flex flex-row justify-content-center">
                              <button pButton class="p-button-sm p-button-danger" (click)="cancelProfilePhoto()" label="Remove"></button>
                              <!-- <button pButton class="p-button-sm" (click)="saveProfilePhoto()" label="Save"></button>     -->
                          </div>
                      </div>
                      <div class="mt-4">
                        <span class="p-float-label">
                          <p-multiSelect id="teams" [options]="filteredTeams" formControlName="team" optionLabel="teamId" display="chip" styleClass="w-full"></p-multiSelect>
                          <label htmlFor="teams">Teams</label>
                        </span>
                      </div>
                    </div>

                    <div class="col-12 text-right mt-3">   <!-- Save cancel buttons -->
                        <button class="p-button-sm p-button-danger mr-2" style="outline: none !important;" pButton type="button" label="Cancel" (click)="resetUserform(user.id)"></button>
                        <button class="p-button-sm" pButton type="button" label="Update" (click)="updateUser();$event.stopPropagation()" [disabled]="!userform.valid"></button>
                    </div>
                    
                </div>
              </form>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="footergrouped">
        <tr>
            <td colspan="8">
              <p-paginator [rows]="pageSize" [totalRecords]="usersDataLength" [showJumpToPageDropdown]="false" [dropdownAppendTo]="paginator"
                (onPageChange)="tableFilter(sortTable,$event,searchquery)" [showPageLinks]="true" [rowsPerPageOptions]="[10,25,50]">
              </p-paginator>
            </td>
        </tr>
       
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-center" style="font-weight: bold;" colspan="8">No users found.</td>
        </tr>
      </ng-template>
    </p-table>

</div>

<!-- Import Users -->
<p-overlayPanel #importUser>
  <ng-template pTemplate>
    <div class="file-drop-area">
      <button class="p-button-sm" pButton type="button" icon="pi pi-plus" label="Choose File"></button>
      <span class="text-overflow-ellipsis white-space-nowrap overflow-hidden inline-block font-medium pl-2 pb-1" style="width:190px;color: var(--primary-color);">{{file ? file.name : 'or drag and drop here (.csv)' }} </span>
      <input class="file-input" pInputText #fileInput accept=".csv" 
              type="file" (change)="processFile(fileInput, importUser)">
    </div>
    <div class="download-sample-file mt-2" (click)="downloadSampleFile()">
      <span class="font-medium">Download Template File</span>
      <i class="pi pi-download"></i>
      <!-- <button class="p-button-sm" pButton type="button" icon="pi pi-download"></button> -->
    </div>
  </ng-template>
</p-overlayPanel>


<!-- Disable User -->
<p-confirmPopup key="disableUserPanel"></p-confirmPopup>

<!-- Create team -->
    <p-overlayPanel #teamPanel [style]="{'width': '600px'}" appendTo="body">
      <form [formGroup]="createTeamForm" novalidate>
        <div class="grid">
          <div class="col-12">
            <div class="w-full my-4">
              <span class="p-float-label">
                <input id="teamid" pInputText formControlName="teamId" [autofocus]="true" pAutoFocus class="w-full p-inputtext-sm" required="true">
                <label htmlFor="teamid" class="_required">Team Name</label>
              </span>
            </div>
            <div class="w-full mb-4">
              <span class="p-float-label">
                <!-- <p-dropdown id="Manager" formControlName="teamManager" optionLabel="email" required="true" [autoDisplayFirst]="false"
                  optionValue="userId" [options]="Managers" class="p-inputtext-sm" styleClass="w-full">
                </p-dropdown> -->
                <p-dropdown id="Manager" [options]="Managers" formControlName="teamManager" optionLabel="email" [autoDisplayFirst]="false"
                  optionValue="userId" styleClass="w-full p-inputtext-sm" required="true" appendTo="body">
                    <ng-template let-manager pTemplate="selectedItem">
                      <span>{{ manager.firstName + " " + manager.lastName }}</span><br>
                      <span>({{ manager.email }})</span>
                    </ng-template>
                    <ng-template let-manager pTemplate="item">
                      <span>{{ manager.firstName + " " + manager.lastName }}</span><br>
                      <span>({{ manager.email }})</span>
                    </ng-template>
                </p-dropdown>
                <label htmlFor="Manager" class="_required">Team Manager</label>
              </span>
            </div>
            <div class="w-full">
              <span class="p-float-label">
                <textarea id="description" pInputTextarea formControlName="teamDescription" rows="2" cols="30" class="w-full p-inputtext-sm" required="true"></textarea>
                <label htmlFor="description" class="_required">Description</label>
              </span>
            </div>
            <div class="col-12 text-right px-0 pb-0">
              <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetTeams(teamPanel)"></button>
              <button class="p-button-sm" pButton type="button" label="Create" (click)="createTeams(teamPanel)" [disabled]="!createTeamForm.valid"></button>
            </div>
          </div>
        </div>
      </form>
    </p-overlayPanel>

<!-- Upadte/Delete teams -->
    <p-overlayPanel #updateTeamPanel appendTo="body">
      <form [formGroup]="createTeamForm" novalidate>
        <div class="grid">
          <div class="col-12">
            <div class="w-full my-4">
              <span class="p-float-label">
                <input id="teamid" pInputText formControlName="teamId" class="w-full p-inputtext-sm" required="true" readonly>
                <label htmlFor="teamid" class="_required">Team Name</label>
              </span>
            </div>
            <div class="w-full mb-4">
              <span class="p-float-label">
                <p-dropdown id="Manager" [options]="Managers" formControlName="teamManager" optionLabel="email" [autoDisplayFirst]="false"
                  optionValue="userId" class="p-inputtext-sm" styleClass="w-full" required="true">
                    <ng-template let-manager pTemplate="selectedItem">
                      <span>{{ manager.firstName + " " + manager.lastName }}</span><br>
                      <span>({{ manager.email }})</span>
                    </ng-template>
                    <ng-template let-manager pTemplate="item">
                      <span>{{ manager.firstName + " " + manager.lastName }}</span><br>
                      <span>({{ manager.email }})</span>
                    </ng-template>
                </p-dropdown>
                <label htmlFor="Manager" class="_required">Team Manager</label>
              </span>
            </div>
            <div class="w-full">
              <span class="p-float-label">
                <textarea id="description1" pInputTextarea formControlName="teamDescription" rows="2" cols="30" class="w-full p-inputtext-sm"></textarea>
                <label htmlFor="description1">Description</label>
              </span>
            </div>
            <div class="col-12 text-right px-0 ">
              <button class="p-button-sm p-button-danger mr-3" pButton type="button" label="Delete" (click)="deteteTeam($event, updateTeamPanel)"></button>
              <button class="p-button-sm" pButton type="button" label="Update" (click)="updateTeamDetails(updateTeamPanel)" [disabled]="!createTeamForm.valid"></button>
            </div>
          </div>
        </div>
      </form>
    </p-overlayPanel>

<!-- Assign Teams -->
    <p-overlayPanel #assignTeamPanel [style]="{'max-width': '300px'}">
      <form [formGroup]="assignTeamForm" novalidate >
        <div class="grid usr">
          <div class="col-12 my-3">
            <span class="p-float-label w-full">
              <p-multiSelect [style]="{'min-width': '200px'}" id="teams" [options]="filteredTeams" formControlName="team" optionLabel="teamId" display="chip"></p-multiSelect>
              <label htmlFor="teams">Teams</label>
            </span>
            <div class="col-12 text-right pr-0">
              <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetAssignTeam(assignTeamPanel)"></button>
              <button class="p-button-sm" pButton type="button" label="Assign" (click)="assignTeams(assignTeamPanel)" [disabled]="!assignTeamForm.valid"></button>
            </div>
          </div>
        </div>
      </form>
    </p-overlayPanel>