import { Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MenuItem, MessageService, SortEvent } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { UsersTeamsService } from 'src/app/services/users-teams.service';
import { whiteSpaceValidator } from 'src/app/shared/directives/white-space-validator.directive';
import * as moment from 'moment';
import { Table } from 'primeng/table';
import { CropperSettings, ImageCroppedEvent, ImageCropperComponent, LoadedImage, base64ToFile } from 'ngx-image-cropper';
import { Subject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
  
  assignteams = false;
  searchquery: string = '';
  teamid: string;
  teamname: string;
  errmsg: string;
  finalSearch: any;
  displayedColumns = [
    'select',
    'firstname',
    'lastname',
    'email',
    'role',
    'lastAccess',
    'teams',
  ];
  activeState: boolean = false
  public edit: boolean = false
  clickedUserData: any = []
  selectedProducts4: any[]
  profileDiv: boolean = true
  Roles: any[] = []
  first = 0;
  rows = 10;
  public currentuser: string;
  breadcrumbItems : MenuItem[];
  tableData: any[];
  userDetails: any;
  selectedUser: any;
  multiUsers: any[];
  userform: FormGroup;
  teams: string[] = [];
  filteredTeams = [];
  showPassword: boolean = false;
  loginType: string;
  url: any;
  csvContent: string;
  public file: File;
  sortTable: any;
  pageIndex: number = 0;
  pageSize: number = 10;
  newUserEmail: string = '';
  tablePagination: any;
  createTeamForm: FormGroup;
  Managers: any;
  isCreateUser: boolean = false;
  isMultiUserSelected: boolean = false;
  teamDetails: any;
  assignTeamForm: FormGroup;
  removeteams: string[] = [];
  isRowExpand: boolean = false;
  usersDataLength: number = 0;
  sortField: string;
  sortOrder: string;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper: boolean = false;
  cropperSettings: CropperSettings;
  menuType: string;
  searchSubject: Subject<string> = new Subject();

  constructor(
    private route: ActivatedRoute,
    private usersService: UsersTeamsService,
    private fb: FormBuilder,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    public layoutService: LayoutService
  ) {
    this.cropperSettings = new CropperSettings();
    // this.cropperSettings.width = 100;
    // this.cropperSettings.height = 100;
    // this.cropperSettings.croppedWidth = 100;
    // this.cropperSettings.croppedHeight = 100;
    // this.cropperSettings.canvasWidth = 400;
    // this.cropperSettings.canvasHeight = 300;
  }

  ngOnInit() {
    this.menuType = localStorage.getItem('menuType');
    this.loginType = localStorage.getItem('login_type');
    this.teamid = this.route.parent.snapshot.paramMap.get('id');
    // this.teamname = this.route.snapshot.paramMap.get('teamname');
    // this.currentuser = localStorage.getItem('email');
    this.userform = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      letUserChoosePassword: [{value: false, disabled: this.loginType === 'ads'}, Validators.required],
      sendPasswordInEmail: [{value: true, disabled: true}, Validators.required],
      password: [{value: '', disabled: false}],
      email: ['', [Validators.required, Validators.email]],
      role: ['', Validators.required],
      team: [''],
      adsusername: [{value: '', disabled: this.loginType != 'ads'}, [Validators.required, whiteSpaceValidator()]],
      phoneNumber:['', [Validators.maxLength(10),Validators.minLength(10)]],
      avatar: ['']
    });
    // if(this.loginType === 'ads'){
    //   this.userform.addControl('adsusername', new FormControl(this.selectedUser?.userId, [Validators.required, whiteSpaceValidator()]));
    //   if(this.selectedUser?.userId) {
    //     this.userform.get('adsusername').disable();
    //   }
    // }
    this.Roles = [
      { label: 'Admin', value: 'Admin' },
      { label: 'Developer', value: 'Developer' },
      { label: 'Manager', value: 'Manager' },
      { label: 'User', value: 'User' },
    ]
    this.breadcrumbItems = [
      {label:'Home', routerLink: '/home'},
      {label:'Users'}
    ];
    this.tableFilter(null,null,null);  // Innitial load
    // this.addremovevalidators();
    this.createTeamForm = this.fb.group({
      teamId: ['', Validators.required],
      teamManager: ['', Validators.required],
      teamDescription: ['', Validators.required]
    });
    this.assignTeamForm = this.fb.group({
      team: ['']
    });
    this.getAllManagers();
    this.getTeams();
    this.searchSubject.pipe( // only for search bar
      debounceTime(500) 
    ).subscribe(searchText => {
      const page = {
        first: 0,
        page: 0,
        rows: this.tablePagination && typeof this.tablePagination.rows === 'number' ? this.tablePagination?.rows : 10
      }
      this.tableFilter(null,page,this.searchquery)
    });
  }

  onRowSelect(e: any) {
    var teamsArr = [];
    this.isCreateUser = false;
    this.isRowExpand = true;
    this.teams = [];
    this.selectedUser = e.data;
    if (this.selectedUser?.teams) {
      this.selectedUser.teams.forEach(teamnameval => {
        teamsArr.push({'teamName': teamnameval.teamId, 'teamId': teamnameval.teamId});
      });
    } else {
      this.selectedUser.teams = [];
    }
    
    this.userform = this.fb.group({
      firstName: [this.selectedUser?.firstName, [Validators.required]],
      lastName: [this.selectedUser?.lastName, [Validators.required]],
      letUserChoosePassword: [{value: false, disabled: this.loginType === 'ads'}, Validators.required],
      sendPasswordInEmail: [{value: true, disabled: true}, Validators.required],
      password: [{value: this.selectedUser?.password, disabled: this.letUserChoosePassword}],
      email: [this.selectedUser?.email, [Validators.required, Validators.email]],
      role: [this.selectedUser?.role, Validators.required],
      team: [teamsArr],
      adsusername: [{value: this.selectedUser?.userId, disabled: this.loginType != 'ads'}, [Validators.required]],
      phoneNumber:[this.selectedUser?.phoneNumber]
    });
    if (this.selectedUser?.teams && this.selectedUser?.teams.length > 0) {
      this.selectedUser.teams.forEach(team => {
        this.teams.push(team.teamId);
      });
    }
  }

  onUserRowCollapse() {
    this.isRowExpand = false;
  }

  onSelectionChange(e: any) {
    this.multiUsers = e;
    if (this.multiUsers.length > 0) {
      this.isMultiUserSelected = true;
    } else this.isMultiUserSelected = false;
  }

  customSort(event: SortEvent) {
    this.tableFilter(event,this.tablePagination,this.searchquery);
  }

  tableFilter(sort: any, page: any, searchquery: string) {
    if(this.isRowExpand) {
      document.getElementById('rowExpansion'+this.selectedUser.id).click();
    }
    this.searchquery = searchquery ? searchquery : null;
    this.sortTable = sort;
    this.sortField = this.sortTable?.field;
    this.sortOrder = this.sortTable?.order;
    this.tablePagination = page;
    this.pageIndex = this.tablePagination ? this.tablePagination.page * this.tablePagination.rows : 0;
    this.pageSize = this.tablePagination ? this.tablePagination.rows : 10;
    this.newUserEmail = '';

    this.usersService
      .getusers(this.searchquery, 
        this.sortOrder,
        this.pageIndex,
        this.pageSize,
        [], 
        this.teamid,
        false, 
        this.newUserEmail, 
        this.sortField).subscribe((response: any) => {
          if (response.status.toLowerCase() === 'success') {
            const userdataobj = response;
            this.tableData = userdataobj.formUsers;
            this.usersDataLength = userdataobj.totalUsers;
            userdataobj.formUsers.forEach(userdataarr => {
              // Get the relative time from UMP timestamp
              const timepassed = moment(userdataarr.lastAccess).from(moment());
              userdataarr['lastAccess'] = timepassed;
            });
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
      });
  }

  clearAllFilter(table: Table) {
    table.clear();
    const page = {
      first: 0,
      page: 0,
      rows: 10
    }
    this.tableFilter(null, page, null);
  }

  getTeams() {
    this.usersService.getteams('', '', 'asc', 0, 50, '', null)
      .subscribe((teamdata: any) => {
        const response = teamdata;
        this.filteredTeams = [];
        if (response.status.toLowerCase() === 'success') {
          if (response.teams.length) {
            response.teams.forEach(teamnameval => {
              this.filteredTeams.push({'teamName': teamnameval.name, 'teamId': teamnameval.name});
            });
          } else {
            this.filteredTeams = [];
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  searchTeams(event: any) {
    // this.errorMessage = [];
    // // console.log('selected team', this.selectedTeam)
    // if (this.userform)
    // this.teamsservice.getteams(event.query, this.teams, 'asc', 0, 5, '', null)
    //   .subscribe((teamdata: any) => {
    //     const response = JSON.parse(teamdata);
    //     this.filteredTeams = [];
    //     if (response.status === 'Success') {
    //       if (response.teams.length) {
    //         response.teams.forEach(teamnameval => {
    //           this.filteredTeams.push({'teamName': teamnameval.name, 'teamId': teamnameval.name});
    //         });
    //       } else {
    //         this.filteredTeams = [];
    //       }
    //     } else {
    //       this.errmsg = response.error;
    //       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
    //       // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
    //     }
    //   });
  }

  // showError(str: string) {
  //   this.errorMessage = [];
  //   if (str === 'email') {
  //     if (this.userform.controls['email'].hasError('email')) {
  //       this.errorMessage = [{severity:'error', summary:'Error', detail: 'Enter valid email id.'}];
  //     }
  //   } else if (str === 'firstName' || str === 'lastName') {
  //     if (this.userform.controls['firstName'].hasError('whitespace') || this.userform.controls['lastName'].hasError('whitespace')) {
  //       this.errorMessage = [{severity:'error', summary:'Error', detail: 'Whitespaces are not allowed'}];
  //     }
  //   } else if (str === 'password') {
  //     if (this.letUserChoosePassword) {
  //       if (this.userform.controls['password'].hasError('whitespace')) {
  //         this.errorMessage = [{severity:'error', summary:'Error', detail: 'Whitespaces are not allowed'}];
  //       } else if (this.userform.controls['password'].hasError('minlength')) {
  //         this.errorMessage = [{severity:'error', summary:'Error', detail: 'Minimum 3 letters are needed to set password'}];
  //       }
  //     }
  //   } else if (str === 'ads') {
  //     if (this.loginType === 'ads') {
  //       if (this.userform.controls['firstName'].hasError('whitespace') || this.userform.controls['lastName'].hasError('whitespace')) {
  //         this.errorMessage = [{severity:'error', summary:'Error', detail: 'Whitespaces are not allowed'}];
  //       }
  //     }
  //   }
  // }

  get team() {
    return this.userform.get('team');
  }
  get letUserChoosePassword() {
    return this.userform.get('letUserChoosePassword').value;
  }

  addremovevalidators() {
    if (!this.letUserChoosePassword) {
      this.userform.get('password').setValidators(null);
      this.userform.get('password').updateValueAndValidity();
      this.userform.get('password').enable();
      this.userform.get('sendPasswordInEmail').disable(); 
    } else {
      this.userform.get('password').setValidators([whiteSpaceValidator(), Validators.minLength(3),Validators.required]);
      this.userform.get('password').patchValue(null);
      this.userform.get('password').updateValueAndValidity();
      this.userform.get('password').disable();
      this.userform.get('sendPasswordInEmail').enable(); 
    }
  }

  showCeateUserAccordian() {
    if (this.selectedUser && this.isRowExpand) {
      this.resetUserform(this.selectedUser.id);
    }
    this.cancelProfilePhoto();
    this.isCreateUser = true;
    this.teams = [];
    this.selectedUser = {
      email: '',
      firstName: '',
      lastName: '',
      lastAccess: '',
      password: '',
      teams: [],
      role: '',
      phoneNumber: '',
      userId: '',
      id: '',
      avatar: ''
    }
    this.userform.reset();
    this.userform = this.fb.group({   // because of reset form
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      letUserChoosePassword: [{value: false, disabled: this.loginType === 'ads'}, Validators.required],
      sendPasswordInEmail: [{value: true, disabled: true}, Validators.required],
      password: [{value: '', disabled: false}, Validators.required],
      email: ['', [Validators.required, Validators.email]],
      role: ['', Validators.required],
      team: [''],
      adsusername: [{value: '', disabled: this.loginType != 'ads'}, [Validators.required]],
      phoneNumber:['', [Validators.maxLength(10),Validators.minLength(10)]],
      avatar: ['']
    });
  }

  createUser(event: any) {
    var sendTeamName = [];
    if (this.loginType === 'saml') {
      this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'You are in SAML login, do you want to proceed?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          if (this.userform?.value?.team?.length) {
            this.userform.value.team.forEach(teamnameval => {
              sendTeamName.push(teamnameval.teamId);
            });
            this.team.setValue(null);
          }
          this.userform.value['avatar'] = this.croppedImage ? this.croppedImage : '';
          this.usersService.createusers(this.userform.value, sendTeamName)
          .subscribe(response => {
              if (response.status.toLowerCase() === 'success') {
                this.messageService.add({ severity: 'success', summary: 'Success', detail: "User created successfully!" });
                this.tableFilter(null, null, null);
                this.isCreateUser = false;
                this.resetUserform(this.selectedUser.id);
                this.layoutService.getProfileSetting();
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
          });
        },
        reject: () => {}
      });
    } else {
      if (this.userform?.value?.team?.length) {
        this.userform.value.team.forEach(teamnameval => {
          sendTeamName.push(teamnameval.teamId);
        });
        this.team.setValue(null);
      }
      this.userform.value['avatar'] = this.croppedImage ? this.croppedImage : '';
      this.usersService.createusers(this.userform.value, sendTeamName)
      .subscribe(response => {
          if (response.status.toLowerCase() === 'success') {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "User created successfully!" });
            this.tableFilter(null, null, response.email);
            this.isCreateUser = false;
            this.resetUserform(this.selectedUser.id);
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          }
      });
    }
  }
  
  resetUserform(id: any) {
    if(this.isRowExpand && this.selectedUser.id) {
      document.getElementById('rowExpansion'+id).click();
    }
    this.userform.reset();
    this.isCreateUser = false;
    this.croppedImage = null;
  }

  // goToUserPage(searchquery: string) {
  //   this.dataSource = new UsersDataSource(this.userservice);
  //   this.dataSource.loaduserdata(
  //     searchquery,
  //     this.sortTable?.order === -1 ? 'desc' : 'asc',
  //     this.pageIndex,
  //     this.pageSize,
  //     this.teamid,
  //     this.newUserEmail,
  //     this.sortTable?.field
  //   );
  //   setTimeout(() => {
  //     this.tableData = this.dataSource.returnData();
  //   }, 400);
  // }
  
  updateUser() {
    var sendTeamName = [];
    if (this.userform.value) {
      this.userform.value.team.forEach(teamnameval => {
        sendTeamName.push(teamnameval.teamId);
      });
      this.team.setValue(null);
    }
    this.userform.value['avatar'] = this.croppedImage ? this.croppedImage : '';
    this.usersService.updateusers(this.userform.value, sendTeamName, this.selectedUser.userId)
      .subscribe(updateUserAPIres => {
        const response = updateUserAPIres;
        if (response.error === '') {
          if (response.status.toLowerCase() === 'success') {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "User updated successfully!" });
            // this.errorMessage = [{severity:'success', summary:'Success', detail: "User updated successfully."}];
            this.tableFilter(this.sortTable,this.tablePagination,this.searchquery);
            document.getElementById('rowExpansion').click();
            this.resetUserform(this.selectedUser.id);
            this.layoutService.getProfileSetting();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
    });
  }

  
  onSelectImage(event) {
    if (event.target.files && event.target.files[0]) {
      var reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (event) => { 
        this.url = event.target.result;
      }
    } else {
      this.url = null;
    }
  }

  cacelUserProfile() {
    this.url = null;
  }

  showImportUser(element: any,event: any) {
    element.show(event);
    this.file = null;
  }

  processFile(fileInput: any, element: any) {
    const files = fileInput.files;
    if (files && files.length) {
      const fileToRead = files[0];
      const fileName = fileToRead.name.toLowerCase();
      const fileType = fileToRead.type;
      if (
        fileName.endsWith('.csv') ||
        fileType === 'text/csv' ||
        fileType === 'application/vnd.ms-excel'
      ) {
        this.file = files[0];
        const fileReader = new FileReader();
        fileReader.addEventListener('load', (event: any) => {
          const textFromFileLoaded = event.target.result;
          this.csvContent = textFromFileLoaded;
          this.usersService.addbulkusers(this.csvContent).subscribe({
            next: (response) => {
              if (response.status.toLowerCase() === 'success') {
                this.tableFilter(this.sortTable,this.tablePagination,this.searchquery)
                this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Users imported successfully!!' });
                element.hide();
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
              }
            },
            error: (error) => { console.error(error); },
          });
        });
        fileReader.readAsText(fileToRead, 'UTF-8');
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid File',
          detail: 'Please upload a valid CSV file.',
        });
      }
    }
  }

  showDisableUserDialog(event: Event) {
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        key:'disableUserPanel',
        message: `Disable ${this.multiUsers.length === 1 ? 'this user' : 'these users'}?`,
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
            this.disableuser();
        },
        reject: () => {}
    });
  }

  disableuser() {
    const usernames = [];
    if (this.isMultiUserSelected === true) {
      this.multiUsers.forEach((user) => {
        usernames.push(user.userId);
      });
    }
    this.usersService.disableusers(usernames).subscribe((detetedusers) => {
      const response = detetedusers;
      if (response.status === 'Success') {
        // window.location.reload();
        this.tableFilter(this.sortTable,this.tablePagination,null);
        this.isMultiUserSelected = false;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
      }
    });
  }

  createTeams(element: any) {
    this.usersService.createteams(this.createTeamForm.value)
      .subscribe(res => {
        if (res.error === '') {
          if (res.status === 'Success') {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "Team created successfully!" });
            this.resetTeams(element);
            this.getTeams();
          }
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  resetTeams(element: any) {
    element.hide();
    this.createTeamForm.reset();
  }

  updateTeamDetails(element: any) {
    this.usersService
      .updatTeamDescription(
        this.teamDetails.id,
        this.createTeamForm.value.teamDescription,
        this.createTeamForm.value.teamManager
      )
      .subscribe(updateteamsAPIres => {
        const response = updateteamsAPIres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.tableFilter(null,null,null);
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "Team updated successfully!" });
            // this.errorMessage = [{severity:'success', summary:'Success', detail: "Team updated successfully!"}];
            element.hide();
            this.teamDetails = {};
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  deteteTeam(event: any, element: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      key:'disableUserPanel',
      message: 'Delete team?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.usersService.deteteteams(this.teamDetails.id).subscribe(response => {
          if (response.status.toLowerCase() === 'success') {
            // this.loadTeamsPage();
            this.tableFilter(null,null,null);
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "Team deleted successfully!" });
            // this.errorMessage = [{severity:'success', summary:'Success', detail: "Team deleted successfully!"}];
            this.teamDetails = {};
            element.hide();
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
          }
        });
      },
      reject: () => {}
    });
  }

  getAllManagers() {
    this.usersService.getmanagers('asc', 0, 1000).subscribe(
      res => {
        this.Managers = res.formUsers;
      });
  }

  showUpdateTeam(team: any, element: any, event: any) {
    event.stopPropagation();
    this.teamDetails = team;
    this.usersService
      .getteamdetails(team.id)
      .subscribe(res => {
        this.teamDetails = res;
        this.createTeamForm = this.fb.group({
          teamId: [this.teamDetails?.teamId, Validators.required],
          teamManager: [this.teamDetails?.manager, Validators.required],
          teamDescription: [this.teamDetails?.description]
        });
        element.show(event)
      }
    );    
  }

  filterOnlyTeams() {
    var userteams = [];
    var onlyTeamsArr = [];
    this.multiUsers.forEach(selectedUsers => {
      if(selectedUsers.teams && selectedUsers.teams.length > 0) {
        userteams.push(selectedUsers.teams);
      } else {
        userteams.push([]);
      }
    });
    userteams.forEach(teamName => {
      onlyTeamsArr.push(teamName.map(teams => {return teams.teamId}));
    });
    const result = onlyTeamsArr.reduce((a, b) => a.filter(c => b.includes(c)));
    this.teams = result;
    this.assignTeamForm.patchValue({
      team: result.map(res => {return {'teamName': res, 'teamId': res}}),
    });
  }

  assignTeams(element: any) {
    const userids = [];
    const selectedTeams = [];
    this.multiUsers.forEach(user => {
      userids.push(user.email);
    });
    this.assignTeamForm.value.team.map(team => {
      selectedTeams.push(team.teamName);
    });
    this.usersService
      .assignteams(selectedTeams, userids, this.removeteams)
      .subscribe(assignteamsres => {
        const response = assignteamsres;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.messageService.add({ severity: 'success', summary: 'Success', detail: "Team assigned successfully!" });
            // this.errorMessage = [{severity:'success', summary:'Success', detail: "Team assigned successfully!"}];
            this.tableFilter(null,null,null);
            element.hide();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
          // this.errorMessage = [{severity:'error', summary:'Error', detail: this.errmsg}];
        }
      });
  }

  resetAssignTeam(element: any) {
    element.hide();
    this.assignTeamForm.reset();
  }

  convertNameToAvatar(str: string) {
    return str.charAt(0).toUpperCase();
  }

  sorting(str: string, order: string) {
    this.sortField = str;
    this.sortOrder = order;
    let sort = {field: this.sortField, order: this.sortOrder};
    this.tableFilter(sort,null,this.searchquery);
  }

  // Image cropper
  assignImageURL(avatar: string) {
    if (avatar) {
      this.imageChangedEvent = { target: { files: [base64ToFile(avatar)] } };
    } else {
      this.imageChangedEvent = null;
    }
  }

  showUploadPopup() {
    document.getElementById('imageInput').click();
  }

  fileChangeEvent(event: any): void {
    this.imageChangedEvent = event;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }

  imageLoaded(image: LoadedImage) {
    this.showCropper = true;
  }

  cropperReady() {
  }

  loadImageFailed() {
  }

  saveProfilePhoto() {
  }

  cancelProfilePhoto() {
    this.imageChangedEvent = null;
    this.croppedImage = null;
  }

  onSearch() {
    this.searchSubject.next(this.searchquery);
  }
  
  helpURL() {
    window.open("https://docs.unvired.com/builder/admin/users/", '_blank');
  }

  downloadSampleFile() {
    this.usersService.downloadSampleFile()
      .subscribe({
        next: (data: any) => {
            const blob = new Blob(["\uFEFF" + data], { type: 'text/csv;charset=utf-8;' });

            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', 'Import_bulk_user_template.csv');
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        error: (error) => { console.log('Error downloading the file.') },
    });
  }

}
