.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

.breadCrumb {
    font-size: 14px !important;
  }
  td {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
  }
  .bordar-left {
    border-left: 1px solid #dee2e6;
  }
  .p-accordion .p-accordion-content {
    padding: 0rem !important;
  }
  .toggle-button {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
  }
  .toggle-button label {
    vertical-align: super;
  }
  
  img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
  }
  .hoverable {
    position: relative;
    // display: block;
    cursor: pointer;
    /* height: 100px;
    width: 100px;
    border: 1px solid #dee2e6;
    border-radius: 50%; */
  }
  .hoverable .hover-text {
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);   
    z-index: 2;
    font-size: 15px;
  }
  .hoverable .closeIcon {
    position: absolute;
    transform: translate(200%,-70%);
    z-index: 2;
    font-size: 15px;
    background: #EF4444;
    border-radius: 50%;
    padding: 4px;
    color: white;
  }
  .hoverable .background {
    position: absolute;
    display: none;
    top: 0;
    left:  0;
    bottom: 0;
    right: 0;
    background-color:rgba(255, 255, 255, 0.5);
    pointer-events: none;
    border-radius: 50%;
    z-index: 1;
  }
  .hoverable:hover .hover-text {
    display: block;
  }
  .hoverable:hover .background {
    display: block;
  }
  
  #imageInput {
    display: none;
  }
  
  
  
  .file-drop-area {
    border: 1px dashed var(--surface-d);
    background-color: var(--surface-b);
    border-radius: 5px;
    position: relative;
    max-width: 100%;
    padding: 18px;
    -webkit-transition: 0.2s;
    transition: 0.2s;
  }
  .file-msg {
    font-size: medium;
    font-weight: 300;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 10px;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: calc(100% - 130px);
    padding-bottom: 2px;
  }
  
  .file-input {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0;
  }
  .cancel-button {
    color: var(--primary-color);
  }
  .cancel-button > i {
    margin-left: 5px;
    font-size: 14px;
  }
  .header-bar {
    border-radius: 6px;
    padding-top: 8px;
    margin: 0px 8px 8px 8px;
  }
  .action-button {
    padding: 18px !important;
    height: 36px !important;
    width: 36px !important;
  }
  .highlight {
    cursor: pointer;
    color: #2586c7;
  }
  .header-style {
    font-weight: bold;
    font-size: 28px;
    color: #5A5D61;
  }
  
  /* ::ng-deep p-autocomplete .p-autocomplete-label{
    display: flex;
    flex-wrap: wrap;
    Overflow-y: scroll;
    max-height: 150px;
    max-width: 200px;
  }
  
  ::ng-deep p-autocomplete .p-autocomplete-token{
    margin-bottom: .5rem
  } */
  
  ::ng-deep .usr p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 40px;
  }
  
  ::ng-deep .usr p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }
  
  #description1:focus ~ label,
  #description1 ~ label {
    top: -.75em!important;
    font-size: 12px!important;
  }

  .download-sample-file {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--surface-b);
    color: var(--primary-color);
    border: 1px dashed var(--surface-d);
    border-radius: 5px;
    padding: 8px;
    cursor: pointer;
    i {
      font-size: 1.25rem;
    }
  }