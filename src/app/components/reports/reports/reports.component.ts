import { AfterViewInit, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { ConfirmationService, MenuItem, MessageService, TreeNode } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { ReportService } from 'src/app/services/report.service';
import { SettingsService } from 'src/app/services/settings.service';
import { UsersTeamsService } from 'src/app/services/users-teams.service';
import { FieldMap, MyRule, QueryBuilderConfig } from '../../rete/QueryBuilder.model';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss']
})
export class ReportsComponent implements OnInit, AfterViewInit {
  breadcrumbItems : MenuItem[];
  formList: any[] = [];
  formCategories: any[] = [];
  teamsArr: any[] = [];
  usersArr: any[] = [];
  tasks = [
    "Open",
    "In Progress",
    "Completed",
    "Failed",
    "Delegated",
    "Cancelled",
    "Archived",
  ];
  maxDate = new Date();
  errmsg: string;
  filteredTeamId: any;
  pageSize = 10;
  pageIndex = 0;
  managerPermission: any;
  filteredFormids: string;
  filtereuserId: string;
  selectedFormName: string;
  formNameOfSelectedForm: any;
  selectedTeamTopatch: string;
  seletecUserName: any;
  usr: any;
  editDisp: boolean = false;
  pageNumbertobeMaintained: any;
  selectedUsernametopatch: string;
  selectedFormNametoPatch: string;
  reportLength: number;
  dataSourceSubmissions: any[] = [];
  clearVal: any
  pagination: any;
  auditLogs: any;
  columns: any[];
  selectedColumns: any[];

  reportArr: any;
  // filterReportForm: FormGroup;
  reportId: any;
  isCreate: boolean = false;
  formFieldsArr: TreeNode[];
  selectedFields: TreeNode[];
  reportDetails: any;
  reportState: string;

  config: QueryBuilderConfig = {
    fields: {},
  };
  filConfig: QueryBuilderConfig = {
    fields: {},
  };
  allowRuleset: boolean = true;
  allowCollapse: boolean = true;
  persistValueOnFieldChange: boolean = false;
  stateOptions: any[] = [{label: 'And', value: 'and'}, {label: 'Or', value: 'or'}];
  formQuery: any;
  isDBdisabled: boolean = false;

  // new******************
  showReportDialog: boolean = false;
  reportName: any;
  reportDescription: any;
  filterFields: any;
  statusField: any;
  delayedSubField: any;
  formOrCategoryField: any = "";
  userOrTeamField: any = "";
  filterFieldsArr: any[] = [
    {label: 'Status'}, 
    {label: 'Delayed Submission Only'},
    {label: 'Form'},
    {label: 'Category'},
    {label: 'User'},
    {label: 'Team'},
  ];
  selectedForm: any;
  step1: boolean = false;
  step2: boolean = false;
  step3: boolean = false;
  isForm: boolean = false;
  isRunReport: boolean = false;
  filStartDate = new Date();
  filEndDate = new Date();
  filStatus: any;
  filDelayedSub: boolean;
  filTeam: any;
  filUser: any;
  filCategory: any;
  filFormQuery: any;
  totalColumns: number = 0;
  formName: any;
  selectedFilterFields: any[] = [];
  currentDate = new Date();

  constructor (
    public layoutService: LayoutService,
    private fb: FormBuilder,
    private formService: FormsService,
    private usersTeamsService: UsersTeamsService,
    private settingservice: SettingsService,
    private router: Router,
    private route: ActivatedRoute,
    private reportService: ReportService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit() {
    const usrObj = localStorage.getItem('token');
    this.usr = JSON.parse(JSON.stringify(jwtDecode(usrObj)));
    this.filStartDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate(), 0, 0, 0);
    this.filEndDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate(), 23, 59, 59, 999);
    // checking role and landscape
    if (this.usr?.ROLE.toLowerCase() === 'developer' && this.usr?.serverType.toLowerCase() === 'production') {
      this.router.navigate(['/']);
    }
    else {
      this.getManagerAccess();
      this.breadcrumbItems = [
          {label:'Home', routerLink: '/home'},
          {label:'Reports'}
      ];
    }
    // this.filterReportForm = this.fb.group({
    //   startDate: [new Date(), Validators.required],
    //   endDate: [new Date(), Validators.required],
    //   formStatus: ["Open"],
    //   submittedUser:[""],
    //   submittedTeam: [""],
    //   delayedSubmissionOnly: [false],
    //   isTeam: [false],
    // });
    this.resetColumns();
    this.getAllReports();
  }

  ngAfterViewInit() {
    if (this.usr?.ROLE.toLowerCase() === 'developer' && this.usr?.serverType.toLowerCase() === 'production') {
      this.router.navigate(['/']);
    } else {
      this.getAllForms();
      this.getAllTeams();
      this.getAllUsers();
    }
  }

  getAllForms() {
    this.formService.getallforms('All').subscribe({
      next: (responseobj: any)=>{
        this.formList = responseobj.forms;
        this.formCategories = responseobj.categories;
      }, 
      error: (error: any) => {
        this.formList = [];
      }
    });
  }

  onFormSelect(data: any) {
    if (data) {
      // this.formQuery = {condition: "and", rules: []};
      // this.createReportForm.controls['formQuery'].patchValue({condition: "and", rules: []});
      // this.createReportForm.controls['formQuery'].updateValueAndValidity();
      this.filteredFormids = data.value;
      // this.selectedFormName = data.value.formName;
      // this.formNameOfSelectedForm = data.value.description;
      this.getAllFormFields(this.filteredFormids, false);
      this.formQuery = {condition: "and", rules: []};
    } else {
      this.formFieldsArr = null;
      this.selectedFields = null;
    }
  }

  getAllTeams() {
    this.usersTeamsService.getteams('', '', 'asc', 0, 0, '', null)
      .subscribe((response: any) => {
        if (response.status.toLowerCase() === 'success') {
          if (response.teams.length) {
              response.teams.forEach(teamnameval => {
              this.teamsArr.push({'teamName': teamnameval.name, 'teamId': teamnameval.teamId});
            });
          } else {
            this.teamsArr = [];
          }
        } else {
          this.errmsg = response.error;
        }
      });
  }

  getAllUsers() {
    this.usersTeamsService.getusersForReportswithteamId('', 'asc', 0, 100, '', this.filteredTeamId, null, null)
      .subscribe((response: any) => {
        if (response.status.toLowerCase() === 'success') {
          if (response.formUsers) {
             this.usersArr = response.formUsers;
          } else {
            this.usersArr = [];
          }
        } else {
          this.errmsg = response.error;
        }
      });
  }

  getManagerAccess() {
    this.settingservice.getgeneralsettings().subscribe((res)=>{
      this.managerPermission = res.general.generalsettingsform.Allow_managers_to_edit_submissions;
    })
  }

  getSubmissionsReports(searchparams: any, pagesize: number, pageIndex: number) {
   this.getManagerAccess();
    if (this.usr.ROLE==='Admin' || (this.usr.ROLE==='Manager' && this.managerPermission===true)) {
      this.editDisp=false;
      if (searchparams.taskStatus==='Open' || searchparams.taskStatus==='In Progress') {
        this.editDisp=true;
      } else{
        this.editDisp=false;
      }
    }
    if (this.pageNumbertobeMaintained > 0) {
      searchparams["pageNumberStored"] = this.pageNumbertobeMaintained;
      // searchparams['pageNumberStored'] =  this.pageIndex;
    }

    // pass the formid and userid
    if (this.filtereuserId) {
      searchparams.submittedBy = this.filtereuserId;
      searchparams.usernametobind = this.selectedUsernametopatch;
    } else {
      this.filtereuserId = searchparams.submittedBy;
      this.selectedUsernametopatch = searchparams.usernametobind;
    }

    if (this.filteredFormids) {
      searchparams.formName = this.filteredFormids;
      searchparams.formnametobind = this.selectedFormNametoPatch;
    } else {
      this.filteredFormids = searchparams.formName;
      this.selectedFormNametoPatch = searchparams.formnametobind;
    }

    if (this.filteredTeamId) {
      searchparams.sumbittedTeamName = this.filteredTeamId;
      searchparams.teamnametobind = this.selectedTeamTopatch;
    } else {
      this.filteredTeamId  = searchparams.sumbittedTeamName;
      this.selectedTeamTopatch = searchparams.teamnametobind;
    }
    // Change the search params submittedby to userid and formtitle to formid


    // (searchparams);
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: searchparams,
    });



    const serachobj = {};
    const creatednDate = new Date(searchparams.createdDate);
    creatednDate.setHours(0, 0, 0, 0);
    const submissionDate = new Date(searchparams.submissionDate);
    submissionDate.setHours(23, 59, 59, 999);
    serachobj["createdDate"] = creatednDate;
    serachobj["submissionDate"] = submissionDate;
    // **************************

    // serachobj["formName"] = searchparams.formName;
    // serachobj["submittedBy"] = searchparams.submittedBy;

    if (this.filteredFormids) {
      serachobj["formName"] = this.filteredFormids;
    } else {
      serachobj["formName"] = searchparams.formName;
    }

    if (this.filtereuserId) {
      serachobj["submittedBy"] = this.filtereuserId;
    } else {
      serachobj["submittedBy"] = searchparams.submittedBy;
    }

    if (this.filteredTeamId) {
      serachobj["sumbittedTeamName"] = this.filteredTeamId;
    } else {
      serachobj["sumbittedTeamName"] = searchparams.sumbittedTeamName;
    }

    // *******************************

    serachobj["taskStatus"] = searchparams.taskStatus;
    serachobj["delayedSubmission"] = searchparams.delayedSubmission;
    // if (this.searchsubmissionform.get('isCategory').value === false) {
    //   serachobj["formCategory"] = searchparams.formCategory;
    // }

    let bindValueForFormName;
    let bindvalueForuserName;
    let bindValueForTeamname;


    // FormName Condition
    if (this.selectedFormNametoPatch) {
      bindValueForFormName = this.selectedFormNametoPatch;
    } else {
      bindValueForFormName = searchparams.formnametobind;
    }

    // User Name Condition
    if (this.selectedFormNametoPatch) {
      bindvalueForuserName = this.selectedUsernametopatch;
    } else {
      bindvalueForuserName = searchparams.usernametobind;
    }

      // Team Name Condition
      if (this.selectedTeamTopatch) {
        bindValueForTeamname = this.selectedTeamTopatch;
      } else {
        bindValueForTeamname = searchparams.teamnametobind;
      }



    // this.searchsubmissionform.patchValue({
    //   createdDate: new Date(searchparams.createdDate),
    //   submissionDate: new Date(searchparams.submissionDate),

    //   // ********Form Name
    //   formName: bindValueForFormName,
    //   // ********Submitted By
    //   submittedBy: bindvalueForuserName,

    //   // ********Team  name
    //   sumbittedTeamName: bindValueForTeamname,

    //   taskStatus: searchparams.taskStatus,
    //   delayedSubmission: searchparams.delayedSubmission,
    // });



    this.formService
      .getreports(serachobj, pagesize, pageIndex)
      .subscribe((reports) => {

        this.reportLength = reports.totalRecords;
        this.pageIndex = reports.nextOffSet;
        
        if (reports.form) {
          reports.form.forEach((element) => {
            const submittedtime = new Date(+element.submittedOn);
            // (submittedtime, dayjs.unix(element.submittedOn).year());
            element["submittedOn"] = submittedtime;
            
          });
          this.dataSourceSubmissions = reports.form;

          if (searchparams.pageNumberStored > 0) {
            this.pageIndex = searchparams.pageNumberStored;
            if (this.clearVal === 0) {
              this.pageIndex=0;
            }
            this.clearVal=1;
          }
        } else {
          this.dataSourceSubmissions = [];
        }
      });
  }

  getAuditLog(event: any, data: any) {
    event.stopPropagation();
    this.reportService.getAuditLogs(data.submissionId).subscribe(response => {
      if (response.status.toLowerCase() === 'success') {
        this.auditLogs = response.auditLogs;
     } else {
       this.errmsg = response.error;
       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
     }
    });
  }

  downloadPdf(event:any, element:any){
    event.stopPropagation();
    this.formService.generateRoportsPdf(element).subscribe((res)=>{
      if (res.status.toLowerCase() === 'success') {
        this.formService.downloadFile(res.pdfId).subscribe((resp)=>{
        const a = document.createElement('a');
        a.setAttribute('type', 'hidden');  
        a.href = URL.createObjectURL(resp);  
        a.download = `${element.formName}`+"-"+`${element.version}` + '.pdf';  
        a.click(); 
        a.remove();
        })
      } else {
        this.errmsg = res.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  formPreview(rowData: any) {    
    let data = {
      submissionId: rowData.submissionId,
      formId: rowData.formReference,
      name: rowData.formName,
      formTitle: rowData.formTitle,
      submittedOn: rowData.submittedOn,
      submittedBy: rowData.submittedBy,
      status: rowData.taskStatus,
      type: "form",
      isReport: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));    
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');

    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  navigateToFormRendererForAudit(formData:any, auditId: string) {
    let data = {
      name: formData.formName,
      formTitle: formData.formTitle,
      status: 'COMPLETED',
      auditId: auditId,
      type: "form",
      isReport: true
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');
  }

  getAllReports() {
    this.formService.getAllReports().subscribe({
      next: (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          this.reportArr = res.reports;
          if (this.reportState !== 'Create') {
            this.reportId = this.reportArr.filter(data => data.defaultReport === true)[0].reportId;
            this.selectedReport(this.reportId);
          }
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  showCreateReport() {
    this.cancelCreateReportDialog(null);
    this.step1 = true;
    this.step2 = false;
    this.step3 = false;
    // this.reportName = '';
    // this.reportDescription = '';
    // this.filterFields = '';
    this.selectedForm = '';
    this.formQuery = {condition: "and", rules: []};
    this.showReportDialog = true;
    // this.statusField = false;
    // this.delayedSubField = false;
    // this.formOrCategoryField = '';
    // this.userOrTeamField = '';


    // this.isCreate = true;
    this.reportState = 'Create';
    // this.reportId = null;
    this.isDBdisabled = false;
  }

  createReport() {
    let selectedColumns
    let query
    const filterfields = []
    if (this.selectedFields) {
      selectedColumns = this.convertToSimpleFormat(this.selectedFields);
    }
    if (this.selectedForm) {
      this.getFormName(this.selectedForm);
    }
    if (this.statusField) {
      filterfields.push('Status');
    }
    if (this.delayedSubField) {
      filterfields.push('Delayed Submission Only');
    }
    if (this.formOrCategoryField) {
      filterfields.push(this.formOrCategoryField);
    }
    if (this.userOrTeamField) {
      filterfields.push(this.userOrTeamField);
    }
    if (this.formQuery && this.formQuery.rules[0]) {
      query = JSON.stringify(this.formQuery)
    }

    const obj = {
      reportName: this.reportName,
      reportDesc: this.reportDescription,
      filterFields: filterfields,
      formId: this.selectedForm,
      formName: this.formName ? this.formName[0].formName : '',
      selectedColumns: selectedColumns ? selectedColumns : '',
      // formQuery: JSON.stringify(this.createReportForm.getRawValue().formQuery)
      formQuery: query ? query : ''
    };

    this.reportService.createReport(obj).subscribe({
      next: (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Report created successfully!' });
          // this.selectedReport(res.reportId);
          this.getAllReports();
          setTimeout(() => {
            this.cancelCreateReportDialog(res.reportId);
          }, 300);
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  showUpdateReport() {
    this.cancelCreateReportDialog(null);
    this.reportName = this.reportDetails?.reportName;
    this.reportDescription = this.reportDetails?.reportDesc;
    this.filterFields = '';
    if (this.reportDetails?.formId) {
      this.selectedForm = this.reportDetails.formId;
      this.formQuery = this.reportDetails.formQuery ? this.convertStringToDate(this.reportDetails.formQuery) : {condition: "and", rules: []};
      this.getAllFormFields(this.reportDetails.formId, true);
      this.selectedFields = this.reportDetails.selectedColumns ? this.reportDetails.selectedColumns : null;
    }
    if (this.reportDetails.filterFields) {
      this.statusField = this.reportDetails.filterFields.includes('Status') ? true : false;
      this.delayedSubField = this.reportDetails.filterFields.includes('Delayed Submission Only') ? true : false;
      this.formOrCategoryField = this.findFormOrCategory(this.reportDetails.filterFields);
      this.userOrTeamField = this.findUserOrTeam(this.reportDetails.filterFields);
    }
    this.step1 = true;
    this.step2 = false;
    this.step3 = false;
    this.showReportDialog = true;
    this.isDBdisabled = false;
    // this.isCreate = true;
    this.reportState = 'Update';
    // this.createReportForm.reset();
    // this.reportId = null;
  }

  updateReport() {
    let selectedColumns
    let query
    const filterfields = []
    if (this.selectedFields) {
      selectedColumns = this.convertToSimpleFormat(this.selectedFields);
    }
    if (this.statusField) {
      filterfields.push('Status');
    }
    if (this.delayedSubField) {
      filterfields.push('Delayed Submission Only');
    }
    if (this.formOrCategoryField) {
      filterfields.push(this.formOrCategoryField);
    }
    if (this.userOrTeamField) {
      filterfields.push(this.userOrTeamField);
    }
    if (this.formQuery && this.formQuery?.rules[0]) {
      query = JSON.stringify(this.formQuery)
    }
    const obj = {
      reportName: this.reportName,
      reportDesc: this.reportDescription,
      filterFields: filterfields,
      formId: this.selectedForm,
      selectedColumns: selectedColumns ? selectedColumns : '',
      // formQuery: JSON.stringify(this.createReportForm.getRawValue().formQuery)
      formQuery: query ? query : '',
      reportId: this.reportDetails?.reportId
    };
    this.reportService.updateReport(obj).subscribe({
      next: (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Report updated successfully!' });
          // this.selectedReport(res.reportId);
          this.cancelCreateReportDialog(res.reportId);
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  deleteReport(event: any) {
    event.stopPropagation();
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Delete report?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.reportService.deleteReport(this.reportId)
          .subscribe(response => {
            if (response.status.toLowerCase() === 'success') {
              this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully deleted!' });
              this.cancelCreateReportDialog(null);
              this.isRunReport = false;
              this.reportId = null;
              this.getAllReports();
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
            }
        });
      },
      reject: () => {
      }
    });
  }

  selectedReport(reportId: any) {
    this.formService.getReportDetails(reportId).subscribe({
      next: async (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          this.reportDetails = res;
          if (this.reportDetails) {
            this.resetFilterFields();
            this.reportName = this.reportDetails.reportName;
            this.reportDescription = this.reportDetails.reportDesc;
            this.isRunReport = true;
            this.selectedFilterFields = this.reportDetails.selectedFilterFields;
            if (this.reportDetails?.formId) {
              this.filFormQuery = this.reportDetails.formQuery ? this.convertStringToDate(this.reportDetails.formQuery) : null;
              this.isDBdisabled = true;
              this.selectedForm = this.reportDetails.formId;
              this.getFormName(this.selectedForm);
              this.config = {fields: {}};
              // console.log('filFormQuery',JSON.parse(this.reportDetails.formQuery))
              await this.getAllFormFields(this.reportDetails.formId, true);
              this.selectedFields = this.reportDetails?.selectedColumns ? this.reportDetails.selectedColumns : null;
              // console.log('this.selectedFields',this.selectedFields)
              // console.log('converted',this.convertStringToDate(this.reportDetails.formQuery))
              // this.treeMode = null;
              
            } else {
              this.formName = null;
              this.selectedForm = null;
              this.selectedFields = null;
              this.filFormQuery = null;
            }
            this.resetColumns();
            // console.log('reportDetails',this.reportDetails)
          }
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }


  async getAllFormFields(formId: any, expanded: boolean) {
    this.formFieldsArr = [];
    this.selectedFields = [];
    this.formService.getFormFields(formId).subscribe(async (res) => {
      
      this.formFieldsArr = this.convertToTreeNode(res, expanded);
      let simplifiedFields
      simplifiedFields = this.checkingFIelds(res, []);
      // console.log('simplifiedFields',simplifiedFields)
      if (this.step1 || this.step2) {
        this.filConfig = {fields: {}};
        setTimeout(async () => {
          this.filConfig = {
            fields: await this.setExpressionParam(simplifiedFields),
          };
        }, 300);
      } else {
        // this.config = {fields: {}};
        setTimeout(async () => {
          this.config = {
            fields: await this.setExpressionParam(simplifiedFields),
          };
        }, 300);
      }
    // console.log('filConfig',this.filConfig)
    });
  }

  convertToTreeNode(data: any[], expanded: boolean): TreeNode[] {
    return data
    .filter(item => !item.key.includes('UNV'))
    .map(item => {
      let treeNode = {
        label: item.name,
        key: item.key,
        expanded: expanded ? true : false,
        format: item?.format,
        fieldType: item.type,
        children: null
      };
      // Recursively convert children
      if (item.items && item.items.length > 0) {
        treeNode.children = this.convertToTreeNode(item.items, item.expanded);
      }
      return treeNode;
    });
  }

  convertToSimpleFormat(data: any[]) {
    return data.map(item => {
      const obj = {
        label: item.label,
        key: item.key,
        expanded: true,
        format: item?.format,
        fieldType: item.fieldType
      };
      return obj;
    });
  }

  // Query Builder
  checkingFIelds(formFields, newArray) {
    formFields.forEach((element) => {
      if (element.items) {
        return this.checkingFIelds(element.items, newArray);
      } else {
        newArray.push(element);
      }
    });
    return newArray;
  }
  
  async setExpressionParam(arrayexample): Promise<FieldMap> {
    const arrayToObject = (array) =>
      array.reduce((obj, item) => {
        obj[item.key] = item;
        return obj;
      }, {});
    const fieldMap: FieldMap = arrayToObject(arrayexample);
    return fieldMap;
  }

  ruleOperationChanged(rule: any) { 
    if (rule.operator == "between" || rule.operator == "not between") {
      if (!rule.value) {
        rule.value = [];
      }else{
        rule.value = [];
      }
    } else {
      if (rule.value != 0 && !rule.value) {
        rule.value = null;
      }
    }    
  }

  ruleSwitchChangedDropdown(rule: MyRule){    
    if(rule.checked){
      rule.value = '';
     }else{
     // rule.value = '';
     }
  }

  ruleSwitchChanged(rule: MyRule){
    if(rule.checked){
     rule.value = null;
    }else{
      rule.value = '';
    }
  }

  getData(data:any){
    data.checked = false;
  }

  // New*****************

  // onFilterFieldChange(event: any) {  Note: not needed this code
  //   // remove form or category
  //   if (event.itemValue === 'Form') {
  //     const index = event.value.indexOf('Category');
  //     if (index > -1) {
  //       this.filterFields.splice(index, 1);
  //     }
  //   } else if (event.itemValue === 'Category') {
  //     const index = event.value.indexOf('Form');
  //     if (index > -1) {
  //       this.filterFields.splice(index, 1);
  //     }
  //   }
  //   // check form is available
  //   if (event.value.includes('Form')) {
  //     this.isForm = true;

  //   } else {
  //     this.isForm = false;

  //   }
  //   // remove team or user
  //   if (event.itemValue === 'User') {
  //     const index = event.value.indexOf('Team');
  //     if (index > -1) {
  //       this.filterFields.splice(index, 1);
  //     }
  //   } else if (event.itemValue === 'Team') {
  //     const index = event.value.indexOf('User');
  //     if (index > -1) {
  //       this.filterFields.splice(index, 1);
  //     }
  //   }
  // }

  cancelCreateReportDialog(rid: any) {
    this.showReportDialog = false;
    this.reportName = '';
    this.reportDescription = '';
    this.filterFields = '';
    this.selectedForm = '';
    this.formQuery = {condition: "and", rules: []};
    this.isForm = false;
    this.statusField = false;
    this.delayedSubField = false;
    this.formOrCategoryField = '';
    this.userOrTeamField = '';
    this.formFieldsArr = null;
    this.selectedFields = null;
    this.reportState = null;
    this.step1 = false;
    this.step2 = false;
    this.step3 = false;
    if (rid) {
      this.selectedReport(rid);
      this.reportId = rid;
    }
    this.resetColumns();
  }

  backToStep1() {
    this.step1 = true;
    this.step2 = false;
    this.step3 = false;
  }

  backToStep2() {
    this.step1 = false;
    this.step2 = true;
    this.step3 = false;
  }

  gotoStep2() {
    this.step1 = false;
    this.step2 = true;
    this.step3 = false;
  }

  gotoStep3() {
    this.step1 = false;
    this.step2 = false;
    this.step3 = true;
  }

  showRunReport() {
    // console.log('check',(JSON.parse(this.reportDetails.filterFields)).includes('Status'))
    this.isRunReport = true;
    this.filFormQuery = this.reportDetails.formQuery ? JSON.parse(this.reportDetails.formQuery) : null;
    if (this.filFormQuery) {
      this.isDBdisabled = true;
    }
  }

  runReport(page: any) {
    if (this.selectedForm) {
      this.getFormName(this.selectedForm)
    }
    this.pagination = page;
    this.pageIndex = this.pagination ? this.pagination.page * this.pagination.rows : 0;
    this.pageSize = this.pagination ? this.pagination.rows : 10;
    this.resetColumns();
    const obj = {
      reportId: this.reportId,
      createdDate: this.filStartDate,
      submissionDate: new Date(this.filEndDate?.getFullYear(), this.filEndDate?.getMonth(), this.filEndDate?.getDate(), 23, 59, 59, 999), // it will take end time of day
      formId: this.selectedForm,
      formName: this.formName ? this.formName[0].formName : null,
      teamId: this.filTeam,
      submittedBy: this.filUser,
      taskStatus: this.filStatus,
      delayedSubmission: this.filDelayedSub,
      formCategory: this.filCategory,
      query: JSON.stringify(this.filFormQuery)
    }    
    this.reportService.runReport(obj, this.pageSize, this.pageIndex).subscribe(async (res) => {
      if (res.status.toLowerCase() === 'success') {
        if (this.reportDetails.selectedColumns) {
          const col = this.reportDetails.selectedColumns;
          col.forEach(item => {
            const obj = {
              header: item.label,
              field: item.key,
              type: item.fieldType,
              format: item?.format,
              class: ''
            }
            this.columns.push(obj);
          });
          // this.totalColumns = this.selectedColumns.length + 1;
        }
        if (this.selectedFilterFields?.length > 0) {
          this.selectedColumns = this.selectedFilterFields;
          this.totalColumns = this.selectedColumns.length + 1;
        } else {
          this.selectedColumns = this.columns;
          this.totalColumns = this.selectedColumns.length + 1;
        }
        this.dataSourceSubmissions = res.form.data;
        this.reportLength = res.form?.totalRecords;
        setTimeout(() => {
          window.scrollTo(0, document.body.scrollHeight);
        }, 500);
      } else {
        this.errmsg = res.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  saveTableColumns() {
    const obj = {
      reportId: this.reportId,
      selectedFilterFields: this.selectedColumns,
    };

    this.reportService.updateFilterFields(obj).subscribe({
      next: (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          // this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Report created successfully!' });
          // this.selectedReport(res.reportId);
          // this.cancelCreateReportDialog(res.reportId);
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  DownloadContent(page: any, typeRequested: string) {
    let attachId;
    if (this.selectedForm) {
      this.getFormName(this.selectedForm)
    }
    this.pagination = page;
    this.pageIndex = this.pagination ? this.pagination.page * this.pagination.rows : 0;
    this.pageSize = this.pagination ? this.pagination.rows : 10;
    this.resetColumns();
    const inputMessage = {
      reportId: this.reportId,
      createdDate: this.filStartDate,
      submissionDate: this.filEndDate,
      formId: this.selectedForm,
      formName: this.formName ? this.formName[0].formName : null,
      teamId: this.filTeam,
      submittedBy: this.filUser,
      taskStatus: this.filStatus,
      delayedSubmission: this.filDelayedSub,
      formCategory: this.filCategory,
      query: JSON.stringify(this.filFormQuery),
      type: typeRequested,
      pageSize: this.pageSize,
      pageIndex: this.pageIndex,
    }
    this.reportService.getCsvReports(inputMessage).subscribe(
      (res) => {
        if (res.status.toLowerCase() === 'success') {
          attachId = res.attachmentId;
        } else {
          this.errmsg = res.error;
        }
      },
      () => {},
      () => {
        if (attachId) {
          this.reportService.DownloadCsv(attachId).subscribe((res) => {
            //
            // const blob = new Blob([res], { type: 'application/octet-stream' });
            //
            const blob = new Blob([res], { type: "octet/stream" });
            const a = document.createElement("a");
            document.body.appendChild(a);
            const url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = attachId + "." + typeRequested;
            a.click();
            setTimeout(() => {
              window.URL.revokeObjectURL(url);
              document.body.removeChild(a);
            }, 0);
          });
        } else {
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
        
      }
    );
  }


  findFormOrCategory(data: any) {
    if (data.includes('Form')) {
      return 'Form';
    } else if (data.includes('Category')) {
      return 'Category';
    } else {
      return null;
    }
  }

  findUserOrTeam(data: any) {
    if (data.includes('User')) {
      return 'User';
    } else if (data.includes('Team')) {
      return 'Team';
    } else {
      return null;
    }
  }

  getFormName(formId: any) {
    if (formId) {
      this.formName = this.formList.filter(item => item.formId === formId);
    } else {
      this.formName = null;
    }
  }

  resetColumns() {
    this.columns = [
      { header: "Submitted on", field: "submittedOn", type: 'date', format: 'MMM d, y, h:mm:ss a', class: ''},
      { header: "Submitted by", field: "submittedBy", type: 'string', format: '', class: ''},
      { header: "Category", field: "formCategory", type: 'string', format: '', class: ''},
      { header: "Title", field: "formTitle", type: 'string', format: '', class: ''},
      { header: "Priority", field: "priority", type: 'string', format: '', class: ''},
      { header: "Approver", field: "primaryUser", type: 'string', format: '', class: ''},
      { header: "Due Date", field: "dueDate", type: 'date', format: 'MMM d, y, h:mm:ss a', class: ''},
      { header: "Audit Log", field: "", type: 'button', format: '', class: 'text-center'},
    ];
    this.selectedColumns = this.columns;
    this.totalColumns = this.selectedColumns.length + 1;
    this.dataSourceSubmissions = [];
  }

  resetFilterFields() {
    this.filStartDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate(), 0, 0, 0);
    this.filEndDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate(), 23, 59, 59, 999);
    this.selectedForm = '';
    this.filTeam = '';
    this.filUser = '';
    this.filStatus = '';
    this.filDelayedSub = false;
    this.filCategory = '';
  }

  convertStringToDate(rule: any) {
    const parsed = JSON.parse(rule);
    parsed.rules.forEach(rule => {
      if (this.isDateString(rule.value)) {
        rule.value = new Date(Date.UTC(
          parseInt(rule.value.substring(0, 4)), // Year
          parseInt(rule.value.substring(5, 7)) - 1, // Month (0-indexed)
          parseInt(rule.value.substring(8, 10)), // Day
          parseInt(rule.value.substring(11, 13)), // Hour
          parseInt(rule.value.substring(14, 16)), // Minute
          parseInt(rule.value.substring(17, 19)), // Second
          parseInt(rule.value.substring(20, 23)) // Millisecond
        ));
      }
    });
    return parsed
  }

  isDateString(value: string) {
    // Use a regular expression to check for date formats
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(value);
  }

  onFormOrCatSelect(event: any) {
    if (event.value === 'Form' && this.reportState === 'Update') {
      this.selectedForm = this.reportDetails.formId;
      this.formQuery = this.reportDetails.formQuery ? this.convertStringToDate(this.reportDetails.formQuery) : {condition: "and", rules: []};
      this.getAllFormFields(this.reportDetails.formId, true);
      this.selectedFields = this.reportDetails.selectedColumns ? this.reportDetails.selectedColumns : null;
    } else {
      this.selectedForm = '';
      this.formFieldsArr = null;
      this.selectedFields = null;
      this.formQuery = {condition: "and", rules: []};
    }
  }
  
  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/reports/", '_blank');
  }

}
