.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.highlight {
  cursor: pointer;
  color: var(--primary-color) !important;
}

.form-list {
  border: none !important;
}
.form-subtext {
  font-size: 14px;
  font-weight: 400;
}

// .desc:focus ~ label,
// .desc ~ label {
//   top: -.75em!important;
//   font-size: 12px!important;
// }

::ng-deep .q-rule{
  display: flex !important;
}
::ng-deep .qBuildCard .p-card .p-card-body{
  padding: 5px 5px !important;

}
::ng-deep .qBuildCard .p-card .p-card-content{
  padding: 0px !important;
}
.reducePadding{
  padding: 10px !important;
}
.reducePadding1{
  padding: 0px 10px 10px 10px !important;
}

::ng-deep .p-button.p-button-info, .p-buttonset.p-button-info > .p-button, .p-splitbutton.p-button-info > .p-button{
  background:var(--primary-color) !important;
  border: none !important;
  border-color: transparent !important;
}
::ng-deep .selectBtn .p-button {
  font-size: 0.875rem !important;
  padding: 10px !important;
  box-shadow: none !important;
} 
::ng-deep .p-dropdown {
  width: -webkit-fill-available !important;
}
::ng-deep .q-row{
  padding: 0px !important;
}
::ng-deep .rep p-fieldset .p-fieldset-legend {
  padding: 10px !important;
  font-weight: normal !important;
}