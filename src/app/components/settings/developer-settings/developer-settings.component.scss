.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.editor-container{
    height: 600px;
    width: 100%;
}

.toggle-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    label {
        margin-right: 5px;
        margin-bottom: 5px;
    }
}