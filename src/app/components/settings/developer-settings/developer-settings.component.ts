import { AfterViewInit, Component, OnInit } from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-developer-settings',
  templateUrl: './developer-settings.component.html',
  styleUrls: ['./developer-settings.component.scss']
})
export class DeveloperSettingsComponent implements OnInit, AfterViewInit {
  public errmsg: string;
  CSSeditorOptions = {theme: 'vs-dark', language: 'css'};
  JSeditorOptions = {theme: 'vs-dark', language: 'javascript'};
  CSScode: string= '';
  JScode: string= '';
  public add_bootstrap_css = false;
  public showtoken: boolean;
  public defaulttoken = '*********';
  public token = 'gvw,frkjg@@uhjiukjkiok;phunugvw,frkjg@@uhjiukjkiok;phunugvw,frkjg@@uhjiukjkiok;phunu';
  public user = 'Apiuser';
  public generalsettings: any;
  public showRefreshToken: boolean;
  breadcrumbItems : MenuItem[];
  selectedOption: any = 'API Authentication';
  expand: boolean = true;
  menuType: string;

  constructor(private settingservice: SettingsService, private messageService: MessageService, public layoutService: LayoutService) { }

  ngOnInit() {
    this.menuType = localStorage.getItem('menuType');
    this.breadcrumbItems = [
      {label:'Developer Settings'}
    ];
  }

  ngAfterViewInit() {
    this.getCSSeditorSettings();
    this.getJSeditorSettings();
    this.getgeneralSettings();
   }

  getCSSeditorSettings() {
    this.settingservice.geteditorsettings('css')
      .subscribe(
        (res) => {
          const response = res;
          // console.log(atob(response.script.data), typeof res);
          if (response.error === '') {
            if (response.status === 'Success') {
              // console.log(res);
              this.CSScode = atob(response.script.data);
              this.add_bootstrap_css = response.script.addbootstrapclasses;
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
        }
      );
  }

  updateToggle() {
    this.add_bootstrap_css = !this.add_bootstrap_css;
  }

  saveCSSeditorcode() {
    this.settingservice.updateeditorsettings('css', this.CSScode, this.add_bootstrap_css)
      .subscribe(
        (res) => {
          const response = res;
          if (response.error === '') {
            if (response.status === 'Success') {
              // console.log(res);
              this.getCSSeditorSettings();
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
          // console.log(res);
        }
      );
  }
  
  getJSeditorSettings() {
    this.settingservice.geteditorsettings('javascript')
      .subscribe(
        (res) => {
          const response = res;
          // console.log(atob(response.script.data), typeof res);
          if (response.error === '') {
            if (response.status === 'Success') {
              // console.log(res);
              this.JScode = atob(response.script.data);
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
        }
      );
  }

  saveJSeditorcode() {
    // console.log(this.editor.value, type);
    this.settingservice.updateeditorsettings('javascript', this.JScode, null)
      .subscribe(
        (res) => {
          const response = res;
          if (response.error === '') {
            if (response.status === 'Success') {
              // console.log(res);
              this.getJSeditorSettings();
            }
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
          // console.log(res);
        }
      );
  }

  getgeneralSettings() {
    this.settingservice.getgeneralsettings()
      .subscribe((res) => {
        if (res) {
          this.generalsettings = res;
          this.user = res?.general?.user;
          this.token = res?.general?.token;
        }
      });
  }

  regenerateToken() {
    this.settingservice.regeneratetoken(this.token)
      .subscribe((restoken) => {
        const tokendata = restoken;   //note: below code is depend on general settings componet field need to change API
        // let data = { 'Active_forms_version_allowed': this.generalsettings.Active_forms_version_allowed, 'Allow_managers_to_edit_submissions': this.generalsettings.Allow_managers_to_edit_submissions };
        // this.settingservice.updategeneralsettings(data, this.user, this.token,this.generalsettingsform.get('loginExpiry').value, this.generalsettingsform.get('passwordResetExpiry').value)
        //   .subscribe(
        //     (res) => {
        //       const response = JSON.parse(res);
        //       if (response.error === '') {
        //         if (response.status === 'Success') {
        //           this.getgeneralsettings();
        //           this.messageService.add({ severity:'success', summary:'Success', detail: 'Token regenerated and saved.' });
        //         }
        //       } else {
        //         this.errmsg = response.error;
        //         this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        //       }
        //     }
        //   );
        });
  }

  public copy() {
    const copyText = document.getElementById('tokeninput') as HTMLInputElement;
    copyText.select();
    document.execCommand('copy');
    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Token copied to clipboard.' });
  }

  updategeneralSettings() {
    // console.log(this.generalsettingsform.value);
     
    //  let data = { 'Active_forms_version_allowed':  this.generalsettingsform.get('Active_forms_version_allowed').value,
    //   'Allow_managers_to_edit_submissions': this.generalsettingsform.get('Allow_managers_to_edit_submissions').value};
    //   console.log(data);
      
    //  this.settingservice.updategeneralsettings(data, this.user, this.token,
    //  this.generalsettingsform.get('loginExpiry').value, this.generalsettingsform.get('passwordResetExpiry').value)
    //    .subscribe(
    //      (res) => {
    //        const response = JSON.parse(res);
    //        if (response.error === '') {
    //          if (response.status === 'Success') {
    //            this.getgeneralsettings();
    //          }
    //        } else {
    //          this.errmsg = response.error;
    //          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
    //        }
    //      }
    //    );
   }

   
   helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#developer", '_blank');
  }

}
