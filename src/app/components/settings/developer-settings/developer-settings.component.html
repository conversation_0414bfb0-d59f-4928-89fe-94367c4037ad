<p-toast></p-toast>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#developer" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

    <p-accordion (onOpen)="expand = true" (onClose)="expand = false" class="mt-4">
        <p-accordionTab [selected]="true">
            <ng-template pTemplate="header">
                <div class="flex flex-row w-full align-items-center">
                    <span>{{selectedOption}}</span>
                    <div *ngIf="expand" class="flex flex-wrap mx-auto gap-3" (click)="$event.stopPropagation()">
                        <div class="flex align-items-center">
                            <p-radioButton value="API Authentication" [(ngModel)]="selectedOption"  inputId="api"></p-radioButton>
                            <label for="api" class="ml-2 mb-0">API Authentication</label>
                        </div>
                        <div class="flex align-items-center">
                            <p-radioButton value="CSS" [(ngModel)]="selectedOption" inputId="css"></p-radioButton>
                            <label for="css" class="ml-2 mb-0">CSS</label>
                        </div>
                        <div class="flex align-items-center">
                            <p-radioButton value="Javascript" [(ngModel)]="selectedOption" inputId="js"></p-radioButton>
                            <label for="js" class="ml-2 mb-0">Javascript</label>
                        </div>
                    </div>
                </div>
            </ng-template>

            <ng-template pTemplate="content">
<!-- API Authentication -->
                <div *ngIf="selectedOption === 'API Authentication'" class="grid">
                    <div class="col-12 text-right">
                        <button type="button" (click)="regenerateToken()" pButton class="bg-blue p-button-sm mr-2" icon="pi pi-history" pTooltip="Regenerate Token" tooltipPosition="left"></button>
                        <button type="button"  (click)="copy()" pButton class="bg-blue p-button-sm" icon="pi pi-copy" pTooltip="Copy Token" tooltipPosition="left"></button>
                        <button *ngIf="showRefreshToken" type="button" (click)="openrefreshdialog()"  pButton class="bg-blue p-button-sm" icon="pi pi-refresh" pTooltip="Click to refresh a token" tooltipPosition="left"></button>
                    </div>
                    <div class="col-6">
                        <span class="p-float-label">
                            <input id="User" pInputText [value]="user" class="w-full" disabled="true">
                            <label htmlFor="User">User</label>
                        </span>
                    </div>
                    <div class="col-6">
                        <span class="p-input-icon-right p-float-label w-full">
                            <i [class]="showtoken ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showtoken=!showtoken;" [pTooltip]="showtoken ? 'Hide paaword' : 'Show paaword'" tooltipPosition="left"></i>
                            <input pInputText id="tokeninput" [value]="showtoken ? token : defaulttoken" type="text" class="w-full">
                            <label for="tokeninput">Token</label>
                        </span>
                    </div>
                </div>

<!-- CSS -->
                <div *ngIf="selectedOption === 'CSS'" class="settings-container">
                    <div class="toggle-button">
                        <label>Include Bootstrap CSS in addition to the custom CSS classes below:</label>
                        <p-inputSwitch [(ngModel)]="add_bootstrap_css" (onChange)="updateToggle()" class="section-margin"></p-inputSwitch>
                    </div>
                    <div class="editor-container">
                        <ngx-monaco-editor style="width: 100%;height: 100%;" [options]="CSSeditorOptions"
                            [(ngModel)]="CSScode" [ngModelOptions]="{standalone: true}">
                        </ngx-monaco-editor>
                    </div>
                    <div class="col-12 text-right pr-0">
                        <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="saveCSSeditorcode()"></button>
                    </div>
                </div>
<!-- Javascript -->
                <div *ngIf="selectedOption === 'Javascript'" class="settings-container">
                    <div class="editor-container">
                        <ngx-monaco-editor style="width: 100%;height: 100%;" [options]="JSeditorOptions"
                            [(ngModel)]="JScode" [ngModelOptions]="{standalone: true}">
                        </ngx-monaco-editor>
                    </div>
                    <div class="col-12 text-right pr-0">
                        <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="saveJSeditorcode()"></button>
                    </div>
                </div>

            </ng-template>
        </p-accordionTab>
    </p-accordion>
</div>