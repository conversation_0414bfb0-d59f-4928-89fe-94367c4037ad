import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MenuItem, Message, MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { SettingsService } from 'src/app/services/settings.service';
import * as dayjs from 'dayjs';
import * as relativeTime from 'dayjs/plugin/relativeTime';
dayjs.extend(relativeTime);
import { jwtDecode } from 'jwt-decode';


@Component({
  selector: 'app-dashboard-settings',
  templateUrl: './dashboard-settings.component.html',
  styleUrls: ['./dashboard-settings.component.scss']
})
export class DashboardSettingsComponent implements OnInit {
  public errmsg: string;
  public settingsdashboard: FormGroup;
  public databasedashboard: FormGroup;
  public displayDbPwdField = false;
  public displayAdminPwdField = false;
  public isRootCompany = false;
  public attributesList = [];
  public defaultAttributesList = [];
  public count: number = 0;
  public disp: boolean = false;
  public disableinputfield:boolean=false;
  public adminshowpassword:boolean;
  public dbshowpassword:boolean;
  public passwordvisible:boolean;
  public generalsettings;
  public attributepairs: any;
  public change:boolean=false;
  public patt='[Hh][Tt][Tt][Pp][Ss]?:\/\/(?:(?:[a-zA-Z\u00a1-\uffff0-9]+-?)*[a-zA-Z\u00a1-\uffff0-9]+)(?:\.(?:[a-zA-Z\u00a1-\uffff0-9]+-?)*[a-zA-Z\u00a1-\uffff0-9]+)*(?:\.(?:[a-zA-Z\u00a1-\uffff]{2,}))(?::\d{2,5})?(?:\/[^\s]*)?'
  databaseArr: any[];
  breadcrumbItems : MenuItem[];
  note: Message[] | undefined;
  selectedOption: string;
  listOfDashboards: any;
  importList: any;
  usr: any;
  showImportDashboard: boolean = false;
  selectedDashboards: any;
  isConfigAccessible: boolean = true;
  isDashboardAccessible: boolean = false;
  menuType: string;

  constructor(
    private fb: FormBuilder,
    private settingservice: SettingsService,
    private router: Router,
    private messageService: MessageService,
    public layoutService: LayoutService,
    private formService: FormsService,
    private confirmationService: ConfirmationService,
  ) {}

  ngOnInit(): void {
    this.menuType = localStorage.getItem('menuType');
    const usrObj = localStorage.getItem('token');
    this.usr = JSON.parse(JSON.stringify(jwtDecode(usrObj)));
    this.getDashboardConfigurations();
    this.breadcrumbItems = [{label:'Dashboard'}];
    this.settingsdashboard = this.fb.group({
      supersetURL: ["", Validators.required],
      supersetDBPwd: [""],
      supersetAdminPwd: [""],
    });
    this.databasedashboard = this.fb.group({
      dashboardDB: this.fb.array([]),
    });
    this.note = [{ severity: 'info', summary: 'Note: ', detail: 'System Settings will be configured by Unvired Support only. Below fields will be configured by the Unvired Support Team and does not need to be set or changed by other Admins.'}]
  }

  getDashboardConfigurations() {
    this.settingservice.getDashboardSettings().subscribe((res)=>{
      this.generalsettings = res;
      if (res.status.toLowerCase() === 'success') {
        this.isRootCompany = this.generalsettings?.rootCompany;
        this.settingsdashboard.patchValue({
           supersetURL:this.generalsettings.supersetURL,
           supersetAdminPwd: this.generalsettings.supersetAdminPwd,
           supersetDBPwd: this.generalsettings.supersetDBPwd,
        })
        //Dashboard Databases DB names patchvalue
        this.attributepairs = res.dashboardDB;
        this.databaseArr = this.attributepairs;
        if (this.databaseArr) {
          this.databaseArr = this.databaseArr.map(v => ({...v, readonly: true}));
        } else {
          this.databaseArr = [];
        }
        this.checkAccessibility();
      } else {
        this.errmsg = res.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
      
    });
  }

  checkAccessibility() {
    const company = localStorage.getItem('companysettings') ? JSON.parse(localStorage.getItem('companysettings')!) : null;  
    if (company && company?.dashboard) {
      if (this.usr?.ROLE.toLowerCase() === 'admin') {
        this.selectedOption = "Configuration";
        if (this.settingsdashboard.valid) {
          this.isDashboardAccessible = true;
        } else {
          this.isDashboardAccessible = false;
        }
      } else if ((this.usr?.ROLE.toLowerCase() === 'developer' && this.usr?.serverType.toLowerCase() !== 'production') || this.usr?.ROLE.toLowerCase() === 'manager') {
        this.isConfigAccessible = false;
        this.selectedOption = "Dashboard List";
        if (this.settingsdashboard.valid) {
          this.getDashboardList();
          this.isDashboardAccessible = true;
        } else {
          this.isDashboardAccessible = false;
          this.messageService.add({ severity:'warn', summary:'Warning', detail: 'Contact your administrator to set up superset configuration!', life: 5000 });
        }
      } 
      // else if (this.usr?.ROLE.toLowerCase() === 'manager') {
      //   this.isConfigAccessible = false;
      //   this.selectedOption = "Dashboard List";
      //   if (this.settingsdashboard.valid) {
      //     this.isDashboardAccessible = true;
      //   } else {
      //     this.isDashboardAccessible = false;
      //     this.messageService.add({ severity:'warn', summary:'Warning', detail: 'Contact your administrator to set up Superset configuration!', life: 5000 });
      //   }
      // } 
      else {
        this.router.navigate(['/home']);
      }
    } else {
      this.router.navigate(['/home']);
    }
  }

  updateDashboardSettings() {
    if (this.settingsdashboard.valid) {
      const data={
        "supersetURL": this.settingsdashboard.getRawValue().supersetURL,
        "supersetDBPwd": this.settingsdashboard.getRawValue().supersetDBPwd,
        "supersetAdminPwd": this.settingsdashboard.getRawValue().supersetAdminPwd,
        "dashboardDB":[]
      }
      this.settingservice.updateDashboardSettings(data).subscribe((res)=>{
        if(res.status.toLowerCase() === "success"){
          this.messageService.add({ severity:'success', summary:'Success', detail: 'Successfully updated!' });
          this.getDashboardConfigurations();
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
    } else {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Please check the system settings' });
    }
    
  }

  removeRow(index) {
    this.change=true;
    if (index > -1 &&
      this.dashboardDB &&
      this.dashboardDB.getRawValue() &&
      this.dashboardDB.getRawValue().length > 0 &&
      this.dashboardDB.getRawValue()[index] &&
      this.dashboardDB.getRawValue()[index].key
    ) {
      let searchIndex = index;
      let ind = this.defaultAttributesList.findIndex(
        (p) => p.key === this.dashboardDB.getRawValue()[searchIndex].key
      );
      if (ind > -1) {
        let obj = {
          dbName: this.dashboardDB.getRawValue()[searchIndex].dbName,
          dbUserName: this.dashboardDB.getRawValue()[searchIndex].dbUserName,
          dbPassword: this.dashboardDB.getRawValue()[searchIndex].dbPassword,

        };
        this.attributesList.push(obj);
      }
    }
    this.dashboardDB.removeAt(index);
  }
  // addCustomRow() {
  //   this.dashboardDB.push(this.createItem("", "", false));
  // }
  // addAttrribute(list: any, index) {
  //   this.dashboardDB.push(this.createItem(list.dbName, "", true));
  //   if (index > -1) {
  //     this.attributesList.splice(index, 1);
  //   }
  // }
  // createItem(dbName: string, dbPassword: string, readOnly: boolean):FormGroup {
  //   return this.fb.group({
  //     dbName: [{ value: dbName, disabled: readOnly }, [Validators.required]],
  //     dbPassword: [{ value: dbPassword, disabled: readOnly }, [Validators.required]],
  //   });
  // }
  saveDbattributespairs() {
    this.databaseArr = this.databaseArr.map(({ readonly, ...r }) => r);
    const valueArr = this.databaseArr.map(function (item) {
      return item.dbName;
    });
    const isDuplicate = valueArr.some(function (item, idx) {
      return valueArr.indexOf(item) !== idx;
    });
    if (isDuplicate) {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Database Name already exists' });
    } else {
      const data={ "dashboardDB" : this.databaseArr }
      this.settingservice.updateDashboardSettings(data).subscribe((res)=>{ 
          const data = res;
          if (data.status.toLowerCase() === "success") {
            this.messageService.add({ severity:'error', summary:'Error', detail: 'Database Name saved' });
          } else {
            this.errmsg = data.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
      });
    }
  }

  get dashboardDB() {
    return this.databasedashboard.get("dashboardDB") as FormArray;
  }
    // ------------------------------------------DATABASE-------------------------------------

  addDbRow() {
    let db: any = {
      dbName: '',
      dbUserName: '',
      dbPassword: '',
    }
    this.databaseArr.push(db);
  }

  removeDbRow(index: number) {
    this.databaseArr.splice(index, 1);
    this.change=true;
  }

  checkValidation(): boolean {
    let res = false;
    if (this.databaseArr?.length == 0) {
      res = true;
    } else if (this.databaseArr?.length > 0) {
      this.databaseArr.forEach(element => {
        if ((element.dbName == '' || element.dbName == null) || (element.dbPassword == '' || element.dbPassword == null) || (element.dbUserName == '' || element.dbUserName == null)) {
          res = true;
          return;
        }
      });
    }
    return res;
  }

  // ------------------------Dashboard List Start-------------------------------

  getDashboardList() {
    this.formService.getListOfDashboards(false).subscribe((response: any) => {
        if (response.status.toLowerCase() === "success") {
          if (response && response.dashboards && response.dashboards.length > 0) {
            this.listOfDashboards = response.dashboards;
          }
        } else {
          this.errmsg = response.error ? response.error : "";
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });  
        }
      });
  }

  getImportDashboardList() {
    this.formService.getDashboardsForImport().subscribe((response: any) => {
      if (response.status.toLowerCase() === 'success') {
        this.showImportDashboard = true;
        this.importList = response.dashboards;
        // this.isNoDashboard = (this.dashboardList.length > 0) ? true : false
      } else {
        this.errmsg = response.error ? response.error : "";
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  importselectedDashboard() {
    this.formService.importDashboard(this.selectedDashboards).subscribe((response: any) => {
      if (response.error === "") {
        this.getDashboardList();
        this.resetImportDashboard();
        this.messageService.add({ severity:'success', summary:'Success', detail: response.success });
      } else {
        this.errmsg = response.error ? response.error : "";
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  deleteDashboard(dash: any, event: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Remove dashboard?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.formService.deletedashboard(dash.id).subscribe((response: any) => {
          if (response.status.toLowerCase() === "success") {
            this.getDashboardList();
            this.messageService.add({ severity:'success', summary:'Success', detail: 'Successfully deleted!' });
          } else {
            this.errmsg = response.error ? response.error : "";
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
        });
      },
      reject: () => {}
    });
  }

  resetImportDashboard() {
    this.selectedDashboards = [];
    this.showImportDashboard = false;
  }

  formatDate(date: any) {
    const timepassed = dayjs(date).fromNow();
    return timepassed;
  }

  clearAllFilter(table: Table) {
    table.clear();
    this.getDashboardList();
  }
  
  helpURL() {
    window.open("https://docs.onvired.com/builder/advanced/settings/#dashboard", '_blank');
  }

}

