<p-toast></p-toast>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.onvired.com/builder/advanced/settings/#dashboard" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

    <p-panel>
        <ng-template pTemplate="header">
            <div class="flex flex-row justify-content-between w-full align-items-center">
                <span>{{selectedOption}}</span>
                <div *ngIf="isConfigAccessible && isDashboardAccessible" class="flex flex-wrap gap-3">
                    <div class="flex align-items-center">
                        <p-radioButton value="Configuration" [(ngModel)]="selectedOption" inputId="config"></p-radioButton>
                        <label for="config" class="ml-2 mb-0">Configuration</label>
                    </div>
                    <div class="flex align-items-center">
                        <p-radioButton value="Dashboard List" [(ngModel)]="selectedOption" (click)="getDashboardList()" inputId="dash2"></p-radioButton>
                        <label for="dash2" class="ml-2 mb-0">Dashboard List</label>
                    </div>
                </div>
                <div></div> <!-- Added to make radio buttons center alligned -->
            </div>
        </ng-template>

        <ng-template pTemplate="content">
        <!-- Superset Configuration -->
            <div *ngIf="selectedOption === 'Configuration'">
                <div class="card">
                    <form *ngIf="isRootCompany" [formGroup]="settingsdashboard" novalidate class="mb-4">
                        <div class="grid">
                            <div class="flex flex-column gap-3 w-full notes">
                            <p-messages [value]="note" [enableService]="false" [closable]="false"></p-messages>
                                <!-- <span><b>Note: </b>System Settings will be configured by Unvired Support only.</span>
                                <span><b>Note: </b>Below fields will be configured by the Unvired Support Team and does not need to be set or changed by other Admins.</span> -->
                            </div>
                            <div class="col-5 mt-2 pr-0">
                                <span class="p-float-label w-full">
                                    <input id="Superset" pInputText class="p-inputtext-sm mb-1 w-full" formControlName="supersetURL" 
                                        pattern="[Hh][Tt][Tt][Pp][Ss]?:\/\/(?:(?:[a-zA-Z\u00a1-\uffff0-9]+-?)*[a-zA-Z\u00a1-\uffff0-9]+)(?:\.(?:[a-zA-Z\u00a1-\uffff0-9]+-?)*[a-zA-Z\u00a1-\uffff0-9]+)*(?:\.(?:[a-zA-Z\u00a1-\uffff]{2,}))(?::\d{2,5})?(?:\/[^\s]*)?"
                                        type="text" required="true">
                                    <label htmlFor="Superset" class="_required">Superset URL</label>
                                </span>          
                                <small *ngIf="settingsdashboard.get('supersetURL').hasError('pattern')" class="error">Please enter valid URL</small>
                            </div>
                            <div class="col-3 mt-2 pr-0">
                                <span class="p-input-icon-right p-float-label w-full">
                                    <i [class]="dbshowpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="dbshowpassword=!dbshowpassword;"></i>
                                    <input id="AdminPassword" pInputText formControlName="supersetAdminPwd" class="p-inputtext-sm w-full" [type]="dbshowpassword ? 'text' : 'password'">
                                    <label htmlFor="AdminPassword">Superset Admin Password</label>
                                </span>
                            </div>
                            <div class="col-3 mt-2 pr-0">
                                <span class="p-input-icon-right p-float-label w-full">
                                    <i [class]="adminshowpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="adminshowpassword=!adminshowpassword;"></i>
                                    <input id="DBPassword" pInputText formControlName="supersetDBPwd" class="p-inputtext-sm w-full" [type]="adminshowpassword ? 'text' : 'password'" required="true">
                                    <label htmlFor="DBPassword" class="_required">Superset Database Password</label>
                                </span>
                            </div>
                            <div class="col-1 text-center mt-2">
                                <button pButton class="p-button-sm w-full" label="Save" (click)="updateDashboardSettings()" [disabled]="!settingsdashboard.valid">
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <p-table [value]="databaseArr" responsiveLayout="scroll" scrollHeight="calc(100vh - 505px)" styleClass="p-datatable-gridlines p-datatable-sm">
                    <ng-template pTemplate="caption">
                        <div class="flex align-items-center justify-content-between">
                          <label class="mb-0" style="font-weight: bold !important;">Dashboard Databases</label>
                            <div>
                                <button class="p-button-sm mr-2 bg-blue" pButton type="button" [label]="passwordvisible ? 'Hide Passwords' : 'Show Passwords'" [icon]="passwordvisible ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="passwordvisible=!passwordvisible" [pTooltip]="passwordvisible ? 'Hide all databases passwords' : 'Show all databases passwords'" tooltipPosition="top"></button>
                                <button class="p-button-sm mr-2 bg-blue" pButton type="button" label="Add Row" (click)="addDbRow()" pTooltip="Add new Database" tooltipPosition="top"></button>
                                <button class="p-button-sm bg-blue" pButton type="button" label="Save" [disabled]="(checkValidation() && !change)" (click)="saveDbattributespairs()"></button>
                            </div>
                        </div>
                    </ng-template>
                    <ng-template pTemplate="header">
                        <tr>
                            <th class="required text-align-center">Database Name</th>
                            <th class="_required">User Name</th>
                            <th class="_required">Database Password</th>
                            <th class="text-center" style="width: 8rem">Action</th>
                        </tr>
                    </ng-template>
                
                    <ng-template pTemplate="body" let-item let-i="rowIndex">
                        <tr>
                            <!-- Name -->
                            <td>
                                <input [readonly]="item.readonly" placeholder="Add Database Name" class="w-full p-inputtext-sm" type="text" pInputText [(ngModel)]="item.dbName" required="true"/>
                            </td>
            
                            <!-- Password -->
                            <td>
                                <input [readonly]="item.readonly" placeholder="Add User Name" class="w-full p-inputtext-sm" type="text" pInputText [(ngModel)]="item.dbUserName" required="true"/>
                            </td>
                
                            <!-- Password -->
                            <td>
                              <input [readonly]="item.readonly" placeholder="Add Database Password" class="w-full p-inputtext-sm" [type]="passwordvisible ? 'text' : 'password'" pInputText [(ngModel)]="item.dbPassword" required="true"/>
                            </td>
                
                            <!-- Action -->
                            <td class="text-center">
                                <button pButton type="button" icon="pi pi-trash" pTooltip="Remove this row" tooltipPosition="top"
                                  class="p-button-rounded p-button-text p-button-danger" style="outline: none !important;" (click)="removeDbRow(i)">
                              </button>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>

        <!-- Dashboard List -->
            <div *ngIf="selectedOption === 'Dashboard List'">
                <p-table #dt 
                    [value]="listOfDashboards" dataKey="id"
                    responsiveLayout="scroll" 
                    [scrollable]="true" 
                    styleClass="p-datatable-sm p-datatable-gridlines"
                    [tableStyle]="{ 'min-width': '50rem' }"
                    [globalFilterFields]="['name', 'userName', 'slug', 'accessRole']">

                        <ng-template pTemplate="caption">
                            <div class="flex flex-row justify-content-between align-items-center">
                                <div>
                                    <span class="p-input-icon-left">
                                        <i class="pi pi-search"></i>
                                        <input style="width: 18rem;" class="p-inputtext-sm" pInputText type="text"
                                        (input)="dt.filterGlobal($event.target.value, 'contains')" placeholder="Search..." />
                                    </span>
                                    <button pButton label="Clear" class="p-button-outlined p-button-sm ml-2" icon="pi pi-filter-slash"
                                        (click)="clearAllFilter(dt)" pTooltip="Clear all filters" tooltipPosition="top">
                                    </button>
                                </div>
                                <button *ngIf="usr.ROLE?.toLowerCase() === 'admin' || usr.ROLE?.toLowerCase() === 'manager'" pButton class="p-button-sm" icon="pi pi-download" (click)="getImportDashboardList()" pTooltip="Import Dashboards" tooltipPosition="top"></button>
                            </div>
                        </ng-template>

                <!-- Header -->
                        <ng-template pTemplate="header" let-columns>
                            <tr>
                                <th style="min-width: 10rem">Name </th>
                                <th style="min-width: 10rem">User Name</th>
                                <th style="min-width: 10rem">Last Updated</th>
                                <th style="min-width: 10rem">Slug</th>
                                <th style="min-width: 10rem" class="text-center">Role</th>
                                <th style="width: 8rem" class="text-center">Actions</th>
                            </tr>
                        </ng-template>

                <!-- Table data -->
                        <ng-template pTemplate="body" let-dash let-columns="columns">
                            <tr>
                                <td>
                                    {{dash.name}}
                                </td>

                                <td>
                                    {{dash.userName}}
                                </td>
                        
                                <td>
                                    {{ formatDate(dash.modifiedAt | date: 'MM/dd/yyyy h:mm a') }}
                                </td>

                                <td>
                                    {{dash.slug}} 
                                </td>

                                <td class="text-center">
                                    <div class="flex flex-row justify-content-center gap-2">
                                        <!-- <i *ngIf="dash.accessRole?.includes('Admin')" style="color:var(--primary-color) !important;font-size: 16px;" class="pi pi-user mr-3" pTooltip="Admin"></i>
                                        <i *ngIf="dash.accessRole?.includes('Manager')" style="color:var(--primary-color) !important;font-size: 16px;" class="pi pi-id-card mr-3" pTooltip="Manager"></i>
                                        <i *ngIf="dash.accessRole?.includes('Developer')" style="color:var(--primary-color) !important;font-size: 16px;" class="pi pi-user-edit" pTooltip="Developer"></i> -->
                                        
                                        <i *ngIf="dash.accessRole?.includes('Admin')" style="color:var(--primary-color) !important;font-size: 18px;" class="fa-solid fa-user-shield" pTooltip="Admin"></i> 
                                        <i *ngIf="dash.accessRole?.includes('Manager')" style="color:var(--primary-color) !important;font-size: 18px;" class="fa-solid fa-user-tie" pTooltip="Manager"></i>
                                        <i *ngIf="dash.accessRole?.includes('Developer')" style="color:var(--primary-color) !important;font-size: 18px;" class="fa-solid fa-user-pen" pTooltip="Developer"></i>
                                    </div>
                                </td>
                        
                                <td>
                                    <div class="flex flex-row justify-content-center">
                                        <!-- <button pButton (click)="Export(dash)" class="p-button-rounded p-button-text mr-2" icon="pi pi-download" pTooltip="Export"></button> -->
                                        <!-- <button pButton (click)="updateDashboard(dash)" class="p-button-rounded p-button-text mr-2" icon="pi pi-pencil" pTooltip="Update"></button> -->
                                        <button pButton (click)="deleteDashboard(dash, $event)" class="p-button-danger p-button-rounded p-button-text" icon="pi pi-trash" pTooltip="Delete"></button>
                                    </div>
                                </td>
                            </tr>
                        </ng-template>

                <!-- No data -->
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td *ngIf="isDashboardAccessible" class="text-center" style="font-weight: bold;" colspan="6">No Dashboards Imported.</td>
                                <td *ngIf="!isDashboardAccessible" class="text-center" style="font-weight: bold;" colspan="6"> 
                                        <span>Contact your administrator to set up Superset configuration! </span>
                                </td>
                            </tr>
                        </ng-template>
                    
                    </p-table>
            </div>
        </ng-template>
    </p-panel>
</div>

<p-confirmPopup></p-confirmPopup>
<!-- Import Dashboards -->
<p-dialog header="Dashboard List" [(visible)]="showImportDashboard" [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" [resizable]="false" (onHide)="resetImportDashboard()">
    <div *ngIf="importList?.length > 0" class="grid imp">
        <div class="col-12 w-full">
            <span class="p-float-label w-full mt-4">
                <p-multiSelect [style]="{'min-width': '100%'}" id="import" [options]="importList" [(ngModel)]="selectedDashboards" optionLabel="name" display="chip" dropdownAppendTo="body" appendTo="body"></p-multiSelect>
                <label htmlFor="import">Select Dashboards</label>
            </span>
            <div class="col-12 mt-3 pr-0 text-right">
                <button class="p-button-sm p-button-danger mr-2" pButton type="button" label="Cancel" (click)="resetImportDashboard()"></button>
                <button class="p-button-sm" pButton type="button" label="Import" (click)="importselectedDashboard()" [disabled]="!selectedDashboards?.length"></button>
            </div>
        </div>
    </div>
    <div *ngIf="importList?.length === 0">
        <div class="no-data">
            <span>All available dashboards have already been imported.</span>
        </div>
    </div>
</p-dialog>