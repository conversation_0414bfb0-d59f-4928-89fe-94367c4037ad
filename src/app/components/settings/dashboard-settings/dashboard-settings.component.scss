.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.notes {
    // padding: 10px;
    margin: auto 14px;
    // border-radius: 5px;
    // background-color: rgba(255, 0, 0, 0.141);
}

.no-data {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

::ng-deep .imp p-multiselect .p-multiselect-label {
  display: flex;
  flex-wrap: wrap;
  overflow-y: scroll;
  max-height: 100px;
}
::ng-deep .imp p-multiselect .p-multiselect-token {
  margin-bottom: .5rem;
}