.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.add-button {
    padding-top: 10px;
    padding-bottom: 0rem;
    margin-top: auto;
    margin-bottom: auto;
  }
  .preview-renderer-container {
    margin: 24px 24px 0px 24px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .bordar {
    border: solid 1px #dee2e6;
    border-radius: 6px;
  }
.editor-container{
  height: 600px;
  width: 100%;
}