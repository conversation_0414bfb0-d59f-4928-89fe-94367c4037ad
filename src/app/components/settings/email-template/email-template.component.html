<p-toast></p-toast>
<div class="card">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
              <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                <i class="pi pi-bars"></i>
            </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#mail-templates" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>
    <div class="col-12 mt-4">
        <form [formGroup]="email_templates_form" novalidate>
            <div class="grid">
              <div *ngIf="templateList" class="col-5 pl-0">
                <span class="p-float-label w-full">
                  <p-dropdown id="Template" class="p-inputtext-sm w-full" styleClass="w-full" formControlName="selectedtemplate" (onChange)="getemail_templates_html()" [options]="templateList" optionLabel="description" optionValue="name" required="true" [autoDisplayFirst]="false"></p-dropdown>
                  <label htmlFor="Template" class="_required">Template</label>
                </span>
              </div>
              <div class="col-6 pl-0">
                <span class="p-float-label w-full">
                  <input id="Subject" pInputText formControlName="subject" type="text" class="w-full">
                  <label htmlFor="Subject">Subject</label>
                </span>
              </div>
              <div class="col-1 px-0">
                <button id="createTemplateClose" class="p-button-sm bg-blue w-full" pButton type="button" label="Create" (click)="createTemplate.toggle($event)"></button>
              </div>
              <div *ngIf="isCustom?.value" class="col-12 mt-2 px-0">
                <span class="p-float-label w-full">
                  <input id="Description" pInputText formControlName="description" type="text" class="w-full">
                  <label htmlFor="Description">Description</label>
                </span>
              </div>
              <div *ngIf="showPreview" class="col-12 mt-2 bordar">
                <div class="preview-renderer-container" [innerHTML]="code"></div>
              </div>
              <div *ngIf="!showPreview" class="col-12 mt-2 editor-container p-0">
                <ngx-monaco-editor ngDefaultControl style="height: 100%;" [options]="editorOptions" [(ngModel)]="code"
                  [ngModelOptions]="{standalone: true}"></ngx-monaco-editor>
              </div>
              <div class="col-12 text-right my-2 pr-0">
                <button *ngIf="isCustom?.value" class="mr-2 p-button-sm p-button-danger" style="outline: none !important;" pButton type="button" label="Delete" (click)="showDeleteTemplateDialog($event)"></button>
                <button class="mr-2 p-button-sm bg-blue" pButton type="button" [label]="showPreview ? 'Edit' : 'Preview'" (click)="showPreview = !showPreview"></button>
                <button class="p-button-sm bg-blue" pButton type="button" label="Save" [disabled]="!email_templates_form.valid" (click)="save_Email_Template()"></button>
              </div>
            </div>
        </form>
    </div>
    
</div>

<!-- create template -->
<p-overlayPanel #createTemplate [style]="{'width': '400px'}">
  <form [formGroup]="createTemplateForm" novalidate>
    <div class="grid">
      <div class="col-12">
        <div class="p-inputgroup mt-4 w-full">
          <span class="p-inputgroup-addon">
            <span>{{ companyName }}_</span>
          </span>
          <span class="p-float-label">
            <input pInputText id="Name" formControlName="name" (input)="name.setValue(name.value.toUpperCase())" type="text" class="p-inputtext-sm" required="true">
            <label for="Name" class="_required">Name</label>
          </span>
        </div>
        <small *ngIf="createTemplateForm.get('name').hasError('pattern')" class="error">Name should consists of _ and letters only.</small>
        <div class="w-full my-4">
            <span class="p-float-label">
              <input pInputText id="Subject" formControlName="subject" type="text" class="p-inputtext-sm w-full" required="true">
              <label htmlFor="Subject" class="_required">Subject</label>
            </span>
        </div>
        <div class="w-full mt-4">
          <span class="p-float-label">
            <textarea id="description" pInputTextarea formControlName="description" rows="2" cols="30" class="w-full p-inputtext-sm" required="true"></textarea>
            <label htmlFor="Description" class="_required">Description</label>
          </span>
        </div>
      </div>
    </div>
    <div class="col-12 mt-2 px-0 text-right">
      <button class="p-button-sm p-button-danger mr-2" style="outline: none !important;" pButton type="button" label="Cancel" (click)="resetTemplateForm(createTemplate)"></button>
      <button class="p-button-sm bg-blue" pButton type="button" label="Create" [disabled]="!createTemplateForm.valid" (click)="createEmailTemplate(createTemplate)"></button>
    </div>
  </form>
</p-overlayPanel>

<!-- Delete Template -->
<p-confirmPopup key="deleteTemplate"></p-confirmPopup>