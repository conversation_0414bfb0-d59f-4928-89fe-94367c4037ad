import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-email-template',
  templateUrl: './email-template.component.html',
  styleUrls: ['./email-template.component.scss']
})
export class EmailTemplateComponent implements OnInit {
  public email_templates_form: FormGroup;
  public errmsg: string;
  code: string = "";
  templateList: any;
  editorOptions = { theme: "vs-dark", language: "html",automaticLayout: true};
  showPreview: boolean = true;
  createTemplateForm: FormGroup;
  templateKeyPattern = '[A-Z_]+';
  companyName: string;
  breadcrumbItems : MenuItem[];
  menuType: string;

  constructor(
    private fb: FormBuilder,
    private settingservice: SettingsService,
    public dialogService: DialogService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    public layoutService: LayoutService
  ) {}

  ngOnInit(): void {
    this.menuType = localStorage.getItem('menuType');
    this.companyName = localStorage.getItem('domain');
    this.email_templates_form = this.fb.group({
      selectedtemplate: ["PDF_MAIL", Validators.required],
      subject: ["", Validators.maxLength(255)],
      description: [""],
      isCustom: [false],
    });
    this.createTemplateForm  = this.fb.group({
      name: ['', [ Validators.required, Validators.pattern(this.templateKeyPattern)]],
      subject: ['', Validators.required],
      description: ['', Validators.required],
      htmlContent: ['']
    });
    this.breadcrumbItems = [
      {label:'Mail Templates'}
    ];
  }

  ngAfterViewInit() {
    this.getAllTemplates();
    this.getemail_templates_html();
  }

  getAllTemplates() {
    this.settingservice.getAllEmailTemplates().subscribe((response: any) => {
      if (response.status.toLowerCase() === 'success') {
        this.templateList = response.emailTemplates;
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  get isCustom() {
    return this.email_templates_form.get("isCustom");
  }

  getemail_templates_html() {
    this.settingservice
      .loademailtemplate(this.email_templates_form.value)
      .subscribe((response) => {
        if (response.status.toLowerCase() === 'success') {
          // console.log(res);
          this.getAllTemplates();
          this.email_templates_form.patchValue({
            selectedtemplate: response.name,
            subject: response.subject,
            description: response.description,
            isCustom: response.customTemplate,
          });
          this.code = atob(response.htmlContent);
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity:'error', summary:'Error template', detail: this.errmsg });
        }
      });
  }

  // createEmailTemplate() {
  //   const newlyCreatedTemplate = this.dialogService.open(CreateEmailTemplateDialogComponent, {
  //           contentStyle: { width: '50vw', overflow: 'auto' },
  //           header: 'Create Email Template',
  //           data: {}
  //       });

  //   newlyCreatedTemplate.onClose.subscribe((res: any) => {
  //       if (res) {
  //         this.email_templates_form.get("selectedtemplate").setValue(res.name);
  //         this.getemail_templates_html();
  //       }
  //   });
  // }

  save_Email_Template() {
    this.settingservice
      .saveemailtemplate(this.email_templates_form.value, this.code)
      .subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.messageService.add({ severity:'success', summary:'Success', detail: 'Email Template Preview Saved!' });
            this.getemail_templates_html();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
  }

  showDeleteTemplateDialog(event: Event) {
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        key:'deleteTemplate',
        message: 'Are you sure?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
            this.delete_Email_Template();
        },
        reject: () => {}
    });
  }

  delete_Email_Template() {
    this.settingservice
      .deleteEmailTemplate(this.email_templates_form.value)
      .subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.messageService.add({ severity:'success', summary:'Success', detail: 'Email Template Deleted!' });
            this.email_templates_form.get("selectedtemplate").setValue("PDF_MAIL");
            this.getemail_templates_html();
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
  }

  createEmailTemplate(element: any) {
    const name_with_prefix = `${this.companyName}_${this.name.value.toUpperCase()}`;
    // this.name.setValue(name_with_prefix);
    const obj = {
      name: name_with_prefix,
      subject: this.createTemplateForm.value.subject,
      description: this.createTemplateForm.value.description,
      htmlContent: '',
      create: true
    }
    this.settingservice.createEmailTemplate(obj)
      .subscribe((response) => {
          if (response.status.toLowerCase() === 'success') {
            element.hide();
            this.getAllTemplates();
            this.messageService.add({ severity:'success', summary:'Success', detail: 'Template Created Successfully!' });
            this.createTemplateForm.reset();
            this.email_templates_form.get("selectedtemplate").setValue(response.name);
            this.getemail_templates_html();
            this.showPreview = false;
          } else {
            this.errmsg = response.error;
            this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
          }
        }
      );
  }

  get name() {
    return this.createTemplateForm.get('name');
  }

  resetTemplateForm(element: any) {
    this.createTemplateForm.reset();
    element.hide();
  }

  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#mail-templates", '_blank');
  }

}
