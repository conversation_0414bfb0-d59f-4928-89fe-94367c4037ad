<p-toast></p-toast>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#general" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
            <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

    <form [formGroup]="generalsettingsform" novalidate>
        <div class="grid formgrid">
            <div class="col-6 mt-4">
                <span class="p-float-label">
                    <p-dropdown id="version" formControlName="Active_forms_version_allowed" optionLabel="label" required="true"
                        optionValue="value" [options]="versionArr" styleClass="w-full" class="p-inputtext-sm">
                    </p-dropdown>
                    <label htmlFor="version" class="_required">Active forms version allowed</label>
                </span>
            </div>
            <div class="col-6 mt-4">
                <div class="flex flex-row align-items-center">
                    <p-inputSwitch id="submissions" formControlName="Allow_managers_to_edit_submissions" required="true"></p-inputSwitch>
                    <label htmlFor="submissions" class="ml-2 mb-1">Allow manager to edit submissions</label>
                </div>
            </div>
            <div class="col-6 mt-4">
                <span class="p-float-label">
                    <input id="Expiry" pInputText formControlName="loginExpiry" (keyup)="showError()" styleClass="w-full" min="0" max="30" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="Expiry" class="_required">Login Expiry (Days)</label>
                </span>
            </div>
            <div class="col-6 mt-4">
                <span class="p-float-label">
                    <input id="Reset" pInputText formControlName="passwordResetExpiry" styleClass="w-full" min="0" max="1440" class="w-full p-inputtext-sm" required="true">
                    <label htmlFor="Reset" class="_required">Password Reset Expiry (Minutes)</label>
                </span>
            </div>
        </div>

        <!-- <div class="flex flex-row">
            <label class="col-4 mb-2 md:col-4 md:mb-0 required">Active forms version allowed:</label>
            <div class="col-8 md:col-8">
                <p-dropdown formControlName="Active_forms_version_allowed" optionLabel="label" required="true"
                    optionValue="value" [options]="versionArr" styleClass="w-full" class="p-inputtext-sm">
                </p-dropdown>
            </div>
        </div>
        
        <div class="flex flex-row">
            <label class="col-4 mb-2 md:col-4 md:mb-0 required">Login Expiry (Days)</label>
            <div class="col-8 md:col-8">
                <input pInputText formControlName="loginExpiry" (keyup)="showError()" styleClass="w-full" min="0" max="30" class="w-full p-inputtext-sm mb-2" required="true">
                <p-message *ngIf="generalsettingsform.controls['loginExpiry'].hasError('maxlength')" class="w-full" severity="error" text="Max length exceeded."></p-message>  comment
            </div>
        </div>
        <div class="flex flex-row">
            <label class="col-4 mb-2 md:col-4 md:mb-0 required">Password Reset Expiry (Minutes)</label>
            <div class="col-8 md:col-8">
            </div>
        </div> -->
        <div class="col-12 text-right pr-0">
            <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="updateGeneralSettings()" [disabled]="!generalsettingsform.valid"></button>
        </div>
    </form>
    
</div>

