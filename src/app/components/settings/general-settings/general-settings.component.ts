import { AfterViewInit, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-general-settings',
  templateUrl: './general-settings.component.html',
  styleUrls: ['./general-settings.component.scss']
})
export class GeneralSettingsComponent implements OnInit, AfterViewInit {
  public checked = true;
  public generalsettings: any;
  public user = 'Apiuser';
  public showRefreshToken: boolean;
  public errmsg: string;
  public showtoken: boolean;
  public defaulttoken = '*********';
  public token = 'gvw,frkjg@@uhjiukjkiok;phunugvw,frkjg@@uhjiukjkiok;phunugvw,frkjg@@uhjiukjkiok;phunu';
  public generalsettingsform: FormGroup;
  public displayPwdField = false;
  public displayAdminPwdField = false;
  public isRootCompany = false;
  public forms_version_allowed;
  versionArr: any[];
  breadcrumbItems : MenuItem[];
  menuType: string;

  constructor(
    private fb: FormBuilder,
    // private cd: ChangeDetectorRef,
    private settingservice: SettingsService,
    private messageService: MessageService,
    public layoutService: LayoutService
  ) {
    this.versionArr = [
      {'label': '1', 'value': '1'},
      {'label': '2', 'value': '2'},
      {'label': '3', 'value': '3'},
      {'label': '4', 'value': '4'},
      {'label': '5', 'value': '5'}
    ];
   }

  ngOnInit() {
    this.menuType = localStorage.getItem('menuType');
    this.generalsettingsform = this.fb.group({
      Active_forms_version_allowed: [this.versionArr[2].value, Validators.required],
      Allow_managers_to_edit_submissions: [true, Validators.required],
      loginExpiry: ['',[Validators.required,  Validators.maxLength(30)]],
      passwordResetExpiry: ['',[Validators.required,  Validators.maxLength(1440)]]
    });
    this.breadcrumbItems = [
      {label:'General Settings'}
    ];
  }
  ngAfterViewInit() {
    this.getgeneralSettings();
  }
  getgeneralSettings() {
    this.settingservice.getgeneralsettings()
      .subscribe((res) => {
          this.generalsettings = res;
          console.log('settt',this.generalsettings)
          this.generalsettingsform.patchValue({
           Active_forms_version_allowed: this.generalsettings?.general?.generalsettingsform.Active_forms_version_allowed,
           Allow_managers_to_edit_submissions: this.generalsettings?.general.generalsettingsform.Allow_managers_to_edit_submissions,
             loginExpiry:(res?.general)?.loginExpiry ? (res?.general)?.loginExpiry : 7,
             passwordResetExpiry:(res?.general)?.passwordResetExpiry ? (res.general)?.passwordResetExpiry : 10
          })

          this.user = (res?.general)?.user;
          this.token = (res?.general)?.token;
          if(res?.general && res?.general?.rootCompany){
            this.isRootCompany = true;
          }else{
            this.isRootCompany = false;
          }
          
        }
      );
  }

  get Active_forms_version_allowed() {
    return this.generalsettingsform.get('Active_forms_version_allowed').value;
  }

  get Allow_managers_to_edit_submissions(){
    return this.generalsettingsform.get('Allow_managers_to_edit_submissions').value;
  }

  updateGeneralSettings() {
   console.log(this.generalsettingsform.value);
    
    let data = { 
      'Active_forms_version_allowed':  this.generalsettingsform.get('Active_forms_version_allowed').value,
      'Allow_managers_to_edit_submissions': this.generalsettingsform.get('Allow_managers_to_edit_submissions').value
    };
     console.log(data);
     
    this.settingservice.updategeneralsettings(data, this.user, this.token,
    this.generalsettingsform.get('loginExpiry').value, this.generalsettingsform.get('passwordResetExpiry').value)
      .subscribe((response) => {
        if (response.status.toLowerCase() === 'success') {
          this.getgeneralSettings();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Successfully updated!' });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }
  public copy() {
    const copyText = document.getElementById('#tokeninput') as HTMLInputElement;
    copyText.select();
    document.execCommand('copy');
    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Token copied to clipboard.' });
  }

  regenerateToken() {
    this.settingservice.regeneratetoken(this.token)
      .subscribe((restoken) => {
        let data = { 'Active_forms_version_allowed': this.generalsettings.Active_forms_version_allowed, 'Allow_managers_to_edit_submissions': this.generalsettings.Allow_managers_to_edit_submissions };
        this.settingservice.updategeneralsettings(data, this.user, this.token,this.generalsettingsform.get('loginExpiry').value, this.generalsettingsform.get('passwordResetExpiry').value)
          .subscribe(
            (res) => {
              const response = JSON.parse(res);
              if (response.error === '') {
                if (response.status === 'Success') {
                  this.getgeneralSettings();
                  this.messageService.add({ severity:'success', summary:'Success', detail: 'Token regenerated and saved.' });
                }
              } else {
                this.errmsg = response.error;
                this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
              }
            }
          );
      });
  }

  pingFormio() {
    this.settingservice.pingformio()
      .subscribe(
        (res) => {
          const response = JSON.parse(res);
          if (response.error === '') {
            if (response.status === 'Success') {
              // Token is still valid, nothing to do
              this.showRefreshToken = false;
            }
          } else {
            this.messageService.add({ severity:'success', summary:'Success', detail: 'The authentication token between the Forms App and the backend is invalid and needs to be refreshed by Unvired support personnel.' });
            this.showRefreshToken = true;
          }
          // console.log(res);
        }
      )
  }
  
  showError() {
    if (this.generalsettingsform.controls['loginExpiry'].hasError('maxlength')) {
      this.messageService.add({ severity:'error', summary:'Error', detail: 'Max length exceeded.' });
    }    
  }
  
  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#general", '_blank');
  }

}
