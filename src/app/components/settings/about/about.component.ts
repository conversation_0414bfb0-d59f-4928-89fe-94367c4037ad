import { Component, OnInit } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss']
})
export class AboutComponent implements OnInit {
  public appversion: any;
  public buildNumber: any;
  breadcrumbItems : MenuItem[];

  constructor(private settingservice: SettingsService, public layoutService: LayoutService) { }

  ngOnInit() {
    this.settingservice.loadappversion()
    .subscribe( (versiondata: any) => {
      // console.log(versiondata);
      this.appversion = versiondata.version;
      this.buildNumber = versiondata.buildNumber;
    },
    (error: any) => {
      console.error('File not found', error.error);
    });

    this.breadcrumbItems = [
      {label:'About'}
    ];
  }

}
