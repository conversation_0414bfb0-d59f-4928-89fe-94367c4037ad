import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-pdf-settings',
  templateUrl: './pdf-settings.component.html',
  styleUrls: ['./pdf-settings.component.scss'],
  // providers: [DialogService]
})
export class PdfSettingsComponent implements OnInit {
  editorOptions = {theme: 'vs-dark', language: 'html'};
  headerCode: string = '';
  footerCode: string = '';
  errmsg: string;
  expand: boolean = true;
  pdfsettingsform: FormGroup;
  showEditor: boolean = false;  // added this to make sure editor take 100% width
  papers: any[] = [
    { key : 'Letter: 8.5in x 11in', value : 'Letter:8.5in x 11in'},
    { key : 'Legal: 8.5in x 14in', value : 'Legal:8.5in x 14in'},
    { key : 'Tabloid: 11in x 17in', value : 'Tabloid:11in x 17in'},
    { key : 'Ledger: 17in x 11in', value : 'Ledger:17in x 11in'},
    { key : 'A0: 33.1in x 46.8in', value : 'A0:33.1in x 46.8in'},
    { key : 'A1: 23.4in x 33.1in', value : 'A1:23.4in x 33.1in'},
    { key : 'A2: 16.54in x 23.4in', value : 'A2:16.54in x 23.4in'},
    { key : 'A3: 11.7in x 16.54in', value : 'A3:11.7in x 16.54in'},
    { key : 'A4: 8.27in x 11.7in', value : 'A4:8.27in x 11.7in'},
    { key : 'A5: 5.83in x 8.27in', value : 'A5:5.83in x 8.27in'},
    { key : 'A6: 4.13in x 5.83in', value : 'A6:4.13in x 5.83in'}
  ];
  selectedOption: any = 'Paper Layout';
  breadcrumbItems : MenuItem[];
  layouts: any[] = [
    { key : 'Portrait', value : 'Portrait'},
    { key : 'Landscape', value : 'Landscape'},
  ];

  constructor(private fb: FormBuilder, 
    private settingservice: SettingsService, 
    private messageService: MessageService,
    public layoutService: LayoutService) { }

  ngOnInit() {
    this.pdfsettingsform = this.fb.group({
      papertype: ['A4:8.27in x 11.7in', Validators.required],
      layout: ['Portrait', Validators.required],
      leftmargin: ['', Validators.required],
      rightmargin: ['', Validators.required],
      topmargin: ['', Validators.required],
      bottommargin: ['', Validators.required]
    });
    this.breadcrumbItems = [
      {label:'PDF Settings'}
    ];
    this.getpdfsettings();
  }

  getpdfsettings() {
    this.settingservice.getpdfsettings().subscribe((response) => 
      {
        if (response.status.toLowerCase() === 'success') {
          if (response?.pdf && Object.keys(response.pdf).length > 0) {
            this.headerCode = atob(response.pdf.header);
            this.footerCode = atob(response.pdf.footer);
            if (response.pdf?.pdfsettings && Object.keys(response.pdf.pdfsettings).length > 0) {
              this.pdfsettingsform.patchValue({
                papertype: response.pdf.pdfsettings.papertype,
                layout: response.pdf.pdfsettings.layout,
                leftmargin: response.pdf.pdfsettings.leftmargin,
                rightmargin: response.pdf.pdfsettings.rightmargin,
                topmargin: response.pdf.pdfsettings.topmargin,
                bottommargin: response.pdf.pdfsettings.bottommargin
              });
            }
          }
          this.showEditor = true;
        } else {
          this.errmsg = response.error;
          this.messageService.add({severity:'error', summary:'Error', detail: this.errmsg});
          this.showEditor = true;
        }
      });
  }

  savepdfsettings() {
    this.settingservice.savepdfsettings(this.headerCode, this.footerCode, this.pdfsettingsform.value)
      .subscribe((response) => {
          if (response.status.toLowerCase() === 'success') {
            // console.log(res);
             this.getpdfsettings();
             this.messageService.add({severity:'success', summary:'Success', detail: 'Successfully saved'});
          } else {
            this.errmsg = response.error;
            this.messageService.add({severity:'error', summary:'Error', detail: this.errmsg});
          }
          // console.log(res);
        }
      );
  }

  showHelpIcon(e: any) {
    if (e.index === 0) this.expand = !this.expand; 
    else this.expand = false;
  }

  helpButtonFunction() {
    window.open("https://github.com/puppeteer/puppeteer/blob/v2.1.1/docs/api.md#pagepdfoptions", '_blank');
  }

  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#pdf", '_blank');
  }

}
