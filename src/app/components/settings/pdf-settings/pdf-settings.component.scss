.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.editor-container{
    height: 600px;
    width: 100%;
}

.mat-form-field {
    width: 45%;
    margin-right: 10px;
}
.submit-btn {
    text-align: center;
    margin: 15px;
}
.help-button {
    padding: 2px 6px;
    border-radius: 20px;
    color: white;
}

::ng-deep .pdf p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 10px 15px 10px 15px !important;
  }

