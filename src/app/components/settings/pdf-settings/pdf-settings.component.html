<p-toast></p-toast>
<div class="card pdf">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#pdf" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        
        </ng-template>
    </p-toolbar>
    <p-accordion (onOpen)="expand = true" (onClose)="expand = false" class="mt-4">

        <p-accordionTab [selected]="true">
            <ng-template pTemplate="header">
                <div class="flex flex-row justify-content-between w-full align-items-center">
                    <span>{{selectedOption}}</span>
                    <div *ngIf="expand" class="flex flex-wrap gap-3" (click)="$event.stopPropagation()">
                        <div class="flex align-items-center">
                            <p-radioButton value="Paper Layout" [(ngModel)]="selectedOption" inputId="layout"></p-radioButton>
                            <label for="layout" class="ml-2 mb-0">Paper Layout</label>
                        </div>
                        <div class="flex align-items-center">
                            <p-radioButton value="PDF Header" [(ngModel)]="selectedOption" inputId="header"></p-radioButton>
                            <label for="header" class="ml-2 mb-0">PDF Header</label>
                        </div>
                        <div class="flex align-items-center">
                            <p-radioButton value="PDF Footer" [(ngModel)]="selectedOption" inputId="footer"></p-radioButton>
                            <label for="footer" class="ml-2 mb-0">PDF Footer</label>
                        </div>
                    </div>
                    <button class="p-button-sm" pButton type="button" icon="pi pi-question" [disabled]="selectedOption !== 'PDF Header'" (click)="helpButtonFunction();$event.stopPropagation()" pTooltip="Help" tooltipPosition="left"></button>
                    <!-- <a href="https://github.com/puppeteer/puppeteer/blob/v2.1.1/docs/api.md#pagepdfoptions" [disabled]="selectedOption !== 'PDF Header'" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
                </div>
            </ng-template>
            <ng-template pTemplate="content">

        <!-- PDF Header -->
                <div *ngIf="selectedOption === 'PDF Header' && showEditor" class="editor-container" (click)="$event.stopPropagation()">
                    <ngx-monaco-editor style="height: 100%;width: 100%;" [options]="editorOptions" pTooltip="Add HTML for PDF header" tooltipPosition="top" life="2000"
                        [(ngModel)]="headerCode">
                    </ngx-monaco-editor>
                </div>

        <!-- PDF Footer -->
                <div *ngIf="selectedOption === 'PDF Footer' && showEditor" class="editor-container">
                    <ngx-monaco-editor style="height: 100%;" [options]="editorOptions" pTooltip="Add HTML for PDF footer" tooltipPosition="top" life="2000" 
                        [(ngModel)]="footerCode"></ngx-monaco-editor>
                </div>

        <!-- PDF Layout -->
                <form *ngIf="selectedOption === 'Paper Layout'" [formGroup]="pdfsettingsform" novalidate class="p-3">
                    <div class="grid">
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <p-dropdown id="pageSize" class="p-inputtext-sm" styleClass="w-full" formControlName="papertype" [options]="papers" optionLabel="key" optionValue="value" required="true"></p-dropdown>
                        <label class="_required" htmlFor="pageSize">Page Size</label>
                        </span>
                    </div>
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <p-dropdown id="Layout" class="p-inputtext-sm" styleClass="w-full" formControlName="layout" [options]="layouts" optionLabel="key" optionValue="value" required="true"></p-dropdown>
                        <label class="_required" htmlFor="Layout">Layout</label>
                        </span>
                    </div>
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <input id="leftMargin" pInputText formControlName="leftmargin" type="text" placeholder="Left Margin" class="w-full" required="true">
                        <label class="_required" htmlFor="leftMargin">Left Margin</label>
                        </span>
                    </div>
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <input id="rightMargin" pInputText formControlName="rightmargin" type="text" placeholder="Right Margin" class="w-full" required="true">
                        <label class="_required" htmlFor="rightMargin">Right Margin</label>
                        </span>
                    </div>
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <input id="TopMargin" pInputText formControlName="topmargin" type="text" placeholder="Top Margin" class="w-full" required="true">
                        <label class="_required" htmlFor="TopMargin">Top Margin</label>
                        </span>
                    </div>
                    <div class="col-2 lg:col-2 sm:col-12 field">
                        <span class="p-float-label w-full">
                        <input id="BottomMargin" pInputText formControlName="bottommargin" type="text" placeholder="Bottom Margin" class="w-full" required="true">
                        <label class="_required" htmlFor="BottomMargin">Bottom Margin</label>
                        </span>
                    </div>
                    </div>
                </form>

            </ng-template>
        </p-accordionTab>
        
    </p-accordion>
    <div class="col my-2 text-right">
        <button class="p-button-sm bg-blue" pButton type="button" label="Save" [disabled]="!pdfsettingsform.valid" (click)="savepdfsettings()"></button>
    </div>
</div>