import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { GeneralSettingsComponent } from './general-settings/general-settings.component';
import { authGuard } from 'src/app/auth/guards/auth.guard';
import { roleGuard } from 'src/app/auth/guards/role.guard';
import { EmailTemplateComponent } from './email-template/email-template.component';
import { PdfSettingsComponent } from './pdf-settings/pdf-settings.component';
import { CompanySettingsComponent } from './company-settings/company-settings.component';
import { CommunicationSettingsComponent } from './communication-settings/communication-settings.component';
import { DashboardSettingsComponent } from './dashboard-settings/dashboard-settings.component';
import { DeveloperSettingsComponent } from './developer-settings/developer-settings.component';
import { AboutComponent } from './about/about.component';

const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    children: [
      {
        path: 'general',
        component: GeneralSettingsComponent,
        data: { role: 'Admin' },
        canActivate: [roleGuard],
      },
      {
        path: 'email-template',
        component: EmailTemplateComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [roleGuard],
      },
      {
        path: 'pdf',
        component: PdfSettingsComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [roleGuard],
      },
      {
        path: 'customization',
        component: CompanySettingsComponent,
        data: { role: 'Admin' },
        canActivate: [roleGuard],
      },
      {
        path: 'communication',
        component: CommunicationSettingsComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [roleGuard],
      },
      {
        path: 'dashboard',
        component: DashboardSettingsComponent,
        data: { role: ['Admin', 'Developer', 'Manager'] },
        canActivate: [roleGuard],
      },
      {
        path: 'developer',
        component: DeveloperSettingsComponent,
        data: { role: ['Admin', 'Developer'] },
        canActivate: [roleGuard],
      },
      {
        path: 'about',
        component: AboutComponent,
      },
      { path: '', redirectTo: 'general', pathMatch: 'full' }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule { }
