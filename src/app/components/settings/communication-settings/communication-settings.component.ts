import { AfterViewInit, Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MenuItem, MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { SettingsService } from 'src/app/services/settings.service';

@Component({
  selector: 'app-communication-settings',
  templateUrl: './communication-settings.component.html',
  styleUrls: ['./communication-settings.component.scss']
})
export class CommunicationSettingsComponent implements OnInit, AfterViewInit {
  public emailsettings: any;
  public emailSettingsForm: FormGroup;
  public errmsg: string;
  public showpassword = false;
  panelOpenState: boolean;
  panelOpenState2 = true;
  editorOptions = { theme: "vs-dark", language: "html",automaticLayout: true};
  code: string = "";
  templateList: any;
  public rootCompany:boolean = false;
  public smssettingsform: FormGroup;
  public displayPwdField = false;
  emailConfigArr: any[] = [
    {key: "SMTP", value: "SMTP"},
    {key: "AWS-SES", value: "AWS-SES"}
  ]
  encryptionArr: any[] = [
    {key: "None", value: "no"},
    {key: "SSL", value: "ssl"},
    {key: "TLS", value: "tls"}
  ]
  public AWS_REGIONS = [
    {key : "us-east-1", value: "US East (N. Virginia)"},
    {key : "us-east-2", value: "US East (Ohio)"},
    {key : "us-west-1", value: "US West (N. California)"},
    {key : "us-west-2", value: "US West (Oregon)"},
    {key : "af-south-1", value: "Africa (Cape Town)"},
    {key : "ap-east-1", value:"Asia Pacific (Hong Kong)"},
    {key : "ap-northeast-1", value: "Asia Pacific (Tokyo)"},
    {key : "ap-northeast-2", value: "Asia Pacific (Seoul)"},
    {key : "ap-southeast-1", value: "Asia Pacific (Singapore)"},
    {key : "ap-southeast-2", value: "Asia Pacific (Sydney)"},
    {key : "ap-south-1", value: "Asia Pacific (Mumbai)"},
    {key : "ca-central-1", value: "Canada (Central)"},
    {key : "cn-north-1", value:"China (Beijing)"},
    {key : "cn-northwest-1", value:"China (Ningxia)"},
    {key : "eu-west-1", value: "EU (Ireland)"},
    {key : "eu-central-1", value: "EU (Frankfurt)"},
    {key : "eu-west-2", value: "EU (London)"},
    {key : "eu-west-3", value: "EU (Paris)"},
    {key : "eu-north-1", value: "EU (Stockholm)"},
    {key : "eu-south-1", value:"Europe (Milan)"},
    {key : "sa-east-1", value: "South America (São Paulo)"},
    {key : "me-south-1", value:"Middle East (Bahrain)"}
  ]
  selectedOption: any = 'SMS';
  expand: boolean = true;
  breadcrumbItems : MenuItem[];
  isTestEmail: boolean = false;
  testEmail: any;
  menuType: string;

  constructor(
    private fb: FormBuilder,
    private settingservice: SettingsService,
    private formservice: FormsService,
    public dialogService: DialogService,
    private messageService: MessageService,
    public layoutService: LayoutService
  ) {}

  ngOnInit(): void {
    this.menuType = localStorage.getItem('menuType');
    this.emailSettingsForm = this.fb.group({
      configtype: ["", Validators.required],
      sendername: [""],
      senderemail: [""],
      encryption: [""],
      host: [""],
      port: [""],
      username: [""],
      password: [""],
      region: [""],
      accessKeyId: [""],
      secretKey: [""]
    });
    this.smssettingsform = this.fb.group({
      accountID: [""],
      authToken: [""],
    });
    this.formservice.getSmsSettings().subscribe(res => {
      let response = res;
      if (response.status === 'Success') {
        this.smssettingsform.get("accountID").patchValue(response.accountid);
        if (response.authtoken === 'PLACEHOLDER-PWD') {
          this.smssettingsform.get("authToken").patchValue("");
          this.displayPwdField = true;
        } else {
          this.smssettingsform.get("authToken").patchValue(response.authtoken);
          this.displayPwdField = false;
        }
      }
    });
    this.breadcrumbItems = [
      {label:'Communication'}
    ];
  }
  ngAfterViewInit() {
    this.getAllTemplates();
    this.getemailsettings();
  }
  get configtype () {
    return this.emailSettingsForm.get('configtype');
  }
  // changeconfigtype(selectedvalue: string) {
    // if(selectedvalue === 'SMTP') {
    //   // this.getemailsettings();
    //   this.emailsettingsform.patchValue({
    //     region: null,
    //     accessKeyId: null,
    //     secretKey: null
    //   })
    // } else {
    //   // this.getemailsettings();
    //   this.emailsettingsform.patchValue({
    //     sendername: null,
    //     senderemail: null,
    //     encryption: null,
    //     host: null,
    //     port: null,
    //     username: null,
    //     password: null
    // })
    // }
  //}
  getAllTemplates() {
    this.settingservice.getAllEmailTemplates().subscribe((res: any) => {
      const response = res;
      if (response.error === "") {
        if (response.status === "Success") {
          this.templateList = response.emailTemplates;
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
      // console.log(res);
    });
  }
  getemailsettings() {
    this.settingservice.getemailsettings().subscribe((res) => {
      // console.log(res, typeof res);
      const response = res;
      // console.log(response);
      if (response.error === "") {
        if (response.status === "Success") {
          this.emailsettings = response.emailsettingsform;
          this.emailSettingsForm.patchValue({
            configtype: this.emailsettings.configtype,
            sendername: this.emailsettings.sendername,
            senderemail: this.emailsettings.senderemail,
            encryption: this.emailsettings.encryption,
            host: this.emailsettings.host,
            port: this.emailsettings.port,
            username: this.emailsettings.username,
            password: this.emailsettings.password,
            region: this.emailsettings.region,
            accessKeyId: this.emailsettings.accessKeyId,
            secretKey: this.emailsettings.secretKey
          });
        }
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        this.rootCompany = response.rootCompany;
      }
    });
  }
  updateemailsettings() {
    // console.log(this.emailsettingsform.value);
    this.settingservice.updateemailsettings(this.emailSettingsForm.value)
      .subscribe((response) => {
        if (response.status.toLowerCase() === "success") {
          this.getemailsettings();
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
        // console.log(res);
      });
  }
  updateSmsSettings() {
    if (this.smssettingsform.get('authToken').value != "") {
      let data = { "accountid": this.smssettingsform.get('accountID').value, "authtoken": this.smssettingsform.get('authToken').value };
      this.formservice.updateSmsSettings(data).subscribe(res => {
        let response = res;
        if (response.error === '') {
          this.messageService.add({ severity:'success', summary:'Success', detail: 'SMS settings has updated successfully.' });
          console.log("res updateSmsSettings = " + res);
          this.smssettingsform.get("accountID").patchValue(response.sms.accountid);
          if (response.sms.authtoken === 'PLACEHOLDER-PWD') {
            this.smssettingsform.get("authToken").patchValue("");
            this.displayPwdField = true;
          } else {
            this.smssettingsform.get("authToken").patchValue(response.sms.authtoken);
            this.displayPwdField = false;
          }
        }
      });
    }
  }

  testEmailFunction() {
    this.settingservice.testemail(this.testEmail)
    .subscribe((response) => {
      this.isTestEmail = false;
      this.testEmail = '';
      this.messageService.add({ severity:'info', summary:'Info', detail: 'Email sent to your email id!' });
    });
  }

  
  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#communication", '_blank');
  }

}
