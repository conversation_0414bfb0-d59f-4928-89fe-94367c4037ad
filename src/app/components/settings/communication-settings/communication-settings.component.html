<p-toast></p-toast>
<div class="card mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
              <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                <i class="pi pi-bars"></i>
              </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#communication" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

    <p-accordion (onOpen)="expand = true" (onClose)="expand = false" class="mt-4">
      <p-accordionTab [selected]="true">
        <ng-template pTemplate="header">
            <div class="flex flex-row w-full align-items-center">
                <span>{{selectedOption}}</span>
                <div *ngIf="expand" class="flex flex-wrap mx-auto gap-3" (click)="$event.stopPropagation()">
                    <div class="flex align-items-center">
                        <p-radioButton value="SMS" [(ngModel)]="selectedOption" inputId="sms"></p-radioButton>
                        <label for="sms" class="ml-2 mb-0">SMS</label>
                    </div>
                    <div class="flex align-items-center">
                        <p-radioButton value="Email Server" [(ngModel)]="selectedOption" inputId="email"></p-radioButton>
                        <label for="email" class="ml-2 mb-0">Email Server</label>
                    </div>
                </div>
            </div>
        </ng-template>

        <ng-template pTemplate="content">
<!-- SMS -->
            <div *ngIf="selectedOption === 'SMS'">
                <div class="col-12 text-center">Enter your <a href="https://www.twilio.com/login" target="_blank" pTooltip="Click to log in Twilio" tooltipPosition="top">Twillo</a> Account ID and Token below.</div>
                <form [formGroup]="smssettingsform" novalidate>
                  <div class="grid">
                    <div class="lg:col-6 sm:col-12 mt-2">
                      <span class="p-float-label w-full">
                        <input id="Account" pInputText formControlName="accountID" class="p-inputtext-sm w-full">
                        <label htmlFor="Account">Account Id</label>
                      </span>
                    </div>
                    <div class="lg:col-6 sm:col-12 mt-2">
                        <span class="p-input-icon-right p-float-label w-full">
                          <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                          <input id="Auth" pInputText formControlName="authToken" class="w-full p-inputtext-sm" [type]="showpassword ? 'text' : 'password'">
                          <label htmlFor="Auth">Auth Token</label>
                        </span>
                    </div>
                    <div class="col-12 text-right">
                        <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="updateSmsSettings()" [disabled]="!smssettingsform.valid"></button>
                    </div>
                  </div>
                </form>
            </div>
            
<!-- Email -->
            <form *ngIf="selectedOption === 'Email Server'" [formGroup]="emailSettingsForm" novalidate>
                <div class="grid">
                  <div class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <p-dropdown id="configType" class="p-inputtext-sm w-full" styleClass="w-full" formControlName="configtype" [options]="emailConfigArr" optionLabel="key" optionValue="value" required="true"></p-dropdown>
                      <label htmlFor="configType" class="_required">Email Configuration Type</label>
                    </span>
                  </div>
                  <!-- config type is SMTP -->
                  <div class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="senderName" pInputText formControlName="sendername" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="senderName">Sender Name</label>
                    </span>
                  </div>
                  <div class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="senderEmail" pInputText formControlName="senderemail" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="senderEmail">Sender Email</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="hostName" pInputText formControlName="host" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="hostName">Host Name</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="Port" pInputText formControlName="port" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="Port">Port</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="userName" pInputText formControlName="username" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="userName">User Name</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-input-icon-right p-float-label w-full">
                      <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                      <input id="password" pInputText formControlName="password" class="p-inputtext-sm w-full" [type]=" showpassword ? 'text' : 'password'">
                      <label htmlFor="password">Password</label>
                    </span>
                  </div>
                  <!-- <div class="col-12 lg:col-6 md:col-6 sm:col-12 my-auto">
                    <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="flex align-items-center">
                      <div *ngFor="let enc of encryptionArr" class="field-checkbox mb-0">
                        <p-radioButton [inputId]="enc.value" [value]="enc" formControlName="encryption"></p-radioButton>
                        <label [for]="enc.value" class="mx-2">{{ enc.key }}</label>
                      </div>
                    </div>
                  </div> -->
                  <div *ngIf="configtype.value && configtype.value === 'SMTP'" class="col-12 lg:col-6 md:col-6 sm:col-12 my-auto">
                    <div class="w-full grid mx-0">
                      <div class="col-3 my-auto">
                        <label>Encryption</label>
                      </div>
                      <div class="col-9 pr-0">
                        <div class="flex align-items-center">
                          <div *ngFor="let enc of encryptionArr" class="field-checkbox mb-0">
                            <p-radioButton [inputId]="enc.value" [value]="enc" formControlName="encryption"></p-radioButton>
                            <label [for]="enc.value" class="mx-2">{{ enc.key }}</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- config type is AWS -->
                  <div *ngIf="configtype.value && configtype.value === 'AWS-SES'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <p-dropdown id="AWSRegion" class="p-inputtext-sm w-full" styleClass="w-full" formControlName="region" [options]="AWS_REGIONS" optionLabel="value" optionValue="key"></p-dropdown>
                      <label htmlFor="AWSRegion">AWS Region</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'AWS-SES'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-float-label w-full">
                      <input id="accessKeyID" pInputText formControlName="accessKeyId" type="text" class="p-inputtext-sm w-full">
                      <label htmlFor="accessKeyID">Access Key ID</label>
                    </span>
                  </div>
                  <div *ngIf="configtype.value && configtype.value === 'AWS-SES'" class="col-12 lg:col-6 md:col-6 sm:col-12 mt-2">
                    <span class="p-input-icon-right p-float-label w-full">
                      <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword=!showpassword;"></i>
                      <input pInputText formControlName="secretKey" class="p-inputtext-sm w-full" [type]=" showpassword ? 'text' : 'password'">
                      <label htmlFor="secretAccessKey">Secret Access Key</label>
                    </span>
                  </div>
                  <div class="col-12 text-right">
                    <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="updateemailsettings()"></button>
                  </div>
                  <p-divider class="col-12 py-0" styleClass="my-2"></p-divider>
                  <div class="col-6">
                    <div class="w-full grid mx-0">
                      <div class="col-3 pl-0 my-auto">
                        <p-checkbox inputId="emailCheck" binary="true" [(ngModel)]="isTestEmail" [ngModelOptions]="{standalone: true}"></p-checkbox>
                        <label htmlFor="emailCheck" class="ml-2">Send Test Email</label>
                      </div>
                      <div *ngIf="isTestEmail" class="p-inputgroup col-9 pr-0">
                        <span class="p-float-label">
                          <input id="email" pInputText type="email" [(ngModel)]="testEmail" [ngModelOptions]="{standalone: true}" class="w-full p-inputtext-sm" required="true">
                          <label htmlFor="email" class="_required">Test Email Id</label>
                        </span>
                        <button class="p-button-sm bg-blue" pButton type="button" label="Test" [disabled]="!testEmail" (click)="testEmailFunction()"></button>
                      </div>
                    </div>
                  </div>
                </div>
            </form>
        </ng-template>
        
      </p-accordionTab>
    </p-accordion>
  </div>