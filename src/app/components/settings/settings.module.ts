import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SettingsRoutingModule } from './settings-routing.module';
import { GeneralSettingsComponent } from './general-settings/general-settings.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { EmailTemplateComponent } from './email-template/email-template.component';
import { DialogService } from 'primeng/dynamicdialog';
import { PdfSettingsComponent } from './pdf-settings/pdf-settings.component';
import { CompanySettingsComponent } from './company-settings/company-settings.component';
import { CommunicationSettingsComponent } from './communication-settings/communication-settings.component';
import { DashboardSettingsComponent } from './dashboard-settings/dashboard-settings.component';
import { DeveloperSettingsComponent } from './developer-settings/developer-settings.component';
import { AboutComponent } from './about/about.component';


@NgModule({
  declarations: [
    GeneralSettingsComponent,
    EmailTemplateComponent,
    PdfSettingsComponent,
    CompanySettingsComponent,
    CommunicationSettingsComponent,
    DashboardSettingsComponent,
    DeveloperSettingsComponent,
    AboutComponent
  ],
  imports: [
    CommonModule,
    SettingsRoutingModule,
    SharedModule
  ],
  providers: [
    DialogService
  ]
})
export class SettingsModule { }
