.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.file-drop-area {
    border: 1px dashed #b2b8be;
    border-radius: 3px;
    position: relative;
    max-width: 100%;
    padding: 26px 20px 30px;
    -webkit-transition: 0.2s;
    transition: 0.2s;
}
.file-msg {
    font-size: medium;
    font-weight: 300;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 10px;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: calc(100% - 130px);
    padding-bottom: 2px;
}

.file-input {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    opacity: 0;
}
.cancel-button {
    color: var(--primary-color);
}
.cancel-button > i {
    margin-left: 5px;
    font-size: 14px;
}
// .overFlow {
//     overflow-y: scroll;
//     max-height: 350px;
// }
// .theme-img {
//     width: 70%;
//     height: 80%;
//     cursor: pointer;
//     border-radius: 4px;
//     transition: top 160ms ease;
//     position: relative;
//     top: 0;
// }
// .theme-img:hover {
//     top: -8px;
//     box-shadow: 0px 8px 8px 0px rgb(0 0 0 / 43%);
// }
// .selectedThemeColor {
//     box-shadow: 0px 0px 16px 5px rgb(86 86 86 / 26%) !important;
// }

.img-preview-container {
    background-color: #f7f7f7;
    position: relative;
}

.img-preview {
    background: center center no-repeat;
    background-size: contain;
    height: 150px;
    width: 150px;
}

.img-preview-error {
    display: none;
}

.img-loading-overlay {
    background-color: black;
    bottom: 0;
    left: 0;
    opacity: .2;
    position: absolute;
    right: 0;
    top: 0;
}

.img-spinning-circle {
    display: inline-block;
    width: 64px;
    height: 64px;
    margin: auto;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.img-spinning-circle:after {
    content: " ";
    display: block;
    width: 46px;
    height: 46px;
    margin: 1px;
    border-radius: 50%;
    border: 5px solid #fff;
    border-color: #fff transparent #fff transparent;
    animation: lds-dual-ring 1.2s linear infinite;
}

@keyframes lds-dual-ring {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.selectedTheme {
    border-radius: 50%;
    box-shadow: 0px 0px 3px 10px var(--primary-100), 0px 0px 5px 14px var(--primary-100) !important;
  }