<p-toast></p-toast>
<div class="card pb-4">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
          <!-- <a href="https://docs.unvired.com/builder/advanced/settings/#customization" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
          <button pButton class="p-button-sm" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        </ng-template>
    </p-toolbar>

    <form [formGroup]="companyform" class="mt-4" novalidate>
        <div class="grid">
          <div class="col-6 mt-3 pr-0">
            <span class="p-float-label w-full">
                <input id="CompanyName" pInputText class="w-full" formControlName="companyName" type="text" required="true">
                <label htmlFor="CompanyName" class="_required">Company Name</label>
            </span>
          </div>
          <div class="col-6 mt-3">
            <span class="p-float-label w-full">
                <p-dropdown id="WorkingDays" styleClass="w-full p-inputtext-sm" formControlName="companyWorkdays" [options]="workdayoptions" optionLabel="key" optionValue="value" required="true"></p-dropdown>
                <label htmlFor="WorkingDays" class="_required">Working Days</label>
            </span>
          </div>
          <div class="col-12 mt-2">
            <div class="flex file-drop-area align-items-center">
                <div class="mr-4" *ngIf="selectedFile">
                    <div class="img-preview{{selectedFile.status === 'fail' ? '-error' : ''}}" [ngStyle]="{'background-image': 'url('+ selectedFile.src + ')'}"></div>
                    <div *ngIf="selectedFile.pending" class="img-loading-overlay">
                        <div class="img-spinning-circle"></div>
                    </div>
                </div>
                <div>
                    <button class="p-button-sm bg-blue" pButton type="button" icon="pi pi-plus" label="Choose Company Logo"></button>
                    <span class="file-msg"><b class="cancel-button">{{file ? file.name : 'or drag and drop here' }} </b></span>
                    <input class="file-input" pInputText #imageInput accept="image/*" type="file" (change)="processFile(imageInput)">
                </div>
            </div>
          </div>
          <div class="flex align-items-center my-2 px-3">
            <p-inputSwitch formControlName="allowThemeSelection" inputId="themeToggle"></p-inputSwitch>
            <label for="themeToggle" class="ml-2 mb-1">Allow Users To Choose Theme</label>
          </div>
          <div class="col-12">
            <p-fieldset>
                <ng-template pTemplate="header">
                    Theme 
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="grid">
                        <div *ngFor="let item of themeArr" class="col-1 text-center">
                            <button class="p-link w-2rem h-2rem" [ngClass]="{'selectedTheme': (companyDetails?.theme?.theme || layoutService.config.theme) === item.name}" (click)="changeTheme(item.name, item.color)">
                                <img src="assets/layout/images/themes/{{item.name}}.{{item.extension}}" class="w-2rem h-2rem" alt="Bootstrap Light Blue">
                            </button>
                        </div>
                    </div>
                </ng-template>
            </p-fieldset>
        </div>
          <div class="col-12 text-right">
            <button class="p-button-sm bg-blue" pButton type="button" label="Save" (click)="saveCompanyDetails()"></button>
          </div>
        </div>
    </form>
</div>
