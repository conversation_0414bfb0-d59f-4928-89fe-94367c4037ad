import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SettingsService } from 'src/app/services/settings.service';
import { THEME_ARRAY, themes } from 'src/app/shared/theme-constants';

class ImageSnippet {
  pending = false;
  status = 'init';
  constructor(public src: string, public file: File) { }
}

@Component({
  selector: 'app-company-settings',
  templateUrl: './company-settings.component.html',
  styleUrls: ['./company-settings.component.scss']
})
export class CompanySettingsComponent implements OnInit {
  public companyform: FormGroup;
  workdayoptions = [
    {key: 'Monday - Friday', value: 'Monday - Friday'},
    {key: 'Monday - Saturday', value: 'Monday - Saturday'},
    {key: 'Monday - Sunday', value: 'Monday - Sunday'}
  ];
  errmsg: string;
  base64comapnyimage: any;
  fileName: any;
  selectedFile: ImageSnippet;
  file: File;
  themeArr: themes[] = THEME_ARRAY;
  breadcrumbItems : MenuItem[];
  menuType: string;
  companyDetails: any;
  
  constructor(private fb: FormBuilder,
              private settingservice: SettingsService,
              private messageService: MessageService,
              public layoutService: LayoutService
              ) { }

  ngOnInit() {
    // this.menuType = localStorage.getItem('menuType');
    this.companyform = this.fb.group({
      companyName: ["", Validators.required],
      companyWorkdays: ['', Validators.required],
      allowThemeSelection: [true],
      theme: ['']
    });
    this.getCompanyDetails();
    this.breadcrumbItems = [
      {label:'Customization'}
    ];
  }
 
  getCompanyDetails() {
    this.settingservice.getcompanysettings()
      .subscribe(response => {
        if (response.status.toLowerCase() === "success") {
          if (response?.company) {
            localStorage.setItem('companysettings', JSON.stringify(response));
            this.companyDetails = response.company;
            this.companyform.patchValue({
              companyName: this.companyDetails.companyName,
              companyWorkdays: this.companyDetails.companyWorkdays,
              allowThemeSelection: this.companyDetails?.allowThemeSelection || false,
              theme: this.companyDetails?.theme || { theme: 'lara-light-indigo', colorScheme: 'light' }
            });
            this.base64comapnyimage = this.companyDetails.attachmentId;
          }
        } else {
          this.errmsg = response.error ? response.error : "";
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
  }

  async saveCompanyDetails() {
      this.settingservice.savecompanysettings(this.companyform.value)
      .subscribe(async response => {
        if (response.status.toLowerCase() === "success") {
          localStorage.removeItem('attachmentId');
          this.messageService.add({severity:'success', summary:'Success', detail: 'Company Details Submited!'});
          this.getCompanyDetails();
        } else {
          this.errmsg = response.error ? response.error : "";
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
        }
      });
    }

  changeTheme(name: string, color: string) {
    if (!this.companyform.get('allowThemeSelection').value) {
      this.layoutService.changeTheme(name, color);
      this.companyform.controls['theme'].setValue({ theme: name, colorScheme: color });
      this.companyform.controls['theme'].updateValueAndValidity();
    }
  }

  processFile(imageInput: any) {
    this.file = imageInput.files[0];
    const reader = new FileReader();

    reader.addEventListener('load', (event: any) => {
      this.selectedFile = new ImageSnippet(event.target.result, this.file);
      this.selectedFile.pending = true;
      this.settingservice.uploadimage(this.selectedFile.file).subscribe({
        next:(res: any) => {
          localStorage.setItem('attachmentId', res.attachmentId);
          this.selectedFile.pending = false;
          this.selectedFile.status = 'ok';
          this.messageService.add({ severity:'success', summary:'Success', detail: 'Image Upload Succesfuly!' });
        },
        error: (error) => {
          this.selectedFile.pending = false;
          this.selectedFile.status = 'fail';
          this.selectedFile.src = '';
          this.messageService.add({ severity:'error', summary:'Error', detail: 'Image Upload Failed!' });
        }
      });
    });
    reader.readAsDataURL(this.file);
  }
  
  helpURL() {
    window.open("https://docs.unvired.com/builder/advanced/settings/#customization", '_blank');
  }

}
