import { Component, OnInit } from '@angular/core';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subscription, map } from 'rxjs';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ConfigService } from 'src/app/services/config.service';
import { FormsService } from 'src/app/services/forms.service';
import { jwtDecode } from 'jwt-decode';

   
@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  /** Based on the screen size, switch from standard to one column per row */
  public cards;
  public externalapps = [];
  public dashboardUrlExist: boolean = true;
  public dashboardUrl = "";
  public loadingCtrl: boolean = true;
  public dashboardTitle = "TurboForms Dashboard";
  public defaultDashObj: any[] = [{ name: 'Turbo Forms', slug: 'turboforms'}];
  public token = "";
  public dashboardSlug = "";
  public companysettingsDashboard = "";
  menuType: string;

  dashboardlistForm: FormGroup;
  public listOfDashboards = [];
  public dashboardArr = [];
  private subscription: Subscription;
  breadcrumbItems : MenuItem[];

  constructor(private breakpointObserver: BreakpointObserver, 
    private configService: ConfigService,
    private formsservice: FormsService,
    private fb: FormBuilder,
    public layoutService: LayoutService
    ) { 
      this.breadcrumbItems = [
        {label:'Home', routerLink: '/home'},
        {label: this.dashboardTitle},
      ];
    }
  ngOnInit() {
    this.menuType = localStorage.getItem('menuType');
    this.loadingCtrl = true;
    this.dashboardlistForm = this.fb.group({
      dashboardType: [''],
    });

    const usrObj = localStorage.getItem('token');
    const usr = JSON.parse(JSON.stringify(jwtDecode(usrObj)));
    this.subscription = this.formsservice.getCompanySettings().subscribe((companysettings: any) => {
       if(Object.keys(companysettings).length > 0){
        if (companysettings && companysettings.dashboard) {          
          this.companysettingsDashboard = companysettings.dashboard
          this.dashboardUrlExist = true;
          this.token = localStorage.getItem('token');
          this.formsservice
          .getListOfDashboards(true).subscribe((responseobj: any) => {
              if (responseobj.status.toLowerCase() === "success") {
                if (responseobj && responseobj.dashboards && responseobj.dashboards.length > 0) {
                  this.defaultDashObj = responseobj.dashboards.filter(item => item?.default);
                  this.listOfDashboards = responseobj.dashboards.filter(item => !item?.default);
                  if (this.defaultDashObj.length > 0) {
                    this.dashboardTitle = this.defaultDashObj[0].name;
                    this.dashboardSlug = this.defaultDashObj[0].slug;
                  } else {
                    this.dashboardTitle = 'Turbo Forms';
                    this.dashboardSlug = 'turboforms';
                    this.defaultDashObj = [{ name: 'Turbo Forms', slug: 'turboforms'}];
                  }
                  // for (let i=0; i< responseobj.dashboards.length; i++) {
                  //   if (responseobj.dashboards[i].default) {
                  //     this.dashboardTitle = responseobj.dashboards[i].name;
                  //     this.dashboardSlug = responseobj.dashboards[i].slug;
                  //     this.defaultDashObj = responseobj.dashboards[i];
                  //     break;
                  //   } else {
                  //     this.dashboardTitle = 'Turbo Forms';
                  //     this.dashboardSlug = 'turboforms';
                  //   }
                  // }

                  this.dashboardUrl = this.companysettingsDashboard + `/login/?apitoken=${this.token}&redirect=${this.dashboardSlug}&standalone=2&expand_filters=0`;
                  // this.listOfDashboards = responseobj.dashboards;
                  if (responseobj.dashboards?.length > 1) {    // If total dashboard is more than 1 then only splitbutton will display
                    this.listOfDashboards.forEach(item => {
                      // if (item.slug === this.dashboardSlug) {
                      //   item.selected = 'true';
                      // } else {
                      //   item.selected = 'false';
                      // }
                      // below code is for split button
                      this.dashboardArr.push(
                        {
                          label: item.name,
                          command: () => {
                              this.loadSelectedDashboard(item);
                          }
                        }
                      )
                    });
                  } else {                    
                    this.dashboardArr = [];
                  }
                }
              }
          });
        } else {
          this.dashboardUrlExist = false;
        }
        this.loadingCtrl = false;
      }
    });

      

    if(this.configService.app && this.configService.app.length > 0) {
      this.configService.app.forEach(element => {
        let appobj = {
          title:element.name,
          icon: element.icon,
          iconcolor: element.icon || '#000',
          desc: element.description || 'App Description',
          url: element.url,
          target: element.target
        };
        this.externalapps.push(appobj);
      });

    }
    if (usr.ROLE.toLowerCase() === 'admin') {
      this.cards = [
        // { title: 'Dashboard', route: 'dashboard',  icon: 'pi pi-home', iconcolor: 'dashboard-color', desc: 'Add or manage users'},
        { title: 'Forms', route: '/forms',  icon: 'pi pi-id-card', iconcolor: 'form-color', desc: 'Create & publish forms'},
        { title: 'Users', route: '/users',  icon: 'pi pi-users', iconcolor: 'user-color', desc: 'Add or manage users'},
        { title: 'Reports', route: '/reports',  icon: 'pi pi-check-circle', iconcolor: 'report-color', desc: 'Build custom reports'},
        { title: 'Settings', route: '/setting/general',  icon: 'pi pi-cog', iconcolor: 'setting-color', desc: 'Manage Settings'} 
      ];
    } else if (usr.ROLE.toLowerCase() === 'manager') {
      this.cards = [
        { title: 'Reports', route: '/reports',  icon: 'pi pi-check-circle', iconcolor: 'report-color', desc: 'Build custom reports'},
      ];
    } else if (usr.ROLE.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() === 'production') {
      this.cards = [
        { title: 'Forms', route: '/forms',  icon: 'pi pi-id-card', iconcolor: 'form-color', desc: 'Create & publish forms'},
        { title: 'Settings', route: '/setting/email-template',  icon: 'pi pi-cog', iconcolor: 'setting-color', desc: 'Manage Settings'} 
      ];
    } else if (usr.ROLE.toLowerCase() === 'developer' && usr?.serverType.toLowerCase() !== 'production') {
      this.cards = [
        { title: 'Forms', route: '/forms',  icon: 'pi pi-id-card', iconcolor: 'form-color', desc: 'Create & publish forms'},
        { title: 'Reports', route: '/reports',  icon: 'pi pi-check-circle', iconcolor: 'report-color', desc: 'Build custom reports'},
        { title: 'Settings', route: '/setting/email-template',  icon: 'pi pi-cog', iconcolor: 'setting-color', desc: 'Manage Settings'} 
      ];
    }
  }

  openapp(url: string, target: string) {
    window.open(url, target);
  }

  loadSelectedDashboard(list: any) {
    this.dashboardTitle = list.name;
    this.dashboardSlug = list.slug;
    this.dashboardUrl = this.companysettingsDashboard + `/login/?apitoken=${this.token}&redirect=${this.dashboardSlug}&standalone=2&expand_filters=0`;
    // this.listOfDashboards.forEach(item => {
    //   if (item.slug === this.dashboardSlug) {
    //     item.selected = 'true';
    //   } else {
    //     item.selected = 'false';
    //   }
    // });
    // element.hide();
  }

  helpURL() {
    window.open("https://docs.unvired.com/builder/getstarted/quickstart/", '_blank');
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
 
}