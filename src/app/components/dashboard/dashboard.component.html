<div class="card custom-height mb-0">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <span>
                        <span class="highlight" [routerLink]="['/home']">Home</span>
                        <span *ngIf="dashboardTitle"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> {{dashboardTitle}} </span>
                    </span>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <p-splitButton *ngIf="dashboardUrlExist && !loadingCtrl && dashboardArr.length > 0" [label]="defaultDashObj[0]?.name" (onClick)="loadSelectedDashboard(defaultDashObj[0])" [model]="dashboardArr" styleClass="p-button-sm"></p-splitButton>
            <!-- <button *ngIf="dashboardUrlExist && !loadingCtrl && listOfDashboards.length > 1" pButton pRipple (click)="FormMenu.show($event)" label="Menu" icon="pi pi-angle-down" iconPos="right" class="mx-1 p-button-sm"></button> -->
            <!-- <a href="https://docs.unvired.com/builder/getstarted/quickstart/" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
            <button pButton class="p-button-sm ml-2" icon="pi pi-question" (click)="helpURL()" pTooltip="Help" tooltipPosition="left"></button>
        
        </ng-template>
    </p-toolbar>

    <div *ngIf="!dashboardUrlExist" class="grid justify-content-center">
        <div class="col-2" *ngFor="let tile of cards">
            <div class="card cursor-pointer" routerLink="{{tile.route}}">
                <div class="col-12 text-center">
                    <i [class]="tile.icon" [ngClass]="tile.iconcolor" style="font-size: 5rem"></i>
                </div>
                <div class="flex flex-column gap-2 align-items-center">
                    <span class="title">{{tile.title}}</span>
                    <span>{{tile.desc}}</span>
                </div>
            </div>
        </div>
        <div class="col-2" *ngFor="let tile of externalapps">
            <div class="card cursor-pointer" (click)="openapp(card.url, card.target)">
                <div class="col-12 text-center">
                    <i [class]="tile.icon" [ngClass]="tile.iconcolor" style="font-size: 5rem"></i>
                </div>
                <div class="flex flex-column gap-2 align-items-center">
                    <span class="title">{{tile.title}}</span>
                    <span>{{tile.desc}}</span>
                </div>
            </div>
        </div>
    </div>

    <div *ngIf="dashboardUrlExist" class="iframe-height">
        <iframe style="height: 100%;width:100%;" [src]="dashboardUrl | safe" [title]="dashboardTitle"></iframe>
    </div>

</div>

<!-- Menu (Not using below code because of split button) -->
<!-- <p-overlayPanel #FormMenu [style]="{'max-width': '400px'}">
    <ul class="custom-ul" *ngFor="let item of listOfDashboards">
        <li class="custom-li my-auto" [ngClass]="[item.selected === 'true' ? 'selected' : ' ']" (click)="loadSelectedDashboard(item, FormMenu)">{{item.name}}</li>
    </ul>
</p-overlayPanel> -->
