.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}

.highlight {
  cursor: pointer;
  color: var(--primary-color) !important;
}

.custom-height {
  height: 100%;
}
.iframe-height {
  height: calc(100vh - 15vh);
}

.title {
  font-weight: bold;
  font-size: 14px;
}

.form-color {
  color: #3b82f6;
}
.user-color {
  color: #f97316;
}
.report-color {
  color: #06b6d4;
}
.setting-color {
  color: #a855f7;
}

.custom-ul {
  list-style-type: none !important;
  padding-left: 0px !important;
  margin-bottom: 5px;
  margin-top: 5px;
}
.custom-li {
  display: flex;
  align-items: center;
  cursor: pointer !important;
  padding: 8px;
}
.menu-icons {
  font-size: 18px;
  margin-right: 8px;
}
.custom-li:hover {
  background-color: #e9ecef;
  border-radius: 5px;
}
.selected {
  font-weight: 700;
  color: var(--primary-color);
}