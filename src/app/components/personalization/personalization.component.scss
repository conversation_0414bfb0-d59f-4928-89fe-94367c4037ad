.layout-topbar-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-secondary);
    width: 2rem;
    transition: background-color .2s;

    &:hover {
        color: var(--text-color);
        background-color: var(--surface-hover);
    }

    i {
        font-size: 1.25rem;
    }

    span {
        font-size: 1rem;
        display: none;
    }
}

@keyframes load {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-1.75rem);
  }
}


  .highlight {
    cursor: pointer;
    color: var(--primary-color) !important;
  }

  ::ng-deep .per p-fieldset .p-fieldset-legend {
    padding: 10px !important;
    font-weight: normal !important;
  }

  ::ng-deep .per p-multiselect .p-multiselect-label {
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    max-height: 40px;
  }
  
  ::ng-deep .per p-multiselect .p-multiselect-token{
    margin-bottom: .5rem;
  }

  .selectedTheme {
    border-radius: 50%;
    box-shadow: 0px 0px 3px 10px var(--primary-100), 0px 0px 5px 14px var(--primary-100) !important;
  }

  .img-loading-overlay {
    background-color: black;
    bottom: 0;
    left: 0;
    opacity: .2;
    position: absolute;
    right: 0;
    top: 0;
}