<div class="card mb-0 per">
    <p-toolbar styleClass="mb-3 p-3">
        <ng-template pTemplate="left">
            <div class="flex flex-row w-100">
                <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                    <i class="pi pi-bars"></i>
                </button>
                <div class="route-bar-breadcrumb mr-4">
                    <ng-template ngFor let-item let-last="last" [ngForOf]="breadcrumbItems">
                        <span>
                            <span [ngClass]="{'highlight' : item.routerLink}" [routerLink]="item.routerLink">{{item.label}}</span>
                            <span *ngIf="!last"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> </span>
                        </span>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template pTemplate="right">
            <!-- <a href="https://docs.unvired.com/builder/admin/forms/" target="_blank" class="p-button p-button-sm ml-2" rel="noopener noreferrer" pTooltip="Help" tooltipPosition="left"><i class="pi pi-question"></i></a> -->
        </ng-template>
    </p-toolbar>

    <div class="grid">
        <div class="col-6">
        <!-- Sidebar -->
            <div class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Sidebar Menu
                    </ng-template>
                    <ng-template pTemplate="content">
                        <div class="col-12 px-0">
                            <div class="flex md:flex-wrap justify-content-around gap-3">
                                <div *ngFor="let name of sidebarOptionArr" >
                                    <p-radioButton [inputId]="name.name" [value]="name.name" [(ngModel)]="sidebarOption" (onClick)="onSidebarOptionChange($event)"></p-radioButton>
                                    <label [for]="name.name" class="ml-1 mb-0">{{ name.name }}</label>
                                </div>
                                <!-- <div class="flex align-items-center">
                                    <p-radioButton value="Expanded" [(ngModel)]="sidebarOption" (click)="onSidebarOptionChange()" inputId="Expanded"></p-radioButton>
                                    <label for="Expanded" class="ml-2 mb-0">Expanded</label>
                                </div>
                                <div class="flex align-items-center">
                                    <p-radioButton value="Collapsed" [(ngModel)]="sidebarOption" (click)="onSidebarOptionChange()" inputId="Collapsed"></p-radioButton>
                                    <label for="Collapsed" class="ml-2 mb-0">Collapsed</label>
                                </div> -->
                            </div>
                        </div>
                    </ng-template>
                </p-fieldset>
            </div>
        
        <!-- Default Page -->
            <div class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Default 
                    </ng-template>
                    <ng-template pTemplate="content">
                        <div class="flex flex-column gap-5 py-2">
                            <span class="p-float-label w-full">
                                <p-dropdown id="Page" [(ngModel)]="defaultPage" class="p-inputtext-sm" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                    [options]="pageArr" optionLabel="name" optionValue="key" appendTo="body" (onChange)="onDefaultPageChange()">
                                </p-dropdown>
                                <label htmlFor="Page">Select Page</label>
                            </span>
                            <span class="p-float-label w-full">
                                <p-dropdown id="report" [(ngModel)]="defaultReport" class="p-inputtext-sm" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                    [options]="reportArr" optionLabel="reportName" optionValue="reportId" appendTo="body" (onChange)="onDefaultReportChange()">
                                </p-dropdown>
                                <label htmlFor="report">Select Report</label>
                            </span>
                            <span class="p-float-label w-full">
                                <p-dropdown id="Dashboard" [(ngModel)]="defaultDashboard" class="p-inputtext-sm" styleClass="w-full p-inputtext-sm" [autoDisplayFirst]="false"
                                    [options]="listOfDashboards" optionLabel="name" appendTo="body" (onChange)="onDefaultDashboardChange()">
                                </p-dropdown>
                                <label htmlFor="Dashboard">Select Dashboard</label>
                            </span>
                            <span class="p-float-label w-full">
                                <p-multiSelect id="favorite" [options]="listOfDashboards" optionLabel="name" [ngModel]="favoriteDashboard" (onChange)="onFavoriteDashboardChange($event)" display="chip" styleClass="w-full p-inputtext-sm" appendTo="body"></p-multiSelect>
                                <label htmlFor="favorite">Add Favorite Dashboard</label>
                            </span>
                        </div>
                    </ng-template>
                </p-fieldset>
            </div>

        <!-- Default Report -->
            <!-- <div class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Default Report 
                    </ng-template>
                    <ng-template pTemplate="content">
                        
                    </ng-template>
                </p-fieldset>
            </div> -->

        <!-- Favorite and Default -->
            <!-- <div class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Dashboard Actions 
                    </ng-template>
                    <ng-template pTemplate="content">
                        <div class="flex md:flex-wrap lg:flex-nowrap gap-3">
                            
                        </div>
                    </ng-template>
                </p-fieldset>
            </div> -->
        </div>

        <div class="col-6">
            <!-- Theme -->
            <div *ngIf="companySettings?.company?.allowThemeSelection" class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Theme 
                    </ng-template>
                    <ng-template pTemplate="content">
                        <div class="grid">
                            <div *ngFor="let item of themeArr" class="col-1 text-center">
                                <button class="p-link w-2rem h-2rem" [ngClass]="{'selectedTheme': layoutService.config.theme === item.name}" (click)="onChangeTheme(item.name, item.color)">
                                    <img src="assets/layout/images/themes/{{item.name}}.{{item.extension}}" class="w-2rem h-2rem" alt="Bootstrap Light Blue">
                                </button>
                            </div>
                        </div>
                    </ng-template>
                </p-fieldset>
            </div>

        <!-- Profile photo -->
            <div class="mb-3">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        Profile 
                    </ng-template>
                    <ng-template pTemplate="content">
                        <!-- <div class="grid"> -->
                            <div class="flex flex-column justify-content-center align-items-center">
                                <!-- <div class="mr-4" *ngIf="croppedImage">
                                    <img [src]="croppedImage ? croppedImage : '/assets/images/dummy_user.png'" />
                                </div> -->
                                <image-cropper #cropper class="max-h-15rem max-w-15rem mb-2"
                                    [imageChangedEvent]="imageChangedEvent"
                                    [maintainAspectRatio]="true"
                                    [aspectRatio]="4/3"
                                    format="png"
                                    (imageCropped)="imageCropped($event)"
                                    (imageLoaded)="imageLoaded($event)"
                                    (cropperReady)="cropperReady()"
                                    (loadImageFailed)="loadImageFailed()"
                                    [style.display]="showCropper ? null : 'none'"
                                    output="base64"
                                ></image-cropper>
                                <div *ngIf="imgLoading" class="img-loading-overlay">
                                    <div class="img-spinning-circle"></div>
                                </div>
                                <button *ngIf="!imageChangedEvent" pButton class="p-button-sm" (click)="showUploadPopup()" label="Upload Profile Photo"></button>    
                                <input *ngIf="!imageChangedEvent" class="file-input" pInputText id="imageInput" accept="image/*" type="file" (change)="fileChangeEvent($event)" hidden>
                                <div *ngIf="imageChangedEvent" class="flex flex-row justify-content-center">
                                    <button pButton class="p-button-sm p-button-danger mr-2" (click)="cancelProfilePhoto()" label="Remove"></button>
                                    <button pButton class="p-button-sm" (click)="saveProfilePhoto()" label="Save"></button>    
                                </div>
                            </div>
                        <!-- </div> -->
                    </ng-template>
                </p-fieldset>
            </div>
        </div>


    <!-- Menu Type
        <div class="col-3">
            <p-fieldset>
                <ng-template pTemplate="header">
                    Menu Type
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="col-12">
                        <div class="flex justify-content-around gap-3">
                            <div class="flex align-items-center">
                                <p-radioButton value="Topbar" [(ngModel)]="menuType" (click)="onMenuTypeChange()" inputId="topbar"></p-radioButton>
                                <label for="topbar" class="ml-2 mb-0">Topbar</label>
                            </div>
                            <div class="flex align-items-center">
                                <p-radioButton value="Sidebar" [(ngModel)]="menuType" (click)="onMenuTypeChange()" inputId="Sidebar"></p-radioButton>
                                <label for="Sidebar" class="ml-2 mb-0">Sidebar</label>
                            </div>
                        </div>
                    </div>
                </ng-template>
            </p-fieldset>
        </div>
        Form Designer 
        <div *ngIf="checkManagerAccess()" class="col-4">
            <p-fieldset>
                <ng-template pTemplate="header">
                    Form Designer Property 
                </ng-template>
                <ng-template pTemplate="content">
                    <div class="col-12">
                        <div class="flex justify-content-around gap-3">
                            <div class="flex align-items-center">
                                <p-radioButton value="Split Panel" [(ngModel)]="formDesignerOption" (click)="onFormDesignerOptionChange()" inputId="Split"></p-radioButton>
                                <label for="Split" class="ml-2 mb-0">Split Panel</label>
                            </div>
                            <div class="flex align-items-center">
                                <p-radioButton value="Dialog" [(ngModel)]="formDesignerOption" (click)="onFormDesignerOptionChange()" inputId="Dialog"></p-radioButton>
                                <label for="Dialog" class="ml-2 mb-0">Dialog</label>
                            </div>
                        </div>
                    </div>
                </ng-template>
            </p-fieldset>
        </div> -->
    </div>
</div>
