import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormsService } from 'src/app/services/forms.service';
import { UtilsService } from 'src/app/services/utils.service';
import { CropperSettings, ImageCroppedEvent, ImageCropperComponent, LoadedImage, base64ToFile } from 'ngx-image-cropper';
import { AuthenticationService } from 'src/app/services/authentication.service';
import { SettingsService } from 'src/app/services/settings.service';
import { jwtDecode } from 'jwt-decode';
import { THEME_ARRAY, themes } from 'src/app/shared/theme-constants';


@Component({
  selector: 'app-personalization',
  templateUrl: './personalization.component.html',
  styleUrls: ['./personalization.component.scss']
})
export class PersonalizationComponent implements OnInit {
  @ViewChild('cropper', { static: false }) cropper: ImageCropperComponent;
  breadcrumbItems : MenuItem[];
  sidebarOption: string = 'Expanded';
  formDesignerOption: string = "Split Panel";
  defaultPage: string = 'home';
  pageArr: any[] = [];
  listOfDashboards: any;
  errmsg: string;
  defaultDashboard: any;
  favoriteDashboard: any[] = [];
  themeArr: themes[] = THEME_ARRAY;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper: boolean = false;
  imgLoading: boolean = false;
  cropperSettings: CropperSettings;
  userToken: any;
  selectedThemeObj: any;
  usr: any;
  reportArr: any;
  defaultReport: any;
  menuType: string = 'Sidebar';
  companySettings: any;
  sidebarOptionArr: any[] = [
    { name: 'Expanded' },
    { name: 'Collapsed' }
  ];

  constructor(public layoutService: LayoutService,
    private formService: FormsService,
    private messageService: MessageService,
    private authservice: AuthenticationService,
    private settingService: SettingsService
    ) {}

  ngOnInit(): void {
    this.companySettings = localStorage.getItem('companysettings') ? JSON.parse(localStorage.getItem('companysettings')!) : null;
    const usrObj = localStorage.getItem('token');
    this.usr = JSON.parse(JSON.stringify(jwtDecode(usrObj)));
    this.breadcrumbItems = [
      {label:'Home', routerLink: '/home'},
      {label:'Personalization'}
    ];
    this.getProfileSetting();
    this.getDashboardList();
    this.getAllReports();
    this.userToken = this.authservice.decode();
    // const theme = JSON.parse(localStorage.getItem('theme'));
    // if (theme) {
    //   this.selectedThemeObj = { theme: theme.theme, colorScheme: theme.colorScheme };
    // }
    if (this.userToken.ROLE?.toLowerCase() === 'manager') {
      this.pageArr = [
        { name: 'Home', key: 'home' },
        { name: 'Reports', key: 'reports' },
        { name: 'Personalization', key: 'personalization' },
      ];
    } else if (this.userToken.ROLE?.toLowerCase() === 'developer') {
      if (this.usr?.serverType.toLowerCase() === 'production') {
        this.pageArr = [
          { name: 'Home', key: 'home' },
          { name: 'Forms', key: 'forms' },
          { name: 'Personalization', key: 'personalization' },
          { name: 'Settings', key: 'setting/general' },
        ];
      } else {
        this.pageArr = [
          { name: 'Home', key: 'home' },
          { name: 'Forms', key: 'forms' },
          { name: 'Reports', key: 'reports' },
          { name: 'Personalization', key: 'personalization' },
          { name: 'Settings', key: 'setting/general' },
        ];
      }
    } else {
      this.pageArr = [
        { name: 'Home', key: 'home' },
        { name: 'Forms', key: 'forms' },
        { name: 'Users', key: 'users' },
        { name: 'Reports', key: 'reports' },
        { name: 'Personalization', key: 'personalization' },
        { name: 'Settings', key: 'setting/general' },
      ];
    }
  }

  getProfileSetting() {
    this.layoutService.getProfileData().subscribe((res: any)=>{ 
      // console.log('profile data personal',res);
      if (res?.profileSettings?.sidebar) {
        this.sidebarOption = res.profileSettings.sidebar;
        if (this.sidebarOption === 'Collapsed') {
          this.layoutService.onButtonHover();
        } else {
          this.layoutService.onClickSidebar();
        }
      } else {
        this.sidebarOption = "Expanded";
      }
      // this.layoutService.sidebarChange.next(this.sidebarOption)
      if (res?.profileSettings?.defaultPage) {
        this.defaultPage = res?.profileSettings?.defaultPage;
      }
      if (res.profileSettings?.theme) {
        // this.layoutService.changeTheme(res.profileSettings?.theme.theme, res.profileSettings?.theme.colorScheme);
        this.selectedThemeObj = { theme: res.profileSettings?.theme.theme, colorScheme: res.profileSettings?.theme.colorScheme };
      } else {
        // this.layoutService.changeTheme('lara-light-indigo', 'light');
        this.selectedThemeObj = { theme: 'lara-light-indigo', colorScheme: 'light' };
      }
      if (res?.userAvatar) {
        this.imageChangedEvent = { target: { files: [base64ToFile(res.userAvatar)] } };
        // localStorage.setItem('Designer', formDesignerOption);
      } else {
        this.imageChangedEvent = null;
      }
      if (res.profileSettings?.formDesigner) {
        this.formDesignerOption = res.profileSettings?.formDesigner;
        // localStorage.setItem('Designer', formDesignerOption);
      }
    });
    // this.settingService.getPersonalization().subscribe(response => {
    //     if (response.status.toLowerCase() === 'success') {
    //       if (response.userAvatar) {
    //         this.imageChangedEvent = this.imageChangedEvent = { target: { files: [base64ToFile(response.userAvatar)] } };;
    //       }
    //       if (response?.profileSettings) {
    //         this.sidebarOption = response.profileSettings?.sidebar;
    //         if (this.sidebarOption === 'Collapsed') {
    //           this.layoutService.onButtonHover();
    //         } else {
    //           this.layoutService.onClickSidebar();
    //         }
    //         // this.menuType = response.profileSettings?.menuType ? response.profileSettings?.menuType : 'Sidebar';
    //         // this.layoutService.state.staticMenuDesktopInactive = this.menuType === 'Topbar' ? true : (this.sidebarOption === 'Expanded' ? false : true);
    //         this.formDesignerOption = response.profileSettings?.formDesigner;
    //         this.defaultPage = response.profileSettings?.defaultPage; 
    //         if (response.profileSettings?.theme) {
    //           this.layoutService.changeTheme(response.profileSettings?.theme.theme, response.profileSettings?.theme.colorScheme);
    //           this.selectedThemeObj = { theme: response.profileSettings?.theme.theme, colorScheme: response.profileSettings?.theme.colorScheme };
    //         } else {
    //           this.layoutService.changeTheme('lara-light-indigo', 'light');
    //           this.selectedThemeObj = { theme: 'lara-light-indigo', colorScheme: 'light' };
    //         }
    //         localStorage.setItem('sidebar',this.sidebarOption);
    //         localStorage.setItem('menuType',this.menuType);
    //         localStorage.setItem('Designer', this.formDesignerOption); 
    //         localStorage.setItem('defaultPage', this.defaultPage);
    //         this.layoutService.sidebarChange.next(this.sidebarOption)
    //       }
    //     } else {
    //       this.errmsg = response.error;
    //       this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
    //     }
    // });
  }

  checkManagerAccess(): boolean {
    if (this.userToken.ROLE === 'Manager') {
      return false;
    } else {
      return true;
    }
  }

  onSidebarOptionChange(event: any) {
    if (event?.value) {
      this.saveProfileSetting();
      // this.layoutService.sidebarChange.next(event.value)
    }
  }

  onMenuTypeChange() { // This function is not used
    if (this.menuType === 'Sidebar') {
      localStorage.setItem('menuType',this.menuType);
      this.saveProfileSetting();
      // this.layoutService.topOrSide.next(this.menuType)
    } else {
      localStorage.setItem('menuType',this.menuType);
      this.saveProfileSetting();
      // this.layoutService.topOrSide.next(this.menuType)
      this.layoutService.state.staticMenuDesktopInactive = true;
    }
  }

  onFormDesignerOptionChange() {
    localStorage.setItem('Designer', this.formDesignerOption);
    this.saveProfileSetting();
  }

  getDashboardList() {
    this.formService.getListOfDashboards(false).subscribe((response: any) => {
        if (response.status.toLowerCase() === "success") {
          if (response && response.dashboards && response.dashboards.length > 0) {
            this.listOfDashboards = response.dashboards;
            this.defaultDashboard = this.listOfDashboards.find((item) => {
              return item.default === true;
            });
            this.favoriteDashboard = this.listOfDashboards.filter(item => item.favorite);
          }
        } else {
          this.errmsg = response.error ? response.error : "";
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });  
        }
      });
  }

  onDefaultDashboardChange() {
    const data = { 
      "id": this.defaultDashboard.id, 
      "userId": this.defaultDashboard.userId, 
      "favourite": this.defaultDashboard.favorite, 
      "default": true, 
      "updateDefault": true 
    }
    this.formService.markDashboardFavorite(data).subscribe((response) => {
      if (response.status.toLowerCase() === 'success') {
      } else {
        this.errmsg = response.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });
      }
    });
  }

  onFavoriteDashboardChange(event: any) {
    const previousSelection = this.favoriteDashboard.slice(); // Make a copy of the previous selection
    this.favoriteDashboard = event.value; // Update the variable with the new dashboard

    // Determine selected and removed items
    const selectedItems = this.favoriteDashboard.filter(item => !previousSelection.includes(item));
    const removedItems = previousSelection.filter(item => !this.favoriteDashboard.includes(item));

    // Perform actions based on selection
    if (selectedItems.length > 0) { // on select
      const data = { 
        "id": selectedItems[0].id, 
        "userId": selectedItems[0].userId, 
        "favourite": true, 
        "default": selectedItems[0].default, 
        "updateDefault": false 
      }
      this.formService.markDashboardFavorite(data).subscribe((response) => {
        if (response.status.toLowerCase() === 'success') {
          // this.messageService.add({ severity:'success', summary:'Success', detail: "Successfully added as favorite!" });
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });  
        }
      });
    }
    if (removedItems.length > 0) {  // on remove
      const data = { 
        "id": removedItems[0].id, 
        "userId": removedItems[0].userId, 
        "favourite": false, 
        "default": removedItems[0].default, 
        "updateDefault": false 
      }
      this.formService.markDashboardFavorite(data).subscribe((response) => {
        if (response.status.toLowerCase() === 'success') {
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg });  
        }
      });
    }
  }

  onDefaultPageChange() {
    // call API to save page
    // localStorage.setItem('defaultPage', this.defaultPage);
    this.saveProfileSetting();
  }

  onChangeTheme(name: string, color: string) {
    this.layoutService.changeTheme(name, color);
    this.selectedThemeObj = { theme: name, colorScheme: color };
    this.saveProfileSetting();
  }

  // changeTheme(theme: string, colorScheme: string) {
  //   const themeLink = <HTMLLinkElement>document.getElementById('theme-css');
  //   const newHref = themeLink.getAttribute('href')!.replace(this.layoutService.config.theme, theme);
  //   this.layoutService.config.colorScheme
  //   this.replaceThemeLink(newHref, () => {
  //       this.layoutService.config.theme = theme;
  //       this.layoutService.config.colorScheme = colorScheme;
  //       this.layoutService.onConfigUpdate();
  //   });
  // }

  // replaceThemeLink(href: string, onComplete: Function) {
  //     const id = 'theme-css';
  //     const themeLink = <HTMLLinkElement>document.getElementById('theme-css');
  //     const cloneLinkElement = <HTMLLinkElement>themeLink.cloneNode(true);

  //     cloneLinkElement.setAttribute('href', href);
  //     cloneLinkElement.setAttribute('id', id + '-clone');

  //     themeLink.parentNode!.insertBefore(cloneLinkElement, themeLink.nextSibling);

  //     cloneLinkElement.addEventListener('load', () => {
  //         themeLink.remove();
  //         cloneLinkElement.setAttribute('id', id);
  //         onComplete();
  //     });
  // }

  async saveProfileSetting() {
    const obj = {
      userId: this.userToken.userName,
      userAvatar: "",
      updateAvatar: false,
      profileSettings: {
        sidebar: this.sidebarOption,
        menuType: this.menuType,
        formDesigner: this.formDesignerOption,
        defaultPage: this.defaultPage,
        defaultReport: this.defaultReport,
        theme: this.selectedThemeObj
      }
    }
    this.settingService.savePersonalization(obj)
    .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          // this.messageService.add({ severity: 'success', summary: 'Success', detail: "Profile settings saved!" });
          this.layoutService.getProfileSetting();
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
    });
  }

  showUploadPopup() {
    document.getElementById('imageInput').click();
  }

  fileChangeEvent(event: any): void {
    this.imgLoading = true;
    this.imageChangedEvent = event;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }

  imageLoaded(image: LoadedImage) {
    this.showCropper = true;
  }

  cropperReady() {
    this.imgLoading = false;
  }

  loadImageFailed() {
    this.imgLoading = false;
    this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Upload another photo!' });
    this.resetImageCropper();
  }

  saveProfilePhoto() {
    const obj = {
      userId: this.userToken.userName,
      userAvatar: this.croppedImage,
      updateAvatar: true,
      profileSettings: {}
    }
    this.settingService.savePersonalization(obj)
    .subscribe(response => {
        if (response.status.toLowerCase() === 'success') {
          this.messageService.add({ severity: 'success', summary: 'Success', detail: "Profile photo uploaded!" });
          this.resetImageCropper();
          this.layoutService.getProfileSetting();
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
    });
  }

  cancelProfilePhoto() {
    this.imageChangedEvent = null;
    this.croppedImage = null;
    this.showCropper = false;
    this.imgLoading = false;
    this.saveProfilePhoto();
  }

  resetImageCropper() {
    this.imageChangedEvent = null;
    this.croppedImage = null;
    this.showCropper = false;
    this.imgLoading = false;
  }

  getAllReports() {
    this.formService.getAllReports().subscribe({
      next: (res: any) => {
        if (res.status.toLowerCase() === 'success') {
          this.reportArr = res.reports;
          this.defaultReport = this.reportArr.filter(data => data.defaultReport === true)[0].reportId;
        } else {
          this.errmsg = res.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });  
        }
      }, 
      error: (error: any) => {
        this.errmsg = error.error;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
      }
    });
  }

  onDefaultReportChange() {
    localStorage.setItem('defaultReport', this.defaultReport);
    this.saveProfileSetting();
  }

}
