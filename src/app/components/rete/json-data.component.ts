import { Optional } from "@angular/core";
import { Inject } from "@angular/core";
import { Component } from "@angular/core";
// import { HotToastRef, HotToastService } from "@ngneat/hot-toast";

@Component({
  selector: "app-jsondata",
  template: `
      <div class="json-data">
        <div class="header">Submission Json:</div>
        <button mat-icon-button class="copy-button" matTooltip="copy tp clipboard"
          type="button" (click)="copy()"
          aria-label="icon-button with a copy icon">
          
        </button>
        <div>
          
        </div>
      </div>
    `,
    styles:[
      `

      .header {
        text-align: center;
        padding-top: 10px;
        color: darkgrey;
      }
      code {
       color: green;
      }
      .copy-button {
        position: absolute;
        top: 45px;
        right: 5px;
        padding: 0;
        color: darkgrey;
        overflow: visible;
        border: none;
        cursor: pointer;
      }
      .result-code{
        margin: 8px;
        padding: 24px 0px 4px 16px;
      }`
    ]
})
export class JsonDataComponent {
  constructor(
    // @Optional() @Inject(HotToastRef) public toastRef: HotToastRef<any>,
    // private toastService: HotToastService
  ) {}
  public copy() {
    // this.toastService.success('Copied to clipboard', {
    //   style: {
    //     border: "1px solid var(--success-color)",
    //     padding: "16px",
    //     color: "var(--success-color)",
    //   },
    //   duration: 2000,
    //   position: "top-right",
    // });
  }
}
