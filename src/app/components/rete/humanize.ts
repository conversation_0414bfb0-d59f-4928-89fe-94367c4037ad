import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
name: 'humanize'
})

 export class HumanizePipe implements PipeTransform {
 transform(value: string) {
 if ((typeof value) !== 'string' || value === 'OData') {
 return value;
 }
 if(value === 'ExecuteJavaScript') {
   return 'Execute JavaScript';
 }
 value = value.replace(/([A-Z]+)/g, " $1").replace(/([A-Z][a-z])/g, " $1");
 return value;
 }
}
