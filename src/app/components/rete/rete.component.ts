import { Compo<PERSON>, OnInit, After<PERSON>iew<PERSON><PERSON>t, <PERSON><PERSON>hild, On<PERSON><PERSON><PERSON>, Element<PERSON><PERSON>,<PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>, ViewChildren ,Renderer2, HostListener} from '@angular/core';
import { NodeEditor, Engine, Connection, Component as comp, Input } from 'rete';
import { AngularRenderPlugin } from 'rete-angular-render-plugin';
import ConnectionPlugin from 'rete-connection-plugin';
import AutoArrangePlugin from 'rete-auto-arrange-plugin';
import HistoryPlugin from 'rete-history-plugin';
import AreaPlugin from 'rete-area-plugin';
import ConnectionPathPlugin from 'rete-connection-path-plugin';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
/** Standard nodes import */
 import { StartComponent } from './nodes/start/start-component';
 import { ErrorComponent } from './nodes/error/error-component';
 import { SuccessComponent } from './nodes/success/success-component';
 import { Clipboard } from '@angular/cdk/clipboard';
/** Integration nodes import */
import { DatabaseOperationsComponent } from './nodes/integration-nodes/database-ops-component';
import { RESTComponent } from './nodes/integration-nodes/REST-component';                     
import { StoredProcedureComponent } from './nodes/integration-nodes/stored-procedure-component';
import { ActiveDirectoryComponent } from './nodes/integration-nodes/active-directory-component';
import { ODataComponent } from './nodes/integration-nodes/OData-component';
import { SAPComponent } from './nodes/integration-nodes/SAP-component';
import {FTPServicecomponent}from './nodes/integration-nodes/FTP-Service-component';

/** General nodes import */
import { ConditionComponent } from './nodes/general-nodes/condition-component';
import { SendEmailComponent } from './nodes/general-nodes/send-emails-component';
import { CalculateComponent } from './nodes/general-nodes/calculate-component';
import { InjectComponent } from './nodes/general-nodes/inject-component';
import { DataMapperComponent } from './nodes/general-nodes/data-mapper-component';
import { CSVMapperComponent } from './nodes/general-nodes/CSV-mapper-component';
import { ApprovalComponent } from './nodes/general-nodes/approval-component';
import { SubWorkflowComponent } from './nodes/general-nodes/sub-workflow-component';
import { ForLoopComponent } from './nodes/general-nodes/for-loop-component';
import { FileCatcherComponent } from './nodes/general-nodes/File-Cacher-component';
import { combineComponent } from './nodes/general-nodes/combine-nodes-component';

/** Form nodes import */
import { CreatePDFComponent } from './nodes/form-nodes/create-pdf-component';
import { CreateFormComponent } from './nodes/form-nodes/create-form-component';
import { UpdateFormComponent } from './nodes/form-nodes/update-form-component';
import { MasterDataUpdateComponent } from './nodes/form-nodes/masterdata-update-component';
import{ MasterDataRecordComponent} from './nodes/form-nodes/masterdata-record-component';
import { MasterDataReadComponent } from './nodes/form-nodes/masterdata-read-component';
import { SendAlertComponent } from './nodes/form-nodes/send-alert-component';
import { ArchiveFormComponent } from './nodes/form-nodes/archive-form-component';
import { ShareFormComponent } from './nodes/form-nodes/share-form-component';
import { ExternalUserComponent } from './nodes/form-nodes/external-user';
import { ReadFormComponent } from './nodes/form-nodes/read-form-component';
import { SearchFormComponent } from './nodes/form-nodes/search-form-component';
import { AssignFormComponent } from './nodes/form-nodes/assign-form-component';

/** Script nodes import */
import { ExecuteFunctionComponent } from './nodes/script-nodes/execute-pa-component';
import { ExecuteJavaScriptComponent } from './nodes/script-nodes/execute-javascript-component';
import { ExecutePythonScriptComponent } from './nodes/script-nodes/execute-pythonscript-component';

// import { ConditionNodeComponent } from './components/condition-node/condition-node.component';
 import { MyNodeComponent } from './nodes/general-nodes/node/node.component';
 import { combineSocket } from './sockets';
import { ActivatedRoute, NavigationExtras, ParamMap, Router } from '@angular/router';
import { nodes } from './nodes';

import { forkJoin, Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { JsonDataComponent } from './json-data.component';
import { Location } from '@angular/common';
import { MenuItem } from "primeng/api";
import { MessageService ,ConfirmationService} from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ReteService } from 'src/app/services/rete.service';
import { UtilsService } from 'src/app/services/utils.service';
import { JsonEditorComponent, JsonEditorOptions } from 'ang-jsoneditor';
import * as moment from 'moment';
import { OverlayPanel } from 'primeng/overlaypanel';
import { FormsService } from 'src/app/services/forms.service';
import { CreateFlowComponent } from '../Forms/create-flow/create-flow.component';
import * as dayjs from 'dayjs';
import { Sidebar } from 'primeng/sidebar';
import MinimapPlugin from 'rete-minimap-plugin';
import { FormsFlowsListComponent } from '../Forms/forms-flows-list/forms-flows-list.component';
@Component({
  selector: 'app-rete',
  templateUrl: './rete.component.html',
  styleUrls: ['./rete.component.scss'],
  providers: [MessageService,DialogService]
})
export class ReteComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('nodeEditor', { static: true }) el: ElementRef;
  @ViewChild('editor', { static: true }) elem: ElementRef;
  // @ViewChild('scrollMe') private myScrollContainer: ElementRef;
  @ViewChildren("commentDiv") commentDivs: QueryList<ElementRef>;
  @ViewChild(JsonEditorComponent, { static: false }) editor1: JsonEditorComponent;
  @ViewChild('sidebar') sidebar: Sidebar;
//for copy
  @HostListener('document:keydown.control.c', ['$event']) onKeydownHandlerCopy(event: KeyboardEvent) { 
    if(window.getSelection().type == "Range"){
      this.copyPaste = false;
       const selectedText = window.getSelection()?.toString();

       
      // this.clipboard.copy(selectedText);
    }else{   
    if(this.editor.selected.list[0] !== undefined){
         let nodeName = this.editor.selected.list[0].name;
       if(nodeName === 'Start' || nodeName === 'Success' || nodeName === 'Error'){
        this.copyPaste = true;
            this.messageService.add({ severity: 'error', sticky: true, summary: `${"Can't copy"} ${nodeName} ${"node"}`});
          }else{
            const selectedText = window.getSelection()?.toString();
            this.copyPaste = true;
            event.preventDefault();
            this.messageService.add({ severity: 'warn', sticky: false, summary: 'Copied, use Control-V to paste'});
            const jsonData = JSON.stringify(this.editor.selected.list[0], null, 2);
            this.clipboard.copy(jsonData);          
          }
        }
      }
}
//fpr paste
@HostListener('document:keydown.control.v', ['$event']) onKeydownHandlerPaste(event: KeyboardEvent) {  
  if(this.copyPaste == false){

  }else{
    this.pasteJsonFromClipboard();
    event.preventDefault();
  }
//  this.pasteJsonFromClipboard();
//  event.preventDefault();
}
//for delete
// @HostListener('document:keydown.delete', ['$event']) onKeydownHandlerDelete(event: KeyboardEvent) {

//   if(window.getSelection().type == "Range"){
//   }else{
//   if(this.editor.selected.list[0] !== undefined){
//     let nodeName = this.editor.selected.list[0].name;
//     if(nodeName === 'Start' || nodeName === 'Success' || nodeName === 'Error'){
//       this.messageService.add({ severity: 'error', sticky: true, summary: `${"Can't Delete"} ${nodeName} ${"node"}`});
//     }else{
//       // this.editor.removeNode(this.editor.selected.list[0]);
//       // this.reteservice.popNodeId(this.editor.selected.list[0].id);
//       this.deleteConfirem=true;
//     }
//   }
//   event.preventDefault();
// }
// }
copyPaste:boolean= false;
 //deleteConfirem: boolean = false;
  @ViewChild("op") op: OverlayPanel; 
  autoIncrementNodeZindex:number=1;
  public isExpanded: boolean;
  unSaved: boolean = false;
  nodecontainers: any;
  isRunOpen = false;
  public editor = null;
  runinfo = [];
  items: any[];
  ilist:any=[];
  public components = [];
  public workflowjson: any;
  public workflowid: string;
  public formid: string;
  formType: string
  public formfields: any;
  public queryconfig: any;
  public jsonfordeploydialog: any;
  allnodes = nodes;
  public workflowtitle: string;
  err: any;
  isDeployable$: Observable<boolean>;
  errmsg: any;
  runWfdialogRef: any;
  public baseUrl: string;
  public downloadBtn:boolean=false;
  public conversationId:any;
  public url:any;
  public testscroll='';
  public formsMasterData=[];
  sidebarVisible1: boolean = false;
  routerData: any;
  breadcrumbItems : MenuItem[];
  disp:boolean=false;
  pickedDeployOption: string;
  wfOptions: any;
  public formUserData: any;
  public editorOptions: JsonEditorOptions;
  formData:any;
  traceBtn=false;
  hideflowpanel:boolean=false;
  runTime:any;  enableDisable:boolean=false;autoIndexingSideBar:boolean=false;autoIndexingSideBar1:boolean=false;
  hideRunAndFormDataBtn:boolean=false;
  flowType: string;
  workflowName: string;
  webhookURL: string;
  dates:any={
    "lastSave":"",
    "lastPublish":"",
    "lastRun":"",
  }
  backgroundImage:any;
  menuType: string;
  formTitle: any;

  constructor(public layoutService: LayoutService,private clipboard: Clipboard,
    private router: Router,private confirmationService: ConfirmationService,
    private reteservice: ReteService,
    private route: ActivatedRoute,
    private utilservice: UtilsService,
    public location: Location,
    private messageService: MessageService, 
    public dialogService: DialogService,
    private formservice: FormsService,
    private ref: DynamicDialogRef,
  ) {
    // this.layoutService.state.staticMenuDesktopInactive = true; 
    this.getTheme();
    window.addEventListener('message', this.receiveMessage.bind(this));
    this.editorOptions = new JsonEditorOptions()
    this.editorOptions.modes = ['code', 'text', 'view'];
    this.editorOptions.mode='code';
    this.ilist = [
      {
          label: 'Save As...',
          icon: 'pi pi-copy',
          command: () => {
             this.saveAs();
          }
      },
  ];
  }
  breadCrum: MenuItem[];
  visible: boolean;
  countNumber:number=1;
  dispFormData:boolean=false;
  messages:any;  sidebarVisible2: boolean = false;baseZIndex:number=500;
  eventReceived:boolean=false;
  receiveMessage(event) {
    if (this.eventReceived != true) {
      this.eventReceived = true;
    if (event.origin !== window.location.origin) {
      return;
    }    
    if(event.data == "flowRun"){
     // this.runTrace();
     this.getflowTestData()
    }else if(event.data == "formData"){
      this.autoIndexingSideBar=false;
       this.getWfTestData();
       
    }else{

    }
    }

  }
  ngOnInit(): void { 
    this.menuType = localStorage.getItem('menuType');
    this.reteservice.clearNodeIdArray();
    this.messages = [{ severity: 'info', summary: 'Note', detail: 'Add Form Data' }];
    this.reteservice.openTestDataPanel$.subscribe((res)=>{   
      if(res == 'open'){
        this.autoIndexingSideBar=false;
        this.getWfTestData();
      }
    });
    this.reteservice.arrangeNodeConnections$.subscribe((res:any)=>{
      if(res.val){
        let node = res.node;
        setTimeout(() => {
          this.editor.view.updateConnections({ node });
        }, 500); 
      }
    });
    this.reteservice.warningMsg$.subscribe((res:any)=>{
      if(res == "noInputs"){
        this.messageService.add({ severity: 'warn', summary: 'Select Input fields',sticky: true});
      }
      else if(res == "noOutputs"){
        this.messageService.add({ severity: 'warn', summary: 'Select Output fields'});
      }else{
        this.messageService.add({ severity: 'warn', summary: 'Remember to map the Input fields',});
 
      }
    });
    this.reteservice.flowTechName$.subscribe((res:any)=>{
        if(res == 'change'){
          this.messageService.add({ severity: 'warn',sticky: true, summary: 'Remember to check next steps in the flow where result from this step is used as it may result in errors'});
        }
    });
    this.reteservice.openSidePanel$.subscribe((res:any)=>{
      if(res.panelName == 'runData'){
        let a = maxZIndex();
       this.autoIndexingSideBar1=true;      
       this.sidebar.el.nativeElement.style.zIndex = (a + 100).toString();
       this.hideRunAndFormDataBtn=true;
       //this.disp=res.panel;
      }
      if(res.panelName == 'testData'){
        this.sidebar.el.nativeElement.style.zIndex = (500).toString();
        this.autoIndexingSideBar=false;
        this.getWfTestData();
      }
    });
    this.reteservice.getRunflowToDialogClose$.subscribe((res:any)=>{
      if(this.disp){
        this.hideRunAndFormDataBtn=false;
      }else{
       // this.hideRunAndFormDataBtn=false;

      }
    })
    this.reteservice.odataMessages$.subscribe((res:any)=>{
      if(res.type == 'error'){
         this.messageService.add({ severity: 'error', sticky: true, summary: res.message});
      }
      if(res.type == 'warning'){
        this.messageService.add({ severity: 'warn', sticky: false, summary: res.message});
     }
      if(res.type == 'success'){
        this.messageService.add({ severity: 'success',sticky: false, summary: res.message});
      }
  });
  this.reteservice.copyPasteNodeData$.subscribe(async (res:any)=>{
    let renameDesc = res.data.description;
    let renameName = res.data.name;
    const {name, position: [x, y], ...params} = res;
    const component = this.editor.components.get(name);
    const newNode = await this.createNode(component, {...params, x: x + 450, y: y + 50});
    newNode.data.description = `${renameDesc}-${'copy'}`;
    newNode.data.name = `${renameName}${'copy'}`;
    this.editor.addNode(newNode);
    //this.editor.selected.clear();
  });

    function maxZIndex() {
      return Array.from(document.querySelectorAll('body *'))
            .map(a => parseFloat(window.getComputedStyle(a).zIndex))
            .filter(a => !isNaN(a))
            .sort()
            .pop();
 }
 
    this.route.paramMap.subscribe((params: ParamMap) => {
      this.routerData = decodeURIComponent(params.get('data') || "");
      this.routerData = JSON.parse(this.routerData);
      this.workflowid = this.routerData.wfId;
      this.formid = this.routerData.formId;
      this.formType = this.routerData.formType;
      this.formTitle = this.routerData.formTitle;
      this.workflowtitle = this.routerData.wfTitle;
      this.workflowName = this.routerData.wfName;
      this.flowType = this.routerData.flowType
     // this.getWorkflowdata();
    });
    this.baseUrl = this.utilservice.getUMPUrl();
    
    this.isDeployable$ = this.reteservice.isDeployable(this.workflowid, this.formType);
    // this.importformfields();
    this.reteservice.setformtype(this.formType)
       this.breadcrumbItems = [
        {label:'Home', routerLink: '/home'},
        {label:'Forms',routerLink: '/forms'},
        {label : this.workflowtitle}
      ];
     this.components = [
      //service components
        new RESTComponent(),
        new DatabaseOperationsComponent(),
        new StoredProcedureComponent(),
        new ActiveDirectoryComponent(),
        new ODataComponent(),
        new SAPComponent(),
        new FTPServicecomponent(),
      //general nodes
        new ApprovalComponent(),
        new DataMapperComponent(),
        new CSVMapperComponent(),
        new ConditionComponent(),
        new CalculateComponent(),
        new SendEmailComponent(),
        new InjectComponent(),
        new SubWorkflowComponent(),
        new ForLoopComponent(),
        new FileCatcherComponent(),
        new combineComponent(),
      //forms nodes
        new CreateFormComponent(),
        new UpdateFormComponent(),
        new AssignFormComponent(),
        new ReadFormComponent(),
        new SearchFormComponent(),
        new ShareFormComponent(),
        new ExternalUserComponent(),
        new ArchiveFormComponent(),
        new CreatePDFComponent(),
        new SendAlertComponent(),
        //master data
        new MasterDataUpdateComponent(),
        new MasterDataReadComponent(),
        new MasterDataRecordComponent(),
        //script node
        new ExecuteFunctionComponent(),
        new ExecuteJavaScriptComponent(),
        new ExecutePythonScriptComponent(),
     ];
     this.el.nativeElement.addEventListener('keydown', this.onKeyDown.bind(this));
  }
  onKeyDown(event: KeyboardEvent) {
    // Add your logic here for handling keyboard events
  }
 async getWorkflowdata() {
    this.reteservice.getworkflow(this.workflowid, this.formType).subscribe(async (res) => {
       if(res.hasOwnProperty('data')){
        this.workflowjson = res;
        if(this.workflowjson.hasOwnProperty('lastUpdated')){
          this.dates.lastSave = dayjs(this.workflowjson.lastUpdated).format('DD-MMM-YYYY h:mm:ss A');
        }
        if(this.workflowjson.hasOwnProperty('publishedAt')){
          this.dates.lastPublish =  dayjs(this.workflowjson.publishedAt).format('DD-MMM-YYYY h:mm:ss A');
        }
        if(this.workflowjson.hasOwnProperty('lastRun')){
          this.dates.lastRun = dayjs(this.workflowjson.lastRun).format('DD-MMM-YYYY h:mm:ss A');
        }
        this.enableDisable = this.workflowjson.disabled;
        this.reteservice.setflowActionType({"flowType":res.actionFlowType,"flowDesc":res.actionFlowTypeDesc,"workflowNodeInfo":res.workflowNodeInfo});
        this.reteservice.setDisableWfData(this.enableDisable)
        if (this.workflowjson) {
          if(Object.keys(this.workflowjson.data.nodes).length == 3){
          this.workflowjson.data.nodes[1].position=[-215,0]
          this.workflowjson.data.nodes[2].position=[-230,220]
          this.workflowjson.data.nodes[3].position=[0,220]
          this.reteservice.setIsNewWf(true);
        }else{
          this.reteservice.setIsNewWf(false);
        }  
         await this.editor.fromJSON(this.workflowjson.data);
          this.unSaved = false;
        }
        this.breadcrumbItems = [
          {label:'Home', routerLink: '/dashboard'},
          {label:'Forms',routerLink: '/forms'},
          {label:this.workflowjson.formTitle,routerLink:'/forms'},
          {label : "Flow :"+" "+" "+ this.workflowtitle}
        ];
      }else{
        this.breadcrumbItems = [
          {label:'Home', routerLink: '/home'},
          {label:'Forms',routerLink: '/forms'},
        ];
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: 'Something went wrong !' });
     }
    });
  }
  
  getWfTestData(){
    this.autoIndexingSideBar=true;
    if(this.dispFormData == false){
      this.reteservice.getWfTestData(this.formid, this.workflowid).subscribe(res=>{
        this.formData = res;
      });
      this.dispFormData = true;
     // this.disp = false;
      this.sidebarVisible2 = false;
    }
  }
  async ngAfterViewInit() {
    this.dispFormData = false;
    await this.getWorkflowdata();
    const container = this.el.nativeElement;    
    let selectednodes = [];
    this.editor = new NodeEditor('demo@0.2.0', container);
    this.editor['workflow'] = {
      wfId: this.workflowid,
      wfTitle: this.workflowtitle,
      formId: this.formid
    };
    this.editor.use(MinimapPlugin);
   this.editor.use(AngularRenderPlugin,{ component: MyNodeComponent });
    this.editor.use(ConnectionPlugin);
   this.editor.use(AutoArrangePlugin, { margin: { x: 74, y:40  }, depth: 25 ,vertical: true});
     this.editor.use(ConnectionPathPlugin, {
       transformer: () => ([x1, y1, x2, y2]) => [[x1, y1], [x1,y1+0.5 * Math.abs(y1 - y2)], [x2,y1+0.5 * Math.abs(y1 - y2)], [x2, y2]],
        curve: ConnectionPathPlugin.curveLinear,
         options: { vertical: true, curvature: 0 }, 
        //  arrow: () => true
        //  arrow: { color: 'black', marker: 'M-0,-10 L-0,10 L23,0 z' }
    });

    this.editor.use(AreaPlugin, {
      background: true,
      // snap: false,
      scaleExtent: { min: 0.6, max: 1.4 },
      //translateExtent: { width: 1000, height: 10 },
    });

    // AreaPlugin.zoomAt(this.editor, this.editor.nodes);
    this.editor.on('click', ({e}) => {
      this.editor.selected.clear();
      this.editor.nodes.map(n => n.update());
      // this.editor.nodes.map((data)=>{
      //  this.editor.view.updateConnections({ data });
      // })
      this.editor.selected.clear();
      
    });

    this.editor.on('rendernode', ({ el,node }) => {  
      el.style.overflowY = 'auto'
      el.addEventListener('wheel', e => {
        e.stopPropagation()
      })
      setTimeout(() => {
        this.editor.view.updateConnections({ node });
      }, 550);
    })

    this.editor.on('zoom', (e) => {    
      if (e.source === 'dblclick' || e.source === 'wheel') {
         let el = this.el.nativeElement;
        el.addEventListener('wheel', event => {
          if (event.deltaY < 0){
            this.el.nativeElement.scrollTop -= 3;
          }
          else if (event.deltaY > 0)
          {
            this.el.nativeElement.scrollTop += 3;
          }
     })
        return false;
      } else {
        return true;
      }
    });
    this.components.map((c) => {
      this.editor.register(c);
    });

    [new StartComponent(),new ErrorComponent(),new SuccessComponent()].map(c => {
      this.editor.register(c);
    });
    
    this.editor.on(['connectioncreate','connectionremove','nodecreate','noderemove','connectionpick'], (data) => {
      this.unSaved = true;

    });
     this.editor.on('nodecreated',(node)=>{
          setTimeout(async () => {
        await this.editor.view.updateConnections({ node });
      }, 550);
    })
    this.editor.view.resize();
    this.editor.trigger('process');
    this.editor.on('noderemove', (node) => {
      if (node.name === 'Start' || node.name === 'Success' || node.name === 'Error') {
        return false;
      } else {
        this.reteservice.setDeleteNodeData(node);
        return true;
      }
    });

    this.editor.bind('addconnectionanimation');
    this.editor.bind('removeconnectionanimation');
    this.editor.on('addconnectionanimation', (data) => {
      let connectionpaths = document.getElementsByClassName(`dashedpathanimatoion`) as HTMLCollectionOf<HTMLElement>;
      for (let i = 0; i < connectionpaths.length; i++) {
         connectionpaths[i].classList.add('animationpath');
      }
    });


    this.editor.on('connectioncreated', async (connection) => {
      const node = connection.input.node;
      const lastInputKey = `merge_${node.data.inputCount - 1}`;
    
      if (connection.input.key === lastInputKey) {
        const newIndex = node.data.inputCount;
        const newInput = new Input(`merge_${newIndex}`, `Input ${newIndex + 1}`, combineSocket);
        node.addInput(newInput);
        node.data.inputCount++;
        setTimeout(async () => {
          await this.editor.view.updateConnections({ node });
        }, 50);
      }
    });

    this.editor.on('connectionremoved', async (connection) => {
      const node = connection.input.node;
      while (node.inputs.size > 1) {
        const lastInputKey = `merge_${node.data.inputCount - 1}`;
        const lastInput = node.inputs.get(lastInputKey);
    
        if (lastInput && lastInput.connections.length === 0) {
          node.removeInput(lastInput);
          node.data.inputCount--;
        } else {
          break;
        }
      }
      setTimeout(async () => {
        await this.editor.view.updateConnections({ node });
      }, 50);
    });


    this.editor.on('removeconnectionanimation', (data) => {
      let connectionpaths = document.getElementsByClassName(`dashedpathanimatoion`) as HTMLCollectionOf<HTMLElement>;
      for (let i = 0; i < connectionpaths.length; i++) {
        connectionpaths[i].classList.remove('animationpath');
      }
    });

    //Z-indexing OF NODE ON CLICK and Node socket connection auto
    this.editor.view.on("nodeselected", (node,el) => {
      if(node.name === 'Start' || node.name === 'Success' || node.name === 'Error'){
      let id;  
      const nodeView = this.editor.view.nodes.get(node);
      const nodeElement = nodeView.el;
      this.reteservice.setNodeButtonId(id);
      nodeElement.style.zIndex=this.countNumber;
      const parentElement = nodeElement.parentElement!;      
      this.countNumber=this.countNumber+1;
      this.reteservice.setNodeIndex(this.countNumber)
      }else if(node.data.category === "Others"){
        const nodeView = this.editor.view.nodes.get(node);
      const nodeElement = nodeView.el;
      let id=nodeElement.childNodes[1].childNodes[1].childNodes[0].childNodes[0].childNodes[0].id;
      let labeledId = id + "-label";
       this.reteservice.setNodeButtonId(labeledId);
      nodeElement.style.zIndex = this.countNumber;
      this.countNumber += 1;
      this.reteservice.setNodeIndex(this.countNumber);
      }else{
      let id;  
      const nodeView = this.editor.view.nodes.get(node);
      const nodeElement = nodeView.el;
      id=nodeElement.childNodes[1].childNodes[2].childNodes[0].childNodes[0].childNodes[0].childNodes[0].childNodes[6].childNodes[1].id;
      this.reteservice.setNodeButtonId(id);
      nodeElement.style.zIndex=this.countNumber;
      const parentElement = nodeElement.parentElement!;      
      this.countNumber=this.countNumber+1;
      this.reteservice.setNodeIndex(this.countNumber)
      setTimeout(async () => {
        await this.editor.view.updateConnections({ node });
      }, 550);
     }
    });

    this.editor.on('renderconnection', ({ el, connection }) => {
      const mainPath = el.querySelector('path');
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('id', 'dashedpath');
      path.setAttribute('class', 'dashedpathanimatoion');
      if (mainPath && mainPath.parentElement) {
        path.setAttribute('d', mainPath.getAttribute('d') || '');
        mainPath.parentElement.appendChild(path);
      }
    });

    this.editor.on('connectioncreate', ({ output, input }) => {            
      if(output.key === 'ForLoop_success') {
        if(input.node.name === 'ForLoop' || input.node.name === 'execjavascript' ||
        input.node.name === 'execpython' || input.node.name === 'response' ||
        input.node.name === 'approvalworkflow' || input.node.name === 'calculate' || input.node.name === 'condition') 
        {
          return false;
        }else{
          return true;
        }
      }else{
        return true
      }
    });

    this.editor.on("multiselectnode", (args) => {
      args.accumulate = args.e.ctrlKey || args.e.metaKey;

      if (args.accumulate) {
        if (!this.editor.selected.contains(args.node)) {
          selectednodes.push(args.node);
        }
      } else {
        selectednodes = [];
        selectednodes[0] = args.node;
      }
    });
    this.changeTransform(850, 100, 1);
    this.reteservice.setEditorData(this.editor);
    this.reteservice.setFlow(this.workflowid,this.formType);
   await this.getworkflowstatus();    
  }
  changeTransform(x: number, y: number, scale: number) {
    const area = this.editor.view.area;
    area.transform.x = x;
    area.transform.y = y;
    area.transform.k = scale;
    area.update();
  }
  canDeactivate(): Observable<boolean> | boolean {
    if (this.unSaved) {
      const result = window.confirm('There are unsaved changes! Are you sure?');
      return of(result);
    }
    return true;
  }

  zoomAt(k: number) {
    const { area, container } = this.editor.view;
    const rect = area.el.getBoundingClientRect();
    const ox = (rect.left - container.clientWidth / 2) * k;
    const oy = (rect.top - container.clientHeight / 2) * k;
    area.zoom(area.transform.k + k, ox, oy);
  }

  importformfields() {
    this.reteservice.importformfields(this.formid)
      .subscribe((res) => {
        this.formfields = res;
      });
  }

  Arrangenodes() {
    this.editor.trigger('arrange', { node: this.editor.nodes[0] });
  }

  async savedata() {
    const data = this.editor.toJSON();
    this.unSaved = false;
    this.reteservice.saveWfdata(this.workflowid, data, false, this.formType);
  }

 async getflowTestData(){
    this.reteservice.getWfTestData(this.formid, this.workflowid).subscribe(res => {
      const response = res;
      this.formData = res;
       this.disp = true;
      this.sidebarVisible2 = true;
        if ((this.utilservice.isObjEmpty(response.testData))) {
          this.traceBtn=true
          this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Create test data by clicking on the "Form Data" button and then try to run'});
        }else {
          if(!response.taskId) {
            this.traceBtn=true
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Form Data is not setup correctly,Save and try again'});
          } else {
            this.traceBtn=false
          }
        }
    });
  }
  nodeNameDesc:any=[];
 async runTrace(){
    this.sidebar.el.nativeElement.style.zIndex = ('500').toString();
    this.hideRunAndFormDataBtn=false;
    this.nodeNameDesc=[];
    let nodeObj:any=[];
    nodeObj = this.editor.toJSON().nodes;
    for(let key in nodeObj){
      if(nodeObj[key].name === 'Start' || nodeObj[key].name === 'Success' || nodeObj[key].name === 'Error'){
      }else{
        let nodelist = {
          name:nodeObj[key].data.name,
          description:nodeObj[key].data.description
        }
        this.nodeNameDesc.push(nodelist)
      }
    }    
    this.downloadBtn = false;
    this.dispFormData = false
     this.runinfo = [];
     let nodeIdlist = this.reteservice.checkNodeIdArray();
     if(nodeIdlist.length > 0){
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Close all nodes'});
     }else{
    this.reteservice.saveworkflowdata(this.workflowid,this.editor.toJSON(),false, this.formType,this.formid,'justDeploy', 'justDeploy', this.workflowjson.wfName,false).subscribe(async (res) => {
       if(res.error == '' && res.status.toLowerCase() == 'success'){
        this.reteservice.getWfTestData(this.formid, this.workflowid).subscribe(res => {
            const response = res;
            this.formData = res;
             this.disp = true;
            this.sidebarVisible2 = true;
              if ((this.utilservice.isObjEmpty(response.testData))) {
                this.traceBtn=true
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Create test data by clicking on the "Form Data" button and then try to run'});
              }else {
                if(!response.taskId) {
                  this.traceBtn=true
                  this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Form Data is not setup correctly,Save and try again'});
                } else {
                  this.traceBtn=false
                }
              }
          });
     //  await this.getflowTestData();
      }else{
         this.disp = false;
        this.sidebarVisible2 = false;
          this.messageService.add({ severity: 'error', summary: 'Error',sticky: true, detail: res.error});
      }
      this.hideflowpanel=true;
      this.autoIndexingSideBar1=false;
    })
     }
  }

  downloadLog() {
    this.reteservice.downloadWf(this.conversationId).subscribe({
      next: (data) => 
      {
        data = data;
        this.downloadFile(data,`${this.conversationId}_SUCCESS_WF-log.log`);
      },
      error: (e) => 
      {
        e=e.error.text;
        this.downloadFile(e,`${this.conversationId}_Wf-log.log`);
      }  
    });
  }

  downloadFile(data: any, filename: string) {
     const blob = new Blob([data], { type: 'octet/stream' });
    const a = document.createElement('a');
    document.body.appendChild(a);
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = filename;
    a.click();
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }, 0);
  }
  closePanel(){
    this.disp = !this.disp
    this.downloadBtn=false;
    this.runinfo=[];
    this.runHistory=false;
    this.nodeNameDesc=[];
    // this.RunSuccess=false;
    this.sidebar.el.nativeElement.style.zIndex = (500).toString();
    this.hideRunAndFormDataBtn=false;
    this.reteservice.setRunflowToDialog(false);
  }

  closeTestDataPanel(){
    this.nodeNameDesc=[]
    this.dispFormData = !this.dispFormData;
    this.runinfo=[];
    this.runHistory=false;
     this.disp=false;
     this.sidebar.el.nativeElement.style.zIndex = (500).toString();
  }

  public copy() {
    this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Copied to clipboard' });
  }
 
  parseJson(objectToparse: any) {
    Object.entries(objectToparse).forEach(([key, value]: [string, string]) => {
     if(typeof value !== 'boolean') {
      if (this.isJsonParsable(value)) {
        objectToparse[key] = value;
      } else {
        objectToparse[key] = value;
      }
     }
    });
  }

  isJsonParsable(str: string) {
    try {
      str;
    } catch (e) {
      return false;
    }
    return true;
  }

  async addNodeComponent(component: comp) {
    const node = await component.createNode({});
    const { container } = this.editor.view.area;
    const [hw, hh] = [container.clientWidth / 2, container.clientHeight / 16];
    const { x, y, k } = this.editor.view.area.transform;
    node.position = [(hw - x-800) / k, (hh - y+150) / k]; // coordinates of the center relative to the viewport in the coordinate system of the scheme
    this.editor.addNode(node);
    const nodeerroutput = `${component.name}_error`;
    if(nodeerroutput && node.outputs.get(nodeerroutput)) {
    this.editor.connect(node.outputs.get(nodeerroutput), this.editor.nodes[2].inputs.get("Error"));
   }
  }

  updateNode(){
    this.editor.on('updateconnection',({el,connection,points})=>{
    });
  }

  ngOnDestroy() {
   
  }
  
  getworkflowstatus() {
    this.reteservice.getWorkflowDeployOptions(this.workflowid, this.formid,this.formType).subscribe((res) => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.wfOptions = response;
            this.pickedDeployOption = this.wfOptions.currentState;
            if (this.wfOptions && this.wfOptions.state && this.wfOptions.state[this.wfOptions.currentState]) {
              let obj = this.wfOptions?.state[this.wfOptions.currentState]
              let arr = [];
              for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                  let item = { key: `${key}`, value: `${obj[key]}` }
                  arr.push(item)
                }
              }
              this.wfOptions.state[this.wfOptions.currentState] = arr;
            }
          }
        }
        else {
          this.errmsg = response.error;
        }
      });         
  }

  saveWorkflow(saveType:boolean){
    let nodeIdlist = this.reteservice.checkNodeIdArray();
    if(nodeIdlist.length > 0){
     this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Close all nodes to publish flow'});
    }else{
    this.reteservice.saveworkflowdata(this.workflowid,this.editor.toJSON(),false, this.formType,this.formid,'justDeploy', 'justDeploy', this.workflowjson.wfName,saveType).subscribe((res) => {
      if (res.status.length > 0) {
        this.messageService.add({ severity: 'success', summary: 'Success', detail: res.status });
      }
      if (res.error.length > 0) {
        this.messageService.add({ severity: 'error', summary: 'Error',sticky: true, detail: res.error});
      }
    });
  }
  }

   goToPreview(msg:any) {
    this.eventReceived = false;
    this.autoIndexingSideBar=false;
    this.dispFormData = false;
    let data = {
      submission:{},
      formId: this.formid,
      type: 'form',
      wfId:this.workflowid,
      iswftestdata: true,
      message:msg
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');

  }

  runWorkflow(){    
    this.runHistory=false;
    this.runinfo=[];
    let data:any={};
    let start; 
    let end;
          this.reteservice.saveWfTestForm(this.formid,this.formData.testData, this.formData.eventData,this.workflowid,this.formData.flowInput,this.formUserData)
              .pipe(delay(100)).subscribe((res) => {
          const response = res;
          if (response.error === "") {
            if (response.status === "Success") {
                  const token = localStorage.getItem('token');
                  const guid = this.utilservice.generateRandomString(32);
                  const url = `${this.baseUrl}/UMP/push?apiToken=${token}&sessionId=${guid}`;  
                  start = Date.now();             
                  this.utilservice.getServerSentEvent(url).subscribe(
                    async (data) => {    
                      let res = await data.data;
                      const resdata1 = await JSON.parse(res)
                      const resdata = await JSON.parse(resdata1.NotificationMessage); 
                      if(!resdata.hasOwnProperty('input') && !resdata.hasOwnProperty('output')){
                          for(let key2 in this.nodeNameDesc){
                          if(resdata.stepName == this.nodeNameDesc[key2].name){
                            resdata.stepName = this.nodeNameDesc[key2].description
                          }
                        }
                      }                  
                      if (resdata.input) {
                        this.conversationId=resdata.input.convId;
                        if(this.isJsonParsable(resdata.output)){                    
                        this.parseJson(resdata.input);
                      }
                      }
                      if (resdata.output) {                   
                        if(this.isJsonParsable(resdata.output)){
                        this.parseJson(resdata.output);
                      }
                      }
                      this.runinfo.push(resdata);  
                      end = Date.now();
                      this.runTime = (end - start);                      
                    });
                    
                const wfNameSpace = `${localStorage.getItem('domain').toLowerCase()}.flow`;
                const wfType = 'workflow';
                  this.reteservice.getRunWfEvents( this.workflowjson.wfName, wfNameSpace, wfType ,  this.formData.testData,  this.formData.eventData, this.formid, guid,  this.formData.flowInput,  this.formData.taskId,  this.formData.formUsers)
                  .pipe(delay(500)).subscribe(
                    res => {
                      if (res.error) {
                        this.downloadBtn=true;
                        this.testscroll='';                        
                         this.messageService.add({ severity: 'error', summary: 'Error',sticky: true , detail: res.error });
                      } else {
                        this.downloadBtn=true;
                        this.testscroll='end';
                        this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Workflow run complete' });
                      }
                      if(this.runinfo.length > 0){
                        this.saveRunFlowData(true);
                         } 
                },
                err => {
                  this.downloadBtn=true;
                  this.messageService.add({ severity: 'error', summary: 'Error',sticky: true , detail: err.error.error});
                  if(this.runinfo.length > 0){
                    this.saveRunFlowData(true);
                     } 
              });
              this.downloadBtn=false; 
              this.hideflowpanel=false;
               
            } else {
              this.errmsg = response.error;
              this.messageService.add({ severity: 'error', summary: 'Error',sticky: true , detail: response.error});
            }
          }
        });
  }

//disbale flow
  disableWorkFlow(data:boolean){
      this.reteservice.disableFlow(this.workflowid,data).subscribe(res=>{
        if(res.status.toLowerCase() == 'success'){
          if(data == true){
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'WorkFlow Disabled Successfully'});
             window.location.reload();
          }else{
            this.messageService.add({ severity: 'success', summary: 'Success', detail: 'WorkFlow Enabled Successfully'}); 
              window.location.reload();
          }
        } else{
          this.messageService.add({ severity: 'error', summary: 'Error',sticky: true, detail: "ERROR"});
        } 
      });
  }
//save flow data
// RunSuccess:boolean=false;
  saveRunFlowData(lastRun:boolean){
// this.RunSuccess=true;
 this.reteservice.setRunflowToDialog(true);
    this.reteservice.saveFlowData(this.workflowid,this.formid,this.formData.flowInput,this.formData.testData,this.runinfo,this.runTime,lastRun).subscribe(res=>{
        if(res.status.toLowerCase() == 'success'){
          this.dates.lastRun = dayjs(res.lastRunAt).format('DD-MMM-YYYY h:mm:ss A');
          if(!lastRun){
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Run Data Saved'});
          }else{

          }
        }else{
          this.messageService.add({ severity: 'error', summary: 'Error',sticky: true, detail: res.error});
        }
    });
  }
//to display run history list dropdown
  runList(){    
    this.reteservice.getAllWfRunList(this.workflowid).subscribe(res=>{      
      this.items=[];
      if(res.status.toLowerCase() == 'success'){
        // this.RunSuccess=true;
         this.reteservice.setRunflowToDialog(true);
          if(res?.allRuns?.length > 0){
            for(let key in res.allRuns){
              if(res.allRuns[key].latestRun == true){
                let format1 = dayjs(res.allRuns[key].createdAt).format('DD-MMM-YYYY h:mm:ss A');
                let date1 = new Date(Number(res.allRuns[key].createdAt)).toLocaleString();
                let data1={
                  label: 'Last run' +' '+ date1 +' '+'('+  (moment(date1).from(moment())) +')',
                  runId:res.allRuns[key].runId,
                  workflowId:res.allRuns[key].workflowId,
                  divide:true
                 }
                 this.items.unshift(data1)
              }else{
                let format2 = dayjs(res.allRuns[key].createdAt).format('DD-MMM-YYYY h:mm:ss A');
                let date2 = new Date(Number(res.allRuns[key].createdAt)).toLocaleString();
                let data2={
                 label: date2 +' '+'('+  (moment(date2).from(moment())) +')',
                 runId:res.allRuns[key].runId,
                 workflowId:res.allRuns[key].workflowId,
                 divide:false
                }
                this.items.push(data2)
              }
            }
          }else{
              // this.op.hide();
              // this.messageService.add({ severity: 'error', summary: 'Error', detail: "No Previous Run Data Found"});
          }
      }else{
        this.messageService.add({ severity: 'error', summary: 'Error',sticky: true, detail: res.error});
      }
    });
  }
  runHistory:boolean=false
  //to get run history by ID
  getRunHistoryByID(data:any){ 
    this.formData={};
    this.runHistory=true;
    this.dispFormData=false
    this.hideflowpanel=true;
     this.disp = true;
    this.sidebarVisible2 = true;
    this.downloadBtn = false;
    this.reteservice.getWfRunData(data.runId,data.workflowId).subscribe(res=>{            
      if(res.status.toLowerCase() == 'success'){
      this.formData!.flowInput=JSON.parse(res.flowInput);
      this.formData!.submissionData=res.formData;
      this.formData!.dateTime = new Date(Number(res.createdAt)).toLocaleString() ;
      // this.formData!.dateTime = dayjs(res.createdAt).format('DD-MMM-YYYY h:mm:ss A');
      this.formData!.runTime = `${((res.totalRunTime/1000)%60)}`
      this.runinfo = res.runData;
      this.messageService.add({ severity: 'success', summary: 'Success', detail: ''});
      }else{
        this.messageService.add({ severity: 'error', summary: 'Error', detail: "ERROR"});
      }
      this.op.hide();
    });
  }

  openFlowDialog() {
    // let data = this.formservice.getNavigationData();
    const ref = this.dialogService.open(FormsFlowsListComponent,{
      header: `Flows (${this.formTitle})`,
      width: '60%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        formId: this.formid,
        formType: this.formType,
        formTitle: this.formTitle
      }
    });
    // if (data.formId || data.formType) {
    //   data.isFlow = true;
    //   this.formservice.setNavigationData(data);
    // }
    this.router.navigate(['forms']);
    if (this.layoutService.state.staticMenuDesktopInactive === false) {
      this.layoutService.state.staticMenuDesktopInactive = true;
    }
  }

  saveAs() {
    const ref = this.dialogService.open(CreateFlowComponent,{
      header: `Copy Flow`,
      width: '60%',
      contentStyle: { 'overflow-y': 'scroll' },
      data: {
        type: "workflow",
        iscopy: true,
        isupdate: false,
        create: false,
        add: false,
        wfdata: {
          wfTitle: this.workflowjson.wfTitle,
          wfName: this.workflowjson.wfName,
          wfId: this.workflowjson.wfId,
          formType: this.formType,
          formId: this.workflowjson.formId,
          wfDesc: this.workflowjson.wfDesc,
        },
        flowType: this.routerData.flowType,
      }
    });
    ref.onClose.subscribe((data: any) => {
      if (data) {
        this.goToFlow(data);
      }
    });
  }

  goToFlow(data: any) {
    let obj = {
      wfTitle: data.wfTitle,
      wfId: data.wfId,
      formType: this.formType,
      formId: this.formid,
      flowType: data.flowType
    }
    this.ref.close();
    const routerData = JSON.stringify(obj);
    this.router.navigate(['flows/flow-editor/', routerData]);
    this.ngOnInit(); 
    setTimeout(() => {
      this.ngAfterViewInit();
    }, 1000);
  }

  mapDisp:boolean=false
  displayMinimap(data:boolean){
    const element = <HTMLElement> document.getElementsByClassName('minimap')[0];
    if(data){
    element.style.display = 'none';
    }else{
      element.style.display = 'block';
    }
  }

  getTheme() {
    this.layoutService.getProfileData().subscribe((res: any)=>{       
      if (res?.profileSettings?.theme) {
        if (res.profileSettings?.theme.colorScheme === 'light')
        this.backgroundImage = 'linear-gradient(90deg,#ececec  1px,transparent 0),linear-gradient(180deg,#ececec 1px,transparent 0) !important'
      } else {
       // this.backgroundImage = 'linear-gradient(90deg, rgb(38 38 38) 1px, transparent 0px), linear-gradient(rgb(38 38 38) 1px, transparent 0px) !important';
      }
    });
  }

  goBack(str: string) {
    if (str == 'forms') {
      this.router.navigate(['forms']);
    } else {
      this.router.navigate(['home']);
    }
    if (this.layoutService.state.staticMenuDesktopInactive === true) {
      this.layoutService.state.staticMenuDesktopInactive = false;
    }
  }

  async createNode(component, { data = {}, meta = {}, x = 0, y = 0 }) {
    const node = await component.createNode(JSON.parse(JSON.stringify(data)));
    node.meta = Object.assign(JSON.parse(JSON.stringify(meta)), node.meta);
    node.position[0] = x;
    node.position[1] = y;
    return node;
}

 async deepCopy(obj) {
  return JSON.parse(JSON.stringify(obj));
}

async pasteJsonFromClipboard() {
  // const text = await navigator.clipboard.readText();
  // this.reteservice.copyPasteNodeData.next(JSON.parse(text));
  try {
    const text = await navigator.clipboard.readText();        
      this.reteservice.copyPasteNodeData.next(JSON.parse(text));
  } catch (err) {
    this.messageService.add({ severity: 'warn', sticky: false, summary: 'copy to clipboard'});
  }
}

// confiremDeleteNode(){
//   this.deleteConfirem = false;
//   this.editor.removeNode(this.editor.selected.list[0]);
//       this.reteservice.popNodeId(this.editor.selected.list[0].id);
// }
 }



