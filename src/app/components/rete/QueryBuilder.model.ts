import { Rule } from 'ngx-angular-query-builder';
export interface RuleSet {
  condition: string;
  index?:number;
  rules: Array<RuleSet | MyRule>;
  collapsed?: boolean;
  isChild?: boolean;
}
export interface MyRule extends Rule{
  field: string;
  value?: any;
  operator?: string;
  entity?: string;
  checked?:boolean,
  key?:number
}
export interface Option {
  name: string;
  value: any;
}
export interface FieldMap {
  [key: string]: Field;
}
export interface Field {
  name: string;
  value?: string;
  type: string;
  index:number;
  nullable?: boolean;
  options?: Option[];
  operators?: string[];
  defaultValue?: any;
  defaultOperator?: any;
  entity?: string;
  validator?: (rule: MyRule, parent: RuleSet) => any | null;
}
export interface EntityMap {
  // [key: string]: Entity;
}
export interface QueryBuilderConfig {
  fields: FieldMap;
  entities?: EntityMap;
  allowEmptyRulesets?: boolean;
  getOperators?: (fieldName: string, field: Field) => string[];
  getInputType?: (field: string, operator: string) => string;
  getOptions?: (field: string) => Option[];
  addRuleSet?: (parent: RuleSet) => void;
  addRule?: (parent: RuleSet) => void;
  removeRuleSet?: (ruleset: RuleSet, parent?: RuleSet) => void;
  removeRule?: (rule: Rule, parent: RuleSet) => void;
  coerceValueForOperator?: (operator: string, value: any, rule: Rule) => any;
  calculateFieldChangeValue?: (currentField: Field, nextField: Field, currentValue: any) => any;
}