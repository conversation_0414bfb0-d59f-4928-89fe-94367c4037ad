
    <div class="card mb-0">
        <p-toolbar styleClass="mb-2 p-0 px-3">
            <ng-template pTemplate="left">
                <div class="flex flex-row w-100 ml-2">
                    <button class="p-link layout-menu-button layout-topbar-button mr-4" (click)="layoutService.onClickSidebar()" (mouseenter)="layoutService.onButtonHover()">
                        <i class="pi pi-bars"></i>
                    </button>
                    <div class="route-bar-breadcrumb mr-4">
                        <span>
                            <span class="highlight" (click)="goBack('home')">Home</span>
                            <span class="highlight" (click)="goBack('forms')"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> Forms </span>
                            <span> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> {{workflowjson?.formTitle}} </span>
                            <span class="highlight" (click)="openFlowDialog()"> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> Flows </span>
                            <span> <i class="pi pi-chevron-right" style="font-size: 10px;padding: 0px 8px"></i> {{workflowtitle}} </span>
                        </span>
                    </div>
                </div>
            </ng-template>
            <ng-template pTemplate="right">
                <button type="button" *ngIf="mapDisp" pButton pRipple icon="pi pi-image" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Show MiniMap" (click)="displayMinimap(mapDisp = !mapDisp)"></button>
                <button type="button" *ngIf="!mapDisp" pButton pRipple icon="pi pi-map" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Hide MiniMap" (click)="displayMinimap(mapDisp = !mapDisp)"></button>
                <button type="button" pButton pRipple icon="pi pi-search-plus" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Zoom In" (click)="zoomAt(0.1)"></button>
                <button type="button" pButton pRipple icon="pi pi-search-minus" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Zoom Out" (click)="zoomAt(-0.1)"></button>
                <button type="button" pButton pRipple icon="pi pi-sitemap" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Arrange" (click)="Arrangenodes()"></button>
                <button type="button" *ngIf="!enableDisable" pButton pRipple icon="pi pi-ban" class="p-button-text mr-2 p-button-lg" tooltipPosition="bottom" pTooltip="Disable" (click)="disableWorkFlow(enableDisable = !enableDisable)"></button>
                <button type="button" *ngIf="enableDisable" pButton pRipple class="p-button-text mr-2 p-button-sm" tooltipPosition="bottom" pTooltip="Enable" ><span class="material-icons" style="font-size:24px;" (click)="disableWorkFlow(enableDisable = !enableDisable)">done_all</span></button>
                <!-- <p-button label="Data JSON" size="small" tooltipPosition="bottom" pTooltip="Data JSON" styleClass="p-button-sm mr-2" (onClick)="getWfTestData()" ></p-button> -->
                <p-splitButton label="Publish"  [model]="ilist" size="small" tooltipPosition="bottom" pTooltip="Publish"
                [disabled]='enableDisable' styleClass="p-button-sm mr-2" (onClick)="saveWorkflow(false)"></p-splitButton>
                <p-splitButton icon="pi pi-play"  label="Run Flow" styleClass="p-button-sm" tooltipPosition="left" pTooltip="Run" 
                [disabled]='enableDisable'  (onClick)="runTrace()" (onDropdownClick)="[op.toggle($event),runList(),$event.stopPropagation()]"></p-splitButton>
                
            </ng-template>
        </p-toolbar>
        <p-toast class="retetoast">
        </p-toast>

        <div class="flex" style="position: relative !important;" > 
        <div #editor class="editor" [style.background-image]="backgroundImage">
        <div #nodeEditor class="node-editor" >
        </div>
        </div>
        <div *ngIf="this.workflowjson" class="card border-noround" style="position: absolute !important;top: 5px; right: 5px;padding: 10px;display: inline-grid;">
            <span *ngIf="this.dates.lastSave"><b>Last Save :</b> {{dates.lastSave}}</span>
            <p-divider *ngIf="this.dates.lastSave  && (this.dates.lastPublish || this.dates.lastRun)" class="retDivider"></p-divider>
            <span *ngIf="this.dates.lastPublish"><b>Last Publish :</b> {{dates.lastPublish}}</span>
            <p-divider *ngIf="this.dates.lastRun && this.dates.lastPublish" class="retDivider"></p-divider>
            <span *ngIf="this.dates.lastRun"><b>Last Run :</b> {{dates.lastRun}}</span>
        </div>
        <p-sidebar #sidebar  class="sideBar" [(visible)]="disp" [closeOnEscape]="false"  [modal]="false" position="right" [showCloseIcon]="false" >
         <!--Run Transe start-->
        <ng-template pTemplate="header">
            <p-toolbar styleClass="p-2 w-full" class="runToolbar">
                <ng-template pTemplate="left">
                    <div> 
                        <button type="button" pButton pRipple icon="pi pi-times" class="p-button-text mr-2" tooltipPosition="bottom" pTooltip="Close" (click)="closePanel()"></button>
                    </div>
                    <div class="flex flex-row w-100 ml-2 font-semibold text-lg" >
                        Trace <span *ngIf="this.formData!.dateTime">&nbsp;&nbsp;{{this.formData!.dateTime}}</span><span>&nbsp;&nbsp;</span><span *ngIf="this.formData!.runTime" style="color: var(--primary-color);">{{this.formData!.runTime}} <span *ngIf="this.formData!.runTime">s</span></span>
                    </div>
                </ng-template>
                    
                <ng-template pTemplate="right">
                    <button *ngIf="downloadBtn" type="button" pButton pRipple icon="pi pi-save" class="p-button-text mr-2" tooltipPosition="bottom" pTooltip="Save" (click)="saveRunFlowData(false)"></button>
                    <div *ngIf="downloadBtn"> 
                        <button type="button" pButton pRipple icon="pi pi-download" class="p-button-text mr-2" tooltipPosition="bottom" pTooltip="Download Log " (click)="downloadLog()"></button>
                    </div>
                </ng-template>
            </p-toolbar>
        </ng-template>
        <ng-template pTemplate="content">
        <div  class="mb-0 border-noround p-1">
            <div style="overflow: auto;">
                <p-accordion class="w-full accord" [multiple]="true" [activeIndex]="[0]" *ngIf="!runHistory">
                    <p-accordionTab header="Flow Input">
                        <textarea id="float-input"  pInputTextarea  [(ngModel)]="formData.flowInput" autoResize="true" class="w-full"  style="max-height: 350px;min-height: 100px;"></textarea>
                    </p-accordionTab>
                </p-accordion>
                <div style="text-align: center;" *ngIf="!runHistory && !hideRunAndFormDataBtn">
                    <div class="formgrid grid mt-3 mx-3">
                        <div class="field col">
                            <p-button label="Form Data" [text]="true" [raised]="true"  tooltipPosition="bottom" pTooltip="Form Data" styleClass="w-full p-button-sm" (onClick)="goToPreview('flowRun')"></p-button>
                        </div>
                        <div class="field col" *ngIf="!runinfo.length > 0">
                            <p-button label="Run" [disabled]="traceBtn" size="small" tooltipPosition="bottom" pTooltip="Run" styleClass=" w-full p-button-sm" (onClick)="runWorkflow()"></p-button>
                        </div>
                        <div class="field col" *ngIf="runinfo.length > 0">
                            <p-button label="Run" [disabled]="traceBtn" size="small" tooltipPosition="bottom" pTooltip="Run" styleClass=" w-full p-button-sm" (onClick)="runWorkflow()"></p-button>
                        </div>
                    </div>
                </div>
             <!--Flow Data Ends-->
            <!--Run Data Starts-->
             <div *ngIf="runinfo.length > 0" class="p-1">
                <p-accordion class="w-full accord" [multiple]="true" *ngIf="hideflowpanel" [activeIndex]="[0]">
                    <p-accordionTab header="Flow Input">
                    <textarea id="float-input"  pInputTextarea  [disabled]="true"  autoResize="true" class="w-full"  style="max-height: 340px;min-height: 100px;">{{formData.flowInput | json}}</textarea>
                    </p-accordionTab>
                    <p-accordionTab header="Form Data">
                        <textarea id="float-input"  pInputTextarea [disabled]="true"  autoResize="true" class="w-full"  style="max-height: 340px;min-height: 100px;">{{formData.submissionData | json}}</textarea>
                    </p-accordionTab>
                </p-accordion>
                <div class="card-list" tabindex="1" #commentDiv *ngFor="let info of runinfo">
                  <p-accordion *ngIf="info.hasOwnProperty('input')" class="w-full" [multiple]="true" >
                    <p-accordionTab>
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2 w-full">
                            
                            <span class="font-bold" [ngClass]="info.stepName == 'error' ? 'errorHeader' : 'successHeader'">{{info.description}}</span>
                            <span class="pi pi-ban ml-auto"   *ngIf="info.stepName == 'error'" style="color: tomato;margin-right: 10px;"></span>
                            <span class="pi pi-check-circle ml-auto" *ngIf="info.stepName != 'error'" style="color: green;margin-right: 10px;"></span>
                        </div>
                    </ng-template>
                    <div #scrollToMe *ngIf="info.input && (info.input | json) !== '{}'" style="overflow: auto !important;transition: 6s;">
                        <div>
                            <span [ngClass]="{'error' : info.stepName == 'error'}"><span *ngIf="info.stepName != 'error'">{{info.stepName}}</span></span>
                            <span style="float: right;" class="mt-1 mr-1">
                            <button type="button" pButton pRipple icon="pi pi-copy" pTooltip="copy tp clipboard" class="p-button-text p-button-sm"
                            (click)="copy(info.input)" tooltipPosition="left"  [cdkCopyToClipboard]="info.input | json" [raised]="true" size="small" [text]="true"></button>
  
                          </span>
                        </div><br>
                        <div><span>Inputs:</span></div>
                        <div>
                          <pre class="result-code"><code id="#result">{{info?.input | json}}</code></pre>
                        </div>
                    </div>
                    <div class="error-msg" *ngIf="info.event === 'workflowError'" style="overflow: auto !important;">
                      {{info.error}}
                    </div>
                    </p-accordionTab>
                  </p-accordion>
                  <p-accordion *ngIf="info.hasOwnProperty('output')" class="w-full" [multiple]="true" >
                    <p-accordionTab>
                    <ng-template pTemplate="header">
                        <div class="flex align-items-center gap-2 w-full">
                           
                            <span class="font-bold" [ngClass]="info.stepName == 'error' ? 'errorHeader' : 'successHeader'">{{info.description}}</span>
                            <span class="pi pi-ban ml-auto" *ngIf="info.stepName == 'error'" style="color: tomato;margin-right: 10px;"></span>
                            <span class="pi pi-check-circle ml-auto" *ngIf="info.stepName != 'error'" style="color: green;margin-right: 10px;"></span>
                        </div>
                    </ng-template>
                    <div #scrollToMe *ngIf="info.output && (info.output | json) !== '{}'" style="overflow: auto !important;">
                        <div>
                            <span [ngClass]="{'error' : info.stepName == 'error'}"><span *ngIf="info.stepName != 'error'">{{info.stepName}}</span></span>
                            <span style="float: right;">
                                <button  type="button" tooltipPosition="left" pButton class="p-button-text p-button-sm" icon="pi pi-copy" pTooltip="copy tp clipboard"
                                (click)="copy(info.output)" [cdkCopyToClipboard]="info.output | json" [raised]="true" size="small" [text]="true"></button>
                            </span>
                        </div><br>
                      <div><span>Outputs:</span></div>
                        <div>
                          <pre class="result-code"><code id="#result">{{info?.output | json}}</code></pre>
                        </div>
                    </div>
                    <div class="error-msg" *ngIf="info.event === 'workflowError'" style="overflow: auto !important;">
                      {{info.error}}
                    </div>
                </p-accordionTab>
                    </p-accordion>
                  <div *ngIf="!info.hasOwnProperty('input') && !info.hasOwnProperty('output')" class="mb-2">
                          <div class="card border-round flex flex-column">
                              <span class="cardMainHead">{{info.description}}</span>
                              <span [ngClass]="{'error' : info.stepName == 'error'}">{{info.stepName}}</span>
                          </div>
                  </div>
                </div>
            </div>
             </div> 
             <!--Run Data Ends-->
        </div>
    </ng-template>
                <!--Run Transe Ends-->
        </p-sidebar>
        <p-sidebar class="sideBar" [(visible)]="dispFormData" [closeOnEscape]="false"  [modal]="false" position="right" [showCloseIcon]="false"  [autoZIndex]="autoIndexingSideBar">
        <!--Display Form data and Form Data-->
            <ng-template pTemplate="header">
                <p-toolbar styleClass="p-2 w-full" class="runToolbar">
                    <ng-template pTemplate="left">
                        <div> 
                            <button type="button" pButton pRipple icon="pi pi-times" class="p-button-text mr-2" tooltipPosition="bottom" pTooltip="Close" (click)="closeTestDataPanel()"></button>
                        </div>
                        <div class="flex flex-row w-100 ml-2 font-semibold text-lg">
                            Form Data
                        </div>
                    </ng-template>
                    <ng-template pTemplate="right">
                        <p-button  *ngIf="this.formData?.error == ''" icon="pi pi-pencil" [text]="true" [raised]="true" tooltipPosition="left" pTooltip="Update Form Data" (onClick)="goToPreview('formData')"></p-button>
                    </ng-template>
                </p-toolbar>
            </ng-template>
            <ng-template pTemplate="content">
                <div class="card p-1 border-noround" *ngIf="this.formData?.error != ''">
                <p-messages *ngIf="this.formData?.error != ''" [(value)]="messages" [enableService]="false" [closable]="false"></p-messages>
                <p-button  *ngIf="this.formData?.error != ''"  label="Add Form Data" [text]="true" [raised]="true" styleClass="w-full my-3" (onClick)="goToPreview('formData')"></p-button>
                </div>
                <json-editor *ngIf="this.formData?.error == ''" [options]="editorOptions" [data]="this.formData?.testData"></json-editor>
            </ng-template>
        <!-- </div> -->
        <!--Ends Display Form data and Form Data-->
        </p-sidebar>
        </div>
    </div>
    <p-overlayPanel #op id="op">
        <p-listbox [options]="items" optionLabel="label" [style]="{'width':'13rem'}" emptyMessage="No run history"
         (onClick)="[getRunHistoryByID($event.value)]" [listStyle]="{'max-height': '250px'}"> 
         <ng-template let-list pTemplate="item">
            <div class="flex align-items-center gap-2">
               <div>{{ list.label }}</div> 
            </div>
           
        </ng-template>
        </p-listbox>
    </p-overlayPanel> 
    <!-- <p-dialog header="Delete Node" [modal]="true" [(visible)]="deleteConfirem" [style]="{ width: '25rem' }">
        <span class="p-text-secondary block mb-5">Are you sure that you want to proceed?</span>
           <div class="flex justify-content-end gap-2">
            <button pButton  label="No" (click)="deleteConfirem = false" class="p-button-outlined"></button>
            <button  pButton  label="Yes" (click)="confiremDeleteNode()"></button>
        </div>
    </p-dialog> -->