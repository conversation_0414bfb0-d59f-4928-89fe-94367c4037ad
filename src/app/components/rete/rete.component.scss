
.rete-background.default{
  // height: calc(100vh - 118px) !important
}
.editor {
    width: 100% !important;
    overflow: hidden;
    background-size: 14px 14px !important;
  //  background-image: linear-gradient(90deg,#ececec  1px,transparent 0),linear-gradient(180deg,#ececec 1px,transparent 0) !important;
   
    height: calc(100vh - 118px) !important;
    border: 1px solid #ececec ;
    // position: relative;
}

.node-editor {
    margin: 0px;
    // padding-top: 50px;
    // padding-left: 750px;
    overflow-y: auto !important;
    // max-height: calc(100vh - 118px) !important;
    // transform: translate(831px, 100px) scale(1);
    // z-index: 0 !important;
}

.runPanel{
     width: 400px !important;
}

.highlight {
  cursor: pointer;
  color:var(--primary-color);
}
.error{
  font-weight: 600;
  font-size: 16px;
  color: tomato;
  text-transform: uppercase;
}
.success{
  font-weight: 600;
  font-size: 16px;
  color: green;
  text-transform: uppercase;
}
.cardMainHead{
  font-size: 16px;
  font-weight: 600;
  color:var(--primary-color);
}
.errorHeader{
  color: tomato;
}
.successHeader{
  color: green;
}

:host ::ng-deep json-editor,
:host ::ng-deep json-editor .jsoneditor,
:host ::ng-deep json-editor > div,
:host ::ng-deep json-editor jsoneditor-outer {
  height: calc(100vh - 195px);
}

::ng-deep .panel .p-panel .p-panel-content{
 // max-height: 350px;
  padding: 0.25rem !important;
}

// ::ng-deep .spanText { 
//   color: var(--cyan-500)!important;
//   display:block;
//   width:150px;
//   overflow:hidden;
//   text-overflow: ellipsis !important;
// }
.layout-topbar-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color-secondary);
  width: 2rem;
  transition: background-color .2s;

  &:hover {
      color: var(--text-color);
      background-color: var(--surface-hover);
  }

  i {
      font-size: 1.25rem;
  }

  span {
      font-size: 1rem;
      display: none;
  }
}

@keyframes load {
0% {
  transform: translateX(0);
}
100% {
  transform: translateX(-1.75rem);
}
}
::ng-deep .retetoast .p-toast .p-toast-message .p-toast-message-content{
  word-break: break-word !important
}
::ng-deep .retetoast .p-toast .p-toast-message .p-toast-message-content .p-toast-detail{
  width: 250px !important;
  height: auto !important
}

::ng-deep .retDivider .p-divider.p-divider-horizontal{
  margin: 1rem 0rem !important;
}

::ng-deep .accord .p-accordion .p-accordion-content{
  padding: 0.5rem !important;
}
::ng-deep .sideBar .p-sidebar-right{
  top:87px !important;
  right: 30px !important;
  width: 450px !important;
  height: calc(100vh -  120px) !important;
}
::ng-deep .sideBar .p-sidebar .p-sidebar-footer{
  display: none !important;
}
::ng-deep .sideBar .p-sidebar .p-sidebar-content{
  padding: 0px !important;
}
::ng-deep .sideBar .p-sidebar .p-sidebar-header{
  padding: 0px !important;
}
::ng-deep .runToolbar .p-toolbar{
  width: -webkit-fill-available !important;
  border-radius:0px !important;
}
::ng-deep .runToolbar{
  width: -webkit-fill-available !important;
}