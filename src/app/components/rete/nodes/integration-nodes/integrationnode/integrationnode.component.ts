import { Component, ChangeDetectorRef, AfterViewInit,ViewChild, ElementRef } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
import { Subject } from 'rxjs';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FormControl } from '@angular/forms';
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { ExpressionBuilderComponent } from '../../expression-builder/expression-builder.component';

interface RFCField {
  NAME: string;
  SAP_TYPE: string;
  TYPE: string;
  DESC: string;
  DECIMAL: string;
  DIRECTION: number;
  INDEX: string;
  MANDATORY: boolean;
  LENGTH: number;
  RFCField: any[];
}

interface RFCParameterFormat {
  RFCInput: any[];
  RFCOutput: any[];
  RFCParameter: RFCField[];
}

@Component({
  selector: 'app-integrationnode',
  templateUrl: './integrationnode.component.html',
  styleUrls: ['./integrationnode.component.scss'],
  providers: [NodeService, DialogService]
})
export class IntegrationnodeComponent extends NodeComponent implements AfterViewInit {
  @ViewChild('expressionArea') expressionArea: ElementRef;
  entityEntityNone = new FormControl();
  fieldsControl = new FormControl();
  sapParameters = new FormControl();
  metadata: any = [];
  public text: String;
  dataItem: any;
  display: boolean;
  displayBtn: boolean = false;
  nodeformdata: FormGroup;
  allSystem: any;
  DispEditIcon: boolean = false;
  selectedSystem: any = '';
  nodeDesc: boolean = false;
  errmsg: string;
  excludeparams = [];
  operationnamepattern = '[a-z0-9]+';
  helpurl = 'https://docs.unvired.com/builder/flows/';
  desc: any;
  optionalprops = [];
  selectedCategory: any = null;
  odataJson: any;
  allEntyityTypes: any = [];
  odataJsonExtracted: any;
  entityList = [];
  ref: DynamicDialogRef | undefined;
  deletedData: any;
  jsonLogicResult: any = [];
  expBuildVal: any;
  expBuildData: any; jsonLogicRule: any; selectedAdsValue: any = "";
  selectedRadioValue: any;
  showhide: boolean = false; addEditData: any; selectedFtpValue: any;
  error: any = '';
  debugMode: boolean = true;
  readonlyOnDisable: boolean = false;
  openPanelForNew: boolean;
  nodeName:any;
  nodeIcon:any;
  constructor(protected override service: NodeService, protected override cdr: ChangeDetectorRef,
    private utilservice: UtilsService, private reteservice: ReteService, private fb: FormBuilder,
    public dialogService: DialogService, private confirmationService: ConfirmationService, public messageService: MessageService) {
    super(service, cdr);
    this.displayBtn = true;
    
    this.readonlyOnDisable = this.reteservice.getDisableWfData();
  }

  ngAfterViewInit() {
    this.desc = this.node.data['description'];
    this.displayBtn = true;
    if (this.node.hasOwnProperty('new')) {
      this.desc = this.node.data['name'];
      let event = {
        collapsed: true
      }
      this.openPanelForNew = false;
      this.panelBtn(event, this.node);
      this.focusIn();
    } else {
      this.desc = this.node.data['description'];
      this.openPanelForNew = true;
    }
    this.nodeName= this.node.name;
    switch(this.node.name){
      case "http":
        this.nodeIcon = 'http';
        break;
        case "query":
          this.nodeIcon = 'sql';
          break;
          case "storedproc":
            this.nodeIcon = 'stored-procedure';
            break;  
            case "ads":
              this.nodeIcon = 'ads';
              break;
              case "odata":
                this.nodeIcon = 'odata';
                break;
                case "sap":
                  this.nodeIcon = 'sap';
                  break;
                  case "ftp":
                    this.nodeIcon = 'ftp';
                    break;
     }
  }

  //GEt system list dropDown
  getallsystems() {
    this.getsystems().subscribe((res) => {
      this.allSystem = res;
      if (this.allSystem.length == 0) {
        this.selectedSystem = '';
        this.allSystem = [];
      } else {
      }
    });
  }
  preSelectedSys:any;
  getSelectedSystemValue(){
    if (this.dataItem.nodeName === 'odata') { 
    this.preSelectedSys=this.input.controls[0].value.values;
    }
    if (this.dataItem.nodeName === 'query') { 
      this.preSelectedSys=this.input.controls[0].value.values;
      }
      if (this.dataItem.nodeName === 'sap') { 
        this.preSelectedSys=this.input.controls[0].value.values;
        }
  }

  async setSystemValue(systemName: any) {   
    if (this.dataItem.nodeName === 'odata') { 
    if(this.preSelectedSys != ''){
      this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared expand entity and fields information'});
      this.input.controls.forEach((key,index) => {
        if (key.value.inputName !== "system" && key.value.inputName !== "operation" &&  key.value.inputName !== "expandtype"
         && key.value.inputName !== "parameters") {          
          key.patchValue({ values: "" });
          this.navigations=[];
          this.oDataEntityTypes = [];
          this.selectfieldList=[];
          this.allEntyityTypes = [];
        }else if(key.value.inputName == "parameters"){
            const control = this.input.controls[index].get('valuepairs') as FormArray;
            control.controls = [];
            key.patchValue({ valuepairs: [] });            
        }else{

        }
      }) 
    }else{

    }
  }
  if (this.dataItem.nodeName === 'query') { 
    if(this.preSelectedSys != ''){
      this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared query keys and data information'});
      this.input.controls.forEach((key,index) => {        
       if (key.value.inputName !== "system" && key.value.inputName !== "exectype" && key.value.inputName !== "flattenoutput" ) {          
           key.patchValue({ values: "" });
           this.dbparamVal=[];
       }else if(key.value.inputName == "flattenoutput"){
        key.patchValue({ values: "false" });
       }
         else{

         }
      }) 
    }else{

    }
  }
  if (this.dataItem.nodeName === 'sap') { 
    if(this.preSelectedSys != ''){
      this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared below information'});
      this.input.controls.forEach((key,index) => {   
       if (key.value.inputName !== "system" && key.value.inputName !== "operation" && key.value.inputName !== "maptype" 
       && key.value.inputName !== "transaction" && key.value.inputName !== "rfcoutputs" && key.value.inputName !== "rfcinputs" ) {          
           key.patchValue({ values: "" });
       }else if(key.value.inputName == "rfcoutputs" ){
            this.sapParameters.setValue("");
            this.sapParameterValue = '';
            this.dispRfcOutputs = [];
            this.dataToProcess = []          
        } else if(key.value.inputName == "rfcinputs"){
          const control = this.input.controls[index].get('valuepairs') as FormArray;
          control.controls = [];
          key.patchValue({ valuepairs: [] }); 
        }
         else{

         }
      }) 
    }else{

    }
  }
    if (systemName !== null) {
      this.DispEditIcon = true;
      this.selectedSystem = systemName;
    } else {
      this.DispEditIcon = false;
      this.selectedSystem = '';
    }
    //if (this.dataItem.nodeName === 'odata') {
      // this.allEntyityTypes = [];
      // this.reteservice.serviceOdata(this.selectedSystem, []).subscribe((res) => {
      //   if (res.error === '') {
      //     if (res.status.toLowerCase() === 'success') {
      //       if(res.data.length == 0){
      //         this.allEntyityTypes.push({'key': 'None',"value":'None'});
      //       }else{
      //         this.allEntyityTypes.push({'key': 'None',"value":'None'});
      //       res.data.forEach((res) => {
      //         this.allEntyityTypes.push({'key':res,"value":res});
      //       });
      //     }
      //       this.allEntyityTypes = this.allEntyityTypes.reduce((unique, o) => {
      //         if(!unique.some(obj => obj.label === o.label && obj.value === o.value)) {
      //           unique.push(o);
      //         }
      //         return unique;
      //     },[]);
      //     }
      //   } else {
      //  this.messageService.add({ severity: 'error', summary: 'ERROR', detail: res.error });

      //     this.messageService.add({ severity: 'error', summary: 'ERROR', detail: res.error });
      //     this.errmsg = res.error;
      //   }
      // });

   // }
  }

  //set system value odata
  async getSapSystemValue(systemName: any) {
    if (systemName !== null) {
      this.DispEditIcon = true;
      this.selectedSystem = systemName;
    } else {
      this.DispEditIcon = false;
      this.selectedSystem = '';
    }
    this.selectedSystem = systemName;

  }

  async getKeys(jsonObjToEvaluate: any) {
    const keyExpr = await (window as any).jsonata(`Key.PropertyRef."$".Name`);
    let keysresult = await keyExpr.evaluate(jsonObjToEvaluate);
    if (keysresult instanceof Array) {
      return keysresult;
    } else {
      return [keysresult];
    }
  }

  async getNavigation(jsonObjToEvaluate: any) {
    const navigationExpr = await (window as any).jsonata(`NavigationProperty."$".Name`);
    let navigationresult = await navigationExpr.evaluate(jsonObjToEvaluate);
    if (navigationresult instanceof Array) {
      return navigationresult;
    } else {
      return [navigationresult];
    }
  }

  async getFields(jsonObjToEvaluate: any) {
    const fieldExpr = await (window as any).jsonata(`Property."$"`);
    let filedresult = await fieldExpr.evaluate(jsonObjToEvaluate);
    if (filedresult instanceof Array) {
      return filedresult;
    } else {
      return [filedresult];
    }
  }

  confirmEntityTypes(event: any){
    event = event.originalEvent;
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
          this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 });
      },
      reject: () => {
          this.messageService.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
      }
  });
  }

  selectedMetadataTypes() {
    this.nodeformdata.controls['fieldsControl'].setValue(this.fieldsControl.value);
  }

  setEntitySet(entities: any) {
    let entitySetExpr = (window as any).jsonata(`"edmx:Edmx"."edmx:DataServices".Schema.EntityContainer.EntitySet."$"`);
    let entitySet = entitySetExpr.evaluate(this.odataJson);
    entitySet.then((res) => {
      res.forEach(entitySet => {
        let entitySetArr = entitySet.EntityType.split('.');
        entities.forEach(entity => {
          if (entity.entityType === entitySetArr[1]) {
            entity['entitySet'] = entitySet.Name;
          }
        })
      });
    });
    return entities;
  }
  //Get systems dropDown Api
  getsystems() {
    const subject = new Subject<any[]>();
    this.reteservice.getworkflowsystems(this.node.name).subscribe(
      (res) => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            if (response.data && response.data.length > 0) {
              subject.next(response.data);
            }
          }
        } else {
          this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });
          this.selectedSystem = '';
          this.allSystem = [];
        }
      });
    return subject.asObservable();
  }

  //open panel 
  panelBtn(event: any, node: any) {
    if (event.collapsed == 'true' || event.collapsed == true) {
      this.displayBtn = false;
      if (node.data.inputs == undefined) {

      } else {
        let newInput = node.data.inputs;
        node.data.input = newInput;
        delete node.data.inputs;
      }
      const data = {
        nodeId: node.id,
        node: this.node,
        nodeData: node.data,
        nodeName: node.name,
        formId: this.editor['workflow'].formId,
        editor: this.editor
      }
      this.dataItem = data;      
      this.setdefaultvalues();
      // setTimeout(() => {
      //   this.reteservice.arrangeNodes.next(true);
      // }, 500);
      this.reteservice.pushNodeId(this.dataItem.nodeId);
    } else {
      if (node.data.input) {
        let newInput = node.data.input;
        node.data.inputs = newInput;
        delete node.data.input;
      }
      this.displayBtn = true;
      this.nodeDesc = false;
      this.DispEditIcon = false;
      this.selectedSystem = '';
      this.excludeparams = [];
      this.allEntyityTypes = [];
      this.metadata = [];
      this.dispRfcOutputs = [];
      this.sapParameterValue = '';
      this.dispRfcOutputs = [];
      this.dbparamVal = [];
      this.allEntyityTypes = [];
      this.navigations =[];
      this.selectfieldList=[];
      this.formatOdataMetadata=[];
      this.oDataEnitySelectedObject=[];
      // this.selectedNavigations=[];
       this.oDataEntityTypes=[]
      this.reteservice.popNodeId(this.dataItem.nodeId);
      // setTimeout(() => {
      //   this.reteservice.arrangeNodes.next(true);
      // }, 500);
      this.enableDisable=false;
    }   
  }

  goToLink(url: string) {
    window.open(url, "_blank");
  }

  selectDBOption(field: any) {
    this.selectedRadioValue = "";
    if (field.toLowerCase() === "query") {
      this.selectedRadioValue = "Query";
      this.dbparamVal = []
      this.input.controls.forEach(key => {
        if (key.value.inputName === "keys") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "data") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "operation") {
          // key.patchValue({values: ""});
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "query") {
          key.patchValue({ required: "Mandatory" });
        }
      })
    } else {
      this.selectedRadioValue = "Operation";

      this.input.controls.forEach(key => {
        if (key.value.inputName === "query") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "keys") {
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "data") {
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "operation") {
          key.patchValue({ required: "Mandatory" });
        }
      })
    }
  }

  optValisExecute: boolean = false;
  selectSapOption(field: any) {
    if (field.toLowerCase() === "execute") {
      this.optValisExecute = true;
      this.input.controls.forEach(key => {
        if (key.value.inputName === "function") {
          // key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "parameters") {
          // key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "rfcinputs") {
          // const control = this.input.controls[5].get('valuepairs') as FormArray;  
          //   control.controls=[];
          //   key.patchValue({ valuepairs: []}); 
        }
        if (key.value.inputName === "rfcoutputs") {
          // key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "data") {
          // key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }

      })
    } else {
      this.optValisExecute = false;
      //   this.input.at(2).get('values').setValue("");
      //   this.input.at(3).get('values').setValue("");
      //   this.sapParameters.setValue("");
      //  this.sapParameterValue='';
      //  this.dispRfcOutputs=[]
      //  const control = this.input.controls[5].get('valuepairs') as FormArray;  
      //  control.controls=[];
      //  this.input.at(5).get('values').setValue("");
      //  this.dataToProcess=[];
      // this.input.at(7).get('values').setValue("");
      this.input.controls.forEach(key => {
        if (key.value.inputName === "function") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "parameters") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "rfcinputs") {
          const control = this.input.controls[5].get('valuepairs') as FormArray;
          control.controls = [];
          key.patchValue({ valuepairs: [] });
        }
        if (key.value.inputName === "rfcoutputs") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "data") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        this.sapParameters.setValue("");
        this.sapParameterValue = '';
        this.dispRfcOutputs = [];
        this.dataToProcess = [];
      });
    }
    setTimeout(() => {
      let data = {
        val: true,
        node: this.node
      }
      this.reteservice.arrangeNodeConnections.next(data);
    }, 200);
  }

  changeADSAction(event: any) {
    if (event == 'authenticate') {
      this.selectedAdsValue = "authenticate";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "operation") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "searchcontrols") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "field") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "value") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "user") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });

        }
        if (key.value.inputName === "password") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });

        }
      });
    } else if (event == 'setattribute') {
      this.selectedAdsValue = "setattribute";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "searchcontrols") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "user") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "password") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "operation") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "field") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "value") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
      });
    }
    else if (event == 'searchobjects') {
      this.selectedAdsValue = "searchobjects";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "user") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "operation") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "password") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "field") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "value") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "searchcontrols") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
      });
    }
    else {
      this.selectedAdsValue = "";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "user") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "password") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "operation") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "field") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "searchcontrols") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "value") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
      });

    }
  }

  changeFtpDropDown(event: any) {
    if (event === 'createdir') {
      this.selectedFtpValue = "createdir";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "filename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "newfilename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "newdirectory") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }

      });
    } else if (event === 'rename') {
      this.selectedFtpValue = "rename";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "newdirectory") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "newfilename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "filename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }

      });
    } else if (event === 'get' || event === 'delete') {
      this.selectedFtpValue = event;
      this.input.controls.forEach(key => {
        if (key.value.inputName === "newfilename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "newdirectory") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        //add validateor
        if (key.value.inputName === "filename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }

      });
    }
    else if (event === 'put') {
      this.selectedFtpValue = event;
      this.input.controls.forEach(key => {
        if (key.value.inputName === "newfilename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "newdirectory") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }

        //add validateor
        if (key.value.inputName === "filename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Mandatory" });
        }
      });
    }
    else {
      this.selectedFtpValue = "";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "newfilename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "newdirectory") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
        if (key.value.inputName === "filename") {
          key.patchValue({ values: "" });
          key.patchValue({ required: "Optional" });
        }
      });
    }
  }
  
  AllWfName: any;
  focusIn() {
    if (this.nodeDesc == false) {
      this.nodeDesc = true;
      window.setTimeout(function () {
        document.getElementById('inputBox').focus();
      }, 10);
    } else {

    }
  }
  duplicate: boolean = false;
  checkforemptydesc: boolean = false;
  nodeNameChange: boolean = false;
  FocusOut() {
    let oldName = this.node.data['name'];
    if (this.nodeDesc != false) {
      this.node.data['description'] = this.desc;
      // if(this.node.data['name'] == 'untitled' || this.duplicate || this.node.data['name'] == ''){
      // this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      // }
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      this.nodeDesc = false;
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      } else {
        this.duplicate = false;
      }
    } else {
      this.nodeDesc = true;

    }
    if (this.node.data['name']) {
    } else {
      this.desc = 'untitled';
      this.node.data['description'] = this.desc;
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      } else {
        this.duplicate = false;
      }
    }
    if(this.node.data['name'] != 'untitled'){
      if((oldName != this.node.data['name']) && (oldName != 'untitled')){
      this.reteservice.flowTechName.next("change");
       }
    }
    // if (this.node.data['name'] != 'untitled') {
    //   if ((oldName != this.node.data['name'])) {
    //     // this.reteservice.flowTechName.next("change");
    //     this.nodeNameChange = true;
    //   } else {
    //     this.nodeNameChange = false;
    //   }
    // } else {
    //   this.nodeNameChange = false
    // }
    //   if(this.node.data['name'] == 'untitled' ||  this.node.data['description'] == "" && !this.duplicate){
    //     this.checkforemptydesc = true
    //  }else{
    //   this.checkforemptydesc = false
    //  }
  }


  checkIfDuplicateExists(arr) {
    return new Set(arr).size !== arr.length
  }
  addSystem(calltype: string, systemname: string) {
    this.addEditData = {
      systemtype: this.dataItem.nodeName,
      system: systemname,
      calltype: calltype
    }
  }

  addkeyvalueType(inputparam: any) {
    if (this.node.name === "sap") {
      this.input.push(this.fb.group({
        inputName: inputparam.inputName,
        description: inputparam.description,
        type: inputparam.type,
        label: inputparam.label,
        valuepairs: this.fb.array([])
      }));
    } else {
      this.input.push(this.fb.group({
        inputName: inputparam.inputName,
        description: inputparam.description,
        type: inputparam.type,
        show: false,
        valuepairs: this.fb.array([])
      }));
    }    
  }
  addSapkeyvalueType(inputparam: any) {
    this.input.push(this.fb.group({
      inputName: inputparam.inputName,
      description: inputparam.description,
      type: inputparam.type,
      label: inputparam.label,
      valuepairs: this.fb.array([])
    }));
  }
  getkeyvaluecontrol(field) {
    return field.controls.valuepairs.controls;
  }
  getsapkeyvaluecontrol(field) {
    return field.controls.valuepairs.controls;
  }
  getDBSelectedFileds(field) {
    return field.controls.query.controls;
  }
  addkeyValuepair(ind: number, key: string, value: string) {
    const control = this.input.controls[ind].get('valuepairs') as FormArray;
    control.push(this.fb.group({
      key: [key, Validators.required],
      value: [value, Validators.required]
    }));
  }
  async addSapkeyValuepair(ind: number, value: string) {
    const control = this.input.controls[ind].get('valuepairs') as FormArray;
    control.push(this.fb.group({
      value: [value, Validators.required]
    }));
  }

  addSapKey(ind: number, value: string) {
    const control = this.input.controls[ind].get('valuepairs') as FormArray;
    control.push(this.fb.group({
      value: [value, Validators.required]
    }));
  }
  async removeKeyValue(index: number, ind: number) {
    const control = this.input.controls[index].get('valuepairs') as FormArray;
    control.removeAt(ind);
  }

  openSapDataMapping(filedVal: any, filedName: any, index: any) {
    let parametersData: any;
    this.nodeformdata.value.input.forEach(ele => {
      if (ele.inputName == 'parameters') {
        parametersData = ele.values;
      }
    });
    let sapData: any = {
      dataFieldValue: filedVal,
      parametersFiledValue: parametersData,
      index: index,
      nodeName: this.dataItem.nodeName,
      formId: this.dataItem.formId
    }
    // this.ref = this.dialogService.open(SapDataMappingComponent, {
    //   header: 'SAP Data Mapping',
    //   width: '50%',
    //   height: '50%',
    //   contentStyle: { overflow: 'auto', padding: '5px' },
    //   baseZIndex: 10000,
    //   maximizable: false,
    //   closable: true,
    //   closeOnEscape: true,
    //   data: sapData
    // });
    // this.ref.onClose.subscribe((res: any) => {
    //   //  this.input.at(index).get('values').setValue(JSON.stringify(res));

    // });

  }
  dispRfcOutputs: any;
  //set default value for forms
  async setdefaultvalues() {
    switch (this.node.name) {
      case "http":
        this.helpurl = this.helpurl + '#' + 'rest';
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            if (inputparam.type === 'keyvalue') {
              if (inputparam.values == "") {
                inputparam.valuepairs = [{ key: '', value: '' }]
                // this.addkeyvalueType(inputparam);
              }
              this.addkeyvalueType(inputparam);
              inputparam.valuepairs.forEach(pair => this.addkeyValuepair(ind, pair.key, pair.value));
            } else {
              this.addInputparamsInNodedata(inputparam);
            }
            
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        
        break;
      case "query":
        this.helpurl = this.helpurl + '#' + 'sql-query';
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            if (inputparam.inputName == 'keys') {               
                //[inputparam.values] = inputparam.values;
                // let format = JSON.parse(inputparam.values);
                // if(typeof format == 'object'){                  
                //   inputparam.values = JSON.stringify([format]);
                // }
              let data = JSON.parse(inputparam.values);
              for (let key in data) {
              this.dbparamVal.push(key)
              //  this.dbparamVal.push(Object.keys(data[key])[0])
              }
            }
            this.excludeparams.push(inputparam.inputName);
            this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
      case "storedproc":
        this.helpurl = this.helpurl + '#' + 'stored-procedure';
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
      case "ads":
        this.helpurl = this.helpurl + '#' + 'active-directory';
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
      case "odata":
        this.helpurl = this.helpurl + '#' + 'odata';
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];                
          this.dataItem.nodeData.input.forEach(async (inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            if (inputparam.type == 'keyvalue') {
              if (!inputparam.hasOwnProperty("valuepairs")) {
                inputparam.valuepairs = [{ key: '', value: '' }]
              }
              this.addkeyvalueType(inputparam);
              inputparam.valuepairs.forEach(pair => this.addkeyValuepair(ind, pair.key, pair.value));
           }else{
            await this.OdataInputParams(inputparam);
           }
            
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
          // this.nodeformdata.addControl("expandEntityNone", this.fb.control(""));
        }
        break;
      case "sap":
        this.helpurl = this.helpurl + '#' + 'sap';
        this.patchFormData();
        this.dataToProcess = [];
        let escape:boolean=false;
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            if (inputparam.inputName === 'system') {
              this.getSapSystemValue(inputparam.values);
            }
            this.excludeparams.push(inputparam.inputName);
            if(inputparam.inputName === "maptype"){
             if(inputparam.values == 'manual'){
              escape=true;
             }else{
              escape=false;
             }
            }
            // if (inputparam.type === 'keyvalue') {
            //   if (inputparam.values == "") {
            //     inputparam.valuepairs = [{value: ''}]
            //   }
            //   this.addSapkeyvalueType(inputparam);
            //   inputparam.valuepairs.forEach(pair => this.addSapkeyValuepair(ind,pair.value));
            // } else {
            //   this.addInputparamsInNodedata(inputparam);
            // }
            if ((inputparam.inputName === "rfcinputs" && inputparam.type === "keyvalue")) {
              if (inputparam.values == "") {
                inputparam.valuepairs = [{ value: '' }]
              }
              this.addSapkeyvalueType(inputparam);
              inputparam.valuepairs.forEach(pair => this.addSapkeyValuepair(ind, pair.value));
            }
            else {
              this.addInputparamsInNodedata(inputparam);
            }
            if (inputparam.inputName == 'parameters' && inputparam.values != '') {
              this.sapParameterValue = JSON.parse(inputparam.values).RFCInput;
              this.sapParameters.setValue(JSON.parse(inputparam.values).RFCInput);
              this.dispRfcOutputs = JSON.parse(inputparam.values).RFCOutput;
            } else {
            }
            if (inputparam.inputName == "data" && inputparam.values != '' && !escape) {
              let a = JSON.parse(inputparam.values)
              const transformedData = Object.keys(a).map(key => ({ [key]: a[key] }));
              for (let key in transformedData) {
                this.dataToProcess.push(transformedData[key]);
              }
            } else {
            }
          });
          await this.hideOptionalFields(this.enableDisable)

        } else {
          this.getnodeparams();
          this.nodeformdata.addControl("sapParameters", this.fb.control(""));
        }
        break;
      case "ftp":
        this.patchFormData();
        this.helpurl = this.helpurl + '#' + 'ftprequest';
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          //add optional params to input for old nodes
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
    }
    if (this.reteservice.getDisableWfData()) {
      this.readonlyOnDisable = this.reteservice.getDisableWfData();
      this.nodeformdata.disable();
    } else {
      this.readonlyOnDisable = this.reteservice.getDisableWfData();
    }
    
  }

  patchFormData() {
    let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
  //  this.helpurl = this.helpurl + '#' + this.dataItem.nodeData.wfName;
    const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
    if (index > -1) {
      wfnodenames.splice(index, 1);
    }
    this.nodeformdata = this.fb.group({
      name: [this.dataItem.nodeData.name, [Validators.pattern(this.operationnamepattern)]],
      description: [this.dataItem.nodeData.description],
      input: this.fb.array([])
    });
    this.getallsystems();
  }

  get input() {
    return this.nodeformdata.get('input') as FormArray;
  }

  get entityType() {
    return this.nodeformdata.get('entityType');
  }

  //api for get node inputs /data
  getnodeparams() {
    this.reteservice.getformsfielsfor$values(this.dataItem.nodeData.wfName, this.dataItem.nodeData.wfNamespace, this.dataItem.formId, this.excludeparams.toString()).subscribe(async (res) => {
      const response = res;
      if (response.error === "") {
        if (response.status === "Success") {
          if (response.prerequisites != "") {
            this.jsonLogicRule = response.prerequisites;
          } else {
            this.jsonLogicRule = [];
          }
          response.inputs.forEach((inputparam, ind) => {
            if (this.node.name === "sap") {
              if ((inputparam.inputName === "rfcinputs" && inputparam.type === "keyvalue")) {
                inputparam.valuepairs = [{ key: '', value: '' }]
                this.addkeyvalueType(inputparam);
              }
              else {
                this.addInputparamsInNodedata(inputparam);
              }
            } else {
              if (inputparam.type === 'keyvalue') {
                inputparam.valuepairs = [{ key: '', value: '' }]
                this.addkeyvalueType(inputparam);
              }
              else {
                // if (this.node.name === "odata") {
                //   this.addOdataInputparamsInNodedata(inputparam);
                // }else{
                this.addInputparamsInNodedata(inputparam);
                // }
              }
            }
          });
        }
        await this.hideOptionalFields(this.enableDisable)
      } else {
        this.reteservice.odataMessages.next({"type":'error','message':response.error})  
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });
        this.error = response.error
      }
    });
  }

  enableDisable:boolean=false;
  async addInputparamsInNodedata(inputParameter: any) { 
    if(inputParameter.required == 'Mandatory'){
      inputParameter.show = true;
    }else{
      inputParameter.show = false;
    }
    inputParameter.radiolistvalue = [];
    inputParameter.listvalue = [];
    
    if (inputParameter.type === "radio" && this.node.name === "query") {
      setTimeout(() => {
        this.selectDBOption(inputParameter.values);
      }, 500);
    }
    if (inputParameter.type === "list" && inputParameter.inputName == "operation" && this.node.name === "sap") {
      setTimeout(() => {
        this.selectSapOption(inputParameter.values);
      }, 500);
    }
    if ((inputParameter.type === "list" || inputParameter.type === "radio" || inputParameter.type === "toggle") && inputParameter.listvalues && Array.isArray(inputParameter.listvalues) && !Array.isArray(inputParameter.listvalues[0])) { inputParameter.listvalues = [inputParameter.listvalues]; }
    if ((inputParameter.type === "user" || inputParameter.type === "team") && inputParameter.values && Array.isArray(inputParameter.values) && !Array.isArray(inputParameter.values[0])) { inputParameter.values = [inputParameter.values]; }
    if (inputParameter.type === "list" && inputParameter.listvalues && inputParameter.inputName !== 'system' && this.node.name != "odata") {
      inputParameter.listvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.listvalue[0].push({ "key": data, "value": data.toLowerCase().replace(/\s+/g, '') })
        });
       inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
      }
    
    }
    if (inputParameter.type === "list" && inputParameter.listvalues && inputParameter.inputName !== 'system' && this.node.name === "odata" && inputParameter.inputName == 'operation') {
      inputParameter.listvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.listvalue[0].push({ "key": data, "value": data })
        });
       inputParameter.values = inputParameter.values;
      }
    
      setTimeout(() => {
       this.selectOdataOperationUpdate(inputParameter.values) ;
      }, 500);   
    }
    if (inputParameter.type === "radio" && inputParameter.listvalues) {
      inputParameter.radiolistvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.radiolistvalue[0].push({ "key": data, "value": data.toLowerCase().replace(/\s+/g, '') })
        });
      }
      inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
    }
    if (inputParameter.type === "radio" && inputParameter.inputName == "maptype" && inputParameter.listvalues) {
      inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
      setTimeout(() => {
        if(inputParameter.values == 'default'){
          this.showDataFiled=false;
        }else{
          this.showDataFiled=true;
        }
        //this.getSapmapType(inputParameter.values);
      }, 200);
    } 
    if(inputParameter.type === "text" && inputParameter.values != ''){
    }   
    this.input.push(this.fb.group(inputParameter));
    
  }

 async hideOptionalFields(hide:any){
    this.input.controls.forEach(key => {
       if(key.value.required == 'Optional'){ 
        if (key.value.show == true) {
          key.patchValue({ show: false });
        }else{
          key.patchValue({ show: true });
        }
     }else if(key.value.type == 'keyvalue'){
      if (key.value.show == true) {
        key.patchValue({ show: false });
      }else{
        key.patchValue({ show: true });
      }
     }else{
      key.patchValue({ show: false });
     }
    });    
  }

  //confirm dilaog box 
  confirmDelete(event: Event, selectedSystem: any) {
    this.confirmationService.confirm({
      target: event.target,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      // acceptButtonStyleClass: 'p-button-danger p-button-sm',
      accept: () => {
        this.deletesystem(selectedSystem);
        this.nodeDesc = false;
      },
      reject: () => {
      }
    });
  }

  //delete system Api
  deletesystem(systemname: string) {
    this.reteservice.deleteflowsystem(systemname).subscribe((res) => {
      const response = res;
      if (response.error === '') {
        if (response.status === 'Success') {
          this.getallsystems();
          this.input.controls.forEach(key => {
            if (key.value.inputName == "system") {
              key.patchValue({ values: "" });
            }
          });
          this.DispEditIcon = false;
          this.selectedSystem = '';
        }
      } else {
        this.reteservice.odataMessages.next({"type":'error','message':response.error})  
        this.errmsg = response.error;
      }
    });
  }

  //close overlay panel add/edit system
  overlayClose(data: any) {
    if (data.calltype == "create") {
      document.getElementById('newClose').click();
      this.getallsystems();
    } else {
      document.getElementById('updateClose').click();
      this.getallsystems();
    }
  }

  //open overlay panel Expbuild system and send data
  openExprBuilder(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number) {
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem;
    this.expBuildVal.callerformfield = '';
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
  }


  openDbExprBuilder(event: any, callerformfieldvalue: string, callerformfield: string, index?: number, keyvalindex?: number) {
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfield,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem
    this.expBuildVal.callerformfield = callerformfield;
    this.expBuildVal.selectedsystem = this.input.value[0].values;
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
  }

  openSapExprBuilder1(event: any, callerformfieldvalue: string, callerformfieldtype: string, inputName: string, index?: number, keyvalindex?: number) {
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "selectedSystem": this.selectedSystem,
      "inputName": inputName,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    this.expBuildVal = this.dataItem;
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal.selectedSystem = this.selectedSystem;
    this.expBuildVal.inputName = inputName;
    this.expBuildVal.formData = this.nodeformdata.value.input;
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
    
  }

  sapParameterValue: any;
  dbparamVal: any = [];
  //close expBuild overlay panel
  overlayCloseExpBuilder(data: any) {
    let val = data.expBuildValue;
    if (this.expBuildData.callerformfieldtype === 'keyvalue' && this.expBuildData.nodeName != "sap") {
      const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
      control.at(this.expBuildData.keyvalindex).get('value').setValue(val);
    } else {
      if (this.expBuildData.nodeName == "sap") {
        if (this.expBuildData.inputName == 'function') {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
          if (data.change == true) {
            this.input.at(this.expBuildData.index + 1).get('values').setValue("");
            this.sapParameters.setValue("");
            this.sapParameterValue = '';
            this.dispRfcOutputs = []
            //  const control =5this.input.controls[5].get('valuepairs') as FormArray;  
            //  control.controls=[];
            this.input.controls.forEach(key => {
              if (key.value.inputName === "rfcinputs") {
                const control = this.input.controls[5].get('valuepairs') as FormArray;
                control.controls = [];
                key.patchValue({ valuepairs: [] });
              }
            })
            this.input.at(7).get('values').setValue("");
            this.dataToProcess = []
          }
        } else if (this.expBuildData.inputName == 'parameters') {
          if (val != '') {
            // if(JSON.parse(val).RFCInput.length > 0){
            //     let parVal = JSON.parse(val).RFCInput;
            //     this.sapParameterValue = parVal;
            //     this.sapParameters.setValue(parVal);
            //     if(this.sapParameterValue){
            //       this.reteservice.warningMsg.next("inputs");
            //     }
            //   }else{
            //     this.reteservice.warningMsg.next("noInputs");
            //   }
            //   if(JSON.parse(val).RFCOutput.length > 0){
            //     this.dispRfcOutputs = JSON.parse(val).RFCOutput;
            //   }else{
            //     this.reteservice.warningMsg.next("noOutputs");
            //   }
            this.input.at(this.expBuildData.index).get('values').setValue(val);
          } else {
            this.dispRfcOutputs = []
            this.sapParameterValue = '';
            this.sapParameters.setValue("");
            this.input.at(this.expBuildData.index).get('values').setValue("");
          }
          if (data.change == true) {
            let paramsVal = JSON.parse(val);
            // if(this.input.at(5).value.valuepairs.length > 0){
            //   let oldRfcIps = this.input.at(5).value.valuepairs;
            //     oldRfcIps.forEach((item)=>{
            //       try{
            //      if(item.value.includes('NAME')){
            //         for(let key in paramsVal.RFCInput){
            //             if(paramsVal.RFCInput[key] == JSON.parse(item.value).NAME){
            //               paramsVal.RFCInput.splice(key, 1);
            //               paramsVal.RFCParameter.splice(key, 1);
            //             } 
            //         }
            //      }else{
            //       for(let key in paramsVal.RFCInput){
            //         if(paramsVal.RFCInput[key] == item.value){
            //           paramsVal.RFCInput.splice(key, 1);
            //           paramsVal.RFCParameter.splice(key, 1);
            //         } 
            //     }
            //      }
            //     }catch{

            //     }
            //     });

            //   }else{
            //   }
            for (let key in paramsVal.RFCInput) {
              this.addSapkeyValuepair(5, paramsVal.RFCInput[key])
            }
            let rfcIps = JSON.parse(val).RFCInput;
            let ips = this.input.at(5).value.valuepairs;
            let arr1 = ips.filter(obj1 => {
              let name;
              try {
                name = JSON.parse(obj1.value).NAME || obj1.value;
              } catch (error) {
                name = obj1.value;
              }
              return rfcIps.includes(name);
            });
            const control = this.input.controls[5].get('valuepairs') as FormArray;
            control.controls = []
            for (let key in arr1) {
              this.addSapkeyValuepair(5, arr1[key].value)
            }
            // let count = 0;
            // this.input.controls.forEach(key => {
            //   if (key.value.inputName === "rfcinputs") {
            //     const control = this.input.controls[5].get('valuepairs') as FormArray;   
            //     for(let key in control.value){
            //       if(control.value[key].value.includes('RFCField') && control.value[key].value.includes('NAME')){
            //         count=count+1;
            //         for(let key2 in this.dataToProcess){
            //             if(JSON.parse(control.value[key].value).NAME != Object.keys(this.dataToProcess[key2]).toString()){
            //               this.dataToProcess.splice(key2,1);
            //             }
            //         }
            //       }
            //     }

            //      if(count == 0){
            //       this.dataToProcess=[];
            //       this.input.at(7).get('values').setValue(""); 
            //      }

            //  }
            //  })
          } else {
            let paramsVal = val;
            for (let key in paramsVal.RFCInput) {
              //  this.addSapkeyValuepair(5,paramsVal.RFCInput[key])
            }
          }
        } else if (this.expBuildData.inputName == 'rfcinputs') {
          const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
          control.at(this.expBuildData.keyvalindex).get('value').setValue(val);
          if (data.change == true) {
            this.formatdata(val);
            this.input.at(7).get('values').setValue(JSON.stringify(this.dataToProcess));
          } else {

          }
        } else {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
        }
      }
      else if (this.expBuildData.nodeName == "query" && this.expBuildData.callerformfieldtype == "keys") {
        this.dbparamVal = [];
        let data = JSON.parse(val);
        if (data.length == 0) {
          this.input.at(this.expBuildData.index).get('values').setValue('');
        } else {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
        }
        for (let key in data) {
          this.dbparamVal.push(Object.keys(data[key])[0])
        }
      }
      else {
        this.input.at(this.expBuildData.index).get('values').setValue(val);
      }
    }
    document.getElementById('expBuildClose').click();
  }

  dataToProcess: any = [];
  name: any;
  formatdata(value: any) {
    let parentObj = {};
    let finalFormat = JSON.parse(value);
    if (finalFormat.RFCField?.length > 0) {
      let childObj = {};
      for (let ii in finalFormat.RFCField) {
        if (finalFormat.RFCField[ii].isCustom) {
          childObj[finalFormat.RFCField[ii].NAME] = finalFormat.RFCField[ii].customValue
        } else {
          if (finalFormat.RFCField[ii].formValue != "testDemo") {
            childObj[finalFormat.RFCField[ii].NAME] = finalFormat.RFCField[ii].formValue
          }
        }
        finalFormat.RFCField.change = false;
      }
      parentObj[finalFormat.NAME] = childObj;
    } else {
      if (finalFormat.isCustom) {
        parentObj[finalFormat.NAME] = finalFormat.customValue
      } else {
        parentObj[finalFormat.NAME] = finalFormat.formValue
      }
      finalFormat.change = false;
    }
    if (this.dataToProcess.length > 0) {
      for (let key in this.dataToProcess) {
        if (Object.keys(this.dataToProcess[key])[0] == Object.keys(parentObj)[0]) {
          delete this.dataToProcess[key][Object.keys(this.dataToProcess[key])[0]];
        }
      }
      this.dataToProcess.push(this.removeEmpty(parentObj));
    } else {
      this.dataToProcess.push(this.removeEmpty(parentObj));
    }
    this.dataToProcess = this.dataToProcess.filter(value => Object.keys(value).length !== 0);
    // this.dataToProcess = this.dataToProcess.filter(obj =>Object.values(obj).some(value => value != ''));

  }

  convertArrayToObject(arr) {
    const result = {};
    arr.forEach(item => {
      Object.assign(result, item);
    });
    return result;
  }

  //prepare node data to save
  async savedata() {
    if (this.node.name == "sap") {
      if (this.dataToProcess.length > 0 && !this.showDataFiled) {
        let newData = this.convertArrayToObject(this.dataToProcess);
        this.input.at(7).get('values').setValue(JSON.stringify(newData));
      } else {
        //this.input.at(7).get('values').setValue("");
      }
    }
    const data = this.nodeformdata.value;
    if (this.jsonLogicRule) {
      data.jsonLogicRule = this.jsonLogicRule;
    }
    // if (this.nodeNameChange) {
    //   this.reteservice.flowTechName.next("change");
    // }
    data.wfName = this.dataItem.nodeData.wfName;
    data.wfNamespace = this.dataItem.nodeData.wfNamespace;
    data.icon = this.dataItem.nodeData.icon;
    data.category = this.dataItem.nodeData.category;
    data.savedata = true;
    data.description = this.dataItem.nodeData.description;
    data.name = this.dataItem.nodeData.name;
    let newInput = data.input;
    data.inputs = newInput;
    delete data.input;
    this.node.data = data;
  }

  //final form data and node data save
  saveNodedata() {
    let nodeId;
    if (this.nodeDesc == true) {
      this.focusIn();
    }
    nodeId = this.reteservice.getNodeButtonId();
    document.getElementById(nodeId).click();
    this.savedata();
    const data = this.editor.toJSON();
    this.reteservice.saveWfdata(this.editor['workflow'].wfId, data, true);
    // this.validataFileds();
  }

  validataFileds() {
    let validate: any;
    let totalMandateFields: number = 0;
    let mandateValue: number = 0;
    this.input.value.forEach((val) => {
      if (val.required === "Mandatory") {
        totalMandateFields = totalMandateFields + 1;
        if (val.values == "" || val.values == null || val.values == 'null') {
        } else {
          mandateValue = mandateValue + 1;
        }
      }
    });
    if (totalMandateFields == mandateValue && !this.duplicate) {//&& !this.checkforemptydesc
      validate = false;
    } else {
      validate = true;
    }
    return validate;
  }

  //remove node 
  removenode(node: any) {
    this.editor.removeNode(node);
  }

  //popup node delete confirm
  confirmNodeDelete(event: Event, node: any) {
    this.confirmationService.confirm({
      target: event.target,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.editor.removeNode(node);
        this.deletedData = this.reteservice.getDeleteNodedata();
        this.reteservice.popNodeId(node.id);
      },
      reject: () => {
      }
    });
  }
  copyToCB() {
    if (this.expressionArea) {
      this.expressionArea.nativeElement.select();
      document.execCommand("copy");
      this.expressionArea.nativeElement.setSelectionRange(0, 0);
    }
  }
  openTestData() {
    this.reteservice.openTestDataPanel.next('open');
  }

  changeName(data: any) {
    try {
      if (data.includes('NAME')) {
        return JSON.parse(data).NAME
      } else {
        return data
      }
    }
    catch {
      return data
    }
  }
  changeIcon(data) {
    try {
      if (data.includes('NAME')) {
        let jsonData = JSON.parse(data);
        if (jsonData.hasOwnProperty('formValue') == "" && jsonData.hasOwnProperty('customValue') == "" && jsonData.RFCField.length == 0) {
          return false;
        } else {
          if (jsonData.RFCField.length > 0) {
            for (let key in jsonData.RFCField) {
              if (jsonData.RFCField[key].change == true) {
                return true;
              }
            }
          }
          return true;
        }
      } else {
        return false;
      }
    }
    catch {
      return false;
    }
  }


  removeEmpty(parentObj: any) {
    Object.keys(parentObj).forEach(key => {
      if (parentObj[key] && typeof parentObj[key] === 'object') {
        this.removeEmpty(parentObj[key]);
      } else if (parentObj[key] === null || parentObj[key] === undefined || parentObj[key] === '') {
        delete parentObj[key];
      }
    });
    return parentObj;
  }
  checkForEmpty: boolean = false;
  openSapExpBuilderDialog(event: any, callerformfieldvalue: string, callerformfieldtype: string, inputName: string, index?: number, keyvalindex?: number) {
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "selectedSystem": this.selectedSystem,
      "inputName": inputName,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    this.expBuildVal = this.dataItem;
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal.selectedSystem = this.selectedSystem;
    this.expBuildVal.inputName = inputName;
    this.expBuildVal.formData = this.nodeformdata.value.input;
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
    if(this.selectedSystem != ''){ 
    this.ref = this.dialogService.open(ExpressionBuilderComponent, {
      header: 'Expression Builder',
      contentStyle: { overflow: 'auto', padding: '5px' },
      style: { "max-height": "75%", "max-width": "75%","z-index":999 },
      modal: true,
      maximizable: false,
      data: this.expBuildVal,
      draggable: true
    });
    this.ref.onClose.subscribe(async (res: any) => {
      if(res == undefined || res.type === 'cancel'){
        this.reteservice.getRunflowToDialogClose.next(true)
      }else{
      let val = res.expBuildValue;
      if (this.expBuildData.inputName == 'function') {
        if (res.change == true) {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
          this.input.at(this.expBuildData.index + 1).get('values').setValue("");
          this.sapParameters.setValue("");
          this.sapParameterValue = '';
          this.dispRfcOutputs = []
          this.input.controls.forEach(key => {
            if (key.value.inputName === "rfcinputs") {
              const control = this.input.controls[5].get('valuepairs') as FormArray;
              control.controls = [];
              key.patchValue({ valuepairs: [] });
            }
          })
          this.input.at(7).get('values').setValue("");
          this.dataToProcess = [];
          this.openSapExpBuilderDialog(event,'','text','parameters',index+1)
        } else {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
        }
      }
      if (this.expBuildData.inputName == 'parameters') {        
        let mapInputOutputs:RFCParameterFormat = JSON.parse(res.expBuildValue);
        let callerFormFieldValue = this.expBuildData.callerformfieldvalue !== '' ? JSON.parse(this.expBuildData.callerformfieldvalue) : {RFCInput:[],RFCOutput:[],RFCParameter:[]};
        if(mapInputOutputs){
          if(mapInputOutputs.RFCInput.length > 0){
            this.sapParameterValue = mapInputOutputs.RFCInput;
            this.sapParameterValue ? this.reteservice.warningMsg.next('inputs') : null;
            this.sapParameters.setValue(mapInputOutputs.RFCInput);   
            mapInputOutputs.RFCInput.forEach((input) => {
                const isDuplicateInCaller = callerFormFieldValue.RFCInput.some((input1: any) => input1 === input);          
                  if (!isDuplicateInCaller) {
                    const control = this.input.controls[5].get('valuepairs') as FormArray;         
                    const isDuplicateInControl = control.value.some((item: any) => {
                      try {
                        const parsedValue = JSON.parse(item.value);
                        return parsedValue.NAME === input;
                      } catch {
                        return item.value === input;
                      }
                    });            
                    if (!isDuplicateInControl) {
                      this.addSapkeyValuepair(5, input);
                    }
                  }
            });
            await this.updateRfcInputs(val);
          }

          if(mapInputOutputs.RFCOutput.length > 0){
            this.dispRfcOutputs = mapInputOutputs.RFCOutput;
          }else{
            this.dispRfcOutputs = [];
            this.reteservice.warningMsg.next('noOutputs');
          }

          if ( mapInputOutputs.RFCInput.length == 0 && mapInputOutputs.RFCOutput.length == 0) {
                this.input.at(this.expBuildData.index).get('values').setValue('');
              } else {
                this.input.at(this.expBuildData.index).get('values').setValue(val);
            }
      }
      else {
          this.dispRfcOutputs = [];
          this.sapParameterValue = '';
          this.sapParameters.setValue('');
          this.input.at(this.expBuildData.index).get('values').setValue('');
        }
       }
      if (this.expBuildData.inputName == 'rfcinputs') {
        const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
        control.at(this.expBuildData.keyvalindex).get('value').setValue(val);
        if (res.change == true) {
          this.formatdata(val);
          this.input.at(7).get('values').setValue(JSON.stringify(this.dataToProcess));
        }
       } 
      if(this.expBuildData.inputName == 'data'){
        this.input.at(this.expBuildData.index).get('values').setValue(val);
      }
    }
    });
    }else{
      this.reteservice.odataMessages.next({"type":"warning",'message':'Select SAP server and proceed'});
    }
  }

  openExpBuilderDialog(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem;
    this.expBuildVal.callerformfield = '';
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
    this.ref = this.dialogService.open(ExpressionBuilderComponent, {
      header: 'Expression Builder',
      contentStyle: { overflow: 'auto', padding: '5px' },
      style: { "max-height": "75%", "max-width": "75%","z-index":999 },
      //baseZIndex: 10000,
      modal: true,
      maximizable: false,
      data: this.expBuildVal,
      draggable: true
    });
    this.ref.onClose.subscribe(async (res: any) => {
      if(res == undefined){
        this.reteservice.getRunflowToDialogClose.next(true)
      }else{
      let val = res.expBuildValue;
      if (this.expBuildData.callerformfieldtype === 'keyvalue' && this.expBuildData.nodeName != "sap") {
        const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
        control.at(this.expBuildData.keyvalindex).get('value').setValue(val);
      } else {
        this.input.at(this.expBuildData.index).get('values').setValue(val);
      }
    }
    });

  }
  openDbExprBuilderDialog(event: any, callerformfieldvalue: string, callerformfield: string, index?: number, keyvalindex?: number){
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfield,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem
    this.expBuildVal.callerformfield = callerformfield;
    this.expBuildVal.selectedsystem = this.input.value[0].values;
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }    
    this.ref = this.dialogService.open(ExpressionBuilderComponent, {
      header: 'Expression Builder',
      contentStyle: { overflow: 'auto', padding: '5px' },
      style: { "max-height": "75%", "max-width": "75%","z-index":999 },
      // baseZIndex: 999,
      modal: true,
      maximizable: false,
      data: this.expBuildVal,
      draggable: true
    });
    this.ref.onClose.subscribe(async (res: any) => {
      if(res == undefined){
        this.reteservice.getRunflowToDialogClose.next(true)
      }else{
      let val = res.expBuildValue;
      if (this.expBuildData.nodeName == "query" && this.expBuildData.callerformfieldtype == "keys") {
        this.dbparamVal = [];
        let data = JSON.parse(val);
        let newData = this.convertArrayToObject(data);
        if (data.length == 0) {
          this.input.at(this.expBuildData.index).get('values').setValue('');
        } else {
         // this.input.at(this.expBuildData.index).get('values').setValue(val);
          this.input.at(this.expBuildData.index).get('values').setValue(JSON.stringify(newData));
        }        
        for (let key in data) {
          this.dbparamVal.push(Object.keys(data[key])[0])
        }
      }
      else {
        this.input.at(this.expBuildData.index).get('values').setValue(val);
      }
    }
    });
  }

async updateRfcInputs(val:any){
  let paramsVal = JSON.parse(val);
      //to add value to formsarray
          if(this.input.at(5).value.valuepairs.length > 0 && this.checkForEmpty==false){
          let oldRfcIps = this.input.at(5).value.valuepairs;
          oldRfcIps.forEach((item)=>{
           if(item.value.includes('RFCField') && item.value.includes('SAP_TYPE') && item.value.includes('NAME')){
              for(let key in paramsVal.RFCInput){
                  if(paramsVal.RFCInput[key] == JSON.parse(item.value).NAME){
                    paramsVal.RFCInput.splice(key, 1);
                    paramsVal.RFCParameter.splice(key, 1);
                  } 
              }
           }else{
            for(let key in paramsVal.RFCInput){
              if(paramsVal.RFCInput[key] == item.value){
                paramsVal.RFCInput.splice(key, 1);
                paramsVal.RFCParameter.splice(key, 1);
              } 
             }
            }
          });
          for(let key in paramsVal.RFCInput){
           await this.addSapkeyValuepair(5,paramsVal.RFCInput[key])
          }
          await this.removeRfcips(val);
        }else{
            await this.removeRfcips(val);
        }
}

async removeRfcips(val:any){
  let paramsVal2 = JSON.parse(val);
  if(this.input.at(5).value.valuepairs.length > 0 && this.checkForEmpty==false){
    let formsArrVal=this.input.at(5).value.valuepairs;
    let arr=[]
    for(let key in paramsVal2.RFCInput){
      formsArrVal.forEach((item,ind)=>{
        if(item.value.includes('RFCField') && item.value.includes('SAP_TYPE') && item.value.includes('NAME')){
            if(paramsVal2.RFCInput[key] == JSON.parse(item.value).NAME){
                arr.push(item.value)
            }
        }else{
          if(paramsVal2.RFCInput[key] == item.value){
            arr.push(item.value)
        }
        }
      })
    }
    const control = this.input.controls[5].get('valuepairs') as FormArray;  
    control.controls=[]
    for(let key in arr){
     await this.addSapkeyValuepair(5,arr[key])
    }
    await this.updateFormatedData();
   }else{
    await this.updateFormatedData();
   }
}
async updateFormatedData(){
    if(this.input.at(5).value.valuepairs.length > 0){
      let formsArrVal=this.input.at(5).value.valuepairs;
      let arr1=[];
      for(let obj of this.dataToProcess){
        const outerKey = Object.keys(obj)[0];
      formsArrVal.forEach((item,ind)=>{
        if(item.value.includes('RFCField') && item.value.includes('SAP_TYPE') && item.value.includes('NAME')){
             if(outerKey == JSON.parse(item.value).NAME){
                 arr1.push(JSON.parse(item.value))
             }
        }else{
          if(outerKey == item.value){
        }
        }
      })
    }
   this.dataToProcess=[];
   for(let newarr in arr1){
       this.formatdata(JSON.stringify(arr1[newarr]))
   }
   if(arr1.length == 0){
    this.input.at(7).get('values').setValue('');
   }else{
    this.input.at(7).get('values').setValue(JSON.stringify(this.dataToProcess));
   }
      }else{

        // this.dataToProcess=[];
        // this.input.at(7).get('values').setValue('');
    }
  }

  compareRFCObjects(obj1, obj2) {
    let differences = [];
    if (JSON.stringify(obj1.RFCInput) !== JSON.stringify(obj2.RFCInput)) {
      differences.push("RFCInput");
    }
    if (JSON.stringify(obj1.RFCOutput) !== JSON.stringify(obj2.RFCOutput)) {
      differences.push("RFCOutput");
    }
    if (obj1.RFCParameter.length !== obj2.RFCParameter.length) {
      differences.push("RFCParameter");
    }
    return differences;
  }

  showDataFiled:boolean=false;
  async getSapmapType(value:any){
    if(value=='default'){
      this.showDataFiled=false;
      this.dataToProcess=[];
      this.input.at(7).get('values').setValue("");
    }else{
      this.showDataFiled=true;
      await this.clearInputsMappedValue();
    }    
  }

 async confirm(event: Event,value:any) {
  let val = value;
  this.input.at(4).get('values').setValue("");
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Clear mapped information and proceed?',
        icon: 'pi pi-exclamation-triangle',
        accept: async () => {
          this.input.at(4).get('values').setValue(val);
           await this.getSapmapType(val);
        },
        reject: () => {
          if(val == 'manual'){
            this.input.at(4).get('values').setValue('default');
          }else{
            this.input.at(4).get('values').setValue('manual');
          }
        }
    });
}
 async clearInputsMappedValue(){
  if(this.input.at(5).value.valuepairs.length > 0){
      let formsArrVal=this.input.at(5).value.valuepairs;
      formsArrVal.forEach((item,ind)=>{
        if(item.value.includes('RFCField') && item.value.includes('SAP_TYPE') && item.value.includes('NAME')){
            item.value = JSON.parse(item.value).NAME;
            this.dataToProcess=[];
            this.input.at(7).get('values').setValue("");
        }
      });
  }else{    
  }
  }

// for odata new one
odataOptionselected :any;
odataOptionEntitySelectedValue:boolean=false;
navigations:any=[];
oDataEntityTypes: any = []
selectfieldList:any=[];
selectedMetaData:any;
formatOdataMetadata:any;
disableDropdown:boolean;
oDataEnitySelectedObject:any=[];
odataSelectedVal:any;

//onChange select odata opearations
 async selectOdataOperation(data:any){
  this.odataOptionselected=data;
    if(data == 'Select' || data == 'Read'){
      this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared entity,expand and fields information'})  
    this.input.controls.forEach((key,index) => {
      if (key.value.inputName == "expandtype") {
        if(index != 3){
        const element6 = this.input.controls.splice(6, 1)[0]; // Remove element at index 7 and get it
         this.input.controls.splice(3, 0, element6); // Insert the element at index 7 to index 4
        }
      }
      if (key.value.inputName == "expand") {
        if(index != 4){
        const element7 = this.input.controls.splice(7, 1)[0]; // Remove element at index 6 and get it
        this.input.controls.splice(4, 0, element7); // Insert the element at index 6 to index 3
        }
      }
      if(data !== 'Read'){
      if (key.value.inputName === "entitydata") {
        key.patchValue({ values: "" });
      }
    }
    })
  }else{
    this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared entity,expand and fields information'})  
    this.input.controls.forEach((key,index) => {
      if (key.value.inputName === "entity") {
        key.patchValue({ values: "" });
      }
      if (key.value.inputName === "expand") {
        key.patchValue({ values: "" });
       // key.patchValue({ required: "Optional"});
      }
      if (key.value.inputName === "fields") {
        key.patchValue({ values: '' });
        this.selectfieldList=[]
      }
      if (key.value.inputName === "metadata") {
        key.patchValue({ values: '' });
      }
      if (key.value.inputName === "filter") {
        key.patchValue({ values: '' });
      }
      if(key.value.inputName == "parameters"){
        const control = this.input.controls[index].get('valuepairs') as FormArray;
        control.controls = [];
        key.patchValue({ valuepairs: [] });            
    }
    })
  }
  }

// onclick get entity type list
  async getEntityTypeList(data:any){
       this.odataSelectedVal=data; 
    this.reteservice.serviceOdata(this.selectedSystem, []).subscribe((res) => {
      if (res.error === '') {
        if (res.status.toLowerCase() === 'success') {
          if(data != ''){
          this.allEntyityTypes = [{'key':data,"value":data}];
          }else{
            this.allEntyityTypes = []
          }
          res.data.forEach((res) => {
            this.allEntyityTypes.push({'key':res,"value":res});
          });
          this.allEntyityTypes = this.allEntyityTypes.reduce((unique, o) => {
            if(!unique.some(obj => obj.label === o.label && obj.value === o.value)) {
              unique.push(o);
            }
            return unique;
        },[]);  
        }
      } else {
        this.reteservice.odataMessages.next({"type":'error','message':res.error})  
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: res.error });
        this.errmsg = res.error;
      }
    });
  
  }

//onChange get navigations
  async selectedEntityTypes(newValues:any) {
    this.navigations=[];
    this.reteservice.serviceOdata(this.selectedSystem, [newValues]).subscribe((res) => {
      let response = res;
      if (response.error === '') {
        if (response.status.toLowerCase() === 'success') {
           let data = response.data; 
           if(this.odataSelectedVal != ''){
            this.reteservice.odataMessages.next({"type":"warning",'message':'Cleared expand entity and fields information'})  
           }else{
           // this.reteservice.odataMessages.next(false)
           }
           this.input.controls.forEach(key => {
            if (key.value.inputName === "expand") {
              key.patchValue({ values: '' });
            }
            if (key.value.inputName === "fields") {
              key.patchValue({ values: '' });
            }
            if (key.value.inputName === "metadata") {
              key.patchValue({ values: '' });
            }
          })          
           this.oDataEntityTypes=[]
          for (let key in data) {
            this.oDataEntityTypes.push(JSON.parse(data[key]));
          }
         }
        let val = this.input.at(2).get('values').value;
        let upadteddata;
        this.oDataEnitySelectedObject=[]
        this.oDataEntityTypes.forEach((item)=>{
          if(item.entityType == val){
             upadteddata = item.navigation;
             this.oDataEnitySelectedObject.push(item)
          }
        })
        for(let key in upadteddata){
              this.navigations.push(upadteddata[key])
            } 
        this.navigations=this.navigations.filter(function(item, pos, self) {
          return Array.isArray(item) == false;          
      });
      this.selectfieldList=this.oDataEnitySelectedObject;
      this.selectfieldList.forEach((item)=>{
    if(item.allFields.length > 0){
      let pName=item.entityType;
      item.allFields.forEach((data)=>{
        data.parent = pName;
      })
    }      
  })
    //   this.navigations = this.navigations.reduce((unique, o) => {
    //     if(!unique.some(obj => obj.label === o.label && obj.value === o.value)) {
    //       unique.push(o);
    //     }
    //     return unique;
    // },[]);
      } else {
        this.errmsg = response.error;
        this.reteservice.odataMessages.next({"type":'error','message':response.error})  
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });

      }
    });

  }
  //radio click with confirm 
  expandTypeconfirm(event:any,value){
    let val = value;
    this.input.controls.forEach(key => {
      if (key.value.inputName === "expandtype") {
        key.patchValue({ values: "" });
      }
    })
      this.confirmationService.confirm({
          target: event.target as EventTarget,
          message: 'Clear mapped information and proceed?',
          icon: 'pi pi-exclamation-triangle',
          accept: async () => {              
            this.input.controls.forEach(key => {
              // if (key.value.inputName === "entity") {
              //   key.patchValue({ values: "" });
              // }
              if (key.value.inputName === "expandtype") {
                key.patchValue({ values: val });
              }
              if (key.value.inputName === "expand") {
                key.patchValue({ values: '' });
              }
              if (key.value.inputName === "fields") {
                this.selectNavigations('')
                key.patchValue({ values: '' });
              }
              if (key.value.inputName === "metadata") {
                key.patchValue({ values: '' });
              }
            })
            if(val == 'manual'){
                this.odataOptionEntitySelectedValue=true;
            }else{
               this.odataOptionEntitySelectedValue=false;
            }
          },
          reject: () => {
            if(val == 'manual'){
              this.input.controls.forEach(key => {
                if (key.value.inputName === "expandtype") {
                  key.patchValue({ values: 'default' });
                }
              })
            }else{
              this.input.controls.forEach(key => {
                if (key.value.inputName === "expandtype") {
                  key.patchValue({ values: 'manual' });
                }
              })
            };
          }
      });
  }
//expand onPanelHide
async selectNavigations(event:any){
  if(event == '' || event.length == 0){
    this.input.controls.forEach(key => {
      if (key.value.inputName === "expand") {
        key.patchValue({ values: '' });
      }
    });
  }
    this.selectfieldList=[];
     let newData=[];
     newData.push(this.oDataEntityTypes[0]);
     this.oDataEntityTypes.forEach((item)=>{
        for(let key in event){
            if(event[key] == item.entitySet || event[key] == item.entityType){
              newData.push(item)
            }
        }
     });
     this.selectfieldList=newData;
      this.selectfieldList.forEach((item)=>{
    if(item.allFields.length > 0){
      let pName=item.entityType;
      item.allFields.forEach((data)=>{
        data.parent = pName;
      })
    }      
  })
  }
// fileds on panel hide with final format
async mapMetaData(event:any){
this.formatOdataMetadata=[];
this.formatOdataMetadata = this.selectfieldList;
  for(let key in this.formatOdataMetadata){
    this.formatOdataMetadata[key].fields=[];
    for(let key2 in event){
      if(this.formatOdataMetadata[key].entityType == event[key2].parent){
        this.formatOdataMetadata[key].fields.push(event[key2]);
        this.formatOdataMetadata[key].fields =  this.formatOdataMetadata[key].fields.filter((value, index, self) =>
        index === self.findIndex((t) => (
          t.name === value.name
        )));
      }
    }
  }  
  this.input.controls.forEach(key => {  
    if (key.value.inputName === "metadata") {
     //  let data =  this.formatOdataMetadata;
    //  data.forEach((item) => {
    //   delete item.allFields;
    //   item.fields = item.fields.map(({ nullable, name, type }) => ({ nullable, name, type }));
    // });    
      key.patchValue({ values: JSON.stringify(this.formatOdataMetadata) });
    }
  }) 
}

checkForNone(event:any){
if(event == true){
  this.disableDropdown=true;
  this.input.controls.forEach((key,index) => {
    if (key.value.inputName === "expand") {
      key.patchValue({ values: "" });
      //key.patchValue({ required: "Optional"});
    }
    if (key.value.inputName === "fields") {
      this.selectNavigations('')
      key.patchValue({ values: '' });
    }
    if (key.value.inputName === "metadata") {
      key.patchValue({ values: '' });
    }
  })
}else{
  this.disableDropdown=false;
  this.input.controls.forEach((key,index) => {
    if (key.value.inputName === "expand") {
      key.patchValue({ values: "" });
     // key.patchValue({ required: "Mandatory" });
    }
    // if (key.value.inputName === "fields") {
    //   this.selectNavigations('')
    //   key.patchValue({ values: '' });
    // }
    // if (key.value.inputName === "metadata") {
    //   key.patchValue({ values: '' });
    // }
  })
}
}
// update oData
noneValChecked: boolean = false;
selectOdataEntityUpdate:any;
selectedOdataFileds:any;
updatedNavigation:any
selectedNavigationsToUpdate:any;
oDataLoader:boolean=false;

async OdataInputParams(inputParameter: any){
  if(inputParameter.required == 'Mandatory'){
    inputParameter.show = true;
  }else{
    inputParameter.show = false;
  }
inputParameter.radiolistvalue = [];
inputParameter.listvalue = [];
if ((inputParameter.inputName == 'operation' || inputParameter.type === "radio") && inputParameter.listvalues && Array.isArray(inputParameter.listvalues) && !Array.isArray(inputParameter.listvalues[0])) { inputParameter.listvalues = [inputParameter.listvalues]; }
  if(inputParameter.inputName == 'system'){
    this.selectedSystem = inputParameter.values;
     this.addinputs(inputParameter)
    }else if (inputParameter.inputName == 'operation') {
      inputParameter.listvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.listvalue[0].push({ "key": data, "value": data })
        });
       inputParameter.values = inputParameter.values;
        this.selectOdataOperationUpdate(inputParameter.values) ;
        this.addinputs(inputParameter)
      }
    }else if(inputParameter.inputName == 'entity'){
      this.allEntyityTypes.push({'key':inputParameter.values,"value":inputParameter.values});
      this.oDataLoader=true;
     this.upateOdataApis(inputParameter.values)
      this.addinputs(inputParameter)
    } else if (inputParameter.inputName == 'expandtype' && inputParameter.type === "radio" && inputParameter.listvalues) {
      inputParameter.radiolistvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.radiolistvalue[0].push({ "key": data, "value": data.toLowerCase().replace(/\s+/g, '') })
        });
      }
      inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
      if(inputParameter.values == 'manual'){
        this.odataOptionEntitySelectedValue=true;
      }else{
        this.odataOptionEntitySelectedValue=false;
      }
      await this.addinputs(inputParameter)
    } else if(inputParameter.inputName == 'expand'){
      // if(inputParameter.values != ''){
      //   this.disableDropdown=false;
      // }else{
      //   this.disableDropdown=true;
      // }
      if(inputParameter.values && Array.isArray(inputParameter.values) && !Array.isArray(inputParameter.values[0])){
        this.selectedNavigationsToUpdate = inputParameter.values
        inputParameter.values = [inputParameter.values];
      }
      //this.selectedNavigationsToUpdate = inputParameter.values
       this.addinputs(inputParameter)
    } else if(inputParameter.inputName == 'fields'){
      if(!inputParameter.values[0]){
        inputParameter.values = [inputParameter.values];
      }
      this.selectedOdataFileds = inputParameter.values;
       this.addinputs(inputParameter);
      await this.setFieldsData();
    } 
    else {
      inputParameter.values = inputParameter.values;
      await this.addinputs(inputParameter)
    }
}
async setFieldsData(){
  this.input.controls.forEach((item,index)=>{
        if (item.value.inputName === "fields") {
          item.patchValue({ values: this.selectedOdataFileds });
    }
  })
}
async addinputs(inputs:any){
  this.input.push(this.fb.group(inputs));
}

upateOdataApis(event){
    this.navigations=[];
    this.reteservice.serviceOdata(this.selectedSystem, [event]).subscribe((res) => {
      let response = res;
      if (response.error === '') {
        if (response.status.toLowerCase() === 'success') {
          this.oDataEntityTypes=[]
           let data = response.data;
          for (let key in data) {
            this.oDataEntityTypes.push(JSON.parse(data[key]));
          }
         }
         let val = this.input.at(2).get('values').value;
         let upadteddata
         this.oDataEntityTypes.forEach((item)=>{
           if(item.entityType == val){
              upadteddata = item.navigation
           }
         });
        // this.navigations.push('None')
        for(let key in upadteddata){
              this.navigations.push(upadteddata[key])
            } 
        this.navigations=this.navigations.filter(function(item, pos, self) {
          return Array.isArray(item) == false;          
      });
      this.selectfieldList=[];
       let newData=[];
       newData.push(this.oDataEntityTypes[0]);       
       this.oDataEntityTypes.forEach((item)=>{        
          for(let key in this.selectedNavigationsToUpdate){
              if(this.selectedNavigationsToUpdate[key] == item.entitySet){
                newData.push(item)
              }
          }
       })
       this.selectfieldList=newData;
        this.selectfieldList.forEach((item)=>{
      if(item.allFields.length > 0){
        let pName=item.entityType;
        item.allFields.forEach((data)=>{
          data.parent = pName;
        })
      }
    });
    this.input.controls.forEach((item,index)=>{
      if(item.value.inputName === "expand"){
          if(item.value.values == ''){
            this.disableDropdown=true;
          }else{
            this.disableDropdown=false;
          }
      } 
    });
    this.oDataLoader=false;
      } else {
        this.errmsg = response.error;
        this.reteservice.odataMessages.next({"type":'error','message':response.error})  
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });
        this.oDataLoader=false;

      }
    });

}
async selectOdataOperationUpdate(data:any){
  this.odataOptionselected=data;
    if(data == 'Select' || data == 'Read'){
    this.input.controls.forEach((key,index) => {
      if (key.value.inputName == "expandtype") {
        if(index != 3){
        const element6 = this.input.controls.splice(6, 1)[0]; // Remove element at index 7 and get it
         this.input.controls.splice(3, 0, element6); // Insert the element at index 7 to index 4
        }
      }
      if (key.value.inputName == "expand") {
        if(index != 4){
        const element7 = this.input.controls.splice(7, 1)[0]; // Remove element at index 6 and get it
        this.input.controls.splice(4, 0, element7); // Insert the element at index 6 to index 3
        }
      }
    })
  }else{
    this.input.controls.forEach((key,index) => {
      if (key.value.inputName === "expandtype") {
      }
      if (key.value.inputName === "entity") {
       // key.patchValue({ values: "" });
      }
      if (key.value.inputName === "expand") {
       // key.patchValue({ values: "" });
       // key.patchValue({ required: "Optional"});
      }
      if (key.value.inputName === "fields") {
      //  key.patchValue({ values: '' });
       // this.selectfieldList=[]
      }
      if (key.value.inputName === "metadata") {
      //  key.patchValue({ values: '' });
      }
    })
  }
  }
}