.nodeIcon{
   // color: var(--purple-500)!important;
   // padding: 12px; 
   width: 50px;
   height: 47px;
   font-size: 26px;
    background: var(--purple-100)!important;
  }
  .nodeIconSap{
    padding: 3px; 
    height: 47px;
    background: var(--purple-100)!important;
  }
  .pmgImg{
    margin-top: 8px;
    margin-left: 10px;
    // margin-bottom: 0px;
    height: 30px !important;
    width: 30px !important;
  }
  .p-inputtext {
    // padding : 8px 35px 8px 10px !important;
    width: -webkit-fill-available;
  }

  
::ng-deep .formCard .p-card .p-card-body{
  padding: 0px!important;
}
::ng-deep .formhttpCard .p-card .p-card-body{
  padding: 10px  0px!important;
}
::ng-deep .formSapCard .p-card .p-card-body{
  padding: 5px 0px!important;
}
formSapCard
.lable{
  margin-bottom:0px !important
}

// ::ng-deep .selectBtn .p-button {
//   font-size: 0.875rem !important;
//   padding: 8px !important;
//   box-shadow: none !important;
// } 
::ng-deep .selectBtn1 .p-button {
  font-size: 0.875rem !important;
  padding: 8px !important;
  box-shadow: none !important;
  width: 50% !important;
} 
::ng-deep .selectBtn2 .p-button {
  font-size: 0.875rem !important;
  padding: 8px !important;
  box-shadow: none !important;
  width: 33.3% !important;
} 
::ng-deep .sapselectBtn .p-button {
  font-size: 0.875rem !important;
  padding: 8px !important;
  box-shadow: none !important;
  margin-bottom: 10px !important;
} 

.lable{
  margin-bottom:0px !important
}

::ng-deep .sapRfcButton{
  margin-left: 20px;
  margin-bottom:  8px;
  color: var(--primary-color);
 font-size: 1.5rem
}

::ng-deep .rfcButton .p-button {
  padding: 8px !important;
  margin-bottom: 10px !important;
} 

::ng-deep .warnMsg .p-inline-message{
  width: 100%;
}

::ng-deep .warnMsg .p-message .p-message-wrapper{
  padding: 10px 10px;
}

::ng-deep .dbCard .p-card .p-card-body{
  padding: 0px!important;
}
::ng-deep .dbCard .p-card .p-card-content{
  padding: 1px !important;
  padding-bottom: 5px !important;
}
.selected {
  // box-shadow: 10px 5px 5px red;
  border: 2px solid rgb(145, 149, 139);
  // box-shadow:0 0 6px 6px rgb(145, 149, 139);
}
:host ::ng-deep .fieldDisplay {
  display: none !important;
}