<div class="nodes-input-socket" *ngFor="let input of inputs">
    <rete-socket rete-socket [io]="input" [socket]="input.socket"></rete-socket>
</div>
<div style="display: inline-flex;" >
<div class="node-container" [ngClass]="[selected()]">
    <p-panel toggleable="true" [collapsed]="openPanelForNew" expandIcon="pi pi-chevron-up" collapseIcon="pi pi-chevron-down " class="panelOpen border-noround"
      (onBeforeToggle)="[panelBtn($event,node),expbuilder.hide()]">
     <ng-template pTemplate="header">
       <!-- <span class="nodeIcon" pTooltip="{{this.nodeName}}" tooltipPosition="top" *ngIf="node.name !== 'sap'"><i class="fas {{this.nodeIcon}}"></i>
       </span> -->
       <span class="nodeIcon" pTooltip="{{this.nodeName}}" tooltipPosition="top" *ngIf="node.name !== 'sapa'"><img class="pmgImg" src="assets/icon/{{this.nodeIcon}}.png"></span>

     <svg *ngIf="node.name === 'sapa' " class="nodeIconSap" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="50" height="43" viewBox="0 0 1080 1080" xml:space="preserve">
       <g transform="matrix(1 0 0 1 540 540)" id="02953293-5fd9-44ca-9c32-7bfc459898bf"  >
       <rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
       </g><g transform="matrix(1 0 0 1 540 540)" id="70ac1fec-5595-47b2-8de5-10e0fb839d51"></g><g transform="matrix(NaN NaN NaN NaN 0 0)"><g ></g>
       </g><g transform="matrix(21.6 0 0 21.6 540 540)"  >
       <path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  transform=" translate(-25.5, -24)" d="M 1 12 L 1 36 L 26 36 L 50 12 Z M 8.398438 18 C 11.800781 18 12.902344 18.800781 12.902344 18.800781 C 12.902344 18.800781 12.300781 20.300781 12 21.097656 C 11 20.699219 7 20 7 21.5 C 7 22.101563 7.199219 22.5 10.199219 23.199219 C 11.398438 23.5 13.199219 24.101563 13.597656 25.902344 L 16.699219 18 L 20.199219 18 L 24 27.5 L 24 18 L 28.5 18 C 31 18 33 20 33 22.5 C 33 25 31 27 28.5 27 L 27 27 L 27 30 L 21.5 30 L 20.902344 28.199219 C 20.199219 28.5 19.398438 28.699219 18.5 28.699219 C 17.601563 28.699219 16.800781 28.5 16.097656 28.199219 L 15.5 30 L 12 30 L 12.5 28.800781 C 11.601563 29.5 10.199219 30 8.398438 30 C 5.101563 30 4 29.097656 4 29.097656 L 4.800781 26.5 C 6.699219 27.699219 10.097656 27.5 10.097656 26.5 C 10.097656 25.5 8.601563 25.402344 6.601563 24.800781 C 4.699219 24.199219 4 22.699219 4 21.402344 C 4 20.101563 5 18 8.398438 18 Z M 27 21 L 27 24 L 28.5 24 C 29.300781 24 30 23.300781 30 22.5 C 30 21.699219 29.300781 21 28.5 21 Z M 18.5 21.402344 L 17 25.699219 C 17.398438 26 17.898438 26.097656 18.5 26.097656 C 19.101563 26.097656 19.601563 26 20 25.699219 Z" stroke-linecap="round" />
       </g><g transform="matrix(8.32 0 0 5.09 327.3 508.56)" id="14e3cb22-c1f3-4f2f-8590-a9fa04f9fa8c"  >
       <rect style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x="-33.0835" y="-33.0835" rx="0" ry="0" width="66.167" height="66.167" />
       </g><g transform="matrix(2.47 0 0 3.38 626.41 497.65)" id="49a1d38c-f87a-41e6-90d8-7141810959e1"  >
       <circle style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  cx="0" cy="0" r="35" />
       </g>
     </svg>  
     <span class="ml-5">
      <div *ngIf="!this.readonlyOnDisable">
       <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && displayBtn"  >{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
       <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && displayBtn">{{node.name}}</span>
         <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && !displayBtn" (click)="focusIn()">{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
         <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && !displayBtn" (click)="focusIn()">{{node.name}}</span>
         <input *ngIf="nodeDesc && displayBtn"  id="inputBox" (click)="focusIn()" (focusout)="focusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
         <input *ngIf="nodeDesc && !displayBtn"   id="inputBox" (click)="focusIn()" (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
        </div>
        <div *ngIf="this.readonlyOnDisable">
          <span class="spanText">{{node.data.description}}</span>
        </div>
       </span>
     </ng-template>
     <ng-template pTemplate="content" >
        <div class="cardBody">
         <div *ngIf="this.error != '' " style="margin-top: 30px;padding: 30px;">
           <span style="color: tomato;">{{this.error}}</span>
         </div>
         <div *ngIf="this.duplicate" style="margin-top: 10px; margin-right: 8px;">
          <p-message class="warnMsg" severity="warn" text="Node name should be unique."></p-message> 
        </div>
        <div *ngIf="this.checkforemptydesc" style="margin-top: 10px; margin-right: 8px;">
          <p-message class="warnMsg" severity="warn" text="Node description is missing."></p-message> 
        </div>
         <form [formGroup]="nodeformdata" novalidate style="border: 0px; margin-right: 5px;padding-left: 2px;margin-bottom: 20px;" autocomplete="off" *ngIf="!displayBtn">
          <p-progressBar mode="indeterminate" [style]="{ height: '6px' }" *ngIf="oDataLoader"></p-progressBar> 
          <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
              <div [formGroupName]="ind">
                <!--System  add ,update and delete-->
                <div *ngIf="field.value.inputName === 'system'">
                <div class="row align-items-center" style="display: flex; " >
                  <span class="p-float-label mt-4 col-9 p-0">
                    <p-dropdown class="integrationNode" [options]="allSystem" (onClick)="getSelectedSystemValue()" optionLabel="sysName"  scrollHeight="150px" emptyMessage="No systems found" 
                      appendTo="body" placeholder="Select a System"  optionValue="sysName" (onChange)="setSystemValue($event.value)"  formControlName="values" [pTooltip]="field.value.description"><!--[showClear]="true" (onClick)="getallsystems()"-->
                      <ng-template let-allSystem pTemplate="item">
                          <div class="country-item">
                              <div>{{allSystem.sysName}}</div>
                          </div>
                      </ng-template>
                      
                    </p-dropdown>
                    <label htmlFor="Description" *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label htmlFor="Description" *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                <div class="col-1 p-2" style="margin-top: 22px;">
                  <i class="pi pi-cog cursor"   *ngIf="field.value.values != '' && !readonlyOnDisable" (click)="[updateaddedit.toggle($event),addSystem('update', field.value.values)]"></i>
                </div>
                <div class="col-1 p-2" style="margin-top: 22px;">
                  <i class="pi pi-plus cursor" *ngIf="!readonlyOnDisable" (click)="[newaddedit.toggle($event),addSystem('create', field.value.values)]"></i>
                </div>
                <div class="col-1 p-2" style="margin-top: 22px;">
                  <i class="pi pi-trash cursor"  style="color: rgb(255, 97, 97);" *ngIf="field.value.values != '' && !readonlyOnDisable" (click)="confirmDelete($event,input?.at(ind).get('values').value)" ></i>
                </div>
              </div>
                </div>
              <!--System  add ,update and delete End-->
<!--REST STARTS-->
              <div *ngIf="this.node.name == 'http' " >
              <!--TYPE === "LIST"-->
                <div  style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <span class="p-float-label mt-4 w-full">
                  <p-dropdown class="integrationNode" [options]="field.value.listvalue"   [pTooltip]="field.value.description"
                  optionLabel="key" optionValue="value" [autoDisplayFirst]="true" appendTo="body"  placeholder="Select...." formControlName="values">
                </p-dropdown><!--[showClear]="true"-->
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT"-->
                <div *ngIf="field.value.type === 'text'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <span class="p-float-label p-input-icon-right mt-4 w-full">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description" />
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"  (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE === "KEYVALUE" & CARD with KEY VALUES-->
                <div *ngIf="field.value.type === 'keyvalue'" [ngClass]="{'fieldDisplay': field.value.show == true}" class="my-1" >
                   <p-card class="formhttpCard" >
                    <div class="mb-2">
                      <span class="ml-3 font-bold">{{field.value.inputName}}</span>
                      <span style="float: right;">
                        <i class="pi pi-plus mr-4 cursor"  *ngIf="!readonlyOnDisable" style="font-size: 1rem;" (click)="addkeyValuepair(ind, '', '')"></i> 
                       </span>
                    </div>
                    <div *ngIf="field.value.type === 'keyvalue'" formArrayName="valuepairs">
                      <div class="form-row" *ngFor="let keyvalue of getkeyvaluecontrol(field); index as i">
                          <ng-container [formGroupName]="i">
                          <div class="row p-0 m-0">
                          <span class="p-float-label p-input-icon-right mt-3 col-5 p-0 mx-1">
                            <input pInputText formControlName="key" placeholder="Parameter key" [pTooltip]="field.value.description"/>
                            <label style="font-size: 12px;">Parameter Key</label>
                          </span>
                          <span class=" p-float-label p-input-icon-right mt-3 col-5 p-0">
                            <input pInputText formControlName="value" placeholder="Parameter value" [pTooltip]="field.value.description"/>
                            <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, keyvalue.value.value, field.value.type, ind, i)"></i>
                            <label style="font-size: 12px;">Parameter Value</label>
                          </span>
                          <i class="pi pi-trash col-1 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97); margin-top: 24px;"  (click)="removeKeyValue(ind,i)"></i>
                          </div>
                        </ng-container>
                      </div>
                     </div>
                  </p-card> 
                </div> 
                <!--END-->
                <!--TYPE === CHECKBOX-->
                <div class="mt-3 ml-2" *ngIf="field.value.type === 'boolean'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <p-checkbox formControlName="values" [binary]="true" [pTooltip]="field.value.description"></p-checkbox>
                  <label  *ngIf="field.value.required == 'Mandatory'" class="required ml-3 lable">{{field.value.label}}</label>
                  <label *ngIf="field.value.required !== 'Mandatory'" class="ml-3 lable">{{field.value.label}}</label>
                </div>
                <!--END-->
                
              </div>

<!--END REST-->
<!--DATABASE OPERATION STARTS-->
              <div *ngIf="this.node.name == 'query' ">
                  <!--TYPE === "LIST" no condition-->
                  <div style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' && field.value.inputName !== 'operation'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="field.value.listvalues" [autoDisplayFirst]="true" appendTo="body"   placeholder="Select...."
                      [pTooltip]="field.value.description"  formControlName="values"><!--[showClear]="true"-->
                        <ng-template let-items pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{items}}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with no codition--> 
                  <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName !== 'query' && field.value.inputName !== 'keys' && field.value.inputName !== 'data'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openDbExprBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                  </div>
                  <!--END-->
                <!--TYPE ==="RADIO"-->
                <div class="mt-1 mb-2" *ngIf="field.value.type === 'radio'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                    optionLabel="key" optionValue="value" (onChange)="selectDBOption($event.value)"></p-selectButton>
                </div>
                <!--END-->
                  <!--TYPE == "TEXT" condtion Query with debug on-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'query' && selectedRadioValue==='Query'" >
                    <span class="p-float-label p-input-icon-right mt-3" style="width: -webkit-fill-available;">
                      <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openDbExprBuilderDialog($event,field.value.values,field.value.inputName, ind)"></i>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                  </div>
                  <!--END-->
                  <!--TYPE === "LIST" with selected Operation-->
                  <div style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' && selectedRadioValue==='Operation'" >
                    <span class="p-float-label mt-3 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="field.value.listvalue"  optionLabel="key" optionValue="value"  appendTo="body"  placeholder="Select...." 
                      formControlName="values" [pTooltip]="field.value.description"> </p-dropdown>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" condtion Operation--><!--need to change this-->
                  <div style="display: block;" *ngIf="!debugMode">
                  <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'keys' && selectedRadioValue==='Operation'" >
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"  (click)="openDbExprBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>1
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>1
                  </span>
                  </div>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" condtion keys with debug off-->
                  <div style="display: block;" *ngIf="debugMode" class="mt-3">
                  <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'keys' && selectedRadioValue==='Operation'" >
                  <p-card class="w-full dbCard">
                    <div class="my-2">
                      <span class="ml-3 font-bold _required">Keys :</span>
                    </div>
                    <div class="grid m-0">
                        <div class="col-11 p-0">
                          <span *ngFor="let keyvalue of this.dbparamVal" style="padding: 8px !important;"> 
                              <p-button class="rfcButton" label="{{keyvalue}}" [disabled]="true" styleClass="p-button-text p-button-raised"></p-button>
                            </span>
                        </div>
                        <div class="col-1 p-0">
                          <i class="pi pi-wrench cursor mb-2" *ngIf="!readonlyOnDisable" (click)="openDbExprBuilderDialog($event,field.value.values,field.value.inputName, ind)"></i>
                        </div>
                    </div>
                    </p-card>
                  </div>
                </div> 
                  <!--END-->
                  <!--TYPE == "TEXT" condtion Operation-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'data' && selectedRadioValue==='Operation'" >
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openDbExprBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                  </div>
                  <!--END-->
                  
                <!--TYPE === CHECKBOX-->
                <div class="mt-3 ml-2" *ngIf="field.value.type === 'boolean'" [ngClass]="{'fieldDisplay': field.value.show == true}" >
                  <p-checkbox formControlName="values" [binary]="true" [pTooltip]="field.value.description"></p-checkbox>
                  <label *ngIf="field.value.required == 'Mandatory'" class="required ml-3 lable">{{field.value.label}}</label>
                  <label *ngIf="field.value.required !== 'Mandatory'" class="ml-3 lable">{{field.value.label}}</label>
                </div>
                <!--END-->
              </div>
<!--DATABASE OPERATION ENDS-->
<!--storedproc STARTS-->
                <div *ngIf="this.node.name == 'storedproc' ">
                  <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                </div>
<!--storedproc ENDS-->
<!--ActiveDirectory STARTS-->
                <div *ngIf="this.node.name == 'ads' ">
                 <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName !== 'user'  && field.value.inputName !== 'password'
                    && field.value.inputName !== 'field' && field.value.inputName !== 'value'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE === "LIST"-->
                <div style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list'">
                  <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown  class="integrationNode" [options]="field.value.listvalue" (onChange)="changeADSAction($event.value)" [pTooltip]="field.value.description"
                    optionLabel="key" optionValue="value" [autoDisplayFirst]="true" appendTo="body"  placeholder="Select...." formControlName="values">
                  </p-dropdown>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                </div>
                <!--END-->
                 <!--TYPE == "TEXT" && selectedValue == "authenticate"-->
                 <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'user' && selectedAdsValue==='authenticate'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'password' && selectedAdsValue==='authenticate'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT" && selectedValue == "setattribute"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'field' && selectedAdsValue==='setattribute'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'value' && selectedAdsValue==='setattribute'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <div class="mt-1" *ngIf="field.value.type === 'radio' && field.value.inputName === 'operation' && selectedAdsValue==='setattribute'">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn2" [options]="field.value.radiolistvalue" 
                  formControlName="values"
                    optionLabel="key" optionValue="value"></p-selectButton>
                </div>
                <!--END-->
                 <!--TYPE == "TEXT" && selectedValue == "searchobjects"-->
                 <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'field' && selectedAdsValue==='searchobjects'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'value' && selectedAdsValue==='searchobjects'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description" />
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <div class="mt-1" *ngIf="field.value.type === 'radio' && field.value.inputName === 'searchcontrols' && selectedAdsValue==='searchobjects'">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn2" [options]="field.value.radiolistvalue" formControlName="values"
                    optionLabel="key" optionValue="value"></p-selectButton>
                </div>
                <!--END-->
                <!--TYPE ==="RADIO"-->
                <div class="mt-1" *ngIf="field.value.type === 'radio' && field.value.inputName !== 'operation' && field.value.inputName !== 'searchcontrols'">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                    optionLabel="key" optionValue="value"></p-selectButton>
                </div>
                <!--END-->
                </div>
<!--ActiveDirectory ENDS-->
<!--OData STARTS-->
                <div *ngIf="this.node.name == 'odata'">
                 
                    <!--TYPE === "LIST" with operation-->
                <div  style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' && field.value.inputName == 'operation'
                        && field.value.inputName !== 'entity' && field.value.inputName !== 'fields' 
                          && field.value.inputName !== 'expand'">
                  <span class="p-float-label mt-4 w-full">
                  <p-dropdown class="integrationNode" [options]="field.value.listvalue" (onChange)="selectOdataOperation($event.value)" [pTooltip]="field.value.description"
                  optionLabel="key" optionValue="value" appendTo="body"  placeholder="Select...." formControlName="values">
                  </p-dropdown>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                </div>
                <!--END-->
                  <!--TYPE === "LIST" with inputName: "entity"-->
                  <div  style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' &&
                       field.value.inputName == 'entity' && field.value.inputName !== 'fields' && field.value.inputName !== 'operation'
                         && field.value.inputName !== 'expand'">
                    <span class="p-float-label mt-4 w-full">
                      <p-dropdown formControlName="values" [filter]="true"  class="integrationNode" [options]="allEntyityTypes"
                       dataKey="value" optionLabel="key" optionValue="value" (onClick)="getEntityTypeList(field.value.values)"  (onChange)="selectedEntityTypes($event.value)"
                        appendTo="body"  placeholder="Select...." ><!--(onChange)="confirmEntityTypes($event)"-->
                        </p-dropdown> 
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                <!--TYPE ==="RADIO" with condition inputName : 'expandtype'-->
                <div class="mt-1" style="display: grid;" *ngIf="(odataOptionselected == 'Select' || odataOptionselected == 'Read')
                     && field.value.type === 'radio' && field.value.inputName === 'expandtype'">
                <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;" >{{field.value.label}}</label>
                <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values" 
                  optionLabel="key" optionValue="value" (click)="expandTypeconfirm($event,input?.at(ind).get('values').value)"></p-selectButton>
                </div>
                <!--END-->
                  <!--TYPE === "LIST" with inputName: "expand" true-->
                  <div  style="display: flex;" *ngIf="((odataOptionselected == 'Select' || odataOptionselected == 'Read') && !odataOptionEntitySelectedValue)&& field.value.inputName !== 'system' && field.value.type === 'list'
                         && field.value.inputName !== 'fields' && field.value.inputName == 'expand' && field.value.inputName !== 'entity' && field.value.inputName !== 'operation'">
                    <span class="p-float-label mt-4 w-full">
                       <p-multiSelect   [options]="navigations" formControlName="values" class="multiSelect" [showToggleAll]="false"
                      appendTo="body"  placeholder="Select...." (onPanelHide)="selectNavigations(field.value.values)"></p-multiSelect><!--(onFocus)="getnavigations()" (onPanelHide)="getListField()"-->
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with inputName: "expand"  false -->
                     <div style="display: flex;" *ngIf="((odataOptionselected == 'Select' || odataOptionselected == 'Read') && odataOptionEntitySelectedValue)&& field.value.inputName == 'expand'">
                          <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                         <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                       <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                       <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                   </span>
                 </div>
               <!--END-->        
                <!--TYPE === "LIST" with fileds-->
                  <div style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' && field.value.inputName == 'fields' && field.value.inputName !== 'expand' && field.value.inputName !== 'entity' && field.value.inputName !== 'operation'" >
                      <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                          <p-multiSelect [showToggleAll]="false"  [options]="selectfieldList" [group]="true" optionGroupLabel="entityType" optionGroupChildren="allFields" class="multiSelect" formControlName="values"
                               defaultLabel="Select..." appendTo="body" (onPanelHide)="mapMetaData(field.value.values)" ></p-multiSelect><!--(onPanelHide)="formatOdata()"-->
                          <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                          <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                  </div>
                <!--END-->
                
                <!--TYPE === "KEYVALUE" & CARD with KEY VALUES-->
                <div *ngIf="(odataOptionselected == 'Select') && field.value.type === 'keyvalue'" class="my-1" >
                  <p-card  class="formCard">
                   <div class="mb-2">
                     <span  class="ml-3 font-bold">{{field.value.inputName}}</span>
                     <span style="float: right;">
                       <i class="pi pi-plus mr-4 cursor"*ngIf="!readonlyOnDisable" style="font-size: 1rem;" (click)="addkeyValuepair(ind, '', '')"></i> 
                      </span>
                   </div>
                   <div *ngIf="field.value.type === 'keyvalue'" formArrayName="valuepairs">
                     <div class="form-row" *ngFor="let keyvalue of getkeyvaluecontrol(field); index as i">
                         <ng-container [formGroupName]="i">
                         <div class="row p-0 m-0">
                         <span class="p-float-label p-input-icon-right mt-3 col-5 p-0 mx-1">
                           <input pInputText formControlName="key"/>
                           <label style="font-size: 12px;">Parameter Key</label>
                         </span>
                         <span span class=" p-float-label p-input-icon-right mt-3 col-5 p-0">
                           <input pInputText formControlName="value" />
                           <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, keyvalue.value.value, field.value.type, ind, i)"></i><!--(click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"-->
                           <label style="font-size: 12px;">Parameter Value</label>
                         </span>
                         <i class="pi pi-trash col-1 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97); margin-top: 24px;"  (click)="removeKeyValue(ind,i)"></i>
                         </div>
                       </ng-container>
                     </div>
                    </div>
                 </p-card> 
               </div>
               <!--END-->
                  <!--TYPE == "TEXT" inputName : "metadata"-->
                      <div style="display: none;" *ngIf="field.value.type === 'text'  && field.value.inputName == 'metadata'" >
                                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                                </span>
                  </div>
                 <!--END-->
                  <!--TYPE == "TEXT" ENTITY Data -->
                  <div style="display: flex;" *ngIf="(odataOptionselected != 'Select') && field.value.type === 'text' && field.value.inputName == 'entitydata' && field.value.inputName !== 'metadata'">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                   <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                  <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
            </div>
           <!--END-->
             <!--TYPE == "TEXT" inputName : "filter"-->
             <div *ngIf="(odataOptionselected == 'Select') && field.value.type === 'text'  && field.value.inputName == 'filter'" >
              <span class="p-float-label p-input-icon-right my-4" style="width: -webkit-fill-available;">
                <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
            </span>
            </div>
            <!--END-->
            
            <!--TYPE == "TEXT" -->
               <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName !== 'metadata' &&
                   field.value.inputName !== 'entitydata' && field.value.inputName !== 'filter'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                            <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                            <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                            <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                           <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                          <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                </div>
            <!--END-->
              </div>

<!--OData ENDS-->
<!--sap STARTS-->
              <div *ngIf="this.node.name == 'sap' ">
                 <!--TYPE === "LIST" with selected Operation-->
                 <div style="display: flex;" class="mt-2" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list' && field.value.inputName === 'operation'">
                  <span class="p-float-label mt-3 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" [options]="field.value.listvalue"  optionLabel="key" optionValue="value"  appendTo="body"  placeholder="Select...." 
                    (onChange)="selectSapOption($event.value)" formControlName="values" [pTooltip]="field.value.description"> </p-dropdown>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                </div>
                <!--END-->
                <div *ngIf="optValisExecute">
                 <!--TYPE == "TEXT" inputName === 'function'-->
                 <div style="display: block;">
                 <div style="display: inline;" *ngIf="field.value.type === 'text' && field.value.inputName === 'function'">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openSapExpBuilderDialog($event,field.value.values,field.value.type, field.value.inputName,ind,'')"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
              </div>
                <!--END-->
                <!--TYPE == "TEXT" inputName === 'parameters' && *debugMode OFF -->
                <div style="display: block;" *ngIf="!debugMode" >
                  <div style="display: inline;" *ngIf="field.value.type === 'text' && field.value.inputName === 'parameters'">
                   <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                     <input pInputText formControlName="values"  [pTooltip]="field.value.description"/>
                     <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openSapExpBuilderDialog($event,field.value.values,field.value.type, field.value.inputName,ind,'')"></i>
                     <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                     <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                 </span>
                 </div>
               </div>
                 <!--END-->
                 <!--TYPE == "TEXT" inputName === 'parameters' && NEW TYPE DEBUG Mode On-->
                <div style="display: block;" *ngIf="debugMode">
                  <div class="mt-4 ml-3" *ngIf="field.value.type === 'text' && field.value.inputName === 'parameters'">
                 <label  *ngIf="field.value.required == 'Mandatory'" class="_required ">{{field.value.label}}</label>
                 <label  *ngIf="field.value.required != 'Mandatory'" >{{field.value.label}}</label>
                 <i class="pi pi-wrench cursor mr-3" *ngIf="!readonlyOnDisable" style="float: right;" 
                  (click)="openSapExpBuilderDialog($event,field.value.values,field.value.type, field.value.inputName,ind,'')"></i>
                 </div>
               </div>
                 <!--END-->
              <!--TYPE == "radio" inputName === 'maptype'-->
               <div class="mt-1" style="display: grid;" *ngIf="field.value.type === 'radio' && field.value.inputName === 'maptype' && debugMode">
                <p-confirmPopup></p-confirmPopup>
                <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;" >{{field.value.label}}</label>
                <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values" (click)="confirm($event,input?.at(ind).get('values').value)"
                  optionLabel="key" optionValue="value" ></p-selectButton>
              </div>
                 <!--END-->
               <!--TYPE === "KEYVALUE" & CARD with KEY VALUES && && *debugMode OFF  && !debugMode-->
               <div *ngIf="(field.value.inputName == 'rfcinputs' && field.value.type == 'keyvalue') &&  field.value.inputName !== 'rfcoutputs'" class="mt-3">
                <p-card class="formSapCard">
                 <div class="mb-2">
                   <span class="ml-3 font-bold">{{field.value.label}} :</span>
                 </div>
                 <div style="margin:10px;">
                  <p-messages  severity="warn" class="warnMsg" *ngIf="this.sapParameterValue">
                    <ng-template pTemplate>
                      <div *ngIf="!showDataFiled" class="ml-2">Click each of the inputs below to map them</div>
                      <div *ngIf="showDataFiled" class="ml-2">Remember to set the data field below</div>
                  </ng-template>
                  </p-messages>
                </div>
                 <div formArrayName="valuepairs" >
                   <span *ngFor="let keyvalue of getsapkeyvaluecontrol(field); index as i" style="padding: 8px !important;">
                       <ng-container [formGroupName]="i">
                       <span class="p-float-label p-input-icon-right w-full mt-4"  style="display: none;">
                         <input pInputText formControlName="value" readonly placeholder="Input value" [pTooltip]="field.value.description"/>
                         <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openSapExpBuilderDialog($event,keyvalue.value.value,field.value.type, field.value.inputName,ind,i)"></i>
                         <label style="font-size: 12px;">Input Value</label>
                       </span>                      
                       <p-button [disabled]="readonlyOnDisable || showDataFiled" class="rfcButton" label="{{changeName(keyvalue.value.value)}}"  *ngIf="changeIcon(keyvalue.value.value)" styleClass="p-button-sm"
                       (onClick)="openSapExpBuilderDialog($event,keyvalue.value.value,field.value.type, field.value.inputName,ind,i)"></p-button>
                       <p-button [disabled]="readonlyOnDisable || showDataFiled" class="rfcButton" label="{{changeName(keyvalue.value.value)}}" styleClass="p-button-text p-button-raised p-button-sm"  *ngIf="!changeIcon(keyvalue.value.value)"
                       (onClick)="openSapExpBuilderDialog($event,keyvalue.value.value,field.value.type, field.value.inputName,ind,i)"></p-button>
                     </ng-container>
                    </span>
                  </div>
               </p-card> 
               </div>
               <!--END-->
               <!--CARD with Output-->
                <div class="mt-3" *ngIf="field.value.inputName == 'rfcoutputs'">
                  <p-card class="formSapCard">
                    <div class="mb-2">
                      <!-- <span class="ml-3 font-bold">{{field.value.label}} :</span> -->
                      <span class="ml-3 font-bold">Selected Outputs :</span>
                    </div>
                    <span *ngFor="let keyvalue of dispRfcOutputs;" style="padding: 8px !important;">
                        <p-button class="rfcButton" label="{{keyvalue}}" [disabled]="true" styleClass="p-button-text p-button-raised"></p-button>
                   </span>
                  </p-card>
                </div>
               <!--END-->
                <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'data'" style="display: none;" ><!--style="display: none;"-->
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText #expressionArea formControlName="values" readonly [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openSapExpBuilderDialog($event,field.value.values,field.value.type, field.value.inputName,ind,'')" ></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'data' && showDataFiled"><!--style="display: none;"-->
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText #expressionArea formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openSapExpBuilderDialog($event,field.value.values,field.value.type, field.value.inputName,ind,'')" ></i><!---->
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE ==="RADIO"-->
                <div class="mt-1" *ngIf="field.value.type === 'radio' && field.value.inputName  !== 'maptype'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                  optionLabel="key" optionValue="value" ></p-selectButton>
              </div>
              <!--END-->
            </div>
              </div>
<!--sap ENDS-->
<!--FTP STARTS-->
              <div *ngIf="this.node.name == 'ftp' ">
                <!--TYPE === "LIST"-->
                <div style="display: flex;" *ngIf="field.value.inputName !== 'system' && field.value.type === 'list'">
                  <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" [options]="field.value.listvalue"  (onChange)="changeFtpDropDown($event.value)" [pTooltip]="field.value.description"
                    optionLabel="key" optionValue="value" [autoDisplayFirst]="true" appendTo="body"  placeholder="Select...." formControlName="values">
                  </p-dropdown>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                </div>
                <!--END-->
                 <!--TYPE == "TEXT"-->
                 <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'newdirectory' && selectedFtpValue==='createdir' " >
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'newfilename' && selectedFtpValue==='rename' ">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT"-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'attachmentid' && selectedFtpValue==='put' ">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"  (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                 <!--TYPE == "TEXT"-->
                 <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'filename' && ((selectedFtpValue === 'get' || selectedFtpValue === 'put' || selectedFtpValue === 'delete') || selectedFtpValue==='rename')">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE == "TEXT" no condition-->
                <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName !== 'newdirectory' && field.value.inputName !== 'newfilename' 
                        && field.value.inputName !== 'filename' && field.value.inputName !== 'attachmentid'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText formControlName="values" [pTooltip]="field.value.description"/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
                </div>
                <!--END-->
                <!--TYPE ==="RADIO" with condition-->
                <div class="mt-3" *ngIf="field.value.type === 'radio' && field.value.inputName === 'connectionmode' && field.value.inputName !== 'mode'">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                    optionLabel="key" optionValue="value"></p-selectButton>
                </div>
                <div class="mt-1" *ngIf="field.value.type === 'radio' && field.value.inputName !== 'connectionmode' && field.value.inputName === 'mode' ">
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required" style="font-size: smaller;">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'" style="font-size: smaller;">{{field.value.label}}</label>
                  <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                    optionLabel="key" optionValue="value"></p-selectButton>
                </div>
                <!--END-->
              </div>
<!--FTP ENDS-->
            </div>
            
          </div>
          <div class="py-2 mr-3" style="float: right;" >
            <p-button  styleClass="p-button-text p-0" *ngIf="!enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Show advanced options</span><i class="pi pi-chevron-down"></i></p-button>
            <p-button  styleClass="p-button-text p-0"  *ngIf="enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Hide advanced options</span><i class="pi  pi-chevron-up"></i></p-button>
          </div>
        </form>

       </div> 
     </ng-template>
 </p-panel>
</div>
<div class="m-1 mb-0" *ngIf="!readonlyOnDisable && !displayBtn">
  <div style="display:table-caption;">
    <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
        pTooltip="Save" tooltipPosition="right" [disabled]="validataFileds()" (onClick)="saveNodedata()"></p-button>
    <p-button icon="pi pi-question" styleClass="p-button-rounded p-button-text p-button-raised p-button-outlined" class="nodeBtnbg"
      pTooltip="Info" (onClick)="goToLink(helpurl)" tooltipPosition="right"></p-button>
    <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger" class="nodeBtn"
      pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button>

  </div>
</div>
</div>
<div class="nodes-outputs">
    <div *ngFor="let output of outputs" class="output-sockets-success">
      <rete-socket *ngIf="output.name == 'Success'"   rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
    <div *ngFor="let output of outputs" class="output-sockets-error">
      <rete-socket *ngIf="output.name == 'Error'"  rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
  </div>
  <p-overlayPanel #newaddedit [dismissable]="false" class="add-edit">
    <div style="padding-bottom: 10px;"><b>Add System</b> <span style="float: right;"><i class="pi pi-times" id="newClose" style="color:#fff;" (click)="newaddedit.hide()"></i></span></div>
    <ng-template pTemplate="body">
      <app-add-edit-system [data]="addEditData"  (overlayclose)="overlayClose($event)"></app-add-edit-system>
  </ng-template>
</p-overlayPanel>
<p-overlayPanel #updateaddedit [dismissable]="false" class="add-edit">
  <div style="padding-bottom: 10px;"><b>Update {{this.selectedSystem}}</b><span style="float: right;"><i class="pi pi-times" id="updateClose" style="color:#fff;" (click)="updateaddedit.hide()"></i></span></div>
    <ng-template pTemplate="body">
      <app-add-edit-system [data]="addEditData"  (overlayclose)="overlayClose($event)"></app-add-edit-system>
  </ng-template>
</p-overlayPanel>
<p-overlayPanel #expbuilder [dismissable]="false"  class="add-edit">
  <div style="padding-bottom: 10px;"><b>Expression Builder</b><span style="float: right;">
    <i class="pi pi-times" id="expBuildClose" style="color:transparent" (click)="expbuilder.hide()"></i>
    <!-- <i class="fa-light fa-chevron-down"></i> -->
    <i class="pi pi-list cursor" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData()" pTooltip="Open Test Data"></i>
  </span>
  </div>
  <ng-template pTemplate="body">
    <app-expression-builder [data]="expBuildVal"  (overlayclose)="overlayCloseExpBuilder($event)"></app-expression-builder>
</ng-template>
</p-overlayPanel> 
<div *ngIf="selected() && displayBtn && !this.readonlyOnDisable" style="margin-left: 150px;height: 45px;"><app-add-nodes ></app-add-nodes></div>

<p-confirmPopup></p-confirmPopup>
<!-- <p-toast>
</p-toast> -->