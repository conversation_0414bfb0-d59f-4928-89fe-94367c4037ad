import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, integrationSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
import { IntegrationnodeComponent } from './integrationnode/integrationnode.component';
export class RESTComponent extends Component {
  override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'http',
    wfNamespace: 'unvired.operation',
    icon: 'http',
    category: 'Services'
  };
  constructor() {
    super('http');
    this.data.render = 'angular';
    this.data.component = IntegrationnodeComponent;
  }
builder(node) {
  const inp1 = new Input('http', 'Input', integrationSocket);
  const out1 = new Output('http_success', 'Success', successSocket, false);
  const out2 = new Output('http_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
