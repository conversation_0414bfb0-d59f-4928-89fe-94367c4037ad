import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, integrationSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
import { IntegrationnodeComponent } from './integrationnode/integrationnode.component';
export class FTPServicecomponent extends Component {
  override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description:'',
    wfName: 'ftp',
    wfNamespace: 'unvired.operation',
    icon: 'store_mall_directory',//home_storage
    category: 'Services'
  };
  constructor() {
    super('ftp');
    this.data.render = 'angular';
    this.data.component = IntegrationnodeComponent;
  }
builder(node) {
  const inp1 = new Input('ftp', 'Input', integrationSocket);
  const out1 = new Output('ftp_success', 'Success', successSocket, false);
  const out2 = new Output('ftp_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
