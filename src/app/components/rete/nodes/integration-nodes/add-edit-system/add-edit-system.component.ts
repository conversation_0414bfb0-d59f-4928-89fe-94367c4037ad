import { Component, Input, Output,EventEmitter} from "@angular/core";
import {FormBuilder,FormGroup,Validators,FormArray} from "@angular/forms";
import { ConfirmationService, MessageService } from 'primeng/api';
import { ReteService } from "src/app/services/rete.service";
@Component({
  selector: 'app-add-edit-system',
  templateUrl: './add-edit-system.component.html',
  styleUrls: ['./add-edit-system.component.scss']
})
export class AddEditSystemComponent {
  systemdetails: FormGroup;
  excludeparams = [];
  isUpdate: boolean;
  unSaved: boolean;
  default = "Custom Application Server";
  errmsg: string ='';
  disp:boolean;
  selectedValue:any;
  staticsystemprops: any;
  optionalprops = [];
  defaultOptionProps = [];
  lowercasealphanumericpattern = "[a-z0-9]+";
  @Input() data: any;
  @Output("overlayclose") eventEmitter: EventEmitter<any> = new EventEmitter<any>();
  constructor(private fb: FormBuilder, private reteservice: ReteService,private confirmationService: ConfirmationService,
    private messageService: MessageService) {}

  ngOnInit(): void {
    this.systemdetails = this.fb.group({
      name: [""],
      description: [""],
      properties: new FormArray([]),
    });
    this.systemdetails.valueChanges.subscribe((value: any) => {
      this.unSaved = true;
    });
    if (this.data.calltype === "update") {
      this.isUpdate = true;
      this.reteservice
        .getworkflowsystemdetails(this.data.systemtype, this.data.system)
        .subscribe((res) => {
          const response = res;
          if (response.error === "") {
            if (response.status === "Success") {
              this.systemdetails.patchValue({
                name: response.sysName,
                description: response.sysDesc,
              });
              this.systemdetails.get("name").disable();
              response.systemProps.forEach((systemproperty) => {
                this.excludeparams.push(systemproperty.name);
                this.addSystemProperties(systemproperty);
              });
              //this.getstaticsystemproperties();
              // setTimeout(() => {
              //   this.selectConnectionType(this.test);
              // }, 1000);
            }
          }
        });
    } else {
      this.isUpdate = false;
      this.getstaticsystemproperties();
    }
  }

  get properties() {
    return this.systemdetails.get("properties") as FormArray;
  }
 

  getstaticsystemproperties() {
    this.reteservice.getworkflowsystemproperties(this.data.systemtype,this.excludeparams.toString()).subscribe((res) => {
        const response = res;
        if (response.error === "") {
          this.disp=false;
          if (response.status === "Success") {
            this.staticsystemprops = response;
            this.staticsystemprops.properties.forEach((systemproperty) => {
              this.addSystemProperties(systemproperty);
            });
          }
        } else {
          this.disp=true;
          this.errmsg = response.error;          
        }
      });
  }

  addSystemProperties(inputParameter: any) {
    // inputParameter.listvalue=[];
    inputParameter.listValues = [inputParameter.listValues];
     if (inputParameter.type === "List" && inputParameter.listValues && this.data.systemtype === 'sap') {
    //  inputParameter.listvalue.push([]);
    // inputParameter.listvalues[0].forEach(data=>{
    //   inputParameter.listvalue[0].push({"key":data,"value":data.toLowerCase().replace(/\s+/g, '')})
   // });
     this.sapAddEditSystem(inputParameter.value)
    // inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');

   }
    this.properties.push(this.fb.group(inputParameter));
  }

  setPropertiesValidators() {
    this.properties.controls.forEach((field) => {
      field.get("value").setValidators([Validators.required]);
      field.get("value").updateValueAndValidity();
    });
    this.properties.controls[1].get("value").setValidators([Validators.pattern(this.lowercasealphanumericpattern)]);
    this.properties.controls[1].updateValueAndValidity();
  }

  sapAddEditSystem(event:any){
    this.selectedValue=event;
    if(this.selectedValue == 'Custom Application Server'){
      this.properties.controls.forEach(key=>{
      if(key.value.name == "mshost"){
        key.patchValue({value: ""});
        key.patchValue({required: false });
      }
      if(key.value.name == "group"){
        key.patchValue({value: ""});
        key.patchValue({required: false});
      }
      //add required
      if(key.value.name == "ashost"){
        key.patchValue({value: ""});
        key.patchValue({required: true });
      }
   });
  }
  if(this.selectedValue == 'Group/Server Selection'){
    this.properties.controls.forEach(key=>{
      if(key.value.name == "ashost"){
        key.patchValue({value: ""});
        key.patchValue({required: false });
      }
       //add required
      if(key.value.name == "mshost"){
        key.patchValue({value: ""});
        key.patchValue({required: true });
      }
      if(key.value.name == "group"){
        key.patchValue({value: ""});
        key.patchValue({required: true});
      }
   });
  }
  }

  validataFileds() {
    let validate: any;
    let totalMandateFields: number = 0;
    let mandateValue: number = 0;
    if (this.systemdetails.value.description === "") {
      validate = true;
    } else {
      this.systemdetails.value.properties.forEach((val) => {
        if (val.required == true) {
          totalMandateFields = totalMandateFields + 1;
          if (val.value == "" || val.value == null || val.value == "null") {
          } else {
            mandateValue = mandateValue + 1;
          }
        }
      });
      if (totalMandateFields == mandateValue) {
        validate = false;
      } else {
        validate = true;
      }
    }
    return validate;
  }

  save(type: any) {
    this.unSaved = false;
    const systemName = this.properties.controls[1].get("value").value;
    this.systemdetails.get("name").setValue(systemName);
    // let indexToRemove: number[] = [];

    // let fromArray = this.systemdetails.get('properties') as FormArray;
    // fromArray.controls.forEach((control, index) => {
    //     if(control.value.value == ""){
    //       indexToRemove.push(index);
    //       }
    // });
    // indexToRemove.reverse().forEach((index) => {
    //   fromArray.removeAt(index);
    // });
    
    this.reteservice.createworkflowsystem(this.systemdetails.getRawValue(),this.data.systemtype,this.isUpdate).subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.eventEmitter.emit({type: type,close: true,calltype: this.data.calltype});
          }
        } else {
          this.errmsg = response.error;
          this.messageService.add({ severity: 'error', summary: 'Error', detail: this.errmsg });
        }
      });
  }

  close(event:any,type: any) {
    if (this.systemdetails.dirty) {
        this.confirm(event,type);
    }else{
    this.eventEmitter.emit({type: type,close: true,calltype: this.data.calltype});
    }     
  }

  confirm(event: Event,type:any) {
    this.confirmationService.confirm({
        target: event.target as EventTarget,
        message: 'Are you sure that you want to proceed?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.eventEmitter.emit({type: type,close: true,calltype: this.data.calltype});
        },
        reject: () => {
        }
    });
}
}
