<div style="width: 400px; height: 350px; overflow: auto;padding-right: 5px; padding-left: 2px;padding-bottom: 5px;" ><p-toast></p-toast>
    <span *ngIf="this.disp" style="color: tomato;">
        {{this.errmsg}}
    </span>
            <form novalidate [formGroup]="systemdetails" novalidate appendTo="div" autocomplete="off"  *ngIf="!this.disp" >
                <span class="p-float-label mt-4 w-full">
                    <input pInputText type="text" class=" w-full" formControlName="description" placeholder="Enter system description"/>
                <label for="float-input" class="_required">Description</label>
                 </span>
                <div formArrayName="properties" *ngFor="let field of properties.controls; let ind = index;">
                <div [formGroupName]="ind" *ngIf="this.data.systemtype !== 'sap'">
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && !(field.value.name === 'systemname' && isUpdate) " >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && (field.value.name === 'systemname' && isUpdate) " >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" readonly [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'List' " style="width: -webkit-fill-available ;">
                        <p-dropdown class=" integrationNode" showClear="true" [autoDisplayFirst]="false" [placeholder]="field.value.placeholder" formControlName="value" [pTooltip]="field.value.help" appendTo="body" [options]="field.value.listValues"></p-dropdown>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Sensitive'">
                        <p-password class="syspassword w-full" [feedback]="false"   formControlName="value" [toggleMask]="true" [pTooltip]="field.value.help"></p-password>

                        <!-- <input pInputText type="text" type="password" class="w-full"  [toggleMask]="true" [placeholder]="field.value.placeholder" formControlName="value" [pTooltip]="field.value.help"/> -->
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Number'">
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <div class="mt-3" *ngIf="field.value.type === 'Boolean'" [pTooltip]="field.value.help">
                        <p-checkbox formControlName="value" [binary]="true"></p-checkbox>&nbsp;&nbsp;
                        <label for="float-input" *ngIf="field.value.required == true" class="required label" >{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false"  class="label">{{field.value.label}}</label>
                      </div>
                </div>
                 <div [formGroupName]="ind" *ngIf="this.data.systemtype === 'sap'">
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && !(field.value.name === 'systemname' && isUpdate) && field.value.name !== 'ashost' && 
                    (field.value.name !== 'mshost' && field.value.name !== 'group')" >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && (field.value.name === 'systemname' && isUpdate) && field.value.name !== 'ashost' && 
                    (field.value.name !== 'mshost' && field.value.name !== 'group')" >
                        <input pInputText type="text" class=" w-full" formControlName="value" readonly [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4" *ngIf="field.value.type === 'List' ">
                        <p-dropdown class=" integrationNode" [autoDisplayFirst]="false" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"
                            appendTo="body" [options]="field.value.listValues" (onChange)="sapAddEditSystem($event.value)"></p-dropdown>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span> 
                    <!--sap LIST SELECT VALUE == Custom Application Server-->
                     <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && field.value.name === 'ashost'  && selectedValue === 'Custom Application Server'  " >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span> 
                    <!--END-->
                     <!--sap LIST SELECT VALUE == Group/Server Selection-->
                     <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && field.value.name === 'mshost'  && selectedValue !== 'Custom Application Server'
                          && selectedValue === 'Group/Server Selection' " >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder"  [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span> 
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Text' && field.value.name === 'group'  && selectedValue !== 'Custom Application Server' 
                        && selectedValue === 'Group/Server Selection' " >
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span> 
                    <!--END-->
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Sensitive'">
                        <p-password [feedback]="false" class="w-full" formControlName="value" [toggleMask]="true" [pTooltip]="field.value.help"></p-password>
                        <!-- <input pInputText type="text" type="password" class=" w-full" [placeholder]="field.value.placeholder"  [toggleMask]="true" formControlName="value" [pTooltip]="field.value.help"/> -->
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <span class="p-float-label mt-4 w-full" *ngIf="field.value.type === 'Number'">
                        <!-- <p-inputNumber inputId="integeronly" formControlName="value" id="numberInput"> </p-inputNumber> -->
                        <input pInputText type="text" class=" w-full" formControlName="value" [placeholder]="field.value.placeholder" [pTooltip]="field.value.help"/>
                        <label for="float-input" *ngIf="field.value.required == true" class="_required">{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </span>
                    <div class="mt-3" *ngIf="field.value.type === 'Boolean'" [pTooltip]="field.value.help">
                        <p-checkbox formControlName="value" [binary]="true"></p-checkbox>&nbsp;&nbsp;
                        <label for="float-input" *ngIf="field.value.required == true" class="_required" >{{field.value.label}}</label>
                        <label for="float-input" *ngIf="field.value.required == false" >{{field.value.label}}</label>
                    </div> 
                </div>
               </div>
            </form>
        </div>
            <div style="text-align: center;" class=" mt-2"  >
                <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (onClick)="close($event,'cancel')" ></p-button>
                <p-button label="Save" styleClass="p-button-sm p-button-sm mt-2 " icon="pi pi-save" (onClick)="save('save')" [disabled]="validataFileds()"></p-button>
              </div> 
        <p-confirmPopup></p-confirmPopup>
