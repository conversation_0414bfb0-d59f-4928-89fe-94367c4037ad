<a style="padding: 0.45625rem 0.45625rem;" class="help-icon p-button p-button-text p-button-sm p-button-rounded" (click)="openTestData(openPanel = true)" pTooltip="Open Test Data" tooltipPosition="left"><i class="pi pi-list"></i></a>

<form [formGroup]="nodeformdata" novalidate autocomplete="off" >
    <p-card class="qBuildCard" *ngIf="this.dataItem.nodeName !== 'ReadRows'">
      <div style="text-align: left" class="query-builder" *ngIf="(config.fields | json) != '{}'; else nofields">
        <query-builder formControlName="query" [config]="config" [allowRuleset]="allowRuleset"
              [allowCollapse]="allowCollapse" [persistValueOnFieldChange]="persistValueOnFieldChange">
          <ng-container *queryButtonGroup="let ruleset;let addRule = addRule;let addRuleSet = addRuleSet;let removeRuleSet = removeRuleSet ;let index = index">
              <p-button  icon="pi pi-plus" styleClass="p-button-info" class="mr-3" (onClick)="addRule();addRuleIndex(ruleset)"></p-button>
              <p-button  icon="pi pi-plus-circle" styleClass="p-button-info" class="mr-3" *ngIf="addRuleSet" (onClick)="addRuleSet()"></p-button>
              <p-button icon="pi pi-minus-circle" styleClass="p-button-danger" class="mr-3 mt-1" *ngIf="removeRuleSet" (onClick)="removeRuleSet()"></p-button>
          </ng-container>
          <ng-container *queryArrowIcon>
            <i class="pi pi-angle-right" ngClass="mat-arrow-icon"></i>
          </ng-container>
        <!--AND / OR STARTS-->
        <ng-container *querySwitchGroup="let ruleset; let onChange = onChange" >
          <div style=" margin: 5px 0px 0px 5px !important;">
          <p-selectButton [options]="stateOptions" class="selectBtn" [(ngModel)]="ruleset.condition" [ngModelOptions]="{ standalone: true }"
          (ngModelChange)="onChange($event)" optionLabel="label" optionValue="value"></p-selectButton>
          </div>
        </ng-container>
        <!--AND / OR ENDS-->
        <div class="grid" >
          <div class="col-1 reducePadding" *queryRemoveButton="let rule; let removeRule = removeRule">
            <p-button icon="pi pi-trash" styleClass="delQueryBtn p-button-danger" (onClick)="removeRule(rule)"></p-button>
          </div>
          <div class="col-4 reducePadding" *queryEntity=" let rule; let entities = entities; let onChange = onChange">
            <p-dropdown [options]="entities" [(ngModel)]="rule.entity" optionLabel="name" optionValue="value"
              [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
          </div> 
          <!--SELECT FILEDS STARTS--> 
        <div class="col-4 reducePadding" *queryField="let rule;let fields = fields;let onChange = onChange;let getFields = getFields">
          <p-dropdown [options]="getFields(rule.entity)" [(ngModel)]="rule.field" optionLabel="name" optionValue="value"
          [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" (onChange)="getData(rule)"
          appendTo="body" class="formsNodeOp"></p-dropdown>
        </div>
        <!--SELECT FILEDS ENDS-->
        <!--SELECT Operation STARTS-->
        <div class="col-3 reducePadding" *queryOperator="let rule;let operators = operators;let onChange = onChange">
          <p-dropdown [options]="operators" [(ngModel)]="rule.operator" 
          [ngModelOptions]="{ standalone: true }" (ngModelChange)="ruleOperationChanged(rule)" (ngModelChange)="onChange(rule)" 
          [autoDisplayFirst]="true" appendTo="body" class="formsNodeOp"></p-dropdown>
        </div>
        <!--SELECT operation ENDS-->
       <!--Select for radio or checkbox or dropdown STARTS-->
       <ng-container *queryInput=" let rule; let field = field; let options = options; type: 'category'; let onChange = onChange ">
        <div class="col-1 reducePadding1">
          <span *ngIf="rule.checked == false">Default</span>
          <span *ngIf="rule.checked == true">Custom</span>
        <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
           (ngModelChange)="ruleSwitchChangedDropdown(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
        </div>
        <div class="col-3 reducePadding" *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
          <div *ngIf="!rule.checked">
          <p-dropdown [options]="options"  (ngModelChange)="onChange(rule)" [(ngModel)]="rule.value"
            optionLabel="name" optionValue="value" [ngModelOptions]="{ standalone: true }"
            (ngModelChange)="onChange()" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
            </div>
            <div *ngIf="rule.checked">     
              <span class="p-input-icon-right">
             <input pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
             <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
           </span>
         </div>
        </div>
      </ng-container>
      <!--Select for radio or checkbox ENDS-->
      <!-- type is date STRAT -->
                  <ng-container *queryInput="let rule; let field = field; type: 'date'; let onChange = onChange;let checked = checked;">
                    <div class="col-1 reducePadding1">
                      <span *ngIf="rule.checked == false">Default</span>
                      <span *ngIf="rule.checked == true">Custom</span>
                    <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                       (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
                    </div>
                    <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                      <div class="col-3 reducePadding" *ngIf="!rule.checked">
                      <p-calendar selectionMode="range" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }"
                        (ngModelChange)="onChange()" appendTo="body" [iconDisplay]="'input'" [showIcon]="true"></p-calendar>
                      </div>
                       <div class="col-3 reducePadding" *ngIf="rule.checked">     
                           <span class="p-input-icon-right">
                          <input pInputText style="width: 160px;"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                          <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                        </span>
                      </div>
                    </ng-container>
                   
                    <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && !rule.checked">
                      <p-calendar  [(ngModel)]="rule.value" [showIcon]="true" [ngModelOptions]="{ standalone: true }"
                        (ngModelChange)="onChange()" appendTo="body"></p-calendar>
                    </div>
                      <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && rule.checked">
                          <span class="p-input-icon-right">
                            <input pInputText style="width: 100%"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                            <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)" ></i>
                          </span>
                      </div>
                  </ng-container>
         <!-- type = date ENDS -->
                       <!-- type = number -->
                       <ng-container *queryInput=" let rule; let field = field; type: 'number'; let onChange = onChange">
                        <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                          <div class="col-2 reducePadding">
                            <span class="p-input-icon-right">
                              <input pInputText  style="width: 100%" [(ngModel)]="rule.value[0]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                              <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[0],1)"></i>
                            </span>
                          </div>
                            <div style="margin-top:20px !important;">To</div>
                          <div class="col-2 reducePadding">
                          <span class="p-input-icon-right">
                            <input pInputText  style="width: 100%;" [(ngModel)]="rule.value[1]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                            <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[1],2)"></i>
                          </span>
                        </div>
                        </ng-container>
                        <ng-container *ngIf="rule.operator !== 'between' && rule.operator !== 'not between'">
                            <div class="col-4 reducePadding">
                            <span class="p-input-icon-right">
                              <input style="width: 100%" type="text" pInputText  placeholder="Integers" [(ngModel)]="rule.value"
                                [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()" />
                              <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                            </span>
                            </div>
                          </ng-container>
                      </ng-container>
                      <!-- type = number ends -->

                     <!--TEXT FILED STARTS-->
                     <div class="col-4 reducePadding" *queryInput=" let rule; let field = field; type: 'string'; let onChange = onChange">
                      <div *ngIf=" rule.operator !== 'is null' || rule.operator !== 'is not null'">
                          <span class="p-input-icon-right">
                            <input pInputText style="width: 100%" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                            <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                          </span>
                      </div>
                  </div>
                  <!--TEXT FILED END-->
                  <!--TIME FILED STARTS-->
                  <ng-container *queryInput=" let rule; let field = field; type: 'time'; let onChange = onChange">
                    <div class="col-1 reducePadding1">
                      <span *ngIf="rule.checked == false">Default</span>
                      <span *ngIf="rule.checked == true">Custom</span>
                    <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                       (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
                    </div>
                    <div class="col-3 reducePadding" *ngIf="!rule.checked">
                    <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null' ">
                        <p-calendar appendTo="body" [showIcon]="true" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" 
                        (ngModelChange)="onChange()" [showTime]="true"  [timeOnly]="true">
                        </p-calendar>
                    </div>
                  </div>
                  <div class="col-3 reducePadding" *ngIf="rule.checked">
                    <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                      <span class="p-input-icon-right">
                        <input style="width: 100%" pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                        <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                      </span>
                  </div>
                </div>
                  </ng-container>
                   <!--Time FILED END-->
        </div>
      </query-builder>
      </div>
      <div class="mt-2" style="text-align: right;">
        <p-button label="Close" icon="pi pi-times" styleClass="p-button-sm p-button-danger" class="mr-2" (onClick)="close()"></p-button>
        <p-button label="Save" icon="pi pi-save" styleClass="p-button-sm"  (onClick)="save()"></p-button>
      
      </div>
      <ng-template #nofields>
        <div class="error-msg">
          {{ errmsg }}
        </div>
      </ng-template>
    </p-card>
<!-- 
div
div
div
div
div
div
-->

    <p-card class="qBuildCard" *ngIf="this.dataItem.nodeName === 'ReadRows'">
      <div style="text-align: left" class="query-builder" *ngIf="(config.fields | json) != '{}'; else nofields">
        <query-builder formControlName="filter" [config]="config" [allowRuleset]="allowRuleset1"
                    [allowCollapse]="allowCollapse1" [persistValueOnFieldChange]="persistValueOnFieldChange1">
          <ng-container
            *queryButtonGroup="let ruleset;let addRule = addRule;let addRuleSet = addRuleSet;let removeRuleSet = removeRuleSet">
            <p-button icon="pi pi-plus" styleClass="addQueryBtn p-button-info" class="mr-3"
               (onClick)="addRule();addRuleIndex(ruleset)"></p-button>
            <p-button icon="pi pi-plus-circle" styleClass="addQueryBtn p-button-info" class="mr-3" *ngIf="addRuleSet"
              (onClick)="addRuleSet()"></p-button>
            <p-button icon="pi pi-minus-circle" styleClass="delQueryBtn p-button-danger" class="mr-3 mt-1"
              *ngIf="removeRuleSet" (onClick)="removeRuleSet()"></p-button>
          </ng-container>
          <ng-container *queryArrowIcon>
            <i class="pi pi-angle-right" ngClass="mat-arrow-icon"></i>
          </ng-container>
           <!--AND / OR STARTS-->
           <ng-container *querySwitchGroup="let ruleset; let onChange = onChange">
            <p-selectButton [options]="stateOptions" class="selectBtn" [(ngModel)]="ruleset.condition" [ngModelOptions]="{ standalone: true }"
            (ngModelChange)="onChange($event)" optionLabel="label" optionValue="value"></p-selectButton>
          </ng-container>
          <!--AND / OR ENDS-->
          <div class="grid" >
            <div class="col-1 reducePadding" *queryRemoveButton="let rule; let removeRule = removeRule">
              <p-button icon="pi pi-trash" styleClass="delQueryBtn p-button-danger" (onClick)="removeRule(rule)"></p-button>
            </div>
            <div class="col-4 reducePadding" *queryEntity=" let rule; let entities = entities; let onChange = onChange">
              <p-dropdown [options]="entities" [(ngModel)]="rule.entity" optionLabel="name" optionValue="value"
                [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
            </div> 
            <!--SELECT FILEDS STARTS--> 
          <div class="col-4 reducePadding" *queryField="let rule;let fields = fields;let onChange = onChange;let getFields = getFields">
            <p-dropdown [options]="getFields(rule.entity)" [(ngModel)]="rule.field" optionLabel="name" optionValue="value"
            [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange($event, rule)" [autoDisplayFirst]="true" (onChange)="getData(rule)"
            appendTo="body" class="formsNodeOp"></p-dropdown>
          </div>
          <!--SELECT FILEDS ENDS-->
          <!--SELECT Operation STARTS-->
          <div class="col-3 reducePadding" *queryOperator="let rule;let operators = operators;let onChange = onChange">
            <p-dropdown [options]="operators" [(ngModel)]="rule.operator" 
            [ngModelOptions]="{ standalone: true }" (ngModelChange)="ruleOperationChanged(rule)" (ngModelChange)="onChange(rule)" 
            [autoDisplayFirst]="true" appendTo="body" class="formsNodeOp"></p-dropdown>
          </div>
          <!--SELECT operation ENDS-->
         <!--Select for radio or checkbox or dropdown STARTS-->
         <ng-container *queryInput=" let rule; let field = field; let options = options; type: 'category'; let onChange = onChange ">
          <div class="col-1 reducePadding1">
            <span *ngIf="rule.checked == false">Default</span>
            <span *ngIf="rule.checked == true">Custom</span>
          <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
             (ngModelChange)="ruleSwitchChangedDropdown(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
          </div>
          <div class="col-3 reducePadding" *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
            <div *ngIf="!rule.checked">
            <p-dropdown [options]="options"  (ngModelChange)="onChange(rule)" [(ngModel)]="rule.value"
              optionLabel="name" optionValue="value" [ngModelOptions]="{ standalone: true }"
              (ngModelChange)="onChange()" [autoDisplayFirst]="true" appendTo="body"></p-dropdown>
              </div>
              <div *ngIf="rule.checked">     
                <span class="p-input-icon-right">
               <input pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
               <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
             </span>
           </div>
          </div>
        </ng-container>
        <!--Select for radio or checkbox ENDS-->
        <!-- type is date STRAT -->
                    <ng-container *queryInput="let rule; let field = field; type: 'date'; let onChange = onChange;let checked = checked;">
                      <div class="col-1 reducePadding1">
                        <span *ngIf="rule.checked == false">Default</span>
                        <span *ngIf="rule.checked == true">Custom</span>
                      <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                         (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
                      </div>
                      <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                        <div class="col-3 reducePadding" *ngIf="!rule.checked">
                        <p-calendar selectionMode="range" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }"
                          (ngModelChange)="onChange()" appendTo="body" [iconDisplay]="'input'" [showIcon]="true"></p-calendar>
                        </div>
                         <div class="col-3 reducePadding" *ngIf="rule.checked">     
                             <span class="p-input-icon-right">
                            <input pInputText style="width: 160px;"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                            <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                          </span>
                        </div>
                      </ng-container>
                     
                      <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && !rule.checked">
                        <p-calendar  [(ngModel)]="rule.value" [showIcon]="true" [ngModelOptions]="{ standalone: true }"
                          (ngModelChange)="onChange()" appendTo="body"></p-calendar>
                      </div>
                        <div class="col-3 reducePadding" *ngIf=" rule.operator !== 'between' && rule.operator !== 'not between' && rule.operator !== 'is null' && rule.operator !== 'is not null' && rule.checked">
                            <span class="p-input-icon-right">
                              <input pInputText style="width: 100%"  [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                              <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)" ></i>
                            </span>
                        </div>
                    </ng-container>
           <!-- type = date ENDS -->
                         <!-- type = number -->
                         <ng-container *queryInput=" let rule; let field = field; type: 'number'; let onChange = onChange">
                          <ng-container *ngIf="rule.operator === 'between' || rule.operator === 'not between'">
                            <div class="col-2 reducePadding">
                              <span class="p-input-icon-right">
                                <input pInputText  style="width: 100%" [(ngModel)]="rule.value[0]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                                <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[0],1)"></i>
                              </span>
                            </div>
                              <div style="margin-top:20px !important;">To</div>
                            <div class="col-2 reducePadding">
                            <span class="p-input-icon-right">
                              <input pInputText  style="width: 100%;" [(ngModel)]="rule.value[1]" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                              <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value[1],2)"></i>
                            </span>
                          </div>
                          </ng-container>
                          <ng-container *ngIf="rule.operator !== 'between' && rule.operator !== 'not between'">
                              <div class="col-4 reducePadding">
                              <span class="p-input-icon-right">
                                <input style="width: 100%" type="text" pInputText  placeholder="Integers" [(ngModel)]="rule.value"
                                  [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()" />
                                <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                              </span>
                              </div>
                            </ng-container>
                        </ng-container>
                        <!-- type = number ends -->
  
                       <!--TEXT FILED STARTS-->
                       <div class="col-4 reducePadding" *queryInput=" let rule; let field = field; type: 'string'; let onChange = onChange">
                        <div *ngIf=" rule.operator !== 'is null' || rule.operator !== 'is not null'">
                            <span class="p-input-icon-right">
                              <input pInputText style="width: 100%" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                              <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                            </span>
                        </div>
                    </div>
                    <!--TEXT FILED END-->
                    <!--TIME FILED STARTS-->
                    <ng-container *queryInput=" let rule; let field = field; type: 'time'; let onChange = onChange">
                      <div class="col-1 reducePadding1">
                        <span *ngIf="rule.checked == false">Default</span>
                        <span *ngIf="rule.checked == true">Custom</span>
                      <p-inputSwitch [(ngModel)]="rule.checked" [ngModelOptions]="{ standalone: true }"
                         (ngModelChange)="ruleSwitchChanged(rule)" (ngModelChange)="onChange(rule)" ></p-inputSwitch>
                      </div>
                      <div class="col-3 reducePadding" *ngIf="!rule.checked">
                      <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null' ">
                          <p-calendar appendTo="body" [showIcon]="true" [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" 
                          (ngModelChange)="onChange()" [showTime]="true"  [timeOnly]="true">
                          </p-calendar>
                      </div>
                    </div>
                    <div class="col-3 reducePadding" *ngIf="rule.checked">
                      <div *ngIf="rule.operator !== 'is null' || rule.operator !== 'is not null'">
                        <span class="p-input-icon-right">
                          <input style="width: 100%" pInputText [(ngModel)]="rule.value" [ngModelOptions]="{ standalone: true }" (ngModelChange)="onChange()"/>
                          <i class="pi pi-wrench cursor" (click)="openExpBuilderDialog($event,rule,rule.value,0)"></i>
                        </span>
                    </div>
                  </div>
                    </ng-container>
                     <!--Time FILED END-->
          </div>
        </query-builder>
      </div>
      <div class="mt-2" style="text-align: right;">
        <p-button label="Close" icon="pi pi-times" styleClass="p-button-sm p-button-danger" class="mr-2" (onClick)="close()"></p-button>
        <p-button label="Save" icon="pi pi-save" styleClass="p-button-sm"  (onClick)="save()"></p-button>
      </div>
      <ng-template #nofields>
        <div class="error-msg">
          {{ errmsg }}
        </div>
      </ng-template>
    </p-card>
  </form>
