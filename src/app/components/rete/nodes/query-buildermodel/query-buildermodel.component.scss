
::ng-deep .q-rule{
    display: flex !important;
}
::ng-deep .qBuildCard .p-card .p-card-body{
    padding: 5px 5px !important;
  
}
::ng-deep .qBuildCard .p-card .p-card-content{
    padding: 0px !important;
}

// ::ng-deep .p-dropdown{
//     width : -webkit-fill-available !important;
// }

::ng-deep .p-dropdown {
    width: -webkit-fill-available !important;
  }

  ::ng-deep .selectBtn .p-button {
    font-size: 0.875rem !important;
    padding: 10px !important;
    box-shadow: none !important;
  
  } 
  ::ng-deep .p-button.p-button-info, .p-buttonset.p-button-info > .p-button, .p-splitbutton.p-button-info > .p-button{
    background:var(--primary-color) !important;
    border: none !important;
    border-color: transparent !important;
  }

  ::ng-deep .q-row{
    padding: 0px !important;
  }

  .reducePadding{
    padding: 10px !important;
  }
  .reducePadding1{
    padding: 0px 10px 10px 10px !important;
  }
  .help-icon {
    position: absolute;
    top: 20px;
    right: 55px;
  }