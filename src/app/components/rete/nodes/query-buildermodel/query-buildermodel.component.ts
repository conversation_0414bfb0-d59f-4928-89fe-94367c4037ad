import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { DialogService, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import {QueryBuilderConfig, FieldMap, Field, MyRule,RuleSet } from "../../QueryBuilder.model";
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { QueryExpBuilderComponent } from '../query-exp-builder/query-exp-builder.component';
@Component({
  selector: 'app-query-buildermodel',
  templateUrl: './query-buildermodel.component.html',
  styleUrls: ['./query-buildermodel.component.scss']
})
export class QueryBuildermodelComponent {
  config: QueryBuilderConfig = {
    fields: {},
  };
  query = {
    condition: "and",
    rules: [],
  };
 
  filter = {
    condition: 'and',
    rules: []
  };
  stateOptions: any[] = [{label: 'And', value: 'and'}, {label: 'Or', value: 'or'}];
  dataItem:any;
  formfieldsItems:any;
  nodeformdata: FormGroup;  formfields: any;
  public allowRuleset: boolean = true;
  public allowCollapse: boolean;
  public persistValueOnFieldChange: boolean = false;
  
  constructor(
    private fb: FormBuilder, public dialogData: DynamicDialogConfig,public ref1: DynamicDialogRef, public dialogService: DialogService,
    private utilservice: UtilsService, private reteservice: ReteService,public ref: DynamicDialogRef){
  }

 async ngOnInit(){
  this.openRunData(this.reteservice.getRunflowToDialog())

  this.nodeformdata = this.fb.group({});
    this.dataItem=this.dialogData.data.nodeData;
    this.formfieldsItems=this.dialogData.data.formData;
         if(this.dataItem.nodeName == "condition"){ 
          if(!this.formfieldsItems && this.formfieldsItems == ""){
          }else{
            this.query =  this.convertStringToDate(this.formfieldsItems)
           // this.query = this.formfieldsItems;
          }         
             await this.importformfields();
              //this.query = this.formfieldsItems;
              this.query.rules.forEach((element,key) => {
               // element.index = key
            //  this.ruleOperationChanged(element);
            //  this.ruleSwitchChanged(element)
              });
             this.nodeformdata.addControl("query", this.fb.control(this.query));             
           }
           
           if(this.dataItem.nodeName == "ReadRows"){
            if(!this.formfieldsItems && this.formfieldsItems == ""){
              
            }else{
              this.filter=this.convertStringToDate(JSON.parse(this.formfieldsItems));
             //  this.filter=JSON.parse(this.formfieldsItems);   
            }
          this.importReadNodefields();
          // this.filter = this.formfieldsItems.value.filter;
          this.filter.rules.forEach( element => {
           // this.ruleOperationChanged(element);
          });
            this.nodeformdata.addControl('filter', this.fb.control(this.filter));
           }
  }

 async importformfields() {
    this.reteservice.importformfields(this.dataItem.formId).subscribe(async (res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.formfields = response.formFields;
            if(this.dataItem.nodeName === 'condition') {
              if(this.dataItem.node.getConnections().length > 0){
                let formfieldsFromPreviousnodes;
                const nodesfnd = await this.utilservice.getpreviousnodes(this.dataItem.nodeId, this.dataItem.nodeName, [], this.dataItem.editor.toJSON());
                if(nodesfnd){
                 formfieldsFromPreviousnodes = this.utilservice.prepareFromfieldsFromPreviousNodes(nodesfnd);
                }                
                if(formfieldsFromPreviousnodes && Array.isArray(formfieldsFromPreviousnodes)){
                formfieldsFromPreviousnodes.forEach(formfield => {                  
                  this.formfields.push(formfield);
                })
              }
              }
             this.config = {
                fields: {},
              };
            this.config = {
              fields: await this.setExpressionParam(this.formfields)
            };
          }
          }   
                           
        } else {
          // this.errmsg =
          //   "Form does not have any fields to setup conditional queries.";
        }
      });
      
  }
 async setExpressionParam(arrayexample): Promise<FieldMap> {
    const arrayToObject = (array) =>
      array.reduce((obj, item) => {
        obj[item.key] = item;
        return obj;
      }, {});
    const fieldMap: FieldMap = arrayToObject(arrayexample);
    return fieldMap;
  }

  testRule(event:any,value:any){
  }
  ruleOperationChanged(rule: any) { 
    if (rule.operator == "between" || rule.operator == "not between") {
      if (!rule.value) {
        rule.value = [];
      }else{
        rule.value = [];
      }
    } else {
      if (rule.value != 0 && !rule.value) {
        rule.value = null;
      }
    }    
  }
  ruleSwitchChangedDropdown(rule:MyRule){    
    if(rule.checked){
      rule.value = '';
     }else{
     // rule.value = '';
     }
  }
  ruleSwitchChanged(rule:MyRule){
    if(rule.checked){
     rule.value = null;
    }else{
      rule.value = '';
    }
  }
  convertStringToDate(rule: any) {
    const parsed = rule;
    parsed.rules.forEach(rule => {
      if (this.isDateString(rule.value)) {
        const val = new Date(rule.value)
        rule.value = new Date(Date.UTC(
          parseInt(rule.value.substring(0, 4)), // Year
          parseInt(rule.value.substring(5, 7)) - 1, // Month (0-indexed)
          parseInt(rule.value.substring(8, 10)), // Day
          parseInt(rule.value.substring(11, 13)), // Hour
          parseInt(rule.value.substring(14, 16)), // Minute
          parseInt(rule.value.substring(17, 19)), // Second
          parseInt(rule.value.substring(20, 23)) // Millisecond
        ));
      }
    });
    return parsed
  }
 
  isDateString(value: string) {
    // Use a regular expression to check for date formats, adjust as needed
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(value);
  }
  
  async importReadNodefields() {
    this.reteservice.importformfields(this.dialogData.data.masterdataId).subscribe(async (res) => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.formfields = response.formFields;
            this.config = {
              fields: await this.setExpressionParam(this.formfields)
            };
         }
        } else {
          //this.errmsg = 'Form does not have any fields to setup conditional queries.';
        }
      });
  }
  save(){    
    let data={
      "qData":this.nodeformdata.value,
      "save":true
    }
    this.ref1.close(data); 
  //  if(this.dataItem.nodeName == "ReadRows"){
  //   this.ref.close(data); 
  //  }else{
  //   this.ref.close(data); 
  //  }
  }

  close(){    
    let data={
      "qData":this.formfieldsItems,
      "save":false
    }
    this.ref1.close(data); 
  }

   getData(data:any){
     data.checked = false;
   }

  openExpBuilderDialog(event: any,rule:any,value:any,index:any){
    let data={
      "rule":rule,
      "value":value,
      "jsonData":this.query,
      "nodeData":this.dataItem
    }
    event.stopPropagation();
    event.preventDefault();
    this.ref = this.dialogService.open(QueryExpBuilderComponent, {
      header: 'Expression Builder',
      contentStyle: { overflow: 'auto', padding: '5px'},
      style:{"max-height":"75%","max-width":"75%","z-index":999},
      modal:true,
      maximizable: false,
      data: data,
      draggable:true
    });
    this.ref.onClose.subscribe((res:any) => {
      if(res.expBuildValue != undefined){
        if ((rule.operator == "between" || rule.operator == "not between") && rule.field != "dateTime") {
              if(index == 1){
                rule.value[0]=res.expBuildValue;
              }else{
                rule.value[1]=res.expBuildValue;
              }
        }else{
          rule.value = res.expBuildValue;
        }
    }
    });
  }

  addRuleIndex(data:any){    
    data.rules.forEach((item,index)=>{
    
      //     item.newId=id;
        //   item.handleClick = function() {
        // }
    })
  }
  openPanel:boolean=false;
  openTestData(event:any){
    let data={
      "panelName":"testData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
  openRunData(event:any){
    let data={
      "panelName":"runData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
}
