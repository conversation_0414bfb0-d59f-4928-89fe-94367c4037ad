import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, generalSocket } from '../../sockets';
import { AngularComponent, AngularComponentData } from 'rete-angular-render-plugin';
// import { MyNodeComponent } from './node/node.component';
export class CSVMapperComponent extends Component {
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'csvparser',
    wfNamespace: 'unvired.operation',
    icon: 'map',
    category: 'Others'
  };
  constructor() {
    super('csvparser');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
builder(node) {
  const inp1 = new Input('csvparser', 'Input', generalSocket);
  const out1 = new Output('csvparser_success', 'Success', successSocket, false);
  const out2 = new Output('csvparser_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
