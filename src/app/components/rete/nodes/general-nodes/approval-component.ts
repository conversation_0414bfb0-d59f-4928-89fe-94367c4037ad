import { Component, Output, Input } from 'rete';
import {
  generalSocket,
  successSocket,
  ApprovalApprovedSocket,
  ApprovalRejectedSocket,
  ApprovalSendbackSocket,
  ApprovalNextSocket,
  errorSocket } from '../../sockets';
import { AngularComponentData } from 'rete-angular-render-plugin';

export class ApprovalComponent extends Component {
  override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'approvalworkflow',
    wfNamespace: 'unvired.flow',
    icon: 'approval',
    category: 'Others'
  };
  constructor() {
    super('approvalworkflow');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
async builder(node) {

  const inp1 = new Input('approvalworkflow', 'Input', generalSocket);

  const out1 = new Output('approvalworkflow_approved', 'Approved', ApprovalApprovedSocket, false);
  // const out2 = new Output('approvalworkflow_rejected', 'Rejected', ApprovalRejectedSocket, false);
  // const out3 = new Output('approvalworkflow_returnback', 'Return to Requestor', ApprovalSendbackSocket, false);
  // const out4 = new Output('approvalworkflow_nextapprover', 'Next Approver', ApprovalNextSocket, false);
  const out5 = new Output('approvalworkflow_error', 'On Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addOutput(out2)
              // .addOutput(out3)
              // .addOutput(out4)
              .addOutput(out5);
}

async worker(node, inputs, outputs) {

}
}
