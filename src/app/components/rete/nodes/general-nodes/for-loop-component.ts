import { Component, Output, Input } from 'rete';
import { successSocket, generalSocket } from '../../sockets';
import { AngularComponentData } from 'rete-angular-render-plugin';
export class ForLoopComponent extends Component {
  //data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'forloop',
    wfNamespace: 'unvired.operation',
    icon: 'loop',
    category: 'Others'
  };
  constructor() {
    super('ForLoop');
   // this.data.render = 'angular';
   // this.data.component = ForloopNodeComponent;
  }
builder(node) {
  const inp1 = new Input('ForLoop', 'Input', generalSocket);
  const out1 = new Output('ForLoop_success', 'Success', successSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
