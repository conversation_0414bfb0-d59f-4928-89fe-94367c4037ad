import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, generalSocket } from '../../sockets';
import { AngularComponent, AngularComponentData } from 'rete-angular-render-plugin';
// import { MyNodeComponent } from './node/node.component';
export class FileCatcherComponent extends Component {
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'filestore',
    wfNamespace: 'unvired.operation',
    icon: 'cloud_sync',
    category: 'Others'
  };
  constructor() {
    super('filestore');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
builder(node) {
  const inp1 = new Input('filestore', 'Input', generalSocket);
  const out1 = new Output('filestore_success', 'Success', successSocket, false);
  const out2 = new Output('filestore_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
