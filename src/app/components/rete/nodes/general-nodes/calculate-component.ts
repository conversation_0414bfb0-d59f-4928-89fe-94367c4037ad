import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, generalSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
 import { MyNodeComponent } from './node/node.component';
export class CalculateComponent extends Component {
 override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'calculate',
    wfNamespace: 'unvired.operation',
    icon: 'calculate',
    category: 'Others'
  };
  constructor() {
    super('calculate');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
builder(node) {
  const inp1 = new Input('calculate', 'Input', generalSocket);
  const out1 = new Output('calculate_success', 'Success', successSocket, false);
  const out2 = new Output('calculate_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
