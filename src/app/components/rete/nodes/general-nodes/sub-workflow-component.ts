import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket,generalSocket } from '../../sockets';
import { AngularComponentData } from 'rete-angular-render-plugin';
// import { MyNodeComponent } from './node/node.component';
export class SubWorkflowComponent extends Component {
  override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'subflow',
    wfNamespace: 'unvired.flow',
    icon: 'account_tree',
    category: 'Others'
  };
  constructor() {
    super('SubWorkflow');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
builder(node) {
  const inp1 = new Input('SubWorkflow', 'Input', generalSocket);
  const out1 = new Output('SubWorkflow_success', 'Success', successSocket, false);
  const out2 = new Output('SubWorkflow_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
