.nodeIcon2{
    color:#292929;
    padding: 11px; 
    background: #bbdefb;
  }
  .nodeIcon{
   // color:var(--blue-500)!important;
    width: 50px;
    height: 47px;
    background: var(--blue-100)!important
  }
.pmgImg{
  margin-top: 8px;
  margin-left: 10px;
  // margin-bottom: 3px;
  width: 30px;
  height: 30px;
}
.forLoopOutputs{
  display: flex;
  margin-top: -14px !important;
  margin-left: 169px;
}
// .output-sockets-error{
//   margin-left: 77px;
// }
// .output-sockets-success{
//   margin-left: 57px;
// }
.approvalOutput{
   display: inline-flex; 
   margin-left: 49px;
}

.conditionNodeOutput{
  display: inline-flex;
  margin-left: 57px;
}
.conditionNodeOp{
  margin-left: 100px !important;
}
.p-inputtext {
  // padding : 8px 35px 8px 10px !important;
  width: -webkit-fill-available;
}
::ng-deep .calNode .p-card{
  margin-top: 10px !important;
}
::ng-deep .calNode .p-card .p-card-body{
  padding: 0px !important;
}
::ng-deep .calNode .p-card .p-card-content{
  padding:  10px !important;
}
::ng-deep .injNode .p-card{
  margin-top: 10px !important;
}
::ng-deep .injNode .p-card .p-card-body{
  padding: 0px !important;
}
::ng-deep .injNode .p-card .p-card-content{
  padding:  10px 0px 10px 5px!important;
}
::ng-deep .approvalCard .p-card .p-card-body{
  margin-top: 10px !important;
  padding: 0px !important;
}
::ng-deep .approvalCard .p-card .p-card-title{
  margin-bottom: 0px !important;
}
::ng-deep .approvalCard .p-card .p-card-content{
  padding: 10px 10px !important;
}
::ng-deep .selectBtn1 .p-button {
  font-size: 0.875rem !important;
  padding: 8px !important;
  box-shadow: none !important;
  width: 50% !important;
} 
::ng-deep .selectBtn2 .p-button {
  font-size: 0.875rem !important;
  padding: 8px !important;
  box-shadow: none !important;
  width: 33.3% !important;
} 
::ng-deep .warnMsg .p-inline-message{
  width: 100%;
}
.selected {
  // box-shadow: 10px 5px 5px red;
  border: 2px solid rgb(145, 149, 139);
  // box-shadow:0 0 6px 6px rgb(145, 149, 139);
}
:host ::ng-deep .fieldDisplay {
  display: none !important;
}

::ng-deep .formhttpCard .p-card .p-card-body{
  padding: 10px  0px!important;
}