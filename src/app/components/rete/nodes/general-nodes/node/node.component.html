<div [ngClass]="{'nodes-input-socket': node.name !== 'merge', 'nodes-input-socket-merge': node.name === 'merge'}" 
     [style.display]="node.name === 'merge' ? 'flex' : ''" 
     [style.justify-content]="node.name === 'merge' ? 'space-evenly' : ''">
  <div *ngFor="let input of inputs; let index = index">
    <rete-socket *ngIf="node.name !== 'merge' || inputs[index]" rete-socket [io]="input" [socket]="input.socket"></rete-socket>
  </div>
</div>
  <div class="inline-flex"><!--[ngClass]="{'panelOpen': node.name !== 'merge', 'panelOpenCombain': node.name === 'merge'}"-->
    <div class="w-25rem border-1 border-300" [ngClass]="[selected()]">
      <p-panel #nodePanel  toggleable="true" [collapsed]="openPanelForNew" expandIcon="pi pi-chevron-up" collapseIcon="pi pi-chevron-down"
      [ngClass]="{'panelOpen': node.name !== 'merge', 'panelOpenCombain': node.name === 'merge'}" (onBeforeToggle)="[panelBtn($event,node),expbuilder.hide()]">
     <ng-template pTemplate="header">
        <span class="nodeIcon" pTooltip="{{this.nodeName}}" tooltipPosition="top" *ngIf="node.name !== 'sap'"><img class="pmgImg" src="assets/icon/{{this.nodeIcon}}.png"></span>
        <span class="ml-5">
          <div *ngIf="!this.readonlyOnDisable && node.name !== 'merge'">
           <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && displayBtn"  >{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
           <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && displayBtn">{{node.name}}</span>
             <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && !displayBtn" (click)="focusIn()">{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
             <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && !displayBtn" (click)="focusIn()">{{node.name}}</span>
             <input *ngIf="nodeDesc && displayBtn"  id="inputBox" (click)="focusIn()" (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
             <input *ngIf="nodeDesc && !displayBtn" id="inputBox" (click)="focusIn()" (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
            </div>
            <div *ngIf="!this.readonlyOnDisable && node.name === 'merge'">
              <span *ngIf="!editing" class="spanText" (click)="focusIn()">{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
              <input *ngIf="editing" id="inputBox"  (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
            </div>
            <div *ngIf="this.readonlyOnDisable">
              <span class="spanText">{{node.data.description}}</span>
            </div>
           </span>
      </ng-template>
      <ng-template pTemplate="content">
        <div class="cardBody">
          <div *ngIf="this.error != '' " style="margin-top: 25px;padding: 30px;">
            <span style="color: tomato;">{{this.error}}</span>
          </div>
          <div *ngIf="this.duplicate" style="margin-top: 10px; margin-right: 8px;">
            <p-message class="warnMsg" severity="warn" text="Node name should be unique."></p-message> 
          </div>
          <form [formGroup]="nodeformdata" novalidate style="border: 0px; margin-right: 5px;padding-left: 2px;margin-bottom: 10px;" autocomplete="off" *ngIf="!displayBtn">
            <div [ngSwitch]="this.dataItem.nodeName">
              <div *ngSwitchCase="'filestore'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                    <!--TYPE === "LIST"-->
                    <div style="display: flex;"
                      *ngIf="field.value.inputName !== 'system' && field.value.type === 'list'">
                      <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                        <p-dropdown class="integrationNode" [options]="field.value.listvalue"
                          [pTooltip]="field.value.description" (onChange)="fileCacherSelect($event.value)" 
                          optionLabel="key" optionValue="value" [autoDisplayFirst]="true" appendTo="body"
                          placeholder="Select...." formControlName="values">
                        </p-dropdown>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                    <!--TYPE == "TEXT" with no condition-->
                    <div style="display: flex;" [ngClass]="{'fieldDisplay': field.value.show == true}"  *ngIf="field.value.type === 'text' && (field.value.inputName != 'attachmentid' && field.value.inputName != 'externalreference' &&
                                                       field.value.inputName != 'filename' && field.value.inputName != 'data' ) ">
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"
                          [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                          (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                    <!--CONDITION STARTS-->
                    <!--CONDITION DELETE or DOWNLOAD-->
                    <div *ngIf="(this.fileCacherSelectValue == 'delete' ||  this.fileCacherSelectValue == 'download')">
                      <div style="display: flex;" *ngIf="field.value.type === 'text' && (field.value.inputName == 'attachmentid' || field.value.inputName == 'externalreference' ) ">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values" [pTooltip]="field.value.description"  />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <div style="display: flex;" *ngIf="field.value.type === 'text' && (field.value.inputName == 'filename' || field.value.inputName == 'data' ) ">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div> 
                    </div>
                    <!--CONDITION DELETE or DOWNLOAD ENDS-->
                    <!--CONDITION UPLOAD-->
                    <div *ngIf="this.fileCacherSelectValue == 'upload'">
                      <div style="display: flex;" *ngIf="field.value.type === 'text' && (field.value.inputName == 'filename' || field.value.inputName == 'data' ) ">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>  
                      <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName == 'externalreference' || field.value.inputName == 'attachmentid'">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div> 
                  </div>
                  <!--CONDITION UPLOAD ENDS-->
                    <!--CONDITION ENDS-->
                    <!--TYPE ==="RADIO"-->
                    <div class="mt-2"
                      *ngIf="field.value.type === 'radio' && node.name !=='ShareForm' && field.value.inputName !== 'recipienttype'">
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                        optionLabel="key" optionValue="value"></p-selectButton>
                    </div>
                    <!--END-->
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="'ForLoop'">
                <!--TYPE == "TEXT"-->
                <div style="display: flex;">
                  <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                    <input pInputText  formControlName="IterableData" #IterableData/>
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,IterableData.value,'',-1)"></i>
                    <label>Iterable Data</label>
                  </span>
                </div>
                <!--END-->
                <!--TYPE === "LIST"-->
                <div style="display: flex;">
                  <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" [options]="aggregatedDataFormats"  optionLabel="label" optionValue="value"
                      appendTo="body" formControlName="aggregatedDataFormat">
                    </p-dropdown>
                    <label>Aggregated data format</label>
                  </span>
                </div>
                <!--END-->
              </div>
              <div *ngSwitchCase="'SubWorkflow'">
                <!--TYPE === "LIST" == Workflow-->
                <div style="display: flex;">
                  <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" [options]="allWorkflows" formControlName="subwfName" [filter]="true"optionLabel="wfTitle"
                    (onChange)="getparamsforsubwf($event.value)" optionValue="wfName" [autoDisplayFirst]="false" appendTo="body"></p-dropdown>
                    <label class="_required">Workflow</label>
                  </span>
                </div>
                <!--END-->
                <!--TYPE === "LIST" == Execution Mode-->
                <div style="display: flex;">
                  <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" [options]="select" formControlName="wfOptions" optionLabel="label" optionValue="value" appendTo="body"></p-dropdown>
                    <label class="_required">Execution Mode</label>
                  </span>
                </div>
                <!--END-->
                <!-- forms starts on select dropdown-->
                <div class="subwf-node" formArrayName="input" *ngFor="let field of input.controls; let ind = index;">
                  <div [formGroupName]="ind">
                      <!--TYPE == "TEXT" with no condition-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text'">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.inputName}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.inputName}}</label>
                    </span>
                  </div>
                  <!--END-->
                  </div>
                </div>
                <!--Ends-->
              </div>
              <div *ngSwitchCase="'response'">
                <div  formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                     <!--TYPE ==="RADIO"-->
                    <div class="mt-2" *ngIf="field.value.type === 'radio'">
                      <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values" optionLabel="key" optionValue="value" (onChange)="getRadioValue($event.value)"></p-selectButton>
                    </div>
                    <!--END-->
                    <!--TYPE == "TEXT" with  condition == injectresult-->
                    <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'injectresult' &&  this.injuctSelectValue == 'success'">
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"  [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                    <!--TYPE == "TEXT" with condition == injecterror-->
                    <div style="display: flex;" *ngIf="field.value.type === 'text' && field.value.inputName === 'injecterror' && this.injuctSelectValue == 'failure'">
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"  [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                    <!--TYPE == "TEXT"-->
                    <div style="display: flex;" *ngIf="field.value.type === 'text'">
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"  [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                <!--TYPE === "KEYVALUE" & CARD with KEY VALUES-->
                <div *ngIf="field.value.type === 'keyvalue'" [ngClass]="{'fieldDisplay': field.value.show == true}" class="my-1">
                  <p-card class="injNode">
                   <div class="mb-2">
                     <span class="ml-3 font-bold capitalize">{{field.value.inputName}}</span>
                     <span style="float: right;">
                       <i class="pi pi-plus mr-4 cursor"  *ngIf="!readonlyOnDisable" style="font-size: 1rem;" (click)="addkeyValuepair(ind, '', '')"></i> 
                      </span>
                   </div>
                   <div *ngIf="field.value.type === 'keyvalue'" formArrayName="valuepairs">
                     <div class="form-row" *ngFor="let keyvalue of getkeyvaluecontrol(field); index as i">
                         <ng-container [formGroupName]="i">
                         <div class="row p-0 m-0">
                         <span class="p-float-label p-input-icon-right mt-4 col-5 p-0 mx-1">
                           <input pInputText   formControlName="key" placeholder="Parameter key" [pTooltip]="field.value.description"/>
                           <label style="font-size: 12px;">Parameter Key</label>
                         </span>
                         <span class=" p-float-label p-input-icon-right mt-4 col-5 p-0">
                           <input pInputText   formControlName="value" placeholder="Parameter value" [pTooltip]="field.value.description"/>
                           <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, keyvalue.value.value, field.value.type, ind, i)"></i>
                           <label style="font-size: 12px;">Parameter Value</label>
                         </span>
                         <i class="pi pi-trash col-1 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97);"  (click)="removeKeyValue(ind,i)"></i>
                         </div>
                       </ng-container>
                     </div>
                    </div>
                 </p-card> 
               </div> 
               <!--END-->
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="'email'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                  <!--TYPE === "LIST"-->
                    <div style="display: flex;" *ngIf="field.value.type === 'list'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="field.value.listvalue" [pTooltip]="field.value.description" optionLabel="key" optionValue="value" [autoDisplayFirst]="true"
                       appendTo="body" placeholder="Select...." formControlName="values">
                      </p-dropdown>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with no condition-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                   <!--TYPE ==="RADIO"-->
                   <div class="mt-2" *ngIf="field.value.type === 'radio'  && field.value.inputName === 'recipienttype' ">
                   <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                   <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                   <p-selectButton class="selectBtn2" [options]="field.value.radiolistvalue" formControlName="values"
                     optionLabel="key" optionValue="value" (onChange)="SendEmailRadio($event.value)" ></p-selectButton>
                    </div>
                  <!--END-->
                  <div *ngIf="field.value.type === 'user' && userTeamSelectValue === 'users'">
                    <!--TYPE == "user" Select BTN-->
                      <div class="mt-3">
                        <p-selectButton class="selectBtn1" [options]="userOptions" formControlName="defaultSelect" optionLabel="label" optionValue="value" (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                      </div>
                    <!--Based on radio select and type is user/formFiled -->
                      <div *ngIf="userTeamSelectValue === 'users' ">
                        <div *ngIf="field.value.defaultSelect === 'users'">
                          <span class="p-float-label mt-4 w-full">
                            <p-multiSelect  class="multiSelect" [options]="allusers" formControlName="values" optionLabel="email" optionValue="userId" [showClear]="true" [pTooltip]="field.value.description"
                               display="chip" [autoDisplayFirst]="true" appendTo="body" [filter]="true"  placeholder="Select...." (onChange)="test($event,field.value.values)" ></p-multiSelect>
                              <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                              <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                          </span>
                        </div>
                      </div>
                      <div *ngIf="userTeamSelectValue === 'users'">
                        <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                          <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                            <input pInputText   formControlName="values" [pTooltip]="field.value.description" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                              <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                              <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                          </span>
                        </div>
                      </div>
                    <!--END--><!--END-->
                  </div>
                  <div *ngIf="field.value.type === 'team' && userTeamSelectValue === 'teams'">
                    <!--TYPE == "Teams" RADIO BTN-->
                      <div class="mt-3">
                        <p-selectButton class="selectBtn1" [options]="teamOptions" formControlName="defaultSelect" optionLabel="label" optionValue="value" (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                      </div>
                    <!--Based on radio select and type is team/formfield -->
                      <div *ngIf="userTeamSelectValue === 'teams' ">
                        <div style="display: flex;" *ngIf="field.value.defaultSelect === 'teams'">
                          <span class="p-float-label mt-4 w-full">
                            <p-multiSelect class="multiSelect"  appendTo="body"  [options]="allteams" formControlName="values" optionLabel="name" optionValue="name" [showClear]="true" [pTooltip]="field.value.description" 
                              display="chip" [autoDisplayFirst]="true" appendTo="body" [filter]="true"  placeholder="Select...." class="multiSelect" (onChange)="test($event)" ></p-multiSelect>
                                <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                                <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                          </span>
                        </div>
                      </div>
                      <div *ngIf="userTeamSelectValue === 'teams'">
                        <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                          <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                            <input pInputText   formControlName="values" [pTooltip]="field.value.description" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                                <label  *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                                <label  *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                          </span>
                        </div>
                      </div> 
                    <!--END--><!--END-->
                  </div>
                  <!--TYPE === "BOOLEAN" & CHECKBOX-->
                  <div class="mt-3" *ngIf="field.value.type === 'boolean'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <p-checkbox formControlName="values" [binary]="true" [pTooltip]="field.value.description"></p-checkbox>
                    <label  *ngIf="field.value.required == 'Mandatory'" class="required ml-3">{{field.value.label}}</label>
                    <label *ngIf="field.value.required !== 'Mandatory'" class="ml-3">{{field.value.label}}</label>
                  </div>
                  <!--END-->
                  <!--TYPE == "EMAIL"-->
                  <div style="display: flex;" *ngIf="field.value.type === 'email' && userTeamSelectValue === 'emails'">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" type="email" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                   <!--TYPE == "MESSAGE"-->
                   <div style="display: flex;" *ngIf="field.value.type === 'textarea'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <textarea pInputTextarea  rows="5" cols="30" formControlName="values" [pTooltip]="field.value.description"  ></textarea>
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="'calculate'">
                <!--TYPE ==="RADIO"-->
                <div class="flex flex-row justify-content-around mt-2" style="text-align: center;"> 
                  <p-button styleClass="p-button-sm" (onClick)="addcalculatenodeRow('', '', '', 'expression')" class="mr-2" *ngIf="!readonlyOnDisable">
                    <span class="material-icons" style="color: black; font-size: 18px;">functions</span>
                    <span class="ml-2 font-bold">Expression</span>
                  </p-button>
                  <p-button styleClass="p-button-sm" (onClick)="addcalculatenodeRow('', '', '', 'jsonata', '{}', '');" *ngIf="!readonlyOnDisable">
                    <img alt="logo" src="assets/icon/jsonata.png" style="width: 18px" />
                    <span class="ml-2 font-bold">JSON Extractor</span>
                  </p-button>
                </div> 
                <!--END-->
                 <div formArrayName="expressions" *ngFor="let expr of expressions?.controls; let ind = index">
                  <div [formGroupName]="ind">   
                      <p-card class="calNode mt-2">
                        <span>
                        <span class="material-icons" *ngIf="expr.get('category').value === 'expression'; else pngicon">functions</span>
                         <ng-template #pngicon><img height="24px" width="24px" src="assets/icon/jsonata.png" /></ng-template>
                         <i class="pi pi-trash cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97);float: right;"  (click)="removecalculatenodeRow(ind)"></i></span>
                          <!-- TYPE == "TEXT" is NAME -->
                          <div style="display: flex;">
                          <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                            <input pInputText  formControlName="key" placeholder="Name" />
                            <label class="_required">Variable Name</label>
                          </span>
                        </div>
                        <!-- END -->
                        <!-- TYPE === "LIST" -->
                        <div style="display: flex;">
                        <span class="p-float-label mt-4" style="padding: 0px; width: -webkit-fill-available;">
                          <p-dropdown  class="integrationNode" [options]="calculateNodeOpt"  optionLabel="label" optionValue="value"
                           [autoDisplayFirst]="true" appendTo="body" formControlName="type" placeholder="type">
                          </p-dropdown>
                          <label class="_required">Variable Type</label>
                        </span>
                      </div>  
                      <!-- END -->
                      <!-- TYPE == "TEXT" is NAME -->
                      <div style="display: flex;" *ngIf="expr.get('category').value === 'expression'; else mapexpression">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText   formControlName="value" placeholder="expression" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, expr.get('value').value, 'expressions', ind, expr.get('key').value)"></i>
                          <label class="_required">Expression</label>
                        </span>
                      </div>
                      <!-- END -->
                      <ng-template #mapexpression>
                         <!-- TYPE === "LIST" -->
                         <div style="display: flex;" >
                          <span class="p-float-label mt-4" style="padding: 0px; width: -webkit-fill-available;">
                          <p-dropdown class="integrationNode" [options]="sourcedataForCalculate" (onClick)="getMapExp()" placeholder="Field" [group]="true" appendTo="body" 
                          formControlName="sourcedata" (openedChange)="sourceDataSelectOpen($event, ind)">
                            <ng-template let-group pTemplate="group">
                              <div class="flex align-items-center">
                                <span style="font-size: 14px;" >{{ group.label }}</span>
                              </div>
                          </ng-template>
                        <ng-template let-group pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                  <span class="material-icons">{{ group.icon }}</span>
                                  <span>{{ group.label }}</span>
                            </div>
                        </ng-template>
                        </p-dropdown>
                        <label class="_required">Map Field</label>
                      </span>
                        </div>  
                        <!-- END -->
                        <!-- TYPE == "TEXT" is Map Expression -->
                       <div style="display: flex;">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText   formControlName="mapexpression" placeholder="Map expression" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openJsonata(expr.get('json').value, expr.get('mapexpression').value,'calculate', ind)"></i>
                          <label class="_required">Map Expression</label>
                        </span>
                      </div>
                      <!-- END -->
                      </ng-template>                    
                      </p-card>
                  </div>
                 </div>
              </div>
              <div *ngSwitchCase="'csvparser'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                  
                   <!--TYPE ==="RADIO"-->
                   <div class="mt-2" *ngIf="field.value.type === 'radio'">
                   <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                   <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                   <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                     optionLabel="key" optionValue="value" (onChange)="csvMapperRadio($event.value)"  ></p-selectButton><!---->
                    </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with condition inputName === 'attachmentid'-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text' &&  field.value.inputName === 'attachmentid' && this.csvRadio=='csv'">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with condition inputName === 'data'-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text' &&  field.value.inputName === 'data'"  >
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with no condition-->
                  <div style="display: flex;" *ngIf="field.value.type === 'text' &&  field.value.inputName !== 'data' && field.value.inputName !== 'attachmentid'" [ngClass]="{'fieldDisplay': field.value.show == true}" >
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                    </span>
                  </div>
                  <!--END-->
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="'jsonparser'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                    <!--TYPE ==="RADIO"-->
                   <div class="mt-2">
                    <p-selectButton class="selectBtn2" [options]="datamapperOptions" formControlName="mapfieldtype" optionLabel="label" optionValue="value" 
                          (onChange)="onMapFieldTypePick($event.value)"  ></p-selectButton>
                     </div>
                   <!--END-->
                   <!--TYPE === "LIST"-->
                   <div style="display: flex;" *ngIf="mapfieldtype?.value === 'formfield' || mapfieldtype?.value === 'flowinput'">
                    <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="fieldsToMap" optionLabel="label" optionValue="value" [autoDisplayFirst]="true"
                       appendTo="body" placeholder="Select...." formControlName="sourcedata" >
                      </p-dropdown>
                      <label class="_required">Map Field Name</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE === "LIST"-->
                  <div style="display: flex;" *ngIf="mapfieldtype?.value === 'previousnode'">
                    <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="fieldsToMap"  optionLabel="name" optionValue="key" [autoDisplayFirst]="true"
                       appendTo="body" placeholder="Select...." formControlName="sourcedata" (onFocus)="getPreviousNodesDetails()">
                      </p-dropdown>
                      <label class="_required">Map Field</label>
                    </span>
                  </div>
                  <!--END-->
                  <!--TYPE == "TEXT" with no condition-->
                  <div style="display: flex;">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText  formControlName="mapexpression" />
                      <i class="pi pi-wrench cursor" *ngIf="sourcedata?.value && !readonlyOnDisable" (click)="openJsonata(field.value.json, field.value.mapexpression,'datamapper' ,ind)"></i>
                      <label class="_required">Expression</label>
                    </span>
                  </div>
                  <div>
                  </div>
                  <!--END-->
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="'approvalworkflow'">
                <!--TYPE === "LIST"-->
                <div style="display: flex;" class="w-full">
                <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                  <p-dropdown class="integrationNode" [options]="approvalforms" optionLabel="formTitle" optionValue="formId" [autoDisplayFirst]="true" appendTo="body" placeholder="Select...." 
                  formControlName="approval_form"></p-dropdown>
                  <label class="_required">Approval form</label>
                </span>
                </div>
                <!--END-->
                <!--Button-->
               <div class="flex justify-content-center mt-3 mb-3">
                <p-button label="Add Approval Level" icon="pi pi-plus" styleClass="p-button-sm p-button-raised p-button-text" (onClick)="addapprovallevel('', 1)"></p-button>
               </div>
               <!--END-->
              <p-card class="approvalCard mt-2" formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                <div [formGroupName]="ind">
                  <div style="display: flex;">
                    <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                      <p-dropdown class="integrationNode" [options]="approversTypes" optionLabel="label" optionValue="value" [autoDisplayFirst]="true" appendTo="body" placeholder="Select...." 
                      formControlName="type"></p-dropdown>
                      <label class="_required">Approvers Type</label>
                    </span>
                    </div>
                <div class="w-full" *ngIf="this.input.at(ind).value.type == 'team' ">
                  <div class="p-inputgroup cat">
                    <span class="p-float-label mt-4" style="padding: 0px;">
                      <p-dropdown class="approvalNode" [options]="approvargroups" optionLabel="name" optionValue="teamId" [autoDisplayFirst]="true" appendTo="body" placeholder="Select...." 
                      formControlName="approver_group"></p-dropdown>
                      <label class="_required">Team</label>
                    </span>
                    <button class="p-button-sm bg-blue mt-4" style="padding: 6px 15px;" disabled pButton type="button">{{ind + 1}}</button>
                  </div>
                </div>
                <div class="w-full" *ngIf="this.input.at(ind).value.type == 'approvers'">
                  <div class="p-inputgroup cat">
                    <span class="p-float-label mt-4" style="padding: 0px;">
                      <p-dropdown class="approvalNode" [options]="approversFormsList" optionLabel="name" optionValue="key" [autoDisplayFirst]="true" appendTo="body" placeholder="Select...." 
                      formControlName="approver_group"></p-dropdown>
                      <label class="_required">Form field</label>
                    </span>
                    <button class="p-button-sm bg-blue mt-4" style="padding: 6px 15px;" disabled pButton type="button">{{ind + 1}}</button>
                  </div>
                </div>
                <div class="w-full">
                <div class="p-inputgroup cat">
                  <span class="p-float-label mt-4">
                    <p-inputNumber  inputId="integeronly" [showButtons]="true"  [min]="1" formControlName="number_of_approvers"> </p-inputNumber>
                    <!-- <input pInputText  style="line-height: 1.15 !important;" min="0" formControlName="number_of_approvers" type="number" /> -->
                    <label class="_required">Number Of Approvers</label>
                  </span>
                  <i class="pi pi-trash col-1 mt-4 p-3 mr-3 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97);"  (click)="removeapprovallevel(ind)"></i>
                </div>
              </div>
                </div>
              </p-card>
              </div>
              <div *ngSwitchCase="'condition'">
                <div style="text-align: center; margin-top: 30px;">
                  <p-button icon="pi pi-pencil" label="Open Query Builder" styleClass="p-button-sm mr-2" (click)="openQueryBuilder(dataItem,nodeformdata)" ></p-button>
                  <span *ngIf="showTick" class="material-icons" style="color: green;">done_all</span>
                </div>
            <!--END-->  
              </div>
            </div>
            <div class="py-2 mr-3" style="float: right;" *ngIf="isNodeNameExcluded(dataItem.nodeName)" >
              <p-button  styleClass="p-button-text p-0" *ngIf="!enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Show advanced options</span><i class="pi pi-chevron-down"></i></p-button>
              <p-button  styleClass="p-button-text p-0"  *ngIf="enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Hide advanced options</span><i class="pi  pi-chevron-up"></i></p-button>
            </div>
          </form>
        </div>
      </ng-template>
    </p-panel>
    </div>
    <div class="m-1 mb-0 w-max" *ngIf="!readonlyOnDisable && !displayBtn">
      <div style="display:table-caption;">
        <!--save btn with condition STARTS-->
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
        *ngIf="this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName != 'condition'
               && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName == 'jsonparser'"
        pTooltip="Save" tooltipPosition="right" [disabled]="jsonParserValidatedFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName != 'condition'
                   && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName == 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="calculateValidatedFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName != 'condition'
                   && this.dataItem.nodeName == 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="subFlowValidatedFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName == 'condition'
            && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="conditionValidatedFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName == 'ForLoop' && this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName != 'condition'
            && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="forLoopValidateFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName == 'approvalworkflow' && this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'condition'
            && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="approveNodeValidateFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            *ngIf="this.dataItem.nodeName != 'approvalworkflow' && this.dataItem.nodeName != 'ForLoop' && this.dataItem.nodeName != 'condition'
            && this.dataItem.nodeName != 'SubWorkflow' && this.dataItem.nodeName != 'calculate' && this.dataItem.nodeName != 'jsonparser'"
            pTooltip="Save" tooltipPosition="right" [disabled]="validataFields()" (onClick)="saveNodedata()"></p-button>
        <!--save btn with condition ENDS-->
        <p-button icon="pi pi-question" styleClass="p-button-rounded p-button-text p-button-raised p-button-outlined" class="nodeBtnbg"
          pTooltip="Info" (onClick)="goToLink(helpurl)" tooltipPosition="right"></p-button>
        <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger" class="nodeBtn"
          pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button>
      </div> 
    </div>
    </div>
  <!--NODES OUTPUT othernodes-->
<div class="nodes-outputs" *ngIf="node.name != 'ForLoop' && node.name != 'approvalworkflow' && node.name != 'condition'">
  <div *ngFor="let output of outputs" class="output-sockets-success">
    <rete-socket *ngIf="output.name == 'Success'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
  </div>
  <div *ngFor="let output of outputs" class="output-sockets-error">
    <rete-socket *ngIf="output.name == 'Error'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
  </div>
</div>
<!--NODES OUTPUT othernodes ENDS-->
<!--FOR lOOP OUTPUTS-->
<div class="forLoopOutputs" *ngIf="node.name == 'ForLoop'">
  <div class="forloop-node-outputs" *ngFor="let output of outputs">
    <rete-socket rete-socket [io]="output" [socket]="output.socket"></rete-socket>
  </div>
</div>
<!--FOR lOOP OUTPUTS ENDS-->
<!--approvalworkflow Output node component-->
<div class="nodes-outputs" *ngIf="node.name === 'approvalworkflow'">
<div *ngFor="let output of outputs" class="output-sockets-success">
  <rete-socket *ngIf="output.name == 'Approved'"  rete-socket [io]="output" [socket]="output.socket"></rete-socket>
</div>
<div *ngFor="let output of outputs" class="output-sockets-error">
 <rete-socket *ngIf="output.name == 'On Error'"  rete-socket [io]="output" [socket]="output.socket"></rete-socket>
</div>
</div> 
<!--approvalworkflow Output node component ENDS-->
<!--condition Output node component-->
<div class="nodes-outputs" *ngIf="node.name === 'condition'">
  <div class="conditionNodeOutput">
    <rete-socket class="" rete-socket [io]="outputs[0]" [socket]="outputs[0].socket"></rete-socket>
    <rete-socket class="conditionNodeOp" rete-socket [io]="outputs[1]" [socket]="outputs[1].socket"></rete-socket>
    <rete-socket class="conditionNodeOp" rete-socket [io]="outputs[2]" [socket]="outputs[2].socket"></rete-socket>
</div>
</div>
<!--condition Output node component ENDS-->

<!--OVERLAY PANEL EXPRESSON BUILDER-->
<p-overlayPanel #expbuilder [dismissable]="true" styleClass="add-edit">
  <div style="padding-bottom: 10px;"><b>Expression Builder</b><span style="float: right;">
    <i class="pi pi-times" id="expBuildClose" style="color:transparent" (click)="expbuilder.hide()"></i>
          <!-- <i class="fa-light fa-chevron-down"></i> -->
    <i class="pi pi-list cursor" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData()" pTooltip="Open Test Data"></i>
  </span></div>
  <ng-template pTemplate="body">
    <app-expression-builder [data]="expBuildVal"
      (overlayclose)="overlayCloseExpBuilder($event)"></app-expression-builder>
  </ng-template>
</p-overlayPanel>
<!--OVERLAY PANEL EXPRESSON BUILDER ENDS-->
<p-confirmPopup></p-confirmPopup>
<div *ngIf="selected() && displayBtn" class="text-center h-4rem"><app-add-nodes ></app-add-nodes>
    <p-button *ngIf="node.name==='merge'" icon="pi pi-trash" styleClass="p-button-rounded p-button-danger" class="ml-2"
          pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button></div>
   
