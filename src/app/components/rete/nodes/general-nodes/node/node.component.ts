import { Component, ChangeDetectorRef, ChangeDetectionStrategy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
import { AngularComponentData} from 'rete-angular-render-plugin';
import { QueryBuilderConfig } from 'ngx-angular-query-builder';
import { HostListener } from "@angular/core";
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FieldMap, Field } from "../../../QueryBuilder.model";
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { UsersService } from 'src/app/services/users.service';
import { TeamsService } from 'src/app/services/team.service';
import { QueryBuildermodelComponent } from '../../query-buildermodel/query-buildermodel.component';
import { JsonataEditorComponent } from '../../jsonata-editor/jsonata-editor.component';
import { ExpressionBuilderComponent } from '../../expression-builder/expression-builder.component';
@Component({
  templateUrl: './node.component.html',
  styleUrls: ['./node.component.scss'],
  // changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [NodeService, DialogService]
})

export class MyNodeComponent extends NodeComponent implements AfterViewInit {
  @HostListener("mousemove") onClick() {}
  @ViewChild('nodePanel', { static: false }) public panelId: ElementRef;
  readonlyOnDisable:boolean=false;
  csvRadio:any;
  allusers: unknown;
  allteams: unknown;
  displayBtn: boolean = false;
  checked1: boolean = false;
  dataItem: any;
  desc: any;
  nodeDesc: boolean = false;
  DispEditIcon: boolean = false;
  selectedSystem: any = '';
  excludeparams = [];
  ref: DynamicDialogRef; expBuildVal: any;
  expBuildData: any; jsonLogicRule: any;
   helpurl = 'https://docs.unvired.com/builder/flows/';
  nodeformdata: FormGroup;
  optionalprops = [];
  fileCacherSelectValue: any;
  allWorkflows: any;
  injuctSelectValue:any;
  calcNodeVarNamePattern = "[A-Za-z0-9]+";
  sourcedataForCalculate: any;
  userTeamSelectValue:any;
  deletedData: any;
  approvalforms = [];
  approvargroups = [];
  approversFormsList = [];
  fieldsToMap = [];
  formfields: any;
  queryBuilddata:any;
  subWorkflowSelectedValue:any;  error:any='';
  query = {
    condition: "and",
    rules: [],
  };
  config: QueryBuilderConfig = {
    fields: {},
  };
  public aggregatedDataFormats = [
    {label: "JSON Array",value: "jsonarray"},
    {label: "Comma Separated String",value: "commastring",}
  ];
  select=[
    {value: 'foreground',label:'Execute as part of parent workflow'},
    {value: 'parallel',label:'Execute as a separate workflow parallelly'},
    {value: 'sequential',label:' Execute as a separate workflow in sequence'}
   ];
   expressionmenu: any[] = [
    { label: 'Expression', value: 'expression' },
    { label: 'JSON Extractor', value: 'jsonextractor' }
  ];
  userOptions: any[] = [
    { label: 'Users', value: 'users' },
    { label: 'Form Fields', value: 'formfields' }
  ];
  teamOptions: any[] = [
    { label: 'Teams', value: 'teams' },
    { label: 'Form Fields', value: 'formfields' }
  ];
  calculateNodeOpt:any=[
    {label:'String',value:'string'},
    {label:'Number',value:'number'},
    {label:'Date',value:'date'}
  ]
  datamapperOptions: any[] = [
    { label: 'Form Fields', value: 'formfield' },
    { label: 'Previous Steps', value: 'previousnode' },
    { label: 'Others', value: 'flowinput' }
  ];
  approversTypes:any[]=[
    { label: 'Team', value: 'team' },
    { label: 'Approvers List', value: 'approvers'}
  ]
  openPanelForNew:boolean;
  nodeName:any;
  nodeIcon:any;editing = false;
  constructor(
    protected override service: NodeService,protected override cdr: ChangeDetectorRef,private utilservice: UtilsService,
    public dialogService: DialogService,
    public messageService: MessageService,
    private reteservice: ReteService, private fb: FormBuilder,
    private confirmationService: ConfirmationService, 
    private usersservice: UsersService,
    private teamsservice: TeamsService,
    ) 
  { super(service, cdr);this.displayBtn = true;
    this.readonlyOnDisable = this.reteservice.getDisableWfData()}

    ngAfterViewInit(){    
      this.displayBtn = true;
          if(this.node.hasOwnProperty('new')){
            this.desc = this.node.data['name'];
            let event={
              collapsed:true
            }
            this.node.name !== 'merge' ? this.openPanelForNew=false : this.openPanelForNew=true;
            this.node.name !== 'merge' ? this.panelBtn(event,this.node) : '';
            if(this.node.name === 'merge'){
              const data = {
                  nodeId: this.node.id,
                  node: this.node,
                  nodeData: this.node.data,
                  nodeName: this.node.name,
                  formId: this.editor['workflow'].formId,
                  editor: this.editor
                }
                this.dataItem = data;  
            }
            this.focusIn() 
           }else{
            this.desc = this.node.data['description'];
            this.openPanelForNew=true;
           }
           if(this.node.name == 'email'){
             this.getAllUsers();
             this.getAllTeams();
           }
           this.nodeName= this.node.name;           
           switch(this.node.name){
            case "filestore":
              this.nodeIcon = 'filestore';
              break;
              case "ForLoop":
                this.nodeIcon = 'loop';
                break;
                case "SubWorkflow":
                  this.nodeIcon = 'flow';
                  break;  
                  case "response":
                    this.nodeIcon = 'response';
                    break;
                    case "email":
                      this.nodeIcon = 'email';
                      break;
                      case "calculate":
                        this.nodeIcon = 'calculate';
                        break;
                        case "csvparser":
                          this.nodeIcon = 'csv';
                          break;
                          case "jsonparser":
                            this.nodeIcon = 'jsonFile';
                            break;
                            case "approvalworkflow":
                              this.nodeIcon = 'approval';
                              break;
                              case "condition":
                                this.nodeIcon = 'if';
                                break;
                                case "merge":
                                this.nodeIcon = 'combine';
                                break; 
           }
       }
       excludedNodes = ['ForLoop', 'jsonparser','condition','calculate','approvalworkflow','SubWorkflow','merge'];

       isNodeNameExcluded(nodeName: string): boolean {
         return !this.excludedNodes.includes(nodeName);
       }
  //open panel 
  async panelBtn(event: any, node: any) {
    if (event.collapsed == 'true' || event.collapsed == true) {
      this.displayBtn = false;
      if (node.data.inputs == undefined) {

      } else {
        let newInput = node.data.inputs;
        node.data.input = newInput;
        delete node.data.inputs;
      }
      const data = {
        nodeId: node.id,
        node: this.node,
        nodeData: node.data,
        nodeName: node.name,
        formId: this.editor['workflow'].formId,
        editor: this.editor
      }
      this.dataItem = data;      
      this.setdefaultvalues();
      this.reteservice.pushNodeId(this.dataItem.nodeId);
    } else {
      this.formDirtyValidate(event)
      if (node.data.input) {
        let newInput = node.data.input;
        node.data.inputs = newInput;
        delete node.data.input;
      }
      this.displayBtn = true;
      this.nodeDesc = false;
      this.DispEditIcon = false;
      this.selectedSystem = '';
      this.excludeparams = [];
      this.reteservice.popNodeId(this.dataItem.nodeId);
      this.enableDisable=false;
    }
  }

  formDirtyValidate(event:any){
   let valid = this.nodeformdata.get('input').dirty
   if(valid){
   let a = document.getElementById('nodePanel')    
   }
  }
  confirmPanelClose(event: Event) {
    this.confirmationService.confirm({
      target: event.target,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
       
      },
      reject: () => {
      }
    });
  }
  goToLink(url: string) {
    window.open(url, "_blank");
  }

  focusIn() {
    this.editing = true;
    if (this.nodeDesc == false) {
      this.nodeDesc = true;
      window.setTimeout(function () {
        document.getElementById('inputBox').focus();
      }, 10);
    } 
  }
  nodeNameChange:boolean=false;
duplicate:boolean=false
  FocusOut(){
    this.editing = false;
    let oldName = this.node.data['name'];
    if(this.nodeDesc != false){
      this.node.data['description'] = this.desc.replace(/\s+$/, "");
       this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      // if(this.node.data['name'] == 'untitled' || this.duplicate){
      //   this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      //   }
      this.nodeDesc = false;
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      }  
    }else{
      this.nodeDesc = true;
    }
    if(this.node.data['name']){
    }else{
      this.desc='untitled1';
      this.node.data['description'] = this.desc;
      this.node.data['name'] = this.desc;
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      }  
    }
    if(this.node.data['name'] != 'untitled'){
      if((oldName != this.node.data['name']) && (oldName != 'untitled')){
      this.reteservice.flowTechName.next("change");
       }
    }
  }

  checkIfDuplicateExists(arr) {
    return new Set(arr).size !== arr.length
}
 async patchFormData() {
    let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
    this.helpurl = this.helpurl + '#' + this.dataItem.nodeData.wfName;
    const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
    if (index > -1) {
      wfnodenames.splice(index, 1);
    }
    this.nodeformdata = this.fb.group({
      name: [this.dataItem.nodeData.name, []],
      description: [this.dataItem.nodeData.description],
      input: this.fb.array([])
    });

  }
  getkeyvaluecontrol(field) {
    return field.controls.valuepairs.controls;
  }
  get input() {
    return this.nodeformdata.get('input') as FormArray;
  }
  get mapfieldtype() {
    return this.input.controls[0]?.get("mapfieldtype");
  }
  get entityType() {
    return this.nodeformdata.get('entityType');
  }
  get sourcedata() {
    return this.input.controls[0]?.get("sourcedata");
  }
  get expressions() {
    return this.nodeformdata.get("expressions") as FormArray;
  }
  removeKeyValue(index: number, ind: number) {
    const control = this.input.controls[index].get('valuepairs') as FormArray;
    control.removeAt(ind);
  }
  async setdefaultvalues() {
    switch (this.dataItem.nodeName) {
        case "filestore":
        this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          for (let key in this.optionalprops) {
            this.dataItem.nodeData.input.push(this.optionalprops[key])
          }
          this.dataItem.nodeData.optionalprops = [];
          this.dataItem.nodeData.input.forEach((inputparam, ind) => {
            this.excludeparams.push(inputparam.inputName);
            this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }        
        break;
        case "ForLoop":
        this.patchFormData();        
        if (this.dataItem.nodeData.IterableData) {
          this.nodeformdata.addControl("IterableData",this.fb.control(this.dataItem.nodeData.IterableData));
        } else {
          this.nodeformdata.addControl("IterableData", this.fb.control(""));
        }
        if (this.dataItem.nodeData.aggregatedDataFormat) {
          this.nodeformdata.addControl("aggregatedDataFormat",this.fb.control(this.dataItem.nodeData.aggregatedDataFormat));
        } else {
          this.nodeformdata.addControl("aggregatedDataFormat",this.fb.control("jsonarray"));
       }
        break;
        case "SubWorkflow":
        this.patchFormData();
        this.getallworkflows();        
        this.nodeformdata.addControl("wfNamespace",this.fb.control(`${localStorage.getItem("domain").toLowerCase()}.flow`,Validators.required));        
        if (this.dataItem.nodeData.wfName && this.dataItem.nodeData.wfOptions) {
          this.nodeformdata.addControl("subwfName",this.fb.control(this.dataItem.nodeData.wfName));
          this.nodeformdata.addControl("wfOptions",this.fb.control(this.dataItem.nodeData.wfOptions));
          this.subWorkflowSelectedValue=this.dataItem.nodeData.wfName;
          if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
            this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
            this.optionalprops = this.dataItem.nodeData.optionalprops;
            for (let key in this.optionalprops) {
              this.dataItem.nodeData.input.push(this.optionalprops[key])
            }
            this.dataItem.nodeData.optionalprops = [];
            this.dataItem.nodeData.input.forEach((inputparam, ind) => {
              // this.excludeparams.push(inputparam.inputName);
              this.addInputparamsInNodedata(inputparam);
            });
          } else {
           this.subWorkflowSelectedValue=this.dataItem.nodeData.wfName;
          this.getnodeparams();
        }
      }else {
        this.nodeformdata.addControl("subwfName",this.fb.control("", Validators.required));
        this.nodeformdata.addControl("wfOptions",this.fb.control(this.select[0].value,Validators.required));        
        }
        break;
        case "response":
          this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          this.dataItem.nodeData.input.forEach((inputparam,ind) => {
            this.excludeparams.push(inputparam.inputName);
            if (inputparam.type === 'keyvalue') {
              if (inputparam.values == "") {
                inputparam.valuepairs = [{ key: '', value: '' }]
                // this.addkeyvalueType(inputparam);
              }
              this.addkeyvalueType(inputparam);
              inputparam.valuepairs.forEach(pair => this.addkeyValuepair(ind, pair.key, pair.value));
            } else {
              this.addInputparamsInNodedata(inputparam);
            }
          //  this.addInputparamsInNodedata(inputparam);
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
        case "email":
          await this.patchFormData();
        if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
          this.optionalprops = this.dataItem.nodeData.optionalprops;
          this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
          this.dataItem.nodeData.input.forEach((inputparam) => {
            this.addInputparamsInNodedata(inputparam);
            this.excludeparams.push(inputparam.inputName);
            
          });
          await this.hideOptionalFields(this.enableDisable)
        } else {
          this.getnodeparams();
        }
        break;
        case "calculate":
         await  this.patchFormData();
        if (this.dataItem.nodeData.expressions) {
          this.nodeformdata.addControl("expressions", this.fb.array([]));
          if (this.dataItem.nodeData.expressions.length > 0) {
            this.dataItem.nodeData.expressions.forEach((pair) => {
              if (pair.category === "expression") {
                this.addcalculatenodeRow(pair.key,pair.type, pair.value,pair.category);
              } else {
                this.addcalculatenodeRow(pair.key, pair.type, pair.sourcedata,pair.category,pair.json,pair.mapexpression);
              }
            });
          }
        } else {
          this.nodeformdata.addControl("expressions", this.fb.array([]));
        }
        break;
        case "csvparser":
          this.patchFormData();
          if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
            this.optionalprops = this.dataItem.nodeData.optionalprops;
            this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
            this.dataItem.nodeData.input.forEach((inputparam) => {
              this.excludeparams.push(inputparam.inputName);
              this.addInputparamsInNodedata(inputparam);
            });
            await this.hideOptionalFields(this.enableDisable)
          } else {
            this.getnodeparams();
          }
        break;
        case "jsonparser":
          this.patchFormData();
          if (this.dataItem.nodeData.input && this.dataItem.nodeData.input.length > 0) {
            this.input.push(this.fb.group(this.dataItem.nodeData.input[0]));            
            if (this.dataItem.nodeData.input[0].mapfieldtype === "previousnode") {
              if (this.dataItem.node.getConnections().length > 0) {
                const nodesfnd = await this.utilservice.getpreviousnodes(this.dataItem.nodeId,this.dataItem.nodeName,[],this.dataItem.editor.toJSON());
                this.fieldsToMap = this.utilservice.prepareFromfieldsFromPreviousNodes(nodesfnd);
              }
            } else if (
              this.dataItem.nodeData.input[0].mapfieldtype === "formfield"
            ) {
              this.getFormfieldAsTree();
            } else if (
              this.dataItem.nodeData.input[0].mapfieldtype === "flowinput"
            ) {
              this.fieldsToMap = [{label: "Flow Input",value: "${flowinput}",}];
            }
          } else {
            this.input.push(this.fb.group({mapfieldtype: [""],sourcedata: [""],json: ["{}"],mapexpression: [""]}));            
          }
        break;
        case "approvalworkflow":
        this.patchFormData();
        this.getapprovalnestedforms();
        this.getapprovargroups();
        this.getApproversFormsList();        
        if (this.dataItem.nodeData.input !== undefined) {
          this.nodeformdata.addControl("approval_form",this.fb.control(this.dataItem.nodeData.approval_form));
          if (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0) {
            this.dataItem.nodeData.input.forEach((inputparam) => {
            this.addapprovallevel(inputparam.type,inputparam.approver_group,inputparam.number_of_approvers);
            });
          }
        } else {
          this.nodeformdata.addControl("approval_form",this.fb.control("", Validators.required));
        }
        break;
        case "condition":
           this.patchFormData();
          //this.openQueryBuilder(this.dataItem);
          if (this.dataItem.nodeData.query ) {
            this.query = this.dataItem.nodeData.query;
            this.query.rules.forEach((element) => {
              this.ruleOperationChanged(element);
            });
            if(this.query.rules.length > 0){
              this.showTick=true;
            }else{
                this.showTick=false;
            }
            
            this.nodeformdata.addControl("query", this.fb.control(this.query));
          } else {
            this.nodeformdata.addControl("query", this.fb.control(""));
          }
        break;
        default:
        break;
    }  
    if(this.reteservice.getDisableWfData()){
      this.readonlyOnDisable = this.reteservice.getDisableWfData()
      this.nodeformdata.disable();
    }  
  }


  async addcalculatenodeRow(key: any,type: string,value: string,category: string,json?: string,mapexpression?: string) {
    this.nodeformdata.markAsDirty();
    if (category === "expression") {
      this.expressions.push(this.fb.group({
          key: [key,[Validators.required,Validators.pattern(this.calcNodeVarNamePattern), ],],
          type: [type, Validators.required],
          value: [value, Validators.required],
          category: [category, Validators.required],
        })
      );      
    } else {
      this.sourcedataForCalculate = await this.prepareSourcedataForMapExpression();
      this.expressions.push(this.fb.group({
        key: [key,[Validators.required,Validators.pattern(this.calcNodeVarNamePattern),],],
          type: [type, Validators.required],
          sourcedata: [value],
          category: [category, Validators.required],
          json: [json],
          mapexpression: [mapexpression],
        })
      );      
    }
  }

 async getMapExp(){
    this.sourcedataForCalculate = await this.prepareSourcedataForMapExpression();
  }

  async prepareSourcedataForMapExpression() {
    let sourcedata = [];
    await this.formFieldsForMappedExpr().then((res) => {
      const response = res;
      if (response.error === "") {
        if (response.status === "Success") {
          sourcedata.push(response.formFields[0]);
        }
      } else {
        // this.errmsg = response.error;
      }
    });

    if (this.dataItem.node.getConnections().length > 0) {
     const nodesfnd = this.utilservice.getpreviousnodes(
          this.dataItem.nodeId,
          this.dataItem.nodeName,
          [],
          this.dataItem.editor.toJSON()
        );
      let formfields = [];
      let allresultfield=[];      
      nodesfnd.forEach((element) => {      
        if (element.name !== "calculate") {
          if (element.name !== 'ForLoop') {
            if(element.name === 'ExecuteJavaScript' || element.name === 'ExecutePythonScript') {
              let errorfield;
              let resultfield;
              (element.data.outputparams).forEach(outputresult => {
                if (outputresult === 'error') {
                  errorfield = {
                    label: `${element.data.name} Error`,
                    value: "${" + `${element.data.name}_error` + "}",
                  };
                } else {                                
                  resultfield = {
                    label: `${outputresult}`,
                    value: "${" + `${element.data.name}_${outputresult}` + "}",
                  };
                  allresultfield.push(resultfield)   
                }                     
              });
              if(errorfield != undefined || resultfield != undefined){
                for(let key in allresultfield){                  
                  formfields.push(allresultfield[key], errorfield);
                }
              }
              formfields  = formfields.reduce((acc, current) => {
                const x = acc.find(item => item.label  === current.label);
                if (!x) {
                  return acc.concat([current]);
                } else {
                  return acc;
                }
              }, []);
            }else{
              let errorfield = {
                label: `${element.data.name} Error`,
                value: "${" + `${element.data.name}_error` + "}",
              };
             let  resultfield = {
                label: `${element.data.name} Result`,
                value: "${" + `${element.data.name}_result` + "}",
              };
                
             formfields.push(resultfield, errorfield);             
        }
        }
        } else {
          if (element.data.expressions && element.data.expressions.length > 0) {
            element.data.expressions.forEach((expression) => {
              formfields.push({label: `${expression.key}`,value: "${" + `${expression.key}` + "}"});
              const errorfield = {
                label: `${element.data.name} Error`,
                value: "${" + `${element.data.name}_error` + "}",
              };
              formfields.push(errorfield);
            });
        }
        
      }
      
      });
      const previousnodesobj = {
        label: "Previous Nodes",
        items: formfields,
      };
      const customnodesobj = {
        label: "Others",
        items: [
          {
            label: "Flow input",
            value: "${flowinput}",
          },
        ],
      };
      sourcedata.push(previousnodesobj);
      sourcedata.push(customnodesobj);
    }
    return sourcedata;
  }

  removecalculatenodeRow(index) {
    this.expressions.removeAt(index);
  }

  async formFieldsForMappedExpr() {
    const formFields = await this.reteservice
      .getFormsFielsForExpressionBuilder(this.dataItem.nodeName, this.dataItem.formId)
      .toPromise();
    return formFields;
  }

  ruleOperationChanged(rule: any) {
    if (rule.operator === "between" || rule.operator === "not between") {
      if (!rule.value) {
        rule.value = [];
      }
    } else {
      if (rule.value != 0 && !rule.value) {
        rule.value = null;
      }
    }
  }
  addkeyvalueType(inputparam: any) {
    this.input.push(this.fb.group({
      inputName: inputparam.inputName,
      description: inputparam.description,
      type: inputparam.type,
      show: false,
      valuepairs: this.fb.array([])
    }));
  }
  addkeyValuepair(ind: number, key: string, value: string) {
    const control = this.input.controls[ind].get('valuepairs') as FormArray;
    control.push(this.fb.group({
      key: [key, Validators.required],
      value: [value, Validators.required]
    }));
  }
  getnodeparams() {
    this.reteservice.getformsfielsfor$values(this.dataItem.nodeData.wfName, this.dataItem.nodeData.wfNamespace, this.dataItem.formId, this.excludeparams.toString()).subscribe(async(res) => {
      const response = res;
      if (response.error === "") {
        if (response.status === "Success") {
          if (response.prerequisites != "") {
            this.jsonLogicRule = response.prerequisites;
          } else {
            this.jsonLogicRule = [];
          }
          response.inputs.forEach(async (inputparam, ind) => {
            //this.addInputparamsInNodedata(inputparam);
            if (inputparam.type === 'keyvalue') {
              inputparam.valuepairs = [{ key: '', value: '' }]
              this.addkeyvalueType(inputparam);

            }
            else {
            await this.addInputparamsInNodedata(inputparam);
            }
          })
        }
        await this.hideOptionalFields(this.enableDisable)
      }else{
        if(this.nodeName != 'SubWorkflow'){
        this.error=response.error 
        this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });
        }
      }
    });
  }

  getallworkflows() {
    this.reteservice.getallworkflows(this.dataItem.editor["workflow"].wfId).subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.allWorkflows = response.workflows;            
          }
        } else {
          //this.errmsg = response.error;
        }
      });
  }

  getapprovalnestedforms() {
    this.reteservice.getNestedFormsForApproval(this.dataItem.editor["workflow"].wfId,this.dataItem.formId).subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.approvalforms = response.forms;
          }
        } else {
        //  this.errmsg = response.error;
        }
      });
  }

  getapprovargroups() {
    this.reteservice.getApprovalGroups(this.dataItem.editor["workflow"].wfId, this.dataItem.formId).subscribe((res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.approvargroups = response.teams;
          }
        } else {
         // this.errmsg = response.error;
        }
      });
  }
  getApproversFormsList(){
    this.reteservice.getApprovalFormsList(this.dataItem.editor["workflow"].wfId).subscribe((res) => {
      const response = res;
      if (response.error === "") {
        if (response.status === "Success") {
          this.approversFormsList = response.formFields;
        }
      } else {
       // this.errmsg = response.error;
      }
    });
    
    
  }
  addapprovallevel(type:string,approver_group: string, number_of_approvers: number) {
    this.input.push(
      this.fb.group({
        type: [type, Validators.required],
        approver_group: [approver_group, Validators.required],
        number_of_approvers: [number_of_approvers, Validators.required],
      })
    );    
  }

  getparamsforsubwf(wfname: string) {
    this.subWorkflowSelectedValue = wfname
    this.dataItem.nodeData.wfName = wfname;
    this.dataItem.nodeData.wfNamespace = `${localStorage.getItem("domain").toLowerCase()}.flow`;
    this.input.clear();
    this.optionalprops = [];
    this.error='';
    this.getnodeparams();
    
  }

  async getAllUsers() {
    this.usersservice.getusers("", "asc", 0, 1000, "", null, null, null).subscribe((res) => {
        const resp = res;
        if (resp.totalUsers !== 0) {
          this.allusers = resp.formUsers;
        } else {
         // this.errmsg = "No users found!.";
        }
      });
  }

  async getAllTeams() {
    this.teamsservice.getteams("", "", "asc", 0, 100, "", null).subscribe((res) => {
        const resp = res;
        if (resp.totalTeams !== 0) {
          this.allteams = resp.teams;
        } else {
        //  this.errmsg = "No Teams found!.";
        }
      });
  }

  getFormfieldAsTree() {
    this.reteservice.getFormsFielsForExpressionBuilder(this.dataItem.nodeName, this.dataItem.formId).subscribe((res) => {
        const response =res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.fieldsToMap = response.formFields[0].items;
          }
        } else {
          //this.errmsg = response.error;
        }
      });
  }

  async onMapFieldTypePick(pickedType: string) {
    if (pickedType === "previousnode") {
      this.sourcedata?.setValue(null);
      if (this.dataItem.node.getConnections().length > 0) {
       await this.getPreviousNodesDetails();
      }
    } else if (pickedType === "formfield") {
      this.sourcedata?.setValue(null);
      this.getFormfieldAsTree();
    } else if (pickedType === "flowinput") {
      this.sourcedata?.setValue(null);
      this.fieldsToMap = [
        {
          label: "Flow Input",
          value: "${flowinput}",
        },
      ];
    }
  }

 async getPreviousNodesDetails(){
    const nodesfnd = await this.utilservice.getpreviousnodes(this.dataItem.nodeId,this.dataItem.nodeName,[],this.dataItem.editor.toJSON());
    this.fieldsToMap = this.utilservice.prepareFromfieldsFromPreviousNodes(nodesfnd);
  }

  async addInputparamsInNodedata(inputParameter: any) { 
    if(inputParameter.required == 'Mandatory'){
      inputParameter.show = true;
    }else{
      inputParameter.show = false;
    }  
    inputParameter.radiolistvalue = [];
    inputParameter.listvalue = [];
    if ((inputParameter.type === "list" || inputParameter.type === "radio") && inputParameter.listvalues && Array.isArray(inputParameter.listvalues) && !Array.isArray(inputParameter.listvalues[0])) { inputParameter.listvalues = [inputParameter.listvalues]; }
    if ((inputParameter.type === "user" || inputParameter.type === "team") && inputParameter.values && Array.isArray(inputParameter.values) && !Array.isArray(inputParameter.values[0])) {
         inputParameter.values = [inputParameter.values];
      }
    if (inputParameter.type === "list" && inputParameter.listvalues && inputParameter.inputName !== 'system') {
      inputParameter.listvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.listvalue[0].push({ "key": data, "value": data.toLowerCase().replace(/\s+/g, '') })
        });
        if(inputParameter.values == undefined){
          inputParameter.values = "";
        }else{
        inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
        }
      }
      if(this.dataItem.nodeName === "filestore"){
        if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
          setTimeout(() => {
            this.fileCacherSelectValue = inputParameter.values;
            }, 200);
        }else{
           setTimeout(async () => {
           await this.fileCacherSelect(inputParameter.values);
            }, 200);    
        }
      }
    }
    if (inputParameter.type === "radio" && inputParameter.listvalues) { 
      inputParameter.radiolistvalue.push([]);
      if (inputParameter.listvalues[0] !== undefined) {
        inputParameter.listvalues[0].forEach(data => {
          inputParameter.radiolistvalue[0].push({ "key": data, "value": data.toLowerCase().replace(/\s+/g, '') })
        });
      }
      inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
      if(inputParameter.inputName === "resulttype" && this.dataItem.nodeName === "response"){
            if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
            setTimeout(() => {
              this.injuctSelectValue = inputParameter.values;
            }, 200); 
            }else{
              setTimeout(() => {
                this.getRadioValue(inputParameter.values);
            }, 200); 
            }
          }
    }
    if(inputParameter.inputName === "recipienttype"){  
          if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
            this.userTeamSelectValue=inputParameter.values;
          }else{
             setTimeout(async() => {
          await this.SendEmailRadio(inputParameter.values);
              }, 200);    
          }
      }
      if(inputParameter.inputName === "inputformat" && this.dataItem.nodeName === "csvparser"){
          if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
            this.csvRadio=inputParameter.values;
          }else{
             setTimeout(async () => {
          await this.csvMapperRadio(inputParameter.values);
              }, 200);    
          }
      }
    this.input.push(this.fb.group(inputParameter));    
  }

 async fileCacherSelect(event: any) {
    if (event.toLowerCase() == "delete" || event.toLowerCase() == "download") {
      let index1;
      let index2;
      this.fileCacherSelectValue = event.toLowerCase();
      this.input.controls.forEach((key,ind )=> {
        if (key.value.inputName == "attachmentid") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
          index1=ind;
        }
        if (key.value.inputName == "externalreference") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
          index2=ind;
        }
        if (key.value.inputName == "data") {          
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });          
        }
        // if (key.value.inputName == "mimetype") {
        //   key.patchValue({ required: "Optional" });
        //   key.patchValue({ values: "" });
        // }
        if (key.value.inputName == "filename") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        }
      });
      const element4 = this.input.controls.splice(index1, 1)[0];
      this.input.controls.splice(2, 0, element4);
      const element6 = this.input.controls.splice(index2, 1)[0];
      this.input.controls.splice(3, 0, element6);
    } else if (event.toLowerCase() == "upload") {
      let index1;
      let index2;
      this.fileCacherSelectValue = event.toLowerCase();
      this.input.controls.forEach((key,ind )=> {        
        if (key.value.inputName == "data") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
          index2=ind;
        }
        if (key.value.inputName == "filename") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
          index1=ind;
        }
        if (key.value.inputName == "attachmentid") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        }
        if (key.value.inputName == "externalreference") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        }
        // if (key.value.inputName == "mimetype") {
        //   key.patchValue({ required: "Optional" });
        //   key.patchValue({ values: "" });
        // }
      });
      const element4 = this.input.controls.splice(index1, 1)[0];
      this.input.controls.splice(2, 0, element4); 
      const element5 = this.input.controls.splice(index2, 1)[0];
      this.input.controls.splice(3, 0, element5); 
    } else {
      this.fileCacherSelectValue = "";
    }    
  }

  //method not used
  fileCacherDownloadDelete(event:any,type:any){
    if(event.length == 0 && (this.fileCacherSelectValue == "delete" || this.fileCacherSelectValue == "download")){
      this.input.controls[6].patchValue({ required: "Mandatory" },{ values: "" });
      this.input.controls[3].patchValue({ required: "Mandatory" },{ values: "" });
    }else{
      if(this.fileCacherSelectValue == "attachmentid"){
        this.input.controls[6].patchValue({ required: "Optional" },{values: "" });        
      }else{
        this.input.controls[3].patchValue({ required: "Optional" },{values: "" });
      }
    }
  
  }

  getRadioValue(event:any){  
    if(event.toLowerCase() === "success"){
      this.injuctSelectValue = event.toLowerCase();
      this.input.controls.forEach(key => {
        if (key.value.inputName === "injectresult") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
        }
        if (key.value.inputName === "injecterror") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        }
      });
    }else if(event.toLowerCase() === "failure"){
      this.injuctSelectValue = event.toLowerCase();
      this.input.controls.forEach(key => {
        if (key.value.inputName === "injecterror") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
        }
        if (key.value.inputName === "injectresult") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        }
      });
    }else{
      this.injuctSelectValue = "";
    }
  }

  async SendEmailRadio(event:any){
    this.userTeamSelectValue="";
    if(event.toLowerCase() === "teams"){
      this.userTeamSelectValue="teams";
     this.input.controls.forEach(key=>{
       if(key.value.inputName === "users"){
         key.patchValue({required: "Optional"});
         key.patchValue({defaultSelect: "users"});
         key.patchValue({values : ""});
       }
       if(key.value.inputName === "emails"){
        key.patchValue({required: "Optional"});
        key.patchValue({values : ""});
      }
       if(key.value.inputName === "teams"){
        key.patchValue({required: "Mandatory"});
        key.patchValue({defaultSelect: "teams"});
        key.patchValue({values : ""});
        }
        });
    }else if(event.toLowerCase() === "users"){
      this.userTeamSelectValue="users";
      this.input.controls.forEach(key=>{
        if(key.value.inputName === "users"){
          key.patchValue({required: "Mandatory"});
          key.patchValue({defaultSelect: "users"});
          key.patchValue({values : ""});
        }
        if(key.value.inputName === "emails"){
         key.patchValue({required: "Optional"});
         key.patchValue({values : ""});
       }
        if(key.value.inputName === "teams"){
         key.patchValue({required: "Optional"});
         key.patchValue({defaultSelect: "teams"});
         key.patchValue({values : ""});
         }
         });
    }else if(event.toLowerCase() === "emails"){
      this.userTeamSelectValue="emails";
      this.input.controls.forEach(key=>{
        if(key.value.inputName === "users"){
          key.patchValue({required: "Optional"});
          key.patchValue({defaultSelect: "users"});
          key.patchValue({values : ""});
        }
        if(key.value.inputName === "emails"){
         key.patchValue({required: "Mandatory"});
         key.patchValue({values : ""});
       }
        if(key.value.inputName === "teams"){
         key.patchValue({required: "Optional"});
         key.patchValue({defaultSelect: "teams"});
         key.patchValue({values : ""});
         }
         });
    }else{
      this.userTeamSelectValue="";
      this.input.controls.forEach(key=>{
        if(key.value.inputName === "users"){
          key.patchValue({required: "Optional"});
          key.patchValue({defaultSelect: "users"});
          key.patchValue({values : ""});
        }
        if(key.value.inputName === "emails"){
         key.patchValue({required: "Optional"});
         key.patchValue({values : ""});
       }
        if(key.value.inputName === "teams"){
         key.patchValue({required: "Optional"});
         key.patchValue({defaultSelect: "teams"});
         key.patchValue({values : ""});
         }
         });
    }
  }

 async csvMapperRadio(event:any){    
    this.csvRadio = "";
    if(event.toLowerCase() === "csv"){
      this.csvRadio = "csv";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ required: "Mandatory" });
          key.patchValue({ values: "" });
          //key.patchValue({show:true})
        }
        if (key.value.inputName === "data") {
          key.patchValue({ required: "Mandatory" });
        //  key.patchValue({show:true})
        }
      });
    }else if(event.toLowerCase() === "json"){
      this.csvRadio = "json";
      this.input.controls.forEach(key => {
        if (key.value.inputName === "data") {
          key.patchValue({ required: "Mandatory" });
          //key.patchValue({show:true})
        }
        if (key.value.inputName === "attachmentid") {
          key.patchValue({ required: "Optional" });
          key.patchValue({ values: "" });
        //  key.patchValue({show:false})
        }
      });
    }else{
      this.csvRadio = "";
    }
     

  }

  openJsonata(json:any,mapexpression:any,nodetype:any,index?: number) {
    this.ref = this.dialogService.open(JsonataEditorComponent, {
      header: 'JSON Parser Expression Builder',
      width: '60%',
      contentStyle: { overflow: 'auto', padding: '0px 5px 0px 5px',"z-index":999 },
      maximizable: false,
      modal:true,
      draggable:true,
      data: {
        json: json,
        jsonata: mapexpression,
        nodeinfo: this.node,
        flowId: this.reteservice.getFlow().flowId,
        formId: this.dataItem.formId,
        dataItem:this.dataItem
      },
    });
    this.ref.onClose.subscribe((res) => {
      if(res == undefined){
        // this.reteservice.getRunflowToDialogClose.next(true)
       }else{
      if (nodetype === "datamapper") {
        this.input.at(0).get('json').setValue(res.json);
        this.input.at(0).get('mapexpression').setValue(res.jsonata);
      } else {
        this.expressions?.at(index).get("json").setValue(res.json);
        this.expressions?.at(index).get("mapexpression").setValue(res.jsonata); 
      }
    }
    });
  }
  showTick:boolean=false;
  openQueryBuilder(nodeData:any,formValue:any){
    let oldData = formValue.value.query;
    let FormData={
      nodeData:nodeData,
      formData:formValue.value.query,
      masterdata:false,
    }
    this.ref = this.dialogService.open(QueryBuildermodelComponent, {
      header: 'Query Builder',
      width:'65%',
      contentStyle: { overflow: 'auto', padding: '5px',"z-index":999},
      style:{"min-width":"50%","max-height":"75%"},
      maximizable: false,
      modal:true,
      draggable:true,
      data: FormData
    });
    this.ref.onClose.subscribe((res:any) => {
      if(res == undefined){
        // this.reteservice.getRunflowToDialogClose.next(true)
       }else{
      if(res.save == true){
        if(res.qData.query.rules.length > 0){
          this.showTick=true;
        }else{
            this.showTick=false;
        }
         this.nodeformdata.get('query').patchValue(res.qData.query)
      }else{
         this.nodeformdata.get('query').patchValue(FormData.formData)
      }
     // this.nodeformdata.get('query').patchValue(res.query)
    }
    });
    
    
  }

  importformfields() {
    this.reteservice.importformfields(this.dataItem.formId).subscribe(async (res) => {
        const response = res;
        if (response.error === "") {
          if (response.status === "Success") {
            this.formfields = response.formFields;
            if(this.dataItem.nodeName === 'condition') {
              if(this.dataItem.node.getConnections().length > 0){
                let formfieldsFromPreviousnodes;
                const nodesfnd = await this.utilservice.getpreviousnodes(this.dataItem.nodeId, this.dataItem.nodeName, [], this.dataItem.editor.toJSON());
                if(nodesfnd){
                 formfieldsFromPreviousnodes = this.utilservice.prepareFromfieldsFromPreviousNodes(nodesfnd);
                }
                if(formfieldsFromPreviousnodes && Array.isArray(formfieldsFromPreviousnodes)){
                formfieldsFromPreviousnodes.forEach(formfield => {
                  this.formfields.push(formfield);
                })
              }
              }
            this.config = {
              fields: this.setExpressionParam(this.formfields)
            };
          }
          }
        } else {
          // this.errmsg =
          //   "Form does not have any fields to setup conditional queries.";
        }
      });
  }

  setExpressionParam(arrayexample): FieldMap {
    const arrayToObject = (array) =>
      array.reduce((obj, item) => {
        obj[item.key] = item;
        return obj;
      }, {});
    const fieldMap: FieldMap = arrayToObject(arrayexample);
    return fieldMap;
  }

  //close expBuild overlay panel
 overlayCloseExpBuilder(data:any){
  let val=data.expBuildValue;
    if (this.expBuildData.callerformfieldtype === 'keyvalue') {
      const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
       control.at(this.expBuildData.keyvalindex).get('value').setValue(val);      
    } else if(this.expBuildData.nodeName == 'calculate'){
        this.expressions.at(this.expBuildData.index).get('value').setValue(val);
    }else if(this.expBuildData.nodeName == 'ForLoop'){
         this.nodeformdata.controls['IterableData'].setValue(val)
    }else{
        this.input.at(this.expBuildData.index).get('values').setValue(val);
    }
    document.getElementById('expBuildClose').click();
}

  //open overlay panel Expbuild system and send data
  openExprBuilder(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number) {
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    if (this.dataItem.nodeName === "calculate") {
      this.dataItem["addedExpressions"] = this.expressions.value;
      this.dataItem["currentExpressionKey"] = keyvalindex;
      this.dataItem["index"] = index;
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }
  }
  //remove node 
  removenode(node: any) {
    this.editor.removeNode(node);
  }
  removeapprovallevel(index) {
    this.input.removeAt(index);
  }
  //popup node delete confirm
  confirmNodeDelete(event: Event, node: any) {
    this.confirmationService.confirm({
      target: event.target,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.editor.removeNode(node);
        this.deletedData = this.reteservice.getDeleteNodedata();
        this.reteservice.popNodeId(node.id);
      },
      reject: () => {
      }
    });
  }
//validation 
  validataFields() {
    let validate: any;
    let totalMandateFields: number = 0;
    let mandateValue: number = 0;
    this.input.value.forEach((val) => {
      if (val.required === "Mandatory") {
        totalMandateFields = totalMandateFields + 1;
        if (val.values == "" || val.values == null || val.values == 'null') {
          // mandateValue = mandateValue - 1;
        } else {
          mandateValue = mandateValue + 1;
        }
      }
    });
    if (totalMandateFields == mandateValue && !this.duplicate) {
      validate = false;
    } else {
      validate = true;
    }
    return validate;
  }
  approveNodeValidateFields(){    
    let validate: any;
   let approver_group :number = 0;
   let number_of_approvers :number = 0;
   let type :number = 0;
   let totalMandateFields: number = 0;
    this.input.value.forEach((val) => {
      totalMandateFields = totalMandateFields + 1;
          if(val.approver_group == 1){
              //approver_group = true;
            }else {
              approver_group = approver_group +1 ;
            }            
        if(val.number_of_approvers == null){
          //number_of_approvers = true;
          }else {
            number_of_approvers = number_of_approvers+1 ;
          }
          if(val.type == ""){
           // type = true;
          }else {
            type = type+1;
          }
    });
    if (((approver_group + number_of_approvers + type) == (totalMandateFields * 3)) && this.nodeformdata.value.approval_form != '' && !this.duplicate && this.input.length > 0) {
      validate = false;
    } else {
      validate = true;
    }            
    return validate;
  }
  forLoopValidateFields(){
    let validate: any;
    if(this.nodeformdata.value.IterableData != '' && !this.duplicate){
      validate = false;
    }else{
      validate = true;
    }    
    return validate;
  }
  conditionValidatedFields(){
    let validate: any;
    if(this.showTick && !this.duplicate){
      validate = false;
    }else{
      validate = true;
    }    
    return validate;
  }
  subFlowValidatedFields(){
    let validate: any;
    if(this.nodeformdata.value.subwfName != '' && this.nodeformdata.value.wfOptions != '' && !this.duplicate){
      validate = false;
    }else{
      validate = true;
    } 
   return validate;
  }
  calculateValidatedFields(){
    let validate: any;
    let totalexpressionslen :number = 0;
    let expression:number = 0;
    let jsonata:number = 0;
    if(this.nodeformdata.value.expressions?.length > 0){
      totalexpressionslen = this.nodeformdata.value.expressions.length;
      for (let ele of this.nodeformdata.value.expressions) {
         if(ele.category == 'expression'){
            if(ele.key != '' && ele.type != '' && ele.value != ''){
              expression = expression +1 ;
            }else{
             // validate = true;
            }
         } 
         if(ele.category == 'jsonata'){
          if(ele.key != '' && ele.type != '' && ele.sourcedata != '' && ele.mapexpression != ''){
            jsonata = jsonata +1 ;
          }else{
          // validate = true;
          }
       } 
      }
    }else{
     // validate = true;
    }
    if(totalexpressionslen == (expression + jsonata) && totalexpressionslen > 0 && !this.duplicate ){
      validate = false;
    }else{
      validate = true;
    }
    return validate
  }
  jsonParserValidatedFields() {
    let validate1 = false;
    let validate =false;
    if (this.input.length > 0 ) {
      for (const val of this.input.value) {
        if ((val.mapexpression.trim().length === 0 || val.mapfieldtype === '' || !val.sourcedata) ) {
          validate1 = true;
          break;
        }
      }
    }
    if(!this.duplicate && !validate1){
      validate = false
    }else{
      validate = true
    }
      return validate
  }
  //final form data and node data save
  saveNodedata() {
    let nodeId;
    if (this.nodeDesc == true) {
      this.focusIn();
    }
    if(this.node.name != 'Combine'){
    nodeId = this.reteservice.getNodeButtonId();
    document.getElementById(nodeId).click();
    }
    this.savedata();
    const data = this.editor.toJSON();
    this.reteservice.saveWfdata(this.editor['workflow'].wfId, data, true);      
  }

  //prepare node data to save
  async savedata() {
    let data:any;
     data = this.nodeformdata.value;
    if (this.jsonLogicRule) {
      data.jsonLogicRule = this.jsonLogicRule;
    }
    if(this.dataItem.nodeName == 'SubWorkflow'){
      data.wfName = this.subWorkflowSelectedValue;
    }else{
      data.wfName = this.dataItem.nodeData.wfName;
    }
    // if(this.nodeNameChange){
    //   this.reteservice.flowTechName.next("change");
    // }
    data.wfNamespace = this.dataItem.nodeData.wfNamespace;
    data.icon = this.dataItem.nodeData.icon;
    data.category = this.dataItem.nodeData.category;
    data.savedata = true;
    data.description = this.dataItem.nodeData.description;
    data.name = this.dataItem.nodeData.name;
    let newInput = data.input;
    data.inputs = newInput;
    delete data.input;
    this.node.data = data;     
  }

  isfirstrow:boolean=false;
  callFirstshow($event){
    if($event===1)
    this.isfirstrow=true;
    else
    this.isfirstrow=false;
  }
  openTestData(){
    this.reteservice.openTestDataPanel.next('open');
  }

  openExpBuilderDialog(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){
    this.expBuildData = {};
    this.expBuildData = {
      "event": event,
      "callerformfieldvalue": callerformfieldvalue,
      "callerformfieldtype": callerformfieldtype,
      "index": index,
      "keyvalindex": keyvalindex,
      "nodeName": this.dataItem.nodeName
    }
    if (this.dataItem.nodeName === "calculate") {
      this.dataItem["addedExpressions"] = this.expressions.value;
      this.dataItem["currentExpressionKey"] = keyvalindex;
      this.dataItem["index"] = index;
    }
    event.stopPropagation();
    event.preventDefault();
    this.expBuildVal = this.dataItem
    if (callerformfieldvalue !== null) {
      this.expBuildVal.exprbuildervalue = callerformfieldvalue;
    } else {
      this.expBuildVal.exprbuildervalue = '';
    }    
    this.ref = this.dialogService.open(ExpressionBuilderComponent, {
      header: 'Expression Builder',
      contentStyle: { overflow: 'auto', padding: '5px'},
      style:{"max-height":"75%","max-width":"75%","z-index":999},
      // baseZIndex:10000,
      modal:true,
      maximizable: false,
      data: this.expBuildVal,
      draggable:true
    });
    this.ref.onClose.subscribe((res:any) => {
      if(res != undefined){
      let val=res.expBuildValue;
    if (this.expBuildData.callerformfieldtype === 'keyvalue') {
      const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
       control.at(this.expBuildData.keyvalindex).get('value').setValue(val);      
    } else if(this.expBuildData.nodeName == 'calculate'){
        this.expressions.at(this.expBuildData.index).get('value').setValue(val);
    }else if(this.expBuildData.nodeName == 'ForLoop'){
         this.nodeformdata.controls['IterableData'].setValue(val)
    }else{
        this.input.at(this.expBuildData.index).get('values').setValue(val);
    }
  }else{
      this.reteservice.getRunflowToDialogClose.next(true)
  }
    });
    
  }
  enableDisable:boolean=false;
  async hideOptionalFields(hide:any){
    this.input.controls.forEach(key => {
       if(key.value.required == 'Optional'){ 
        if (key.value.show == true) {
          key.patchValue({ show: false });
        }else{
          key.patchValue({ show: true });
        }
     }else if(key.value.type == 'keyvalue'){
      if (key.value.show == true) {
        key.patchValue({ show: false });
      }else{
        key.patchValue({ show: true });
      }
     }else{
      key.patchValue({ show: false });
     }
    });    
  }
}