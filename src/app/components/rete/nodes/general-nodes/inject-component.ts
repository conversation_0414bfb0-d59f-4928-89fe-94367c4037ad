import { Component, Output, Input } from 'rete';
import { injectSocket, successSocket, errorSocket, generalSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
// import { MyNodeComponent } from './node/node.component';
export class InjectComponent extends Component {
   override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'response',
    wfNamespace: 'unvired.operation',
    icon: 'edit_note',
    category: 'Others'
  };
  constructor() {
    super('response');
    // this.data.render = 'angular';
    // this.data.component = MyNodeComponent;
  }
builder(node) {
  const inp1 = new Input('response', 'Input', injectSocket);
  const out1 = new Output('response_success', 'Success', successSocket, false);
  const out2 = new Output('response_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
