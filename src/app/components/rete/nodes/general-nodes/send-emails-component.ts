import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, generalSocket } from '../../sockets';


export class SendEmailComponent extends Component {
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'email',//email
    wfNamespace: 'unvired.operation',
    icon: 'attach_email',
    category: 'Others'
  };
  constructor() {
    super('email');
  }
builder(node) {
  const inp1 = new Input('email', 'Input', generalSocket);
  const out1 = new Output('email_success', 'Success', successSocket, false);
  const out2 = new Output('email_error', 'Error', errorSocket, false);
  // const ctrl = new StrControl(this.editor, 'shortname');
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(ctrl)
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
