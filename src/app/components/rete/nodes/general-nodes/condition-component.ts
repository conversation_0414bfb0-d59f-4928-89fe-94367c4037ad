import { Component, Output, Input } from 'rete';
import {conditionElseSocket,conditionThenSocket ,strSocket,generalSocket, errorSocket} from '../../sockets';
import { AngularComponentData } from 'rete-angular-render-plugin';

export class ConditionComponent extends Component {
  // data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'condition',
    wfNamespace: 'unvired.operation',
    icon: 'usb',
    category: 'Others'
  };
  constructor() {
    super('condition');
    // this.data.render = 'angular';
    // this.data.component = ConditionNodeComponent;
  }
async builder(node) {
  const inp1 = new Input('condition', 'InputCondition', generalSocket);
  const out1 = new Output('condition_then', 'Then', conditionThenSocket, false);
  const out2 = new Output('condition_else', 'Else', conditionElseSocket, false);
  const out3 = new Output('condition_error', 'On Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              //  .addControl(new StrControl(this.editor, 'shortname'))
               .addOutput(out2)
              .addOutput(out3);
}

async worker(node, inputs, outputs) {
  // console.log(node);
  // await this.editor.view.updateConnections({ node });
 //  outputs.transform = node.data.transformdata;
}
}
