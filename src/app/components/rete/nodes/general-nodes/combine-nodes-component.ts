import { Component, Output, Input } from 'rete';
import { injectSocket, successSocket, errorSocket, generalSocket ,combineSocket} from '../../sockets';
import { AngularComponentData } from 'rete-angular-render-plugin';

export class combineComponent extends Component{
    override data: AngularComponentData;
    defaultNodeData = {
      name: 'untitled',
      description: '',
      wfName: 'merge',
      wfNamespace: 'unvired.operation',
      icon: 'merge',
      category: 'Others',
      inputCount: 1,
    };
    constructor() {
      super('merge');
    }
    builder(node) {
      node.data.inputCount = node.data.inputCount || 1;

      for (let i = 0; i < node.data.inputCount; i++) {
        const input = new Input(`merge_${i}`, `Input ${i + 1}`, combineSocket);
        node.addInput(input);
      }
        // const inp1 = new Input('merge', 'Input', combineSocket,true);
        const out1 = new Output('merge_success', 'Success', successSocket, false);
        const out2 = new Output('merge_error', 'Error', errorSocket, false);
        node.data = Object.keys(node.data).length === 1 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
        return node.addOutput(out1).addOutput(out2);
      }
      worker(node, inputs, outputs) {
        //  outputs.transform = node.data.transformdata;
       }
}