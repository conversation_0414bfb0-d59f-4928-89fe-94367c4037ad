<i class="pi pi-list cursor help-icon" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData(openPanel = true)" pTooltip="Open Test Data"></i>

<div>
    <div class="justify-content-center" style="width: 430px;">
        <p-progressBar mode="indeterminate" [style]="{ height: '6px' }" *ngIf="sapLoader"></p-progressBar>
        <p-listbox [options]="listdata" filterBy="label,items" [group]="true" [listStyle]="{ 'height': '200px'}" class="explistbox"
            [filter]="true" (onChange)="seteditorvalue($event)">
            <ng-template let-group pTemplate="group">
                <div class="flex align-items-center">
                    <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                </div>
            </ng-template>
        </p-listbox>
    </div>
    <div class="justify-content-center mt-2">
        <textarea id="float-input" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30"
            pInputTextarea [(ngModel)]="exprbuildervalue" [autoResize]="false"></textarea>
    </div>
    <div class="flex justify-content-center mt-2">
        <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
        <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
    </div>
</div>