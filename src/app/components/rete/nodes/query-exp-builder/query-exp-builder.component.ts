
import { Component, OnInit, Inject, AfterViewInit, HostListener, Input, Output, EventEmitter, ElementRef, ViewChild } from '@angular/core';
import { SelectItemGroup } from 'primeng/api';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FormControl, FormGroup } from '@angular/forms';
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
@Component({
  selector: 'app-query-exp-builder',
  templateUrl: './query-exp-builder.component.html',
  styleUrls: ['./query-exp-builder.component.scss']
})
export class QueryExpBuilderComponent {
  @Input() data: any;
  @ViewChild('expressionArea') expressionArea: ElementRef;
  constructor(private utilservice: UtilsService, private reteservice: ReteService, private confirmationService: ConfirmationService,
    public dialogData: DynamicDialogConfig,public ref: DynamicDialogRef,) {
   this.data= this.dialogData.data;
  }
  checkForChange = false;
  exprbuildervalue: string;
  sapLoader:boolean=true;  
  listdata: any = [];
  async ngOnInit() { 
    if(this.data.value == undefined){
      this.exprbuildervalue= "";
    }else{
      this.exprbuildervalue= this.data.value;
    }
    this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeData.nodeName, this.data.nodeData.formId).subscribe((res) => {
      this.listdata= res.formFields;
      this.sapLoader=false;        
    });
    if (this.data.nodeData.node.getConnections().length > 0) {
      let arr;
      const nodesfnd =  this.utilservice.getpreviousnodes(this.data.nodeData.nodeId, this.data.nodeData.nodeName, [], this.data.nodeData.editor.toJSON());
      const prevNodeData = await this.utilservice.preparePreviousNodesTree(nodesfnd, this.data.nodeData);
      for (let key in prevNodeData) {
        if (prevNodeData[key].label == "Previous Node") {
          arr = prevNodeData[key].items;
          for (let key2 in arr) {
            this.listdata.push(arr[key2]);
          }
        }
      }
      for(let key in prevNodeData){
            this.listdata.push(prevNodeData[key])
       }
     this.listdata = this.listdata.filter((item) => item.label !== 'Previous Node');
     this.sapLoader=false;
      }
    
    this.openRunData(this.reteservice.getRunflowToDialog())
  }
  seteditorvalue(event: any) {
      const curPos = this.expressionArea.nativeElement.selectionStart;
      if (this.data.nodeData.nodeName === "execjavascript" || this.data.nodeData.nodeName === "execpython") {
        this.exprbuildervalue =this.exprbuildervalue.slice(0, curPos) + event.value +"," +this.exprbuildervalue.slice(curPos);
      } else {
        this.exprbuildervalue = this.exprbuildervalue.slice(0, curPos) + event.value + this.exprbuildervalue.slice(curPos);
      }
  }

  async save(type: any) {
    if (this.data.exprbuildervalue !== this.exprbuildervalue) {
      this.checkForChange = true;
    } else {
      this.checkForChange = false;
    }
    let data ={
      "type": type, "close": true, "expBuildValue": this.exprbuildervalue, "change": this.checkForChange 
    }
    this.ref.close(data);
    this.reteservice.getRunflowToDialogClose.next(true);
  }

  close(event: Event, type: any) {
    if (this.data.exprbuildervalue !== this.exprbuildervalue) {
      this.checkForChange = true;
      this.confirm(event, type);
    } else {
      this.checkForChange = false;
      this.exprbuildervalue = this.data.exprbuildervalue;
      let data ={
        "type": type, "close": true, "expBuildValue": this.exprbuildervalue, "change": this.checkForChange 
      }
      this.ref.close(data);
      this.reteservice.getRunflowToDialogClose.next(true)
    }
    
  }
  confirm(event: Event, type: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.exprbuildervalue = this.data.exprbuildervalue;
         let data ={
          "type": type, "close": true, "expBuildValue": this.exprbuildervalue, "change": this.checkForChange 
        }
        this.ref.close(data);
        this.reteservice.getRunflowToDialogClose.next(true);
      },
      reject: () => {
      }
    });
  }
  openPanel:boolean=false;
  openTestData(event:any){
    let data={
      "panelName":"testData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
  openRunData(event:any){
    let data={
      "panelName":"runData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
}
