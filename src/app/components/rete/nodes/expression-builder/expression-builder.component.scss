::ng-deep .sapCard .p-card .p-card-body{
    padding: 0px 0px 0px 0px !important;
    margin-top:10px !important ;
}
::ng-deep .sapCard .p-card .p-card-content{
    padding: 5px !important;
  }

  ::ng-deep .sapCard .p-card .p-card-title{
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom:0px !important;
    margin-left: 10px !important;
  }

  ::ng-deep .explistbox .p-listbox{
    border-radius: 0px !important;
    width: 100% !important;
  }

  ::ng-deep .tabview .p-tabview .p-tabview-nav li{
    width: 215px !important;
  }
  ::ng-deep .tabview .p-tabview .p-tabview-nav li .p-tabview-nav-link{
    justify-content: center !important;
  }

  ::ng-deep .p-fieldset .p-fieldset-legend{
    padding: 0px !important;
    font-weight: normal !important;
  }

  ::ng-deep .p-fieldset .p-fieldset-content{
    padding:  0px !important;
  }
  ::ng-deep .p-listbox .p-listbox-list{
    padding:  0px !important;
  }
  ::ng-deep .condition .p-listbox .p-listbox-list .p-listbox-item{
    padding:  8px 8px !important;
  }
  ::ng-deep .expwarnMsg .p-inline-message{
    width: 100%;
  }
  
  ::ng-deep .expwarnMsg .p-message .p-message-wrapper{
    padding: 10px 10px;
  }

  ::ng-deep .expwarnMsg .p-message{
    margin: 0px !important ;
  }
  .help-icon {
    position: absolute;
    top: 25px;
    right: 55px;
  }
  .help-icon1{
    position: absolute;
    top: 25px;
    right: 88px;
  }

#editor {
  width: 100%;
  height: 100px;
 // padding: 10px;
  // background-color: #ffffff;
  // color: #000000;
 // font-size: 14px;
 // font-family: monospace;
  //border: 1px solid #ced4da;
  white-space: pre-wrap !important;
  text-wrap: wrap !important;
  line-height: 30px;
  overflow: auto !important;
}

#editor:focus{
outline: 0 none !important;
outline-offset: 0 !important;
box-shadow: 0 0 0 0.2rem #C7D2FE !important;
border-color: #6366F1 !important;
}

.expBuilderEditor{
  white-space: pre-wrap !important;
  text-wrap: wrap !important;
  overflow: auto !important;
}

.expBuilderEditor:focus{
  outline: 0 none !important;
  outline-offset: 0 !important;
  box-shadow: 0 0 0 0.2rem #C7D2FE !important;
  border-color: var(--primary-color) !important;
  }

  ::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token{
    margin-right: 0.5rem !important;
  }
  
  .extra-hidden{
    display: none !important;
  }
