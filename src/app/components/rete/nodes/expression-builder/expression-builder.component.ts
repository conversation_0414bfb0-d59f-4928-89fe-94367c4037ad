import { Component,Input, Output, EventEmitter, ElementRef, ViewChild } from '@angular/core';
import { ConfirmationService } from 'primeng/api';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

interface RFCField {
  NAME: string;
  SAP_TYPE: string;
  TYPE: string;
  DESC: string;
  DECIMAL: string;
  DIRECTION: number;
  INDEX: string;
  MANDATORY: boolean;
  LENGTH: number;
  RFCField: any[];
}

interface InputMapping {
  NAME: string;
  SAP_TYPE: string;
  TYPE: string;
  DESC: string;
  DECIMAL: string;
  DIRECTION: number;
  INDEX: string;
  MANDATORY: boolean;
  LENGTH: number;
  RFCField: any[];
  formValue:string;
  customValue:string;
  isCustom:boolean;
  change:boolean;
}

interface RFCParameterFormat {
  RFCInput: any[];
  RFCOutput: any[];
  RFCParameter: RFCField[];
}

@Component({
  selector: 'app-expression-builder',
  templateUrl: './expression-builder.component.html',
  styleUrls: ['./expression-builder.component.scss']
})
export class ExpressionBuilderComponent {
  @Input() data: any;
  @Output('overlayclose') eventEmitter: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('expressionArea') expressionArea: ElementRef;
  @ViewChild('sapExpressionArea') sapExpressionArea: ElementRef;
  @ViewChild('editor') editor!: ElementRef <HTMLElement>;
  @ViewChild('sapExp') sapExp: ElementRef;
  @ViewChild('editortest') editortest!: ElementRef;
  private searchSubject: Subject<string> = new Subject<string>();
  
  isLoader:boolean=true;
  sapFormGroup!: FormGroup;
  expressionBuilderValue: string;
  searchKeyword:string='';
  isReturn:boolean = false;
  parameterFormat: any = {
    RFCInput: [],
    RFCOutput: [],
    RFCParameter: [],
  };
  inputMappingfields:InputMapping;

  sapExprbuildervalue:string='';
  listdata: any = [];
  sapExpBuild:any=[]
  dbListTable:any =[];
  previousNode: any;
  formsFileds: any;
  
  dbFormGroup!:FormGroup;
  sapfunctionName: any; rfcInputArray: any;
  rfcOutputArray: any;
  expParameterFormat: any; checkForChange = false;
  
  parmeterVal:any;
  IpValue:any;
  inputTableData:any=[];
  allFormFields:any=[];
  IpValueValue:any;
  expandedRowKeys: any;
  dynamicData:any;
  finalDataToProcess:any;searchText: string;
  editorOptionsJSONata = {
    minimap: { enabled: false },
    lineNumbers: 'off',
    language:'jsonata',
    theme: 'jsonataTheme',
    contextmenu: false,
    automaticLayout: true,
    scrollBeyondLastLine: false,
  };
    
  messages1:any;
  // inputsFiled:any;
  error:boolean=true;

  handleKeyDown(evnt:any){
    const editor = this.editor.nativeElement;
    const caretPosition = this.saveCaretPosition(editor);
    const text = editor.innerText;
    let regex:any;
  if(this.data.nodeName == "calculate"){
    regex = /\b\w+\(.*?\)|[+\-*/]|\${[^}]+}/g;
  }
  if(this.data.nodeName != "calculate"){
    regex = /\${[^}]+}/g;
  }
    const highlightedText = text.replace(regex, (match) => {
      return `<span style='background-color: var(--primary-100); padding: 4px; border-radius: 5px;'>${match}</span>`;
    });
    editor.innerHTML = highlightedText;
    this.restoreCaretPosition(editor, caretPosition);    
  }
  
  sendit(){
    const editor = this.editor.nativeElement;
    this.expressionBuilderValue = editor.innerText;    
  }

   saveCaretPosition(element: HTMLElement): number {
      let caretOffset = 0;
      const selection = window.getSelection();
      if (selection?.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(element);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        caretOffset = preCaretRange.toString().length;
      }
      return caretOffset;
  }

  restoreCaretPosition(element: HTMLElement, caretOffset: number) {
    const range = document.createRange();
    const selection = window.getSelection();
    range.setStart(element, 0);
    range.collapse(true);
    let nodeStack = [element];
    let node;
    let found = false;
    let offset = 0;
    while (!found && (node = nodeStack.pop())) {
      if (node.nodeType === 3) {
        const nextOffset = offset + node.length;
        if (caretOffset <= nextOffset) {
          range.setStart(node, caretOffset - offset);
          found = true;
        } else {
          offset = nextOffset;
        }
      } else {
        let i = node.childNodes.length;
        while (i--) {
          nodeStack.push(node.childNodes[i]);
        }
      }
    }
    selection?.removeAllRanges();
    selection?.addRange(range);
    element.focus();
  }

  constructor(private utilservice: UtilsService, private reteservice: ReteService, private confirmationService: ConfirmationService,
    public dialogData: DynamicDialogConfig,public ref: DynamicDialogRef,private formBuilder: FormBuilder) {
   this.data= this.dialogData.data;
  }
 
 async ngAfterViewInit() {
  if (this.data.nodeName != 'sap' && this.data.callerformfield !== 'keys') {
    this.api();
  }
  }


  async ngOnInit() {  
    this.searchSubject.pipe(debounceTime(500),distinctUntilChanged()).subscribe((keyword) => {
      if (keyword.length >= 3) {
        this.getExplorerData(keyword);
      }
    });
    this.messages = [{ severity: 'info', detail: 'No Data Found' }];  
    this.messages1 = [{ severity: 'error', detail: 'NO INPUTS SELECTED' }];  

    if(this.data.nodeName === 'sap'){
      if (this.data.inputName === "function"){
          this.expressionBuilderValue = this.data.exprbuildervalue;
          if(this.data.exprbuildervalue){
            this.searchKeyword = this.data.exprbuildervalue;
            this.getExplorerData(this.searchKeyword);
            this.isLoader =  false;
            return;
          }
          this.isLoader =  false;
        }
      
      if (this.data.inputName === "parameters"){
          this.initializeForm();
          this.sapfunctionName = this.data.formData.filter((key: any) => key.inputName === "function").map((key: any) => key.values)[0];
          this.expParameterFormat = this.data.exprbuildervalue;
          await this.getSapInputsOutputs();
          return;
        }

      if(this.data.inputName === "rfcinputs" ){ 
          if(this.data.exprbuildervalue.includes('RFCField')){  
            await this.getSelectValue();
            await this.api();
          }else{
            await this.newSelectValue();
            await this.api();
            this.data.formData.forEach(async (key) => {
            if (key.inputName === "parameters") {
              this.dynamicData =  JSON.parse(key.values);
              this.dynamicData.RFCParameter.forEach((res)=>{
                if(res.NAME == this.data.exprbuildervalue){              
                  this.inputMappingfields=res;
                  this.newVal=this.inputMappingfields.RFCField;
                }
              })
            }
          });
          }
        }

      if(this.data.inputName === "data"){
        this.api();
        this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeName, this.data.formId).subscribe((res) => {
         this.listdata= res.formFields;
         this.isLoader=false;        
         });
         this.expressionBuilderValue = this.data.exprbuildervalue;
         this.finalDataToProcess = JSON.parse(this.data.exprbuildervalue);        
      }
      
    }

   if(this.data.nodeName == 'query' && this.data.callerformfield == 'keys'){
      this.dbFormGroup = new FormGroup({
        parentTable: new FormControl(''),
        refTable: new FormControl(''),
      });
      if(this.data.selectedsystem == ""){
        this.error = true
      }else{
        this.error = false;
      }

      if(this.data.exprbuildervalue != ""){
        this.expParameterFormat =  [JSON.parse(this.data.exprbuildervalue)];
        this.finalFormat = JSON.parse(this.data.exprbuildervalue)
        this.reteservice.getAllDbTablesInfo(this.data.nodeName,this.data.selectedsystem).subscribe((res)=>{          
          if(res.status.toLowerCase() == 'success'){
              this.dbListTable = res.tableInfo[0].tables;
              let key = Object.keys(this.expParameterFormat[0])[0];
              this.dbListTable.forEach((item)=>{
                if(key == item.tableName){
                  this.dbFormGroup.patchValue({ parentTable: item });
                  this.dbTableRelTable=item.relatedTables;
                }
              });
              
              if(this.dbFormGroup.value.parentTable != ""){
                let arr=[];
                let ref = this.dbFormGroup.value.parentTable.relatedTables;
                if(ref.length > 0){
                  let dataa=[];
                  for(let refKye in this.expParameterFormat){
                    dataa=Object.keys(this.expParameterFormat[refKye]);
                  }
                  for(let a in dataa){
                    for(let refkey2 in ref){
                      if(ref[refkey2] == dataa[a]){
                       arr.push(ref[refkey2]);
                      }
                    }
                  }
              }
            this.dbFormGroup.patchValue({ refTable: arr });
            }
          }          
          });

      }else{
        this.getBdtableList()
      }
    }

    if(this.data.nodeName == 'ForLoop'){
      this.expressionBuilderValue = this.data.exprbuildervalue;
      setTimeout(() => {
        const editor = this.editor.nativeElement;
        let regex:any;
        editor.innerText = this.expressionBuilderValue 
        const text = editor.innerText;
       if(this.data.nodeName == "calculate"){
        regex = /\b\w+\(.*?\)|[+\-*/]|\${[^}]+}/g;
      }
      if(this.data.nodeName != "calculate"){
        regex = /\${[^}]+}/g;
      }
        const highlightedText = text.replace(regex, (match) => {
          return `<span style='background-color: var(--primary-100); padding: 4px; border-radius: 5px;'>${match}</span>`;
        });
        editor.innerHTML = highlightedText;
        this.moveCursorToEnd(editor)
      }, 1000);

    }else{
      this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeName, this.data.formId).subscribe((res) => {
        const data = res.formFields;
        let list=[]; 
        let list2=[];
        let keyName:any;
        for(let key1 in data){
           keyName = data[key1].label
          for(let key2 in data[key1].items){
              if(data[key1].items[key2].hasOwnProperty('items')){
                list2.push(data[key1].items[key2]);
              }else{
                list.push(data[key1].items[key2]) 
              }
          }
        }
       let obj={
          "label" : keyName,
          "items" : list
        }
        list2.unshift(obj)
        for(let a in list2){
          this.listdata.push(list2[a])
        }
      });
      this.expressionBuilderValue = this.data.exprbuildervalue;
      setTimeout(() => {
        const editor = this.editor.nativeElement;
        let regex:any;
        editor.innerText = this.expressionBuilderValue 
        const text = editor.innerText;
       if(this.data.nodeName == "calculate"){
        regex = /\b\w+\(.*?\)|[+\-*/]|\${[^}]+}/g;
      }
      if(this.data.nodeName != "calculate"){
        regex = /\${[^}]+}/g;
      }
        const highlightedText = text.replace(regex, (match) => {
          return `<span style='background-color: var(--primary-100); padding: 4px; border-radius: 5px;'>${match}</span>`;
        });
        editor.innerHTML = highlightedText;
        this.moveCursorToEnd(editor)
      }, 1000);

    }
     this.openRunData(this.reteservice.getRunflowToDialog());
  }

  onSearchInput(event: Event): void {
    const input = (event.target as HTMLInputElement).value;
    this.searchSubject.next(input);
  }

  initializeForm(): void {
    this.sapFormGroup = this.formBuilder.group({
      rfcInputData: [''],
      rfcOutputData: ['']
    });
  }

  async moveCursorToEnd(element: HTMLElement) {
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(element);
    range.collapse(false); // collapse to end
    selection?.removeAllRanges();
    selection?.addRange(range);
    element.focus();
  }



  allCalculateFunctions=[];
 async getFunctions() {
  this.isLoader=true;
    this.reteservice.getAllCalculatFunctions().subscribe((res) => {
      const response = res;
      // let a = response.data.calculateFunctions[0];
      if (response.error === "") {
        if (response.status === "Success") {
          this.allCalculateFunctions=response.data.calculateFunctions;          
        }        
      } else {
      //  this.errmsg = response.error;
      }
      this.isLoader=false;
    });
  }

  
  async setIpOpval(): Promise<void> {
 
    const sep = this.data.exprbuildervalue ? JSON.parse(this.data.exprbuildervalue) : { RFCInput:[] ,RFCOutput:[],RFCParameter:[]};

    const mandatoryInputs: any[] = this.rfcInputArray?.filter((input) => input.MANDATORY === true) || [];

    const inputArray: any[] = this.rfcInputArray?.filter((input) => sep.RFCInput?.includes(input.NAME)) || [];
    
    const combinedInputs = [...mandatoryInputs, ...inputArray].filter((input, index, self) => index === self.findIndex((t) => t.NAME === input.NAME));

    this.sapFormGroup.patchValue({ rfcInputData: combinedInputs });

    const mandatoryOutput: any[] = this.rfcOutputArray?.filter((input) => input.NAME === 'RETURN') || [];
    
    const outputArr: any[] = this.rfcOutputArray?.filter((input) => sep.RFCOutput?.includes(input.NAME)) || [];

    const combinedOutputs = [...mandatoryOutput, ...outputArr].filter((input, index, self) => index === self.findIndex((t) => t.NAME === input.NAME));

    this.sapFormGroup.patchValue({ rfcOutputData: combinedOutputs });

    this.isLoader = false;
  }

  async api() {
      let arr;
    if (this.data.node.getConnections().length > 0) {
      const nodesfnd =  this.utilservice.getpreviousnodes(this.data.nodeId, this.data.nodeName, [], this.data.editor.toJSON());
      
      let uniqueData = nodesfnd.filter((obj, index, self) => index === self.findIndex((t) => (t.id === obj.id)));
      
      const prevNodeData = await this.utilservice.preparePreviousNodesTree(uniqueData, this.data);
      for (let key in prevNodeData) {
        if (prevNodeData[key].label == "Previous Node") {
          arr = prevNodeData[key].items;
          for (let key2 in arr) {
            this.listdata.push(arr[key2]);
          }
        }
      }
      for(let key in prevNodeData){
            this.listdata.push(prevNodeData[key])
       }

     this.listdata = this.listdata.filter((item) => item.label !== 'Previous Node');
    
     this.isLoader=false;
    }else{
      if (this.data.nodeName === "ForLoop") {
      this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeName, this.data.formId).subscribe((res) => {
        this.listdata= res.formFields;
        this.isLoader=false;        
      });
    }
    }
    if (this.data.nodeName === "calculate") {
      await this.getFunctions();
     }
      this.sapExpBuild = this.listdata;
      this.sapExpBuild = this.sapExpBuild.filter((item) => item.label !== 'Custom Input');

      this.sapExpBuild = this.sapExpBuild.reduce((unique, o) => {
        if(!unique.some(obj => obj.label === o.label && obj.value === o.value)) {
          unique.push(o);
        }
        return unique;
    },[]);
  }

  async sapRfcDropDown(){
    let arr;
    if (this.data.node.getConnections().length > 0) {
      const nodesfnd =  this.utilservice.getpreviousnodes(this.data.nodeId, this.data.nodeName, [], this.data.editor.toJSON());
      const prevNodeData = await this.utilservice.preparePreviousNodesTree(nodesfnd, this.data);
      for (let key in prevNodeData) {
        if (prevNodeData[key].label == "Previous Node") {
          this.listdata = prevNodeData[key].items;        
          for (let key2 in arr) {
            this.listdata.push(arr[key2]);
          }
        }
    }
      for(let key in prevNodeData){
            this.listdata.push(prevNodeData[key])
      }
      this.listdata = this.listdata.filter((item) => item.label !== 'Others');
      
    return this.listdata = this.listdata.filter((item) => item.label !== 'Previous Node');
    }else{
      return this.listdata = [];
    }
    
    
  }

  getExplorerData(name: string): void {
    const rfcName = name.toUpperCase();
    const systemName = this.data.selectedSystem;
  
    this.reteservice.sapRfcExplorerData(rfcName, systemName, false).subscribe({
      next: (res) => {
        if (res && res.RFCData) {
          this.sapfunctionName = res.RFCData;
        } else {
          this.sapfunctionName = [];
        }
      },
      error: (err) => {
        this.sapfunctionName = [];
        this.reteservice.odataMessages.next({"type":"error",'message':`${err.message} || An error occurred while fetching RFC data.`});
      },
    });
  }

  async getSapInputsOutputs(): Promise<void> {
    try {
      const rfcName = this.sapfunctionName;
      const sysName = this.data.selectedSystem;
  
      let response = await this.reteservice.sapRfcInputOutputApi(rfcName, sysName);

        if (response.status === "Success") {
              this.rfcInputArray = response.data.RFCInput || [];
              this.rfcOutputArray = response.data.RFCOutput || [];
              await this.setIpOpval();
            } else {
              this.reteservice.odataMessages.next({"type":"warning",'message':response.message});
            }
    } catch (err) {
      this.reteservice.odataMessages.next({"type":"error",'message':`Unexpected error in getSapInputsOutputs:: ${err}`});
    }
    this.isLoader = false;
  }

  chipValue:any=[];
 async seteditorvalue(event:any,nodeName:any,type:any) {
    if (this.data.nodeName !== 'sap') {
      const curPos = this.expressionArea.nativeElement.selectionStart;
      if (this.data.nodeName === "execjavascript" || this.data.nodeName === "execpython") {
        let  caretPosition = this.getCaretPosition();
        let regex = /\${[^}]+}/g;
        const editor = this.editor.nativeElement as HTMLElement;
        editor.focus();
        this.expressionBuilderValue =this.expressionBuilderValue.slice(0, caretPosition) + event.value +"," +this.expressionBuilderValue.slice(caretPosition);
        editor.innerText = this.expressionBuilderValue;
        const text = editor.innerText;
        const highlightedText = text.replace(regex, (match) => {
          return `<span style='background-color: var(--primary-100); padding: 4px; border-radius: 5px;'>${match}</span>`;
        });
        editor.innerHTML = highlightedText;
       await  this.moveCursorToEnd(editor)
        editor.focus();
      } else 
      {
       let  caretPosition = this.getCaretPosition();
       let regex:any
        const editor = this.editor.nativeElement as HTMLElement;
        editor.focus();
        this.expressionBuilderValue = this.expressionBuilderValue.slice(0, caretPosition) + event.value + this.expressionBuilderValue.slice(caretPosition);
        editor.innerText = this.expressionBuilderValue;
        const text = editor.innerText;
        if(this.data.nodeName == "calculate"){
          regex = /\b\w+\(.*?\)|[+\-*/]|\${[^}]+}/g;
        }
        if(this.data.nodeName != "calculate"){
          regex = /\${[^}]+}/g;
        }
        const highlightedText = text.replace(regex, (match) => {
          return `<span style='background-color: var(--primary-100); padding: 4px; border-radius: 5px;'>${match}</span>`;
        });
        editor.innerHTML = highlightedText;
        await  this.moveCursorToEnd(editor)
        editor.focus();
      }
    } else {
      if (this.data.inputName === 'parameters') {
        this.expParameterFormat = event.value.key;
      } else if (this.data.inputName === 'function') {
        this.expressionBuilderValue = event.value.key;
        this.save('save')
      } else if(this.data.inputName == 'data'){
        const curPos = this.expressionArea.nativeElement.selectionStart;
        this.expressionBuilderValue = this.expressionBuilderValue.slice(0, curPos) + event.value + this.expressionBuilderValue.slice(curPos);
      }
    }
  }
  getCaretPosition(): number {
    const editor = this.editor.nativeElement;
    const selection = window.getSelection();
    let caretOffset = 0;
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const preCaretRange = range.cloneRange();
      preCaretRange.selectNodeContents(editor);
      preCaretRange.setEnd(range.endContainer, range.endOffset);
      caretOffset = preCaretRange.toString().length;
    }
    return caretOffset;
  }
 async scrollToPosition(position: number) {
    const editorElement = this.editor.nativeElement;
    editorElement.scrollTop = position;
  }
 async setCaretPosition(position: number) {
    const editorElement = this.editor.nativeElement;
    const range = document.createRange();
    const selection = window.getSelection();
    selection.removeAllRanges();
    let node = editorElement.firstChild;
    let offset = position;
    while (node && node.nodeType !== Node.TEXT_NODE) {
      node = node.nextSibling;
    }
    if (!node) {
      node = document.createTextNode('');
      editorElement.appendChild(node);
    }
    if (offset > node.textContent.length) {
      offset = node.textContent.length;
    }
    range.setStart(node, offset);
    range.collapse(true);
    selection.addRange(range);
    editorElement.focus();
  }
  setSapeditorvalue(event:any){
    if(this.sapExprbuildervalue !== undefined){
      this.sapExprbuildervalue = this.sapExprbuildervalue.concat(event.value);
    }else{
      this.sapExprbuildervalue = event.value;
    }
  }
  
  async save(type: any) {
    if (this.data.nodeName === 'sap') {
      if (this.data.inputName === 'function') {
        if (this.data.exprbuildervalue !== this.expressionBuilderValue) {
          this.checkForChange = true;
        } else {
          this.checkForChange = false;
        }
        let data ={
          "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
        }
        this.ref.close(data)
        this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange });
      }
      else if (this.data.inputName === 'parameters') {
       let allData:RFCParameterFormat = this.processRFCData(this.sapFormGroup.value);
       this.expParameterFormat = JSON.stringify(allData);
       if (this.data.exprbuildervalue !== this.expParameterFormat) {
        this.checkForChange = true;
      } else {
        this.checkForChange = false;
      }
        let data ={
          "type": type, "close": true, "expBuildValue": this.expParameterFormat, "change": this.checkForChange 
        }
        this.ref.close(data)
        this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expParameterFormat, "change": this.sapFormGroup.touched });
      }else if (this.data.inputName === 'rfcinputs') {
        if (this.inputMappingfields.hasOwnProperty('change') && this.inputMappingfields.change){
          this.checkForChange = true;
        } else {
          this.checkForChange = false;
        } 
        let data ={
          "type": type, "close": true, "expBuildValue": JSON.stringify(this.inputMappingfields), "change": this.checkForChange 
        }
        this.ref.close(data)   
      this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": JSON.stringify(this.inputMappingfields), "change": this.checkForChange });
      }else{
        let data ={
          "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
        }
        this.ref.close(data) 
        this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange });
      }
    }
    else if(this.data.nodeName !== 'sap' && this.data.callerformfield === 'keys'){
      if (this.data.exprbuildervalue !== this.finalFormat) {
        this.checkForChange = true;
      } else {
        this.checkForChange = false;
      }
      let data ={
        "type": type, "close": true, "expBuildValue": JSON.stringify(this.finalFormat), "change": this.checkForChange 
      }
      this.ref.close(data);
      this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": JSON.stringify(this.finalFormat), "change": this.checkForChange });
    }else {
      if (this.data.exprbuildervalue !== this.expressionBuilderValue) {
        this.checkForChange = true;
      } else {
        this.checkForChange = false;
      }
      let data ={
        "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
      }
      this.ref.close(data);
    //  this.reteservice.setBlockUi(false);
    //  this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange });
    }
  //  this.reteservice.getRunflowToDialogClose.next(true)
  }
  
 
  close(event: Event, type: any) {
    if (this.data.exprbuildervalue !== this.expressionBuilderValue) {
      this.checkForChange = true;
      this.confirm(event, type);
    } else {
      this.checkForChange = false;
      this.expressionBuilderValue = this.data.exprbuildervalue;
      let data ={
        "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
      }
      this.ref.close(data);
      this.reteservice.getRunflowToDialogClose.next(true)
      this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange });
    }
    
  }

  confirm(event: Event, type: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.expressionBuilderValue = this.data.exprbuildervalue;
        if(this.data.inputName === 'rfcinputs'){
          let data ={
            "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
          }
          this.ref.close(data)  
          this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": false });
        }else{
          if(this.data.nodeName == 'query' && this.data.callerformfield == 'keys'){
            if(this.expressionBuilderValue == ''){
              let a = [];
              let data ={
                "type": type, "close": true, "expBuildValue": JSON.stringify(a), "change": this.checkForChange 
              }
              this.ref.close(data);
              this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": JSON.stringify(a), "change": false });
            }
         }else{
          let data ={
            "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
          }
          this.ref.close(data);
        this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": false });
         }
         let data ={
          "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": this.checkForChange 
        }
        this.ref.close(data);
         this.eventEmitter.emit({ "type": type, "close": true, "expBuildValue": this.expressionBuilderValue, "change": false });
        }
        this.reteservice.getRunflowToDialogClose.next(true);
      },
      reject: () => {
      }
    });
  }

  clearVal(nodeName: any) {
    if (nodeName == 'sap') {
      this.expParameterFormat = '';
    } else {
      this.expressionBuilderValue = '';
    }
  }

  copyToCB() {
    if (this.expressionArea) {
      this.expressionArea.nativeElement.select();
      document.execCommand("copy");
      this.expressionArea.nativeElement.setSelectionRange(0, 0);
    }
  }

  copyInputMessage(){
    // inputElement.select();
    // document.execCommand('copy');
    // inputElement.setSelectionRange(0, 0);
  }
  async setformatDataToUpadate() {
    let data;
    let Objcount = 0;
    data =this.dynamicData;
    if(data==''){
      
    }else{
      for(let key in data){
       for(let key2 in this.IpValue){
          if(this.IpValue[key2].NAME == key){
            //if  object i.e contains RFC selected fileds
            if(typeof(data[key]) == "object"){
              if(this.IpValue[key2].RFCField.length === Object.keys(data[key]).length){
                for(let i in data[key]){
                  if (data[key][i].indexOf('${') == 0 && data[key][i].endsWith('}')){
                    Objcount ++;
                  }else{
                    Objcount--;
                  }
                }
                if(this.IpValue[key2].RFCField.length == Objcount){
                  let allValue;
                  for(let i in data[key]){
                       allValue = data[key][i];
                     }
                 let pos = allValue.indexOf('.');
                 let reFormat =  allValue.slice(0, pos); 
                 reFormat = reFormat+'}';
                 this.IpValue[key2].isSelected = true;
                 this.IpValue[key2].isCustom = false;
                 this.IpValue[key2].formValue = reFormat;
                }else{
                }
              }else{  
                 for(let ii in data[key]){
                 for(let i in this.IpValue[key2].RFCField){
                  if(this.IpValue[key2].RFCField[i].NAME == ii){
                    this.IpValue[key2].RFCField[i].isSelected = true;
                     if (data[key][ii].indexOf('${')== 0 && data[key][ii].endsWith('}')){
                      this.IpValue[key2].RFCField[i].isCustom = false;
                      this.IpValue[key2].RFCField[i].formValue = data[key][ii];
                     }else{
                      this.IpValue[key2].RFCField[i].isCustom = true;
                      this.IpValue[key2].RFCField[i].formValue = '';
                      this.IpValue[key2].RFCField[i].customValue = data[key][ii];
                     }
                   }
                    }                      
                 }                 
              setTimeout(() => {
                document.getElementById(this.IpValue[key2].NAME).click();
              }, 500);
               }
            }
            //if string i.e does not contains RFC selected fileds
            if(typeof(data[key]) == "string"){
              this.IpValue[key2].isSelected = true;
             if (data[key].indexOf('${')== 0 && data[key].endsWith('}')){
                this.IpValue[key2].isCustom = false;
                this.IpValue[key2].formValue = data[key];
              }else{
                this.IpValue[key2].isCustom = true;
                this.IpValue[key2].formValue = '';
                this.IpValue[key2].customValue = data[key];
              }
            }
          }
       }
      }
    }
}

async getSelectValue(){
  this.allFormFields = await this.sapRfcDropDown();
  this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeName, this.data.formId).subscribe((res) => {
    const response = res;
    setTimeout(() => {
      if (response) {
        if (response.error === "") {
          if (response.status === "Success") {            
              for(let key in response.formFields){
               this.allFormFields.unshift(response.formFields[key])
              }
           this.allFormFields.unshift({label:'Custom Input',items:[{label:'Custom Input', value:''}]});
            if(this.data.exprbuildervalue.includes('RFCField')){
            this.inputMappingfields = JSON.parse(this.data.exprbuildervalue);
            if(this.inputMappingfields.RFCField.length > 0){
              for(let key in this.inputMappingfields.RFCField){
                if(this.inputMappingfields.RFCField[key].formValue == '' && this.inputMappingfields.RFCField[key].isCustom != true){
                this.inputMappingfields.RFCField[key].formValue = 'testDemo';
                }
              }
            }
          }
          // this.inputsFiled = this.inputMappingfields.RFCField;
          this.newVal=this.inputMappingfields.RFCField;
          }
        }
        }  
    }, 100);
  });

}

async newSelectValue(): Promise<void> {
  try {
    this.allFormFields = await this.sapRfcDropDown();

    this.reteservice.getFormsFielsForExpressionBuilder(this.data.nodeName, this.data.formId).subscribe({
      next: (res) => {
        if (res && res.error === "" && res.status === "Success") {
          this.processFormFields(res.formFields);
          this.handleExprBuilderValue();
        }
      },
      error: (err) => {
        console.error("Error fetching form fields:", err);
      },
      complete: () => {
      },
    });
  } catch (err) {
  }
}

private processFormFields(formFields: any): void {
  for (const key in formFields) {
    this.allFormFields.unshift(formFields[key]);
  }
  this.allFormFields.unshift({
    label: "Custom Input",
    items: [{ label: "Custom Input", value: "" }],
  });
}

private handleExprBuilderValue(): void {
  if (this.data.exprbuildervalue.includes("RFCField")) {
    this.inputMappingfields = JSON.parse(this.data.exprbuildervalue);
    // this.inputsFiled = this.inputMappingfields.RFCField;
    this.newVal = this.inputMappingfields.RFCField;
  }
}
messages:any;
newVal:any;
onInput(event: any) {
  this.newVal = this.inputSearch(event.target.value);  
}

inputSearch(searchText: string){

  let items = this.inputMappingfields.RFCField;
  if (!items) {
    return [];
  }
  if (!searchText) {
    return items;
  }
  searchText = searchText.toLowerCase();
  return items.filter(item => {
    return JSON.stringify(item.NAME).toLowerCase().includes(searchText) || JSON.stringify(item.DESC).toLowerCase().includes(searchText);
  });
}

async extractIpParms(){
    if(this.inputMappingfields.RFCField.length > 0){
    for(let key in this.inputMappingfields.RFCField){
      this.inputMappingfields.RFCField[key].formValue ='';
      this.inputMappingfields.RFCField[key].customValue ='';
      this.inputMappingfields.RFCField[key].isCustom = false;
      this.inputMappingfields.RFCField[key].change = false;
    }
    }else{
      this.inputMappingfields.formValue ='';
      this.inputMappingfields.customValue ='';
      this.inputMappingfields.isCustom = false;
      this.inputMappingfields.change = false;
    }
  }

  disabledFormFiled(rowData:any){
    if(rowData.formValue == ''){
      rowData.isCustom = true;
    }else{
      rowData.isCustom = false;
      rowData.customValue = '';
    }
    this.inputMappingfields.change=true;
    rowData.change=true;
  }

  detectchange(parent:any,child:any){
   if(child == ''){
    parent.change=true;
   }else{
    parent.change=true;
    child.change=true;
   }
  }
  switchToExp:boolean=false;
  pickedFunctionListArr=[];
  selectedFunctionName:string;
  pickedFunctionType(event:any){
    const editor = this.editor.nativeElement as HTMLElement;
    editor.focus();
    this.selectedFunctionName=event.value
    this.pickedFunctionListArr=[]
    for(let key in this.allCalculateFunctions){
      if(this.allCalculateFunctions[key].label == event.value){
        this.pickedFunctionListArr.push(this.allCalculateFunctions[key].item)
      }
    }    
  }
  calculateTabChange(event:any){
    const editor = this.editor.nativeElement as HTMLElement;
    editor.focus();
  }
 async getBdtableList(){
      this.reteservice.getAllDbTablesInfo(this.data.nodeName,this.data.selectedsystem).subscribe((res)=>{          
      if(res.status.toLowerCase() == 'success'){
          this.dbListTable = res.tableInfo[0].tables;
      }
      });
  }

  dbTableRelTable:any=[];
  newarr:any=[];

  dbTableSelect(event:any){
     this.dbTableRelTable = [];
     this.finalFormat = []
    if(event.value != null){
       this.dbTableRelTable = this.dbFormGroup.value.parentTable.relatedTables;
     }else{
       this.dbTableRelTable = [];
    }
  }
  finalFormat:any=[];
  formatParent(){
    let parentObj:any={};
    parentObj[this.dbFormGroup.value.parentTable.tableName]=this.dbFormGroup.value.parentTable.keys;
    this.finalFormat.push(parentObj); 
    this.finalFormat = this.finalFormat.filter((item, index) => {
      let currentItemString = JSON.stringify(item);
        return index === this.finalFormat.findIndex(obj => {
          return JSON.stringify(obj) === currentItemString;
      });
  });   
  }

  getDbRelTab(event:any){
    this.finalFormat=[];
    this.newarr = this.dbFormGroup.value.refTable;
  }
  
  formatDbData(){
      let parentObj:any={};
       parentObj[this.dbFormGroup.value.parentTable.tableName]=this.dbFormGroup.value.parentTable.keys;
       this.finalFormat.push(parentObj);   
    this.dbListTable.forEach((item)=>{
        for(let key in this.newarr){
          let relObj:any={}
            if(this.newarr[key] == item.tableName){
              relObj[item.tableName]=item.keys;
              this.finalFormat.push(relObj)
            }
           
        }
    });
    
    this.finalFormat = this.finalFormat.filter((item, index) => {
      let currentItemString = JSON.stringify(item);
        return index === this.finalFormat.findIndex(obj => {
          return JSON.stringify(obj) === currentItemString;
      });
  });
  }
  openPanel:boolean=false;
  openTestData(event:any){
    let data={
      "panelName":"testData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
  openRunData(event:any){
    let data={
      "panelName":"runData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }

  selectedValue:any;
  openSapExpBuilderDialog(value,data,multi){
    this.sapExprbuildervalue = value;
    this.selectedValue={
      "name":data.NAME,
      "multi":multi}
  }

  mapIpExpValue(sapExp:any){
    if(this.selectedValue.multi){
    for(let key in this.newVal){
        if(this.newVal[key].NAME == this.selectedValue.name){
          this.newVal[key].customValue = this.sapExprbuildervalue;
        }
    }
    }else{
      this.inputMappingfields.customValue = this.sapExprbuildervalue;
    }
      sapExp.hide();
  }

removeFromMultiSelect(option: any, type: string): void {
  if (type === 'input') {
    const currentValues = this.sapFormGroup.get('rfcInputData')?.value || [];
    const updatedValues = currentValues.filter((item: any) => item.NAME !== option.NAME);
    this.sapFormGroup.patchValue({ rfcInputData: updatedValues });
  } else if (type === 'output') {
    const currentValues = this.sapFormGroup.get('rfcOutputData')?.value || [];
    const updatedValues = currentValues.filter((item: any) => item.NAME !== option.NAME);
    this.sapFormGroup.patchValue({ rfcOutputData: updatedValues });
  }
}

isOptionDisabled(option: any): boolean {
  return option.NAME === 'RETURN';
}

 processRFCData(inputData: any): RFCParameterFormat {
  const { rfcInputData, rfcOutputData } = inputData;

  const combinedData = [...rfcInputData, ...rfcOutputData];
  const uniqueParameters: { [key: string]: RFCField } = {};

  combinedData.forEach((param) => { uniqueParameters[param.NAME] = param });

  const RFCInput = this.extractRfcNames(rfcInputData);
  const RFCOutput = this.extractRfcNames(rfcOutputData);
  const RFCParameter = Object.values(uniqueParameters);

  return { RFCInput, RFCOutput, RFCParameter };
}

extractRfcNames(rfcData:RFCField[]):string[]{
  return rfcData.map((param) => param.NAME);
}

}
