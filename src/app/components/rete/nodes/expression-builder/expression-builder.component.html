<p-confirmPopup></p-confirmPopup>
<i class="pi pi-list cursor help-icon" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData(openPanel = true)" pTooltip="Open Test Data"></i>
<div [ngSwitch]="this.data.nodeName">
    <p-progressBar mode="indeterminate" [style]="{ height: '6px' }" *ngIf="isLoader"></p-progressBar>
    <div *ngSwitchCase="'calculate'">
            <p-tabView class="tabview" [style]="{ width: '430px' }" (onChange)="calculateTabChange($event)"><!--(onChange)="calculateTabChange($event)"-->
                <p-tabPanel header="Form">
                    <ng-template pTemplate="header">
                        <span>Form</span>
                    </ng-template>
                    <p-listbox [options]="listdata" filterBy="label,items" [group]="true"  class="explistbox" [listStyle]="{'height': '200px'}"
                    [filter]="true" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                    <ng-template let-list pTemplate="item">
                        <div class="flex align-items-center gap-2">
                            <div>{{ list.label }}</div>
                        </div>
                    </ng-template>
                    <ng-template let-group pTemplate="group">
                        <div class="flex align-items-center">
                            <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                        </div>
                    </ng-template>
                </p-listbox>
                </p-tabPanel>
                <p-tabPanel header="Function">
                    <ng-template pTemplate="header">
                        <span>Function</span>
                    </ng-template>
                <div class="mt-2">
                <p-fieldset>
                    <ng-template pTemplate="header">
                        <p-dropdown  (onChange)="pickedFunctionType($event)"  placeholder="Select..." 
                                [options]="allCalculateFunctions"  optionLabel="label" optionValue="label"  styleClass="w-full p-inputtext-sm" appendTo="body">
                        </p-dropdown>
                    </ng-template>
                    <ng-template pTemplate="content">
                        <p-listbox [options]="pickedFunctionListArr[0]" class="condition"  optionLabel="label" optionValue="value" 
                        (onChange)="seteditorvalue($event,this.data.nodeName,selectedFunctionName)" [listStyle]="{'height': '200px'}">
                            <ng-template let-list pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div><span class="material-icons">{{list.icon}}</span>&nbsp;&nbsp;{{ list.label }}</div>
                                </div>
                            </ng-template>
                        </p-listbox>
                    </ng-template>
                </p-fieldset>
                </div>
                </p-tabPanel>
                <div class="justify-content-center mt-2">
                    <textarea style="display: none;" id="float-input" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30"
                        pInputTextarea [(ngModel)]="expressionBuilderValue" [autoResize]="false"></textarea>
                        <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event,this.data.nodeName)" (keyup)="sendit()" ></div>

                </div>
            </p-tabView>
            <div class="flex justify-content-center mt-2">
                <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
                <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
            </div>
    </div>
    <div *ngSwitchCase="'sap'">
        <div class="w-30rem" *ngIf="this.data.inputName === 'function'">
                <div class="p-inputgroup p-1">
                    <input type="text" pInputText placeholder="Keyword" [(ngModel)]="searchKeyword"  (keydown.enter)="getExplorerData(searchKeyword)" (input)="onSearchInput($event)" />
                    <button icon="pi pi-search" pButton (click)="getExplorerData(searchKeyword)"></button>
                </div>
                <div class="justify-content-center mt-2 p-1">
                    <p-listbox [options]="sapfunctionName" [listStyle]="{'height': '200px'}" 
                        [filter]="false" optionLabel="key" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                    </p-listbox>
                </div>
                <div class="mt-2 p-1">
                    <textarea [disabled]="true" #expressionArea placeholder="Expression" pInputTextarea class="w-full h-6rem line-height-3 expBuilderEditor"
                        [(ngModel)]="expressionBuilderValue" [autoResize]="false" appendTo="body"></textarea>
                </div>
        </div>
        <div class="w-30rem" *ngIf="this.data.inputName === 'parameters'">
             <form [formGroup]="sapFormGroup">
                <p-card header="RFC Input" class="sapCard">
                     <p-multiSelect class="multiSelect" appendTo="body" [showToggleAll]="false"  [options]="rfcInputArray" formControlName="rfcInputData" defaultLabel="Input's"
                        optionLabel="NAME" display="chip"  optionDisabled="MANDATORY"><!--(onChange)="selectRfcIpOp($event.itemValue,'input')"-->
                        <ng-template let-value pTemplate="selectedItems">
                            <div class="p-multiselect-token p-multiselect-token-container" *ngFor="let option of value">
                                <div class="p-multiselect-token-label" [ngClass]="{ '_required': option.MANDATORY}">{{ option.NAME }}</div>
                                <span *ngIf="!option.MANDATORY" class="p-multiselect-token-icon pi pi-times-circle" (click)="removeFromMultiSelect(option, 'input')"></span>
                            </div>
                        </ng-template>
                        <ng-template let-option pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div [ngClass]="{ '_required': option.MANDATORY}" >{{ option.NAME }}</div>
                            </div>
                        </ng-template>
                    </p-multiSelect>  
                </p-card>
                <p-card header="RFC Output" class="sapCard pt-1">
                    <p-multiSelect class="multiSelect" appendTo="body" [showToggleAll]="false" [options]="rfcOutputArray" formControlName="rfcOutputData" defaultLabel="Output's"
                        optionLabel="NAME" display="chip" [optionDisabled]="isOptionDisabled">
                        <ng-template let-option pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div [ngClass]="{ '_required': option.NAME ==='RETURN'}">{{ option.NAME }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-value pTemplate="selectedItems">
                            <div class="p-multiselect-token p-multiselect-token-container" *ngFor="let option of value">
                                <div class="p-multiselect-token-label" [ngClass]="{ '_required': option.NAME ==='RETURN'}">{{ option.NAME }}</div>
                                <span *ngIf="option.NAME !== 'RETURN'" class="p-multiselect-token-icon pi pi-times-circle" (click)="removeFromMultiSelect(option, 'output')"></span>
                            </div>
                        </ng-template>
                    </p-multiSelect>
                </p-card>
            </form>
        </div>

        <div *ngIf="this.data.inputName === 'rfcinputs'" class="w-30rem mt-1">
            <div *ngIf="isLoader" >
                <div class="border-round border-1 surface-border p-2 surface-card">
                    <p-skeleton height="2rem" styleClass="mb-2 w-2" />
                    <p-skeleton height="3rem" />
                </div>
                <div class="flex justify-content-center mt-2">
                    <p-skeleton width="5rem" height="2rem" styleClass="mr-2"/>
                    <p-skeleton width="5rem" height="2rem" />
                </div>
            </div>
            <div *ngIf="inputMappingfields != 'undefined' ">             
                <div *ngIf="inputMappingfields?.RFCField?.length != 0" class="max-h-25rem overflow-y-auto p-2">
                    <span class="p-input-icon-left w-full" *ngIf="inputMappingfields?.RFCField?.length != 0 && !isLoader">
                        <input type="text" pInputText [(ngModel)]="searchText" placeholder="Search..." (input)="onInput($event)" class="w-full">
                        <i class="pi pi-search"></i>
                    </span>
                    <p-messages *ngIf="newVal?.length == 0" [(value)]="messages"  [enableService]="false" [closable]="false" />
                    <p-card class="sapCard"  *ngFor="let fields of newVal " >
                        <div class="flex flex-column gap-2">
                            <label htmlFor="username" class="label font-semibold">{{fields.NAME}}</label>
                            <p-dropdown *ngIf="!fields.isCustom" class="integrationNode" appendTo="body"  placeholder="Select...." (onChange)="disabledFormFiled(fields)"
                                [options]="allFormFields" [(ngModel)]="fields.formValue" [group]="true">
                                <ng-template let-group pTemplate="group">
                                        <div class="flex align-items-center">
                                            <span style="font-size: 16px;font-weight: 500;">{{ group.label}}</span>
                                        </div>
                                    </ng-template>
                            </p-dropdown>
                            <p-dropdown *ngIf="fields.isCustom" class="integrationNode" appendTo="body"  placeholder="Select...." (onChange)="disabledFormFiled(fields)"
                                [options]="allFormFields"  [(ngModel)]="fields.formValue" [group]="true">
                                <ng-template let-group pTemplate="group">
                                        <div class="flex align-items-center">
                                            <span style="font-size: 16px;font-weight: 500;">{{ group.label}}</span>
                                        </div>
                                    </ng-template>
                            </p-dropdown> 
                            <span class="p-float-label p-input-icon-right" *ngIf="fields.isCustom">
                                <input pInputText class="p-inputtext-sm w-full"   [(ngModel)]="fields.customValue" (change)="detectchange(inputMappingfields,fields)"/>
                                <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="[sapExp.toggle($event),openSapExpBuilderDialog(fields.customValue,fields,true)]" ></i>
                            </span>
                        </div>            
                    </p-card>
                </div>
                <div style="max-height: 150px !important;">
                    <p-card class="sapCard" *ngIf="inputMappingfields?.RFCField.length == 0">
                        <div class="flex flex-column gap-2">
                            <label htmlFor="username" class="label font-semibold">{{inputMappingfields!.NAME}}</label>
                            <p-dropdown class="integrationNode" appendTo="body"  placeholder="Select...." (onChange)="disabledFormFiled(inputMappingfields)"
                            [options]="allFormFields"  [(ngModel)]="inputMappingfields.formValue" [group]="true">
                            <ng-template let-group pTemplate="group">
                                <div class="flex align-items-center">
                                    <span style="font-size: 16px;font-weight: 500;">{{ group.label}}</span>
                                </div>
                            </ng-template>
                    </p-dropdown> 
                    <span class="p-float-label p-input-icon-right" *ngIf="inputMappingfields.isCustom" >
                            <input class="p-inputtext-sm w-full"  pInputText  [(ngModel)]="inputMappingfields.customValue" (change)="detectchange(inputMappingfields,'')" />
                            <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="[sapExp.toggle($event),openSapExpBuilderDialog(inputMappingfields.customValue,inputMappingfields,false)]"></i>
                        </span>
                        </div>            
                    </p-card>
                </div>
            </div>
        </div>
        <div *ngIf="this.data.inputName === 'data'">
            <div class="justify-content-center" style="width: 430px;">
                <p-listbox [options]="listdata" filterBy="label,items" [group]="true" [listStyle]="{ 'height': '200px'}" class="explistbox"
                    [filter]="true" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                    <ng-template let-group pTemplate="group">
                        <div class="flex align-items-center">
                            <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                        </div>
                    </ng-template>
                </p-listbox>
            </div>
            <div class="justify-content-center mt-2">
                <!-- <textarea style="display: none;" id="float-input" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30"
                    pInputTextarea [(ngModel)]="expressionBuilderValue" [autoResize]="false"></textarea>
                    <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" >
                    </div> -->
                    <textarea id="float-input" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30" 
                        pInputTextarea [(ngModel)]="expressionBuilderValue" HighlightDirective [autoResize]="false" ></textarea>
                  <!-- <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" > -->
                <!-- </div> -->
            </div> 
            
        </div>
        <div class="flex justify-content-center mt-2" *ngIf="this.data.inputName !== 'rfcinputs' || !isLoader">
            <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
            <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
        </div>
    </div>
    <div *ngSwitchCase="'query'">
        <div *ngIf="this.data.callerformfield == 'keys'" style="width: 430px;">
            <div class="justify-content-center">
                <div  *ngIf="this.error">
                    <p-messages  severity="warn" class="expwarnMsg" styleClass="mt-0" >
                      <ng-template pTemplate>
                        <div class="ml-2">Please select the system</div>
                    </ng-template>
                    </p-messages>
                  </div>
            <form [formGroup]="dbFormGroup" style="padding: 1px;width: 430px;">
            <div style="display: flex;">
                <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                    <p-dropdown class="integrationNode" placeholder="Select..."  styleClass="w-full p-inputtext-sm" appendTo="body"
                     autoDisplayFirst="false" formControlName="parentTable" [options]="dbListTable" optionLabel="tableName"
                    (onChange)="dbTableSelect($event)" (onHide)="formatParent()" ></p-dropdown><!--(onFocus)="getBdtableList()"showClear="true" -->
                    <label for="float-label">Table</label>
                </span>
              </div>
                <span class="p-float-label mt-4 mb-2 p-0">
                       <p-multiSelect class="multiSelect" appendTo="body" formControlName="refTable"  [options]="dbTableRelTable" (onChange)="getDbRelTab($event)" (onPanelHide)="formatDbData()">
                        </p-multiSelect>  
                    <label htmlFor="Description">Related Tables</label>
                  </span>
                </form>
            </div>
            <div class="flex justify-content-center mt-2">
                <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
                <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
            </div>
        </div>
        <div *ngIf="this.data.callerformfield != 'keys'"  style="width: 430px;">
            <div class="justify-content-center">
                <p-listbox [options]="listdata" [group]="true" filterBy="label,items" [listStyle]="{ 'height': '200px'}" class="explistbox"
                    [filter]="true" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                    <ng-template let-group pTemplate="group">
                        <div class="flex align-items-center">
                            <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                        </div>
                    </ng-template>
                </p-listbox>
            </div>
            <div class="justify-content-center mt-2">
                <textarea id="float-input" style="display: none;" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30"
                    pInputTextarea [(ngModel)]="expressionBuilderValue" [autoResize]="false"></textarea>
                    <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" >
                    </div>
            </div>
            <div class="flex justify-content-center mt-2">
                <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
                <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
            </div>
        </div>
    </div>
    <div *ngSwitchCase="'ForLoop'">
        <div class="justify-content-center" style="width: 430px;">
            <p-listbox [options]="listdata" filterBy="label,items" [group]="true" [listStyle]="{ 'height': '200px'}" class="explistbox"
                [filter]="true" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                <ng-template let-group pTemplate="group">
                    <div class="flex align-items-center">
                        <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                    </div>
                </ng-template>
            </p-listbox>
        </div>
        <div class="justify-content-center mt-2">
            <textarea style="display: none;" id="float-input" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30"
                pInputTextarea [(ngModel)]="expressionBuilderValue" [autoResize]="false"></textarea>
                <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" >
                </div>
        </div>
        <div class="flex justify-content-center mt-2">
            <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
            <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
        </div>
    </div>
    <div *ngSwitchDefault>
        <div class="justify-content-center" style="width: 430px;">
            <p-listbox [options]="listdata" filterBy="label,items" [group]="true" [listStyle]="{ 'height': '200px'}" class="explistbox"
                [filter]="true" (onChange)="seteditorvalue($event,this.data.nodeName,'all')">
                <ng-template let-group pTemplate="group">
                    <div class="flex align-items-center">
                        <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                    </div>
                </ng-template>
            </p-listbox>
        </div>
        <div class="justify-content-center mt-2" style="width: 430px;">
            <textarea id="float-input" style="display: none;" #expressionArea class="w-full " placeholder="Expression" rows="3" cols="30" 
                pInputTextarea [(ngModel)]="expressionBuilderValue" HighlightDirective [autoResize]="false" ></textarea>
                  <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" >
                </div>
        </div>
        <div class="flex justify-content-center mt-2">
            <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="close($event,'cancel')" ></p-button>
            <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (onClick)="save('save')"></p-button>
        </div>
    </div>
</div>

<p-overlayPanel #sapExp [style]="{'width':'430px'}">
    <p-listbox [options]="sapExpBuild" filterBy="label,items" [group]="true" [style]="{'width':'400px'}" [listStyle]="{ 'height': '250px'}" class="explistbox"
                [filter]="true" (onChange)="setSapeditorvalue($event)">
                <ng-template let-group pTemplate="group">
                    <div class="flex align-items-center">
                        <span style="font-size: 16px;font-weight: 600;">{{ group.label }}</span>
                    </div>
                </ng-template>
            </p-listbox>
            <div class="justify-content-center mt-2">
                <textarea  id="float-input" #sapExpressionArea class="w-full " chip="true" placeholder="Expression" rows="3" cols="30"
                    pInputTextarea [(ngModel)]="sapExprbuildervalue" [autoResize]="false"></textarea>
                    <!-- <div id="editor" #editor autofocus="true" class="editor" pInputTextarea contenteditable="true" (keydown)="handleKeyDown($event)" (keyup)="sendit()" >
                    </div> -->
            </div>
            <div style="text-align: center;" class=" mt-2"  >
                <p-button label="Close" styleClass="p-button-sm p-button-danger mr-2" icon="pi pi-times" (click)="sapExp.hide()" ></p-button>
                <p-button label="Save" styleClass="p-button-sm p-button-sm mt-2 " icon="pi pi-save" (click)="mapIpExpValue(sapExp)" ></p-button>
              </div> 
</p-overlayPanel>
