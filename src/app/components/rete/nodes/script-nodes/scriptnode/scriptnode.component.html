<!--new expand panel-->
<div class="nodes-input-socket" *ngFor="let input of inputs">
    <rete-socket rete-socket [io]="input" [socket]="input.socket"></rete-socket>
  </div>
  <div style="display: inline-flex;" >
    <div class="node-container" [ngClass]="[selected()]">
      <p-panel toggleable="true" [collapsed]="openPanelForNew" expandIcon="pi pi-chevron-up" collapseIcon="pi pi-chevron-down"
        class="panelOpen" (onBeforeToggle)="[panelBtn($event,node),expbuilder.hide()]">
        <ng-template pTemplate="header">
          <!-- <i class="{{node.data.icon}} nodeIcon" *ngIf="node.name !== 'sap'" pTooltip="{{this.nodeName}}"
            tooltipPosition="top"></i> -->
            <span class="nodeIcon" pTooltip="{{this.nodeName}}" tooltipPosition="top" *ngIf="node.name !== 'sapa'"><img class="pmgImg" src="assets/icon/{{this.nodeIcon}}.png"></span>
          <!-- <span class="material-icons nodeIcon2" pTooltip="Node Info" tooltipPosition="top" *ngIf="node.name !== 'sap'">{{node.data.icon}}</span> -->
          <svg *ngIf="node.name === 'sap' " class="nodeIconSap" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="50" height="43" viewBox="0 0 1080 1080"
            xml:space="preserve">
            <g transform="matrix(1 0 0 1 540 540)" id="02953293-5fd9-44ca-9c32-7bfc459898bf">
              <rect
                style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;"
                vector-effect="non-scaling-stroke" x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
            </g>
            <g transform="matrix(1 0 0 1 540 540)" id="70ac1fec-5595-47b2-8de5-10e0fb839d51"></g>
            <g transform="matrix(NaN NaN NaN NaN 0 0)">
              <g></g>
            </g>
            <g transform="matrix(21.6 0 0 21.6 540 540)">
              <path
                style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                transform=" translate(-25.5, -24)"
                d="M 1 12 L 1 36 L 26 36 L 50 12 Z M 8.398438 18 C 11.800781 18 12.902344 18.800781 12.902344 18.800781 C 12.902344 18.800781 12.300781 20.300781 12 21.097656 C 11 20.699219 7 20 7 21.5 C 7 22.101563 7.199219 22.5 10.199219 23.199219 C 11.398438 23.5 13.199219 24.101563 13.597656 25.902344 L 16.699219 18 L 20.199219 18 L 24 27.5 L 24 18 L 28.5 18 C 31 18 33 20 33 22.5 C 33 25 31 27 28.5 27 L 27 27 L 27 30 L 21.5 30 L 20.902344 28.199219 C 20.199219 28.5 19.398438 28.699219 18.5 28.699219 C 17.601563 28.699219 16.800781 28.5 16.097656 28.199219 L 15.5 30 L 12 30 L 12.5 28.800781 C 11.601563 29.5 10.199219 30 8.398438 30 C 5.101563 30 4 29.097656 4 29.097656 L 4.800781 26.5 C 6.699219 27.699219 10.097656 27.5 10.097656 26.5 C 10.097656 25.5 8.601563 25.402344 6.601563 24.800781 C 4.699219 24.199219 4 22.699219 4 21.402344 C 4 20.101563 5 18 8.398438 18 Z M 27 21 L 27 24 L 28.5 24 C 29.300781 24 30 23.300781 30 22.5 C 30 21.699219 29.300781 21 28.5 21 Z M 18.5 21.402344 L 17 25.699219 C 17.398438 26 17.898438 26.097656 18.5 26.097656 C 19.101563 26.097656 19.601563 26 20 25.699219 Z"
                stroke-linecap="round" />
            </g>
            <g transform="matrix(8.32 0 0 5.09 327.3 508.56)" id="14e3cb22-c1f3-4f2f-8590-a9fa04f9fa8c">
              <rect
                style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                vector-effect="non-scaling-stroke" x="-33.0835" y="-33.0835" rx="0" ry="0" width="66.167"
                height="66.167" />
            </g>
            <g transform="matrix(2.47 0 0 3.38 626.41 497.65)" id="49a1d38c-f87a-41e6-90d8-7141810959e1">
              <circle
                style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                vector-effect="non-scaling-stroke" cx="0" cy="0" r="35" />
            </g>
          </svg>
          <span class="ml-5">
            <div *ngIf="!this.readonlyOnDisable">
             <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && displayBtn"  >{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
             <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && displayBtn">{{node.name}}</span>
               <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && !displayBtn" (click)="focusIn()">{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
               <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && !displayBtn" (click)="focusIn()">{{node.name}}</span>
               <input *ngIf="nodeDesc && displayBtn"  id="inputBox" (click)="focusIn()" (focusout)="focusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
               <input *ngIf="nodeDesc && !displayBtn"   id="inputBox" (click)="focusIn()" (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
              </div>
              <div *ngIf="this.readonlyOnDisable">
                <span class="spanText">{{node.data.description}}</span>
              </div>
             </span>
        </ng-template>
        <ng-template pTemplate="content">
          <div class="cardBody">
            <div *ngIf="this.duplicate" style="margin-top: 10px; margin-right: 8px;">
              <p-message class="warnMsg" severity="warn" text="Node name should be unique."></p-message> 
            </div>
            <form [formGroup]="nodeformdata" novalidate style="border: 0px; margin-right: 5px;padding-left: 2px;margin-bottom: 10px;"  autocomplete="off" *ngIf="!displayBtn">
              <div [ngSwitch]="node.name">
  <!--EXECUTE AP STARTS-->
                <div *ngSwitchCase="'execjava'">
                    <!--TYPE === "LIST"-->
                    <div style="display: flex;">
                      <span class="p-float-label mt-4 w-full">
                        <p-dropdown class="integrationNode" [options]="libraries" [autoDisplayFirst]="true"
                          appendTo="body" (onChange)="getfunctions($event.value)" placeholder="Select...."
                          [formControl]="library">
                        </p-dropdown>
                        <label class="_required">Library</label>
                      </span>
                    </div>
                    <div>
                      <span class="p-float-label mt-4" style="padding: 0px;">
                        <p-dropdown class="integrationNode" [options]="functions" [autoDisplayFirst]="true"
                          appendTo="body" placeholder="Select...." [formControl]="function">
                        </p-dropdown>
                        <label class="_required">PA function</label>
                      </span>
                    </div>
                    <!--END-->
                    <!--Buttons starts-->
                    <div class="mt-3 mb-2 flex justify-content-evenly">
                      <p-button icon="pi pi-sort-alpha-up" (onClick)="addRow('','text', '', [])"  pTooltip="Add Text"></p-button>
                      <p-button icon="pi pi-sort-numeric-up" (onClick)="addRow('','number', '', [])" pTooltip="Add Number"></p-button>
                      <p-button icon="pi pi-file-word" (onClick)="addRow('','list', '', [])" pTooltip="Add List"></p-button>
                      <p-button icon="pi pi-eye-slash" (onClick)="addRow('','sensitive', '', [])" pTooltip="Add Sensitive"></p-button>
                    </div>
                    <!--ENDS-->
                  <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                    <div [formGroupName]="ind" class="mt-2">
                      <p-card class="formCard">
                        <div class="flex justify-content-end" *ngIf="field.value.type === 'list'">
                            <span>
                              <i class="pi pi-plus mr-3 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;" (click)="addlistoptionvalues(ind)"></i> 
                             </span>
                          </div>
                        <div class="row p-0 m-0">
                          <!--TYPE == "TEXT"-->
                            <span class=" p-float-label p-input-icon-right mt-3 col-5 p-0 mx-2">
                              <input pInputText  formControlName="name" />
                              <label>Input's</label>
                            </span>
                            <span *ngIf="field.value.type !== 'list'" class=" p-float-label p-input-icon-right mt-3 col-5 p-0">
                              <input pInputText  formControlName="value" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,field.value.value, field.value.type, ind)"></i>
                              <label>Input's</label>
                            </span>
                            <span *ngIf="field.value.type === 'list'" class=" p-float-label p-input-icon-right mt-3 col-5 p-0">
                              <input pInputText  formControlName="value" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,field.value.value, field.value.type, ind)"></i>
                              <label>Input's</label>
                            </span>
                            <i class="pi pi-trash mt-2  col-1 cursor" *ngIf="!readonlyOnDisable" style="font-size: 1rem;color: rgb(255, 97, 97);"
                              (click)="removeRow(ind)"></i>
                        </div>
                        <div  *ngIf="field.value.type === 'list'" formArrayName="listvalues">
                          <div *ngFor="let listoption of getlistcontrol(field); index as i" >
                            <div class="row mx-2">
                            <span class="p-float-label p-input-icon-right mt-4 col-10 p-0">
                              <input pInputText  [formControlName]="i" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,listoption.value, field.value.type, ind,i)"></i>
                              <label>Option value</label>
                            </span>
                            <i class="pi pi-trash col-1 cursor" *ngIf="!readonlyOnDisable"style="font-size: 1rem;color: rgb(255, 97, 97);margin-left: 16px;"
                              (click)="removeOptionvalue(ind, i)"></i>
                            </div>
                          </div>
                        </div>
                      </p-card>
                      <!--END-->
                    </div>
                  </div>
                </div>
  <!--EXICUTION PA ENDS-->
  <!--Execute Java Script STARTS-->
                <div *ngSwitchCase="'execjavascript'">
                  <div style="display: flex;">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText [formControl]="inputparams" />
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, inputparams?.value, 'text')"></i>
                      <label class="_required">Input Parameters</label>
                  </span>
                  </div>
                   <p-messages *ngIf="!inputparams.value"
                    [(value)]="scriptWarningMessages" 
                    [enableService]="false" 
                    [closable]="false" />
                   <!--Buttons starts-->           
                   <div class="mt-3" style="text-align: center;">
                    <p-button icon="pi pi-pencil"  label="Open Editor" [disabled]="!inputparams.value" styleClass="p-button-sm mr-2" (click)="openGenerater('js')" ></p-button>
                    <span *ngIf="showTick" class="material-icons" style="color: green;">done_all</span>
                  </div>
                   <!--Buttons ENDS-->
                </div>
  <!--Execute Java Script ENDS-->
  <!--Execute Python STARTS-->
                <div *ngSwitchCase="'execpython'">
                
                  <div style="display: flex;">
                    <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                      <input pInputText [formControl]="inputparams"/>
                      <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable" (click)="openExpBuilderDialog($event, inputparams?.value, 'text')"></i>
                      <label class="_required">Input Parameters</label>
                  </span>
                  </div>
                  <p-messages *ngIf="!inputparams.value"
                    [(value)]="scriptWarningMessages" 
                    [enableService]="false" 
                    [closable]="false" />
                   <!--Buttons starts-->           
                   <div class="mt-3" style="text-align: center;">
                    <p-button icon="pi pi-pencil" label="Open Editor" styleClass="p-button-sm mr-2" [disabled]="!inputparams.value" (click)="openGenerater('python')" ></p-button>
                    <span *ngIf="showTick" class="material-icons" style="color: green;">done_all</span>
                  </div>
                   <!--Buttons ENDS-->
                </div>
  <!--Execute Python ENDS-->
              </div>
            </form>
          </div>
        </ng-template>
      </p-panel>
    </div>
    <div class="m-1 mb-0" *ngIf="!readonlyOnDisable && !displayBtn">
      <div style="display:table-caption;">
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" *ngIf="node.name == 'execjava'"
            pTooltip="Save" tooltipPosition="right" [disabled]="javaValidataFields()" (onClick)="saveNodedata()"></p-button><!---->
            <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" *ngIf="node.name != 'execjava'"
            pTooltip="Save" tooltipPosition="right" [disabled]="validataFields()" (onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-question" styleClass="p-button-rounded p-button-text p-button-raised p-button-outlined" class="nodeBtnbg"
          pTooltip="Info" (onClick)="goToLink(helpurl)" tooltipPosition="right"></p-button>
        <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger" class="nodeBtn"
          pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button>
      </div>
    </div>
  </div>
  <div class="nodes-outputs">
    <div *ngFor="let output of outputs" class="output-sockets-success">
      <rete-socket *ngIf="output.name == 'Success'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
    <div *ngFor="let output of outputs" class="output-sockets-error">
      <rete-socket *ngIf="output.name == 'Error'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
  </div>
  <p-overlayPanel #expbuilder [dismissable]="true" styleClass="add-edit">
    <div style="padding-bottom: 10px;"><b>Expression Builder</b><span style="float: right;">
      <i class="pi pi-times" id="expBuildClose" style="color:transparent" (click)="expbuilder.hide()"></i>
                <!-- <i class="fa-light fa-chevron-down"></i> -->
      <i class="pi pi-list cursor" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData()"  pTooltip="Open Test Data"></i>
    </span></div>
    <ng-template pTemplate="body">
      <app-expression-builder [data]="expBuildVal"
        (overlayclose)="overlayCloseExpBuilder($event)"></app-expression-builder>
    </ng-template>
  </p-overlayPanel>
  <p-confirmPopup></p-confirmPopup>
  <div *ngIf="selected() && displayBtn" style="margin-left: 150px;height: 45px;"><app-add-nodes ></app-add-nodes></div>
