.nodeIcon{
    // color: var(--orange-500)!important;
    // padding: 16px; 
    width: 50px;
    height: 47px;
    background: var(--orange-100)!important;
  }
  ::ng-deep .formCard .p-card .p-card-body{
    padding: 0px !important;
  }
  .p-inputtext {
    width: -webkit-fill-available;
  }
  ::ng-deep .selectBtn .p-button {
    font-size: 0.875rem !important;
    padding: 8px !important;
    box-shadow: none !important;
  
  } 
  .selected {
    // box-shadow: 10px 5px 5px red;
    border: 2px solid rgb(145, 149, 139);
  // box-shadow:0 0 6px 6px rgb(145, 149, 139);
  }
  .pmgImg{
    margin-top: 8px;
    margin-left: 8px;
    margin-bottom: 0px;
    height: 32px !important;
    width: 32px !important;
  }