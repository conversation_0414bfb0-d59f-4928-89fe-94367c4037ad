import { Component, ChangeDetectorRef} from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
import { ConfirmationService, Message } from 'primeng/api';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { ScriptEditorDialogComponent} from '../script-editor-dialog/script-editor-dialog.component';
import { ExpressionBuilderComponent } from '../../expression-builder/expression-builder.component';

@Component({
  selector: 'app-scriptnode',
  templateUrl: './scriptnode.component.html',
  styleUrls: ['./scriptnode.component.scss'],
  providers: [NodeService,DialogService]
})
export class ScriptnodeComponent extends NodeComponent {
  scriptWarningMessages: Message[] | undefined;
  openPanelForNew:boolean;
  displayBtn:boolean=false;
  desc:any;
  deletedData:any;
  nodeDesc: boolean = false;
  nodeformdata: FormGroup;
  jsonLogicRule: any;
  dataItem:any;
  DispEditIcon:boolean=false;
  helpurl = 'https://docs.unvired.com/builder/flows/';
  libraries: any;
  functions: any;
  unSaved: boolean;
  readonlyOnDisable:boolean=false;
   ref: DynamicDialogRef | undefined;
  //  library = new FormControl();
  //  function = new FormControl();
  // inputparams = new FormControl();
   outputparams = new FormControl();
  snippet = new FormControl();
  nodeName:any;
  nodeIcon:any;
   constructor(
    protected override service: NodeService,
    protected override cdr: ChangeDetectorRef,
    private utilservice: UtilsService,private fb: FormBuilder,
    private reteservice: ReteService,private confirmationService: ConfirmationService,
    public dialogService: DialogService,
  ) {
    super(service, cdr);this.displayBtn=true;
    this.readonlyOnDisable = this.reteservice.getDisableWfData();
    this.scriptWarningMessages = [{ severity: 'warn', detail: 'Input Paramters is required!!!' }];
  }
  ngAfterViewInit(){  
    this.displayBtn = true;
        if(this.node.hasOwnProperty('new')){
          this.desc = this.node.data['name'];
          let event={
            collapsed:true
          }
          this.openPanelForNew=false;
          this.panelBtn(event,this.node);
          this.focusIn();
         }else{
          this.desc = this.node.data['description'];
          this.openPanelForNew=true;
         }
         this.nodeName= this.node.name;         
         switch(this.node.name){
          case "execjava":
            this.nodeIcon = 'java';
            break;
            case "execjavascript":
              this.nodeIcon = 'javascript';
              break;
              case "execpython":
                this.nodeIcon = 'python';
                break;
         }
     }

 //open panel 
 async panelBtn(event:any,node:any){       
  if(event.collapsed == 'true' || event.collapsed == true){ 
    this.displayBtn = false;
      if (node.data.inputs == undefined) {

      } else {
        let newInput = node.data.inputs;
        node.data.input = newInput;
        delete node.data.inputs;
      }
      const data = {
        nodeId: node.id,
        node: this.node,
        nodeData: node.data,
        nodeName: node.name,
        formId: this.editor['workflow'].formId,
        editor: this.editor
      }
      this.dataItem = data;      
      await this.patchFormData();
      await this.setinputform(); 
      this.reteservice.pushNodeId(this.dataItem.nodeId);
  }else{
    if(node.data.input){
    let newInput=node.data.input;
    node.data.inputs=newInput;
    delete node.data.input;
    }
    this.displayBtn = true;
    this.nodeDesc=false;
    this.DispEditIcon=false;
    this.reteservice.popNodeId(this.dataItem.nodeId);

  }
}

goToLink(url: string){
  window.open(url, "_blank");
}
AllWfName:any;
  focusIn() {
    if (this.nodeDesc == false) {
      this.nodeDesc = true;
      window.setTimeout(function () {
        document.getElementById('inputBox').focus();
      }, 10);
    } else {
     
    }
  }
duplicate:boolean=false;
nodeNameChange:boolean=false;
  FocusOut(){
    let oldName = this.node.data['name'];
    if(this.nodeDesc != false){
      this.node.data['description'] = this.desc;
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      this.nodeDesc = false;
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      }
    }else{
      this.nodeDesc = true;
    }
    if(this.node.data['name']){
    }else{
      this.desc='untitled';
      this.node.data['description'] = this.desc;
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      } 
    }
    if(this.node.data['name'] != 'untitled'){
      if((oldName != this.node.data['name']) && (oldName != 'untitled')){
      this.reteservice.flowTechName.next("change");
       }
    }
  }
  checkIfDuplicateExists(arr) {
    return new Set(arr).size !== arr.length
}
async patchFormData(){
  let wfnodenames= this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
  this.helpurl = this.helpurl + '#' + this.dataItem.nodeData.wfName;
  const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
  if (index > -1) {
    wfnodenames.splice(index, 1);
  }
  this.nodeformdata = this.fb.group({
    name: [this.dataItem.nodeData.name],
    description: [this.dataItem.nodeData.description]
  });
}
checkvalues() {
  this.nodeformdata.valueChanges.subscribe( (value) => {
    this.unSaved = true;
  } );
}
get input() {
  return this.nodeformdata.get('input') as FormArray;
}
get inputparams() {
  return this.nodeformdata.get('inputparams');
}

addRow(paramname: string, paramtype: string, paramvalue: string, listvalues: any) {
  this.nodeformdata.markAsDirty();
  if(paramtype === 'list') {
    this.input.push(
      this.fb.group({
        name: [paramname],
        type: [paramtype],
        value: [paramvalue],
        listvalues: this.fb.array(listvalues)
      })
    );
  } else {
    this.input.push(this.fb.group({
      name: [paramname],
      type: [paramtype],
      value: [paramvalue]
    }));
  }
}
removeOptionvalue(index, i) {
  const control = this.input.controls[index].get('listvalues') as FormArray;
  control.removeAt(i);
}
removeRow(index) {
  this.input.removeAt(index);
}
addlistoptionvalues(i) {  
  const control = this.input.controls[i].get('listvalues') as FormArray;
  control.push(new FormControl(''));
}
getlistcontrol(field) {
  return field.controls.listvalues.controls;
}
get function() {
  return this.nodeformdata.get('function');
}
get library() {
  return this.nodeformdata.get('library');
}
async setinputform() {
   switch (this.node.name) {
    case 'execjava':
      let lib:any =this.node.data['library'];      
      this.getlibraries();   
      if (this.node.data['library'] != "" || this.node.data['function'] != "" || (this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0)) {
        if(lib != ""){
          this.getfunctions(lib);
        }
        this.nodeformdata.addControl("library",this.fb.control(this.dataItem.nodeData.library));
        this.nodeformdata.addControl("function",this.fb.control(this.dataItem.nodeData.function));             
        this.nodeformdata.addControl('input', this.fb.array([]));
          if (this.node.data['input']) {
              let inputsArray=[];
              inputsArray.push(this.node.data['input'])
              inputsArray[0].forEach((pair) => {
               if (pair.listvalues) {
                 this.addRow(pair.name, pair.type, pair.value, pair.listvalues);
               } else {
                 this.addRow(pair.name, pair.type, pair.value, []);
               }
             });
          }
      } else {
        this.nodeformdata.addControl('library', this.fb.control(''));
        this.nodeformdata.addControl('function', this.fb.control(''));
        this.nodeformdata.addControl('input', this.fb.array([]));        
       }
      
       
      break;
      case 'execjavascript':
      case 'execpython':                
        if(this.node.data.hasOwnProperty('inputparams') || this.node.data.hasOwnProperty('snippet')){
          let snippet:any=this.node.data['snippet'];
          if(snippet != ''){
            this.showTick=true;
          }else{
              this.showTick=false;
          }
        this.nodeformdata.addControl("inputparams",this.fb.control(this.dataItem.nodeData.inputparams));
        this.inputparams.patchValue(this.dataItem.nodeData.inputparams)
        this.nodeformdata.addControl("snippet",this.fb.control(atob(snippet)));
        this.nodeformdata.addControl("outputparams",this.fb.control(this.dataItem.nodeData.outputparams));                
      } else {
        this.nodeformdata.addControl("inputparams", this.fb.control(""));
        this.nodeformdata.addControl("snippet", this.fb.control(""));
        this.nodeformdata.addControl("outputparams", this.fb.control(""));        
      }
       break;
    default:
      break;
  }
  if(this.reteservice.getDisableWfData()){
    this.readonlyOnDisable = this.reteservice.getDisableWfData()
    this.nodeformdata.disable();
  }
}
getlibraries() {
  const inputparams = {type: 'library'};
  this.reteservice.getPAlibrariesAndFunctions(inputparams).subscribe((res) => {
    if(res.status.toLowerCase() == 'success'){
      this.libraries=res.libraries;
    }else{

    }
    });
}
getfunctions(libraryname: string) {      
  const inputparams = {type: 'function',libName: libraryname,};
  this.reteservice.getPAlibrariesAndFunctions(inputparams).subscribe((res) => {    
      this.functions = res.functions.map(element => element.name);     
    });
}
    //remove node 
    removenode(node: any) {
      this.editor.removeNode(node);
    }
  //popup node delete confirm
  confirmNodeDelete(event:Event,node:any) {
    this.confirmationService.confirm({
        target: event.target,
        message: 'Are you sure that you want to proceed?',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.editor.removeNode(node);
        this.deletedData=this.reteservice.getDeleteNodedata();
        this.reteservice.popNodeId(node.id);
        },
        reject: () => {
        }
    });  
}
expBuildVal:any;
expBuildData:any;
//open overlay panel Expbuild system and send data
  openExprBuilder(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){        
    this.expBuildData={};
    this.expBuildData={
      "event":event,
      "callerformfieldvalue":callerformfieldvalue,
      "callerformfieldtype":callerformfieldtype,
      "index":index,
      "keyvalindex":keyvalindex,
      "nodeName":this.dataItem.nodeName
    }       
      event.stopPropagation();
      event.preventDefault();
      this.expBuildVal=this.dataItem
      if (callerformfieldvalue != null || callerformfieldvalue != undefined) {
        this.expBuildVal.exprbuildervalue = callerformfieldvalue;
      } else {
        this.expBuildVal.exprbuildervalue = '';
      }
  }
  openExpBuilderDialog(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){
    this.expBuildData={};
    this.expBuildData={
      "event":event,
      "callerformfieldvalue":callerformfieldvalue,
      "callerformfieldtype":callerformfieldtype,
      "index":index,
      "keyvalindex":keyvalindex,
      "nodeName":this.dataItem.nodeName
    }       
      event.stopPropagation();
      event.preventDefault();
      this.expBuildVal=this.dataItem
      if (callerformfieldvalue != null || callerformfieldvalue != undefined) {
        this.expBuildVal.exprbuildervalue = callerformfieldvalue;
      } else {
        this.expBuildVal.exprbuildervalue = '';
      }
      this.ref = this.dialogService.open(ExpressionBuilderComponent, {
        header: 'Expression Builder',
        contentStyle: { overflow: 'auto', padding: '5px' },
        style: { "max-height": "75%", "max-width": "75%" ,"z-index":999},
        modal: true,
        maximizable: false,
        data: this.expBuildVal,
        draggable: true
      });
      this.ref.onClose.subscribe(async (res: any) => {
        if(res == undefined){
          this.reteservice.getRunflowToDialogClose.next(true)
        }else{
        let val = res.expBuildValue;
        if(this.expBuildData.callerformfieldtype === 'list') {
          if(this.expBuildData.keyvalindex >= 0) {
           const control = this.input.controls[this.expBuildData.index].get('listvalues') as FormArray;
           control.at(this.expBuildData.keyvalindex).setValue(val);
        } else {
           this.input.at(this.expBuildData.index).get('value').setValue(val);
        }
        } else {
          if(this.expBuildData.nodeName == 'execjavascript' || this.expBuildData.nodeName == 'execpython'){
              this.inputparams.setValue(val);
              this.nodeformdata.controls['inputparams'].patchValue(val);
             this.nodeformdata.controls['inputparams'].updateValueAndValidity();          
          } else{
             this.input.at(this.expBuildData.index).get('value').setValue(val);
        }
       }
      }
      });
  }
  overlayCloseExpBuilder(data:any){
    let val=data.expBuildValue;
     if(this.expBuildData.callerformfieldtype === 'list') {
       if(this.expBuildData.keyvalindex >= 0) {
        const control = this.input.controls[this.expBuildData.index].get('listvalues') as FormArray;
        control.at(this.expBuildData.keyvalindex).setValue(val);
     } else {
        this.input.at(this.expBuildData.index).get('value').setValue(val);
     }
     } else {
       if(this.expBuildData.nodeName == 'execjavascript' || this.expBuildData.nodeName == 'execpython'){
           this.inputparams.setValue(val);
           this.nodeformdata.controls['inputparams'].patchValue(val);
          this.nodeformdata.controls['inputparams'].updateValueAndValidity();          
       } else{
          this.input.at(this.expBuildData.index).get('value').setValue(val);
     }
    }
      document.getElementById('expBuildClose').click();
  }
  showTick:boolean=false;
  openGenerater(language: string){
    let data:any;
    if(language == 'js'){
        data={
          language:language,
          inputParams: this.inputparams.value,
          nodeFormData:this.nodeformdata.get('snippet').value,
        }
    }else{
      data={
        language:language,
        inputParams: this.inputparams.value,
        nodeFormData:this.nodeformdata.get('snippet').value,
      }
    }
    this.ref = this.dialogService.open(ScriptEditorDialogComponent, {
      header: 'Editor',
      width: '50%',
      contentStyle: { overflow: 'auto',padding:'0rem 1rem'},
      baseZIndex: 10000,
      height:'500px',
      data:data,
      closeOnEscape: false,
      dismissableMask: false,
      showHeader: true,
      closable: false,
  });
  this.ref.onClose.subscribe((res) => {
    if(res !== undefined){
      if(res != ''){
        this.showTick=true;
      }else{
          this.showTick=false;
      }
           this.nodeformdata.get('snippet').patchValue(atob(res));
    }else{      
      this.nodeformdata.get('snippet').patchValue(data.nodeFormData);
    }    
  });  
  }

   //final form data and node data save
   saveNodedata(){      
    let nodeId;
    if(this.nodeDesc == true){
      this.focusIn();
    }
    nodeId =this.reteservice.getNodeButtonId();     
     document.getElementById(nodeId).click();
    this.savedata();
    const data = this.editor.toJSON(); 
     this.reteservice.saveWfdata(this.editor['workflow'].wfId, data, true);
  }

//prepare node data to save
async savedata() { 
  const data = this.nodeformdata.value;    
  data.snippet = btoa(data.snippet);
  if (this.jsonLogicRule) {
    data.jsonLogicRule = this.jsonLogicRule;
  } 
  this.reteservice.getOutputParamsForScriptNodes(data.snippet)
  .subscribe(res => {
    const response = res;
    if (response.error === '') {
      if (response.status === 'Success') {
        const outputparams = response.result;
        data.outputparams = outputparams;
      }
    }
      else {
        //this.errmsg = response.error;
      }
  });

  data.wfName = this.dataItem.nodeData.wfName;
  data.wfNamespace = this.dataItem.nodeData.wfNamespace;
  data.icon = this.dataItem.nodeData.icon;
  data.category = this.dataItem.nodeData.category;
  data.savedata = true;
  data.description=this.dataItem.nodeData.description;
  data.name=this.dataItem.nodeData.name;
  let newInput=data.input;
  data.inputs=newInput;
  delete data.input; 
  this.node.data = data;        
}

openTestData(){
  this.reteservice.openTestDataPanel.next('open');
}

validataFields(){  
  let validate:any;
  if(this.nodeformdata.value.inputparams?.trim().length != 0 && this.nodeformdata.value.snippet != '' && this.showTick && !this.duplicate){
    validate = false;
  }else{
    validate = true;
  }  
  return validate; 
}

javaValidataFields(){
  let validate:any;
  if(this.nodeformdata.value.function != '' && this.nodeformdata.value.library != '' && !this.duplicate){
    validate = false;
  }else{
    validate = true;
  }
  return validate; 
}
}
