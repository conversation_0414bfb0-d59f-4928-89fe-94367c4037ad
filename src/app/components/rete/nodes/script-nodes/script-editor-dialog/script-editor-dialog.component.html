<!-- Editor STARTS -->
<a style="padding: 0.45625rem" (click)="closeModel($event)" rel="noopener noreferrer" #closeButton
  class="help-icon p-button p-button-text p-button-sm p-button-rounded" pTooltip="close" tooltipPosition="left"><i class="pi pi-times"></i></a>

<form [formGroup]="nodeformdata">
  <div appendTo="body">
    <ngx-monaco-editor
      style="width: 100%; height: 380px;"
      [options]="JavaScriptEditorOptions"
      formControlName="snippet"
      (onInit)="onInit($event)"
    ></ngx-monaco-editor>
  </div>
</form>
<!-- Editor ENDS -->

<div class="my-1" style="text-align: center;">
  <p-button
    label="Generate"
    icon="pi pi-book"
    styleClass="p-button-sm mr-2"
    (click)="generateScript()"
  ></p-button>
  <p-button
    label="Reset"
    icon="pi pi-refresh"
    styleClass="p-button-sm mr-2"
    severity="info"
    (click)="resetScript()"
  ></p-button>
  <p-button
    label="Save"
    icon="pi pi-save"
    styleClass="p-button-sm"
    (click)="save()"
  ></p-button>
</div>