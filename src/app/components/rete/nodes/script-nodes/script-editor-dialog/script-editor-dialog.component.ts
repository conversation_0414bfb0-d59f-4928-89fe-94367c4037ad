import { Component, OnInit, HostListener, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ReteService } from 'src/app/services/rete.service';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationService } from 'primeng/api';

@Component({
  selector: 'app-script-editor-dialog',
  templateUrl: './script-editor-dialog.component.html',
  styleUrls: ['./script-editor-dialog.component.scss']
})
export class ScriptEditorDialogComponent implements OnInit {
  @ViewChild('closeButton', { static: true }) closeButton!: ElementRef<HTMLAnchorElement>;

  @HostListener('document:keydown.escape', ['$event'])
  onKeydownHandler(event: KeyboardEvent): void {
    this.closeButton.nativeElement.click();
  }

  isEditorFocused = false;
  errmsg: string | null = null;
  nodeformdata!: FormGroup;
  JavaScriptEditorOptions:any;
  

  constructor(
    private reteservice: ReteService,
    public dialogData: DynamicDialogConfig,
    public confirmationService: ConfirmationService,
    public ref: DynamicDialogRef,
    private fb: FormBuilder
  ) {}

  async ngOnInit(): Promise<void> {
    console.log(this.dialogData.data);
    this.JavaScriptEditorOptions = {
    theme: 'vs-dark',
    language: this.dialogData.data.language === 'python' ? 'python': 'javascript',
    minimap: { enabled: false },
    foldingStrategy: 'indentation',
    wrappingIndent: 'indent',
    formatOnPaste: true,
    formatOnType: true,
    contextmenu: false,
    scrollBeyondLastLine: false,
  };
    this.initializeForm();
  }

  private initializeForm(): void {
    this.nodeformdata = this.fb.group({
      snippet: this.fb.control(this.dialogData.data.nodeFormData || '')
    });
  }

  generateScript(): void {
    this.reteservice.generateScript(this.dialogData.data.language, this.dialogData.data.inputParams, this.dialogData.data.nodeFormData).subscribe({
        next: (res) => this.handleScriptResponse(res),
        error: (err) => (this.errmsg = err.message || 'An error occurred while generating the script.')
      });
  }

  resetScript(): void {
    this.reteservice.generateScript(this.dialogData.data.language, this.dialogData.data.inputParams).subscribe({
        next: (res) => this.handleScriptResponse(res),
        error: (err) => (this.errmsg = err.message || 'An error occurred while resetting the script.')
      });
  }

  private handleScriptResponse(response: any): void {
    if (response.error === '') {
      if (response.status === 'Success') {
        this.nodeformdata.get('snippet')?.patchValue(atob(response.script));
      }
    } else {
      this.errmsg = response.error;
    }
  }

  onInit(editor: any): void {
    editor.onDidFocusEditorWidget(() => (this.isEditorFocused = true));
    editor.onDidBlurEditorWidget(() => (this.isEditorFocused = false));
  }

  save(): void {
      this.ref.close(btoa(this.nodeformdata.value.snippet));
  }

  closeModel(event: Event): void {
    const originalSnippet = this.dialogData.data.nodeFormData;
    const updatedSnippet = this.nodeformdata.value.snippet;
    if (originalSnippet === updatedSnippet) {
       this.ref.close()
      } else {
        this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      rejectButtonStyleClass: 'p-button-text',
      accept: () => this.ref.close()
    });
      }
    
  }
}