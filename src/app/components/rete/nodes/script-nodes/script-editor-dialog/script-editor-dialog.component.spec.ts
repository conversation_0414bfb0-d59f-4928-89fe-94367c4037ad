import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ScriptEditorDialogComponent } from './script-editor-dialog.component';

describe('ScriptEditorDialogComponent', () => {
  let component: ScriptEditorDialogComponent;
  let fixture: ComponentFixture<ScriptEditorDialogComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ScriptEditorDialogComponent]
    });
    fixture = TestBed.createComponent(ScriptEditorDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
