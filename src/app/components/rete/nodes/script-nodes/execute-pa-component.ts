import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
import { ScriptnodeComponent } from './scriptnode/scriptnode.component';
export class ExecuteFunctionComponent extends Component {
 override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    library: '',
    function: '',
    inputs: [],
    wfName: 'execjava',
    wfNamespace: 'unvired.operation',
    icon: 'pi pi-bolt',
    category: 'ScriptExecutors'
  };
  constructor() {
    super('execjava');
    this.data.render = 'angular';
    this.data.component = ScriptnodeComponent;
  }
builder(node) {
  const inp1 = new Input('execjava', 'Input', strSocket);
  const out1 = new Output('execjava_success', 'Success', successSocket, false);
  const out2 = new Output('execjava_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
  // console.log('From worker node', node);
 //  outputs.transform = node.data.transformdata;
}
}
