<div class="success-node-input" *ngFor="let input of inputs">
    <rete-socket rete-socket [io]="input" [socket]="input.socket"></rete-socket>
</div>
<div class="start-node-container mb-0 border-noround" [ngClass]="[selected()]">
    <p-panel toggleable="false" collapsed="true" class="startPanel" >
        <ng-template pTemplate="header">
            <!-- <i class="pi pi-check nodeIcon" pTooltip="Success Node" tooltipPosition="top"></i> -->
            <span class="nodeIcon" pTooltip="Start Node" tooltipPosition="top"><img class="pmgImg" src="assets/icon/success.png"></span>
            <svg *ngIf="node.name === 'sap' " class="nodeIconSap" xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="50" height="43" viewBox="0 0 1080 1080"
              xml:space="preserve">
              <g transform="matrix(1 0 0 1 540 540)" id="02953293-5fd9-44ca-9c32-7bfc459898bf">
                <rect
                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;"
                  vector-effect="non-scaling-stroke" x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
              </g>
              <g transform="matrix(1 0 0 1 540 540)" id="70ac1fec-5595-47b2-8de5-10e0fb839d51"></g>
              <g transform="matrix(NaN NaN NaN NaN 0 0)">
                <g></g>
              </g>
              <g transform="matrix(21.6 0 0 21.6 540 540)">
                <path
                  style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                  transform=" translate(-25.5, -24)"
                  d="M 1 12 L 1 36 L 26 36 L 50 12 Z M 8.398438 18 C 11.800781 18 12.902344 18.800781 12.902344 18.800781 C 12.902344 18.800781 12.300781 20.300781 12 21.097656 C 11 20.699219 7 20 7 21.5 C 7 22.101563 7.199219 22.5 10.199219 23.199219 C 11.398438 23.5 13.199219 24.101563 13.597656 25.902344 L 16.699219 18 L 20.199219 18 L 24 27.5 L 24 18 L 28.5 18 C 31 18 33 20 33 22.5 C 33 25 31 27 28.5 27 L 27 27 L 27 30 L 21.5 30 L 20.902344 28.199219 C 20.199219 28.5 19.398438 28.699219 18.5 28.699219 C 17.601563 28.699219 16.800781 28.5 16.097656 28.199219 L 15.5 30 L 12 30 L 12.5 28.800781 C 11.601563 29.5 10.199219 30 8.398438 30 C 5.101563 30 4 29.097656 4 29.097656 L 4.800781 26.5 C 6.699219 27.699219 10.097656 27.5 10.097656 26.5 C 10.097656 25.5 8.601563 25.402344 6.601563 24.800781 C 4.699219 24.199219 4 22.699219 4 21.402344 C 4 20.101563 5 18 8.398438 18 Z M 27 21 L 27 24 L 28.5 24 C 29.300781 24 30 23.300781 30 22.5 C 30 21.699219 29.300781 21 28.5 21 Z M 18.5 21.402344 L 17 25.699219 C 17.398438 26 17.898438 26.097656 18.5 26.097656 C 19.101563 26.097656 19.601563 26 20 25.699219 Z"
                  stroke-linecap="round" />
              </g>
              <g transform="matrix(8.32 0 0 5.09 327.3 508.56)" id="14e3cb22-c1f3-4f2f-8590-a9fa04f9fa8c">
                <rect
                  style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                  vector-effect="non-scaling-stroke" x="-33.0835" y="-33.0835" rx="0" ry="0" width="66.167"
                  height="66.167" />
              </g>
              <g transform="matrix(2.47 0 0 3.38 626.41 497.65)" id="49a1d38c-f87a-41e6-90d8-7141810959e1">
                <circle
                  style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                  vector-effect="non-scaling-stroke" cx="0" cy="0" r="35" />
              </g>
            </svg>
            <span class="ml-3">
                <div class="capitalize">{{node.meta['name']}}</div> 
            </span>
          </ng-template>
        </p-panel>
</div>
