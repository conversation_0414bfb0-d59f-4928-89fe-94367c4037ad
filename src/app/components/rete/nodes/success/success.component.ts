import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
@Component({
  selector: 'app-success',
  templateUrl: './success.component.html',
  styleUrls: ['./success.component.scss'],
  providers: [NodeService]
})
export class SuccessComponent extends NodeComponent {

  constructor(
    protected override service: NodeService,
    protected override cdr: ChangeDetectorRef,
  ) { 
    super(service, cdr);
  }
 // ngOnInit(){}
}