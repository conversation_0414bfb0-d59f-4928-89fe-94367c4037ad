import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { SuccessComponent as SuccessComp} from './success.component';
import { AngularComponentData } from 'rete-angular-render-plugin';
export class SuccessComponent extends Component {
   override data: AngularComponentData;
  constructor() {
    super('Success');
    this.data.render = 'angular';
    this.data.component = SuccessComp;
  }

  builder(node) {
    const input = new Input('Success', 'Input', successSocket, true);
    node.meta = {
      category: 'standard',
      name: 'success',
      index: 1,
      icon: 'done',
      description: 'Workflow or operation has resulted in a successful result.',
      wfName: 'flowresult',
      wfNamespace: 'unvired.operation'
    };
    return node.addInput(input);
  }

  worker(node, inputs, outputs) {
   //  outputs.success = node.data.success;
  }
}
