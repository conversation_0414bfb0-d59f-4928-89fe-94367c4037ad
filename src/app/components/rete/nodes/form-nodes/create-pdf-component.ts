import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, formsSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
import { FormnodeComponent } from './formnode/formnode.component';
export class CreatePDFComponent extends Component {
 override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'formpdf',
    wfNamespace: 'unvired.flow',
    icon: 'picture_as_pdf',
    category: 'Forms'
  };
  constructor() {
    super('CreatePDF');
    this.data.render = 'angular';
    this.data.component = FormnodeComponent;
  }
builder(node) {
  const inp1 = new Input('CreatePDF', 'Input', formsSocket);
  const out1 = new Output('CreatePDF_success', 'Success', successSocket, false);
  const out2 = new Output('CreatePDF_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
