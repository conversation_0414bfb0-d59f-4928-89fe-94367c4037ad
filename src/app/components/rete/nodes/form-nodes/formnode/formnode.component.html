<!--new expand panel-->
<div class="nodes-input-socket" *ngFor="let input of inputs">
    <rete-socket rete-socket [io]="input" [socket]="input.socket"></rete-socket>
  </div>
  <div class="flex">
    <div class="node-container" [ngClass]="[selected()]">
      <p-panel  toggleable="true" [collapsed]="openPanelForNew" expandIcon="pi pi-chevron-up" collapseIcon="pi pi-chevron-down"
        class="panelOpen" (onBeforeToggle)="[panelBtn($event,node),expbuilder.hide()]">
        <ng-template pTemplate="header">
          <!-- <span class="material-icons nodeIcon2" pTooltip="{{this.nodeName}}" tooltipPosition="top"
            *ngIf="node.name !== 'sap'">{{node.data.icon}}</span> -->
            <span class="nodeIcon" pTooltip="{{this.nodeName}}" tooltipPosition="top" *ngIf="node.name !== 'sapa'"><img class="pmgImg" src="assets/icon/{{this.nodeIcon}}.png"></span>

          <svg *ngIf="node.name === 'sap' " class="nodeIconSap" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="50" height="43" viewBox="0 0 1080 1080"
            xml:space="preserve">
            <g transform="matrix(1 0 0 1 540 540)" id="02953293-5fd9-44ca-9c32-7bfc459898bf">
              <rect
                style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;"
                vector-effect="non-scaling-stroke" x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
            </g>
            <g transform="matrix(1 0 0 1 540 540)" id="70ac1fec-5595-47b2-8de5-10e0fb839d51"></g>
            <g transform="matrix(NaN NaN NaN NaN 0 0)">
              <g></g>
            </g>
            <g transform="matrix(21.6 0 0 21.6 540 540)">
              <path
                style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                transform=" translate(-25.5, -24)"
                d="M 1 12 L 1 36 L 26 36 L 50 12 Z M 8.398438 18 C 11.800781 18 12.902344 18.800781 12.902344 18.800781 C 12.902344 18.800781 12.300781 20.300781 12 21.097656 C 11 20.699219 7 20 7 21.5 C 7 22.101563 7.199219 22.5 10.199219 23.199219 C 11.398438 23.5 13.199219 24.101563 13.597656 25.902344 L 16.699219 18 L 20.199219 18 L 24 27.5 L 24 18 L 28.5 18 C 31 18 33 20 33 22.5 C 33 25 31 27 28.5 27 L 27 27 L 27 30 L 21.5 30 L 20.902344 28.199219 C 20.199219 28.5 19.398438 28.699219 18.5 28.699219 C 17.601563 28.699219 16.800781 28.5 16.097656 28.199219 L 15.5 30 L 12 30 L 12.5 28.800781 C 11.601563 29.5 10.199219 30 8.398438 30 C 5.101563 30 4 29.097656 4 29.097656 L 4.800781 26.5 C 6.699219 27.699219 10.097656 27.5 10.097656 26.5 C 10.097656 25.5 8.601563 25.402344 6.601563 24.800781 C 4.699219 24.199219 4 22.699219 4 21.402344 C 4 20.101563 5 18 8.398438 18 Z M 27 21 L 27 24 L 28.5 24 C 29.300781 24 30 23.300781 30 22.5 C 30 21.699219 29.300781 21 28.5 21 Z M 18.5 21.402344 L 17 25.699219 C 17.398438 26 17.898438 26.097656 18.5 26.097656 C 19.101563 26.097656 19.601563 26 20 25.699219 Z"
                stroke-linecap="round" />
            </g>
            <g transform="matrix(8.32 0 0 5.09 327.3 508.56)" id="14e3cb22-c1f3-4f2f-8590-a9fa04f9fa8c">
              <rect
                style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                vector-effect="non-scaling-stroke" x="-33.0835" y="-33.0835" rx="0" ry="0" width="66.167"
                height="66.167" />
            </g>
            <g transform="matrix(2.47 0 0 3.38 626.41 497.65)" id="49a1d38c-f87a-41e6-90d8-7141810959e1">
              <circle
                style="stroke: rgb(0,0,0); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"
                vector-effect="non-scaling-stroke" cx="0" cy="0" r="35" />
            </g>
          </svg>
          <span class="ml-5">
            <div *ngIf="!this.readonlyOnDisable">
             <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && displayBtn"  >{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
             <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && displayBtn">{{node.name}}</span>
               <span class="spanText" *ngIf="!nodeDesc && node.data.description !== '' && !displayBtn" (click)="focusIn()">{{node.data.description}}<i *ngIf="duplicate" style="color: tomato" class="pi pi-times-circle ml-2"></i></span>
               <span class="spanText" *ngIf="!nodeDesc && node.data.description == '' && !displayBtn" (click)="focusIn()">{{node.name}}</span>
               <input *ngIf="nodeDesc && displayBtn"  id="inputBox" (click)="focusIn()" (focusout)="focusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
               <input *ngIf="nodeDesc && !displayBtn"   id="inputBox" (click)="focusIn()" (focusout)="FocusOut()" pInputText type="text" [(ngModel)]="desc" autocomplete="off" />
              </div>
              <div *ngIf="this.readonlyOnDisable">
                <span class="spanText">{{node.data.description}}</span>
              </div>
             </span>
        </ng-template>
        <ng-template pTemplate="content" >
          <div class="cardBody" >
            <div *ngIf="this.error != '' " style="margin-top: 30px;padding: 30px;">
              <span style="color: tomato;">{{this.error}}</span>
            </div>
            <div *ngIf="this.duplicate" style="margin-top: 10px; margin-right: 8px;">
              <p-message class="warnMsg" severity="warn" text="Node name should be unique."></p-message> 
            </div>
            <form  [formGroup]="nodeformdata" novalidate autocomplete="off" style="border: 0px; margin-right: 5px;padding-left: 2px; margin-bottom: 10px;" *ngIf="!displayBtn">
              <!--MASTERDATA for READROWS-->
              <div *ngIf="this.dataItem.nodeName === 'ReadRows' && this.dataItem.nodeName !== 'SearchForm'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
              <!--MASTERDATA for -->
              <div style="display: flex;" *ngIf="field.value.type === 'masterdataname' && field.value.defaultSelect === 'forms'">
                <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                  <p-dropdown class="integrationNode" [options]="allMasterData" formControlName="values" 
                    [showClear]="true" [pTooltip]="field.value.description" optionLabel="masterdataName"
                    optionValue="masterdataName" [autoDisplayFirst]="true" appendTo="body" [filter]="true" (onChange)="getMasterdataId($event.value)"
                    placeholder="Select....">
                  </p-dropdown>
                  <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                  <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                </span>
              </div>
              <!--END-->
                <div *ngIf="field.value.type === 'text' &&  field.value.inputName === 'filter'">
                  <span class="p-float-label p-input-icon-right mt-4 " style="display: none;">
                    <input pInputText   formControlName="values" [pTooltip]="field.value.description" />
                    <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"  (click)="openQueryBuilder(dataItem,field.value.values,ind)"></i>
                    <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                    <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                  </span>
                  <div style="text-align: center; margin-top: 10px;">
                    <p-button icon="pi pi-pencil" [disabled]="(this.input?.at(0).get('values').value == null || this.input?.at(0).get('values').value == '')" label="Open Query Builder" styleClass="p-button-sm mr-2" (click)="openQueryBuilder(dataItem,field.value.values,ind)" ></p-button>
                    <span *ngIf="showTick" class="material-icons" style="color: green;">done_all</span>
                  </div>
                </div>
                </div>
                </div>
              </div>
              <!--MASTERDATA for READROWS ENDS-->
              <div *ngIf="this.dataItem.nodeName !== 'ReadRows' && this.dataItem.nodeName !== 'SearchForm'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                    <div>
                      <!--ReadForm & ""ArchiveForm""-->
                      <div class="mt-3" *ngIf="this.dataItem.nodeName === 'ReadForm' || this.dataItem.nodeName === 'ArchiveForm'">
                    
                      </div>
                      <!--END-->
                      <!--MASTERDATA for -->
                      <div style="display: flex;" *ngIf="field.value.type === 'masterdataname' && field.value.defaultSelect === 'forms'">
                        <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                          <p-dropdown class="integrationNode" [options]="allMasterData" formControlName="values" 
                            [showClear]="true" [pTooltip]="field.value.description" optionLabel="masterdataName"
                            optionValue="masterdataName" [autoDisplayFirst]="true" appendTo="body" [filter]="true"
                            placeholder="Select....">
                          </p-dropdown>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
  
                      <!--TYPE == "formname" SELECT BTN-->
                      <div class="mt-3" *ngIf="field.value.type === 'formname'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <p-selectButton  class="selectBtn1" [options]="formNameOptions" formControlName="defaultSelect"
                          optionLabel="label" optionValue="value"
                          (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                      </div>
                      <!--Based on radio select and type is FORM -->
                      <div style="display: flex;" *ngIf="field.value.type === 'formname' && field.value.defaultSelect === 'forms'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                          <p-dropdown class="integrationNode"  [options]="allforms" [showClear]="true"
                            [pTooltip]="field.value.description" optionLabel="formTitle" optionValue="formName"
                            [autoDisplayFirst]="true" appendTo="body" [filter]="true" placeholder="Select...."
                            formControlName="values">
                          </p-dropdown>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <div style="display: flex;"  *ngIf="field.value.type === 'formname' && field.value.defaultSelect === 'formfields'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END--><!--END-->
                      <!--TYPE ==="RADIO" Recipient Type-->
                      <div class="mt-2" *ngIf="field.value.type === 'radio' && node.name !=='ShareForm' && field.value.inputName === 'recipienttype'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                          optionLabel="key" optionValue="value"
                          (onChange)="CreateFormRadio($event.value)"></p-selectButton>
                      </div>
                      <!--END-->
                      <div *ngIf="field.value.type === 'user' && (userTeamSelectValue === 'user' || userTeamSelectValue === 'users')">
                        <!--TYPE == "user" Select BTN-->
                        <div class="mt-3">
                          <p-selectButton class="selectBtn1" [options]="userOptions" formControlName="defaultSelect"
                            optionLabel="label" optionValue="value"
                            (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                        </div>
                        <!--Based on radio select and type is user/formFiled -->
                        <div
                          *ngIf="(this.node.name === 'CreateForm' || this.node.name === 'AssignForm') && userTeamSelectValue === 'user' ">
                          <div style="display: flex;" *ngIf="field.value.defaultSelect === 'users'">
                            <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                              <p-dropdown [options]="allusers" formControlName="values" class="integrationNode"
                                [showClear]="true" [pTooltip]="field.value.description" optionLabel="email"
                                optionValue="id" [autoDisplayFirst]="true" appendTo="body" [filter]="true"
                                placeholder="Select....">
                              </p-dropdown>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <div *ngIf="(this.node.name !== 'CreateForm' || this.node.name !== 'AssignForm') && userTeamSelectValue === 'users' ">
                          <div style="display: flex;width: -webkit-fill-available;" *ngIf="field.value.defaultSelect === 'users'">
                            <span class="p-float-label mt-4 w-full">
                              <p-multiSelect [options]="allusers" formControlName="values" optionLabel="email"
                                optionValue="id" [showClear]="true" [pTooltip]="field.value.description" display="chip"
                                [autoDisplayFirst]="true" appendTo="body" [filter]="true" class="multiSelect"
                                placeholder="Select...."></p-multiSelect>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <div *ngIf="userTeamSelectValue === 'user' || userTeamSelectValue === 'users'">
                          <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                            <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                              <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <!--END--><!--END-->
                      </div>
                      <div *ngIf="field.value.type === 'team' && (userTeamSelectValue === 'team' || userTeamSelectValue === 'teams')">
                        <!--TYPE == "Teams" RADIO BTN-->
                        <div class="mt-3">
                          <p-selectButton class="selectBtn1" [options]="teamOptions" formControlName="defaultSelect"
                            optionLabel="label" optionValue="value"
                            (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                        </div>
                        <!--Based on radio select and type is team/formfield -->
                        <div *ngIf="(this.node.name === 'CreateForm' || this.node.name === 'AssignForm') && userTeamSelectValue === 'team' ">
                          <div style="display: flex;" *ngIf="field.value.defaultSelect === 'teams'">
                            <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                              <p-dropdown formControlName="values" [options]="allteams" class="integrationNode"
                                [showClear]="true" [pTooltip]="field.value.description" optionLabel="name"
                                optionValue="teamId" [autoDisplayFirst]="true" appendTo="body" [filter]="true"
                                placeholder="Select...."></p-dropdown>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <div
                          *ngIf="(this.node.name !== 'CreateForm' || this.node.name !== 'AssignForm') && userTeamSelectValue === 'teams' ">
                          <div style="display: flex;width: -webkit-fill-available;" *ngIf="field.value.defaultSelect === 'teams'">
                            <span class="p-float-label mt-4  w-full">
                              <p-multiSelect [options]="allteams" formControlName="values" optionLabel="name" class="multiSelect"
                                optionValue="teamId" [showClear]="true" [pTooltip]="field.value.description"
                                display="chip" [autoDisplayFirst]="true" appendTo="body" [filter]="true"
                                placeholder="Select...."></p-multiSelect>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <div *ngIf="userTeamSelectValue === 'team' || userTeamSelectValue === 'teams'">
                          <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                            <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                              <input pInputText  formControlName="values"
                                [pTooltip]="field.value.description" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <!--END--><!--END-->
                      </div>
                      <!--TYPE === "LIST"-->
                      <div style="display: flex;" *ngIf="field.value.type === 'list'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                          <p-dropdown [options]="field.value.listvalue" [showClear]="true" class="integrationNode"
                            [pTooltip]="field.value.description" optionLabel="key" optionValue="value"
                            [autoDisplayFirst]="true" appendTo="body" placeholder="Select...." formControlName="values">
                          </p-dropdown>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                      <!--TYPE == "TEXT" general-->
                      <div class="flex" *ngIf="field.value.type === 'text' &&  field.value.inputName !== 'firstname' && this.node.name !== 'ArchiveForm'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                      <!--TYPE == "TEXT" change new  condition field.value.inputName !== 'firstname' && this.node.name === 'ArchiveForm'-->
                      <div class="flex" *ngIf="field.value.type === 'text' &&  field.value.inputName !== 'firstname' && this.node.name === 'ArchiveForm'">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                       <!--TYPE == "TEXT" change new (field.value.inputName === 'firstname' && this.node.name !== 'ArchiveForm')-->
                       <div class="flex" *ngIf="field.value.type === 'text' &&  field.value.inputName === 'firstname' && this.node.name !== 'ArchiveForm'" >
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                      <!--TYPE == "number"-->
                      <div style="display: flex;" *ngIf="field.value.type === 'number'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  type="number" formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                      <!--TYPE == "EMAIL"-->
                      <div style="display: flex;" *ngIf="field.value.type === 'email'" >
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  type="email" formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor"  *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                      <!--END-->
                      <!--TYPE == "EMAIL"-->
                      <!-- <div style="display: flex;" *ngIf="field.value.type === 'email' && this.shareRadio !== 'private'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                          <input pInputText  type="email" formControlName="values"
                            [pTooltip]="field.value.description" />
                          <i class="pi pi-wrench cursor"  *ngIf="!readonlyOnDisable"
                            (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div> -->
                      <!--END-->
                      <!--TYPE ==="RADIO" no condition-->
                      <div class="mt-2" [ngClass]="{'fieldDisplay': field.value.show == true}"
                        *ngIf="field.value.type === 'radio' && node.name !=='ShareForm' && field.value.inputName !== 'recipienttype' &&  field.value.inputName !== 'priority'">
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                          optionLabel="key" optionValue="value" (onChange)="getRadioValue($event.value)"></p-selectButton>
                      </div>
                      <!--END-->
                      <!--TYPE ==="RADIO" type priority-->
                      <div class="mt-2" [ngClass]="{'fieldDisplay': field.value.show == true}"
                      *ngIf="field.value.type === 'radio' && field.value.inputName === 'priority'">
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      <p-selectButton class="selectBtn2" [options]="field.value.radiolistvalue" formControlName="values"
                        optionLabel="key" optionValue="value" (onChange)="getRadioValue($event.value)"></p-selectButton>
                      </div>
                      <!--END-->
                      <!--TYPE ==="RADIO" node.name ==='ShareForm'-->
                      <div class="mt-1" *ngIf="field.value.type === 'radio' && node.name ==='ShareForm'">
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        <p-selectButton class="selectBtn1" [options]="field.value.radiolistvalue" formControlName="values"
                          optionLabel="key" optionValue="value" (onChange)="shareFormRadio($event.value)"
                          [pTooltip]="field.value.description"></p-selectButton>
                        <!--(onChange)="shareFormRadio($event.value)" -->
                      </div>
                      <!--END-->
                      <!--TYPE === "BOOLEAN" & CHECKBOX-->
                      <div class="mt-3" *ngIf="field.value.type === 'boolean'" [ngClass]="{'fieldDisplay': field.value.show == true}">
                        <p-checkbox formControlName="values" [binary]="true"
                          [pTooltip]="field.value.description"></p-checkbox>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="required ml-3 lable">{{field.value.label}}</label>
                        <label *ngIf="field.value.required !== 'Mandatory'" class="ml-3 lable">{{field.value.label}}</label>
                      </div>
                      <!--END-->
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="this.dataItem.nodeName !== 'ReadRows' && this.dataItem.nodeName === 'SearchForm'">
                <div formArrayName="input" *ngFor="let field of input?.controls; let ind = index;">
                  <div [formGroupName]="ind">
                   <!-- multiselect based on condition Start  -->
                    <div *ngIf="field.value.inputName == 'searchcriteria'">
                      <div style="display: flex;width: -webkit-fill-available;">
                        <span class="p-float-label mt-4 w-full">
                          <p-multiSelect [options]="field.value.listvalue" formControlName="values" optionLabel="key"
                            optionValue="value" [showClear]="false" [pTooltip]="field.value.description" display="chip"
                            [autoDisplayFirst]="true" appendTo="body" [filter]="true" class="multiSelect" [showToggleAll]="false"
                            placeholder="Select...." (onChange)="searchOn($event.value)" ></p-multiSelect><!--(onRemove)="searchOnRemove($event.removed)"-->
                          <label *ngIf="field.value.required == 'Mandatory'"
                            class="_required">{{field.value.label}}</label>
                          <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                        </span>
                      </div>
                    </div>
                     <!-- Ends -->
                      <!-- dropdown no condition -->
                    <div style="display: flex;" *ngIf="field.value.type === 'list' && field.value.inputName == 'status' &&  isFiledsSelected(field.value.inputName)">
                      <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                        <p-dropdown class="integrationNode" [options]="field.value.listvalue" formControlName="values" 
                          [showClear]="true" [pTooltip]="field.value.description" optionLabel="key"
                          optionValue="value" [autoDisplayFirst]="true" appendTo="body" [filter]="true" placeholder="Select....">
                        </p-dropdown>
                        <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--Ends-->
                    <!--TYPE == "TEXT" general-->
                    <div class="flex" *ngIf="field.value.type === 'text' &&  isFiledsSelected(field.value.inputName)" >
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"
                          [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                          (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                     <!--TYPE == "TEXT" returnfields-->
                     <div class="flex" *ngIf="field.value.type === 'text' &&  field.value.inputName == 'returnfields'" >
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"
                          [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                          (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                   <div class="mt-2" *ngIf="ind == 1 && isFiledsSelected('usersorteams')">
                      <label *ngIf="field.value.required == 'Mandatory'" class="_required">{{field.value.label}}</label>
                      <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      <p-selectButton class="selectBtn1" [options]="[{'key':'User','value':'user'},{'key':'Team','value':'team'}]" [(ngModel)]="searchuserteamVal"
                      optionLabel="key" optionValue="value" [ngModelOptions]="{ standalone: true }" (onChange)="searchFormRadio($event.value)"></p-selectButton>
                    </div> 
                    <!--END-->
                      <!--END-->
                      <div *ngIf="field.value.type === 'user' && searchuserteamVal === 'user' && isFiledsSelected('usersorteams')">
                        <!--TYPE == "user" Select BTN-->
                        <div class="mt-3">
                          <p-selectButton class="selectBtn1" [options]="userOptions" formControlName="defaultSelect"
                            optionLabel="label" optionValue="value"
                            (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                        </div>
                        <!--Based on radio select and type is user/formFiled -->
                          <div style="display: flex;width: -webkit-fill-available;" *ngIf="searchuserteamVal === 'user'  && field.value.defaultSelect === 'users'">
                            <span class="p-float-label mt-4 w-full">
                              <p-multiSelect [options]="allusers" formControlName="values" optionLabel="email"
                                optionValue="id" [showClear]="true" [pTooltip]="field.value.description" display="chip"
                                [autoDisplayFirst]="true" appendTo="body" [filter]="true" class="multiSelect"
                                placeholder="Select...."></p-multiSelect>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                            <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                              <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                                <input pInputText  formControlName="values" [pTooltip]="field.value.description" />
                                <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                  (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                                <label *ngIf="field.value.required == 'Mandatory'"
                                  class="_required">{{field.value.label}}</label>
                                <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                              </span>
                            </div>
                          
                        <!--END-->
                      </div>
                      <div *ngIf="field.value.type === 'team' && searchuserteamVal === 'team' && isFiledsSelected('usersorteams')">
                        <!--TYPE == "Teams" RADIO BTN-->
                        <div class="mt-3">
                          <p-selectButton class="selectBtn1" [options]="teamOptions" formControlName="defaultSelect"
                            optionLabel="label" optionValue="value"
                            (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                        </div>
                        <!--Based on radio select and type is team/formfield -->
                        <div *ngIf="searchuserteamVal === 'team' ">
                          <div style="display: flex;width: -webkit-fill-available;" *ngIf="field.value.defaultSelect === 'teams'">
                            <span class="p-float-label mt-4  w-full">
                              <p-multiSelect [options]="allteams" formControlName="values" optionLabel="name" class="multiSelect"
                                optionValue="teamId" [showClear]="true" [pTooltip]="field.value.description"
                                display="chip" [autoDisplayFirst]="true" appendTo="body" [filter]="true"
                                placeholder="Select...."></p-multiSelect>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <div *ngIf="searchuserteamVal === 'team'">
                          <div style="display: flex;" *ngIf="field.value.defaultSelect === 'formfields'">
                            <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                              <input pInputText  formControlName="values"
                                [pTooltip]="field.value.description" />
                              <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                                (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                              <label *ngIf="field.value.required == 'Mandatory'"
                                class="_required">{{field.value.label}}</label>
                              <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                            </span>
                          </div>
                        </div>
                        <!--END-->
                      </div>
                    <!--TYPE == "formname"-->
                    <div class="mt-3" *ngIf="field.value.type === 'formname' && isFiledsSelected(field.value.inputName)" >
                      <p-selectButton  class="selectBtn1" [options]="formNameOptions" formControlName="defaultSelect"
                        optionLabel="label" optionValue="value"
                        (onChange)="input?.at(ind).get('values').setValue(null)"></p-selectButton>
                    </div>
                    <!--Based on radio select and type is FORM -->
                    <div style="display: flex;" *ngIf="field.value.type === 'formname' && field.value.defaultSelect === 'forms' &&  isFiledsSelected(field.value.inputName)" >
                      <span class="p-float-label mt-4 w-full" style="padding: 0px;">
                        <p-dropdown class="integrationNode"  [options]="allforms" [showClear]="true"
                          [pTooltip]="field.value.description" optionLabel="formTitle" optionValue="formName"
                          [autoDisplayFirst]="true" appendTo="body" [filter]="true" placeholder="Select...."
                          formControlName="values">
                        </p-dropdown>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <div style="display: flex;"  *ngIf="field.value.type === 'formname' && field.value.defaultSelect === 'formfields' &&  isFiledsSelected(field.value.inputName)">
                      <span class="p-float-label p-input-icon-right mt-4" style="width: -webkit-fill-available;">
                        <input pInputText  formControlName="values"
                          [pTooltip]="field.value.description" />
                        <i class="pi pi-wrench cursor" *ngIf="!readonlyOnDisable"
                          (click)="openExpBuilderDialog($event,field.value.values, field.value.type, ind)"></i>
                        <label *ngIf="field.value.required == 'Mandatory'"
                          class="_required">{{field.value.label}}</label>
                        <label *ngIf="field.value.required != 'Mandatory'">{{field.value.label}}</label>
                      </span>
                    </div>
                    <!--END-->
                     <!--formname END-->
                  </div>
                </div>
              </div>
              <div class="py-2 mr-3" style="float: right;" *ngIf="isNodeNameExcluded(dataItem.nodeName)">
                <p-button  styleClass="p-button-text p-0" *ngIf="!enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Show advanced options</span><i class="pi pi-chevron-down"></i></p-button>
                <p-button  styleClass="p-button-text p-0"  *ngIf="enableDisable" (click)="hideOptionalFields(enableDisable = !enableDisable)"><span class="pr-2">Hide advanced options</span><i class="pi  pi-chevron-up"></i></p-button>
              </div>
            </form>
      
          </div>
        </ng-template>
      </p-panel>
    </div>
    <div class="m-1 mb-0" *ngIf="!readonlyOnDisable && !displayBtn">
      <div style="display:table-caption;">
        <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-sm" 
            pTooltip="Save" tooltipPosition="right"  [disabled]="validataFileds()"(onClick)="saveNodedata()"></p-button>
        <p-button icon="pi pi-question" styleClass="p-button-rounded p-button-text p-button-raised p-button-outlined" class="nodeBtnbg"
          pTooltip="Info" (onClick)="goToLink(helpurl)" tooltipPosition="right"></p-button>
        <p-button icon="pi pi-trash" styleClass="p-button-rounded p-button-danger" class="nodeBtn"
          pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button>
    
      </div>
    </div>
  </div>
  <div class="nodes-outputs">
    <div *ngFor="let output of outputs" class="output-sockets-success">
      <rete-socket *ngIf="output.name == 'Success'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
    <div *ngFor="let output of outputs" class="output-sockets-error">
      <rete-socket *ngIf="output.name == 'Error'" rete-socket [io]="output" [socket]="output.socket"></rete-socket>
    </div>
  </div>
  <p-overlayPanel #expbuilder [dismissable]="true" styleClass="add-edit">
    <div style="padding-bottom: 10px;"><b>Expression Builder</b><span style="float: right;">
      <i class="pi pi-times" id="expBuildClose" style="color:transparent" (click)="expbuilder.hide()"></i>
      <!-- <i class="fa-light fa-chevron-down"></i> -->
      <i class="pi pi-list cursor" style="color: var(--primary-color) !important;font-size: 1.3rem" (click)="openTestData()" pTooltip="Open Test Data"></i>
    </span></div>
    <ng-template pTemplate="body">
      <app-expression-builder [data]="expBuildVal"
        (overlayclose)="overlayCloseExpBuilder($event)"></app-expression-builder>
    </ng-template>
  </p-overlayPanel>
  <p-confirmPopup></p-confirmPopup>
  <div *ngIf="selected() && displayBtn" style="margin-left: 150px;height: 45px;"><app-add-nodes ></app-add-nodes></div>
  <!-- <div style="display:flex;width:150px;margin-left: 100px;justify-content: space-evenly;height: 45px;" *ngIf="!displayBtn">
    <p-button icon="pi pi-question" styleClass="p-button-rounded p-button-text p-button-raised" class="nodeBtnTest"
      pTooltip="Info" (onClick)="goToLink(helpurl)" tooltipPosition="right"></p-button>
    <p-button icon="pi pi-times" styleClass="p-button-rounded p-button-text p-button-raised" class="nodeBtnTest"
      pTooltip="Remove Node" tooltipPosition="right" (click)="confirmNodeDelete($event,node)"></p-button>
    
    <p-button icon="pi pi-save" styleClass="p-button-rounded p-button-text p-button-raised" class="nodeBtnTest"
      pTooltip="Save" tooltipPosition="right" [disabled]="validataFileds()"
      (onClick)="saveNodedata()"></p-button>
  </div> -->