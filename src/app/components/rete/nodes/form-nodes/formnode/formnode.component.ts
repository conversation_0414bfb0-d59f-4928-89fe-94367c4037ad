import { Component, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { QueryBuilderConfig } from 'ngx-angular-query-builder';
import { FieldMap } from '../../../QueryBuilder.model';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { UsersService } from 'src/app/services/users.service';
import { ReteService } from 'src/app/services/rete.service';
import { TeamsService } from 'src/app/services/team.service';
import { UtilsService } from 'src/app/services/utils.service';
import { QueryBuildermodelComponent } from '../../query-buildermodel/query-buildermodel.component';
import { ExpressionBuilderComponent } from '../../expression-builder/expression-builder.component';

// import {QueryBuildermodelComponent} 
@Component({
  selector: 'app-formnode',
  templateUrl: './formnode.component.html',
  styleUrls: ['./formnode.component.scss'],
  providers: [NodeService, DialogService]
})
export class FormnodeComponent extends NodeComponent{
  openPanelForNew:boolean;
  displayBtn:boolean=false;
  desc:any;
  dataItem:any;
  DispEditIcon:boolean=false;
  nodeDesc: boolean = false;
  nodeformdata: FormGroup;
  optionalprops = [];
  jsonLogicRule: any;
  helpurl = 'https://docs.unvired.com/builder/flows/';
  excludeparams = [];
  ingredient!: string;
  allforms: any;
  allusers: any;
  allteams: any;
  expBuildVal:any;
  expBuildData:any;
  allMasterData:any;
  formfields: any;
  ref: DynamicDialogRef;
  queryBuilddata:any;
  error:any='';
  enableDisable:boolean=false;

  config: QueryBuilderConfig = {
    fields: {}
  };
  formNameOptions: any[] = [
    { label: 'Forms', value: 'forms' },
    { label: 'Form Fields', value: 'formfields' }
];
userOptions: any[] = [
  { label: 'Users', value: 'users' },
  { label: 'Form Fields', value: 'formfields' }
];
teamOptions: any[] = [
  { label: 'Teams', value: 'teams' },
  { label: 'Form Fields', value: 'formfields' }
];
filter = {
  condition: 'and',
  rules: []
};
nodeName:any;
nodeIcon:any;
constructor(
  protected override service: NodeService,private usersservice: UsersService,
  protected override cdr: ChangeDetectorRef,private reteservice: ReteService,    private teamsservice: TeamsService,
  private confirmationService: ConfirmationService,public messageService: MessageService,
  private utilservice: UtilsService,private fb: FormBuilder,public dialogService: DialogService,
) {
  super(service, cdr);this.displayBtn=true;
  this.readonlyOnDisable = this.reteservice.getDisableWfData();

}
ngAfterViewInit(){  
  this.displayBtn = true;
      if(this.node.hasOwnProperty('new')){
        this.desc = this.node.data['name'];
        let event={
          collapsed:true
        }
        this.openPanelForNew=false;
        this.panelBtn(event,this.node);
        this.focusIn();
        // setTimeout(() => {
        //   this.reteservice.arrangeNodes.next(true);
        // }, 500);
       }else{
        this.desc = this.node.data['description'];
        this.openPanelForNew=true;
       }
       if(this.node.name == 'SearchForm' || this.node.name == 'FormAlert'){
        this.getAllUsers();
        this.getAllTeams();
      }
      this.nodeName= this.node.name;
      switch(this.node.name){
        case "UpdateRecord":
          this.nodeIcon = 'masterdata';
          break;
          case "UpdateRows":
            this.nodeIcon = 'updaterows';
            break;
            case "ReadRows":
              this.nodeIcon = 'readrows';
              break;  
              case "CreateForm":
                this.nodeIcon = 'create';
                break;
                case "UpdateForm":
                  this.nodeIcon = 'update';
                  break;
                  case "AssignForm":
                    this.nodeIcon = 'assign';
                    break;
                    case "ReadForm":
                      this.nodeIcon = 'read';
                      break;
                      case "SearchForm":
                      this.nodeIcon = 'search';
                      break;
                      case "ShareForm":
                        this.nodeIcon = 'share';
                        break;  
                        case "contact":
                          this.nodeIcon = 'contact';
                          break;
                          case "ArchiveForm":
                            this.nodeIcon = 'archive';
                            break;
                            case "CreatePDF":
                              this.nodeIcon = 'pdf';
                              break;
                              case "FormAlert":
                                this.nodeIcon = 'alert';
                                break;
       }
   }

 //open panel 
 async panelBtn(event:any,node:any){         
  if(event.collapsed == 'true' || event.collapsed == true){ 
    // this.displayBtn = false;
    // let newInput=node.data.inputs;
    // node.data.input=newInput;
    // delete node.data.inputs;
    // const data= {
    //   nodeId: node.id,
    //   node: this.node,
    //   nodeData: node.data,
    //   nodeName: node.name,
    //   formId: this.editor['workflow'].formId,
    //   editor: this.editor
    // }
    //  this.dataItem=data;               
   // //  await this.patchFormData();
    // await this.setDefaultValues();
    // // setTimeout(() => {
    // //   this.reteservice.arrangeNodes.next(true);
    // // }, 500);
    // this.reteservice.pushNodeId(this.dataItem.nodeId);
    this.displayBtn = false;
    if (node.data.inputs == undefined) {

    } else {
      let newInput = node.data.inputs;
      node.data.input = newInput;
      delete node.data.inputs;
    }
    const data = {
      nodeId: node.id,
      node: this.node,
      nodeData: node.data,
      nodeName: node.name,
      formId: this.editor['workflow'].formId,
      editor: this.editor
    }
    this.dataItem = data;      
    this.setDefaultValues();
    this.reteservice.pushNodeId(this.dataItem.nodeId);
  }else{
    if(node.data.input){
    let newInput=node.data.input;
    node.data.inputs=newInput;
    delete node.data.input;
    }
    this.displayBtn = true;
    this.nodeDesc=false;
    this.DispEditIcon=false;
    this.searchuserteamVal="";
    this.searchOnArray=[];
    this.reteservice.popNodeId(this.dataItem.nodeId);
    // setTimeout(() => {
    //   this.reteservice.arrangeNodes.next(true);
    // }, 500);
    this.enableDisable=false;
  }
}
goToLink(url: string){
  window.open(url, "_blank");
}
AllWfName:any;
  focusIn() {
    if (this.nodeDesc == false) {
      this.nodeDesc = true;
      window.setTimeout(function () {
        document.getElementById('inputBox').focus();
      }, 10);
    } else {
     
    }
  }
duplicate:boolean=false;
nodeNameChange:boolean=false;
  FocusOut(){
    let oldName = this.node.data['name'];
    if(this.nodeDesc != false){
      this.node.data['description'] = this.desc;
      // if(this.node.data['name'] == 'untitled' || this.duplicate){
      // this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      // }
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      this.nodeDesc = false;
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      }
    }else{
      this.nodeDesc = true;

    }
    if(this.node.data['name']){
    }else{
      this.desc='untitled';
      this.node.data['description'] = this.desc;
      this.node.data['name'] = this.desc.replace(/[^a-zA-Z]/g, "").toLowerCase();
      let wfnodenames = this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
      const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
      if (this.checkIfDuplicateExists(wfnodenames)) {
        this.duplicate = true;
      }else{
        this.duplicate = false;
      } 
    }
    if(this.node.data['name'] != 'untitled'){
      if((oldName != this.node.data['name']) && (oldName != 'untitled')){
      this.reteservice.flowTechName.next("change");
       }
    }
    // if(this.node.data['name'] != 'untitled'){
    //   if((oldName != this.node.data['name'])){
    //   // this.reteservice.flowTechName.next("change");
    //   this.nodeNameChange = true;
    //    }else{
    //     this.nodeNameChange = false;
    //    }
    // }else{
    //   this.nodeNameChange = false
    // }
    
  }
  checkIfDuplicateExists(arr) {
    return new Set(arr).size !== arr.length
}

async patchFormData(){
  let wfnodenames= this.utilservice.getAllNodeNames(this.dataItem.editor.toJSON());
    this.helpurl = this.helpurl + '#' + this.dataItem.nodeData.wfName;
    const index = wfnodenames.indexOf(this.dataItem.nodeData.name);
    if (index > -1) {
      wfnodenames.splice(index, 1);
    }
    this.nodeformdata = this.fb.group({
      name: [this.dataItem.nodeData.name],
      description: [this.dataItem.nodeData.description],
      input: this.fb.array([])
     });
}
get input() {
  return this.nodeformdata.get('input') as FormArray;
}
get entityType() {
  return this.nodeformdata.get('entityType');
}
readonlyOnDisable:boolean=false;
async setDefaultValues() {   
   if(this.dataItem.nodeName !== 'ReadRows') {
    this.patchFormData();
    this.getAllForms();
    if(this.dataItem.nodeName != 'SearchForm' || this.dataItem.nodeName != 'FormAlert'){
      this.getAllUsers();
      this.getAllTeams();
    }
    this.getMasterData();
     if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
      this.jsonLogicRule = this.dataItem.nodeData.jsonLogicRule;
      //add optional params to input for old nodes
      this.optionalprops = this.dataItem.nodeData.optionalprops;
      for(let key in this.optionalprops ){
        this.dataItem.nodeData.input.push(this.optionalprops[key])
      }  
      this.dataItem.nodeData.optionalprops=[];      
      this.dataItem.nodeData.input.forEach(async (inputparam,ind) => {
        this.excludeparams.push(inputparam.inputName);
      await this.addInputparamsInNodedata(inputparam);
      });
      if(this.nodeName != 'CreatePDF'){
      await this.hideOptionalFields(this.enableDisable)     
      } 
   } else {
    if(this.reteservice.getFlow().flowType.toLowerCase() == 'masterdata' && this.dataItem.nodeData.category == 'MasterData'){
      this.reteservice.getworkflow(this.reteservice.getFlow().flowId,this.reteservice.getFlow().flowType).subscribe(async (res) => {
    this.defaultMasterData = res.formName
  });
 }
    this.getformparams();
  }
} else {  
        this.patchFormData();
        this.getMasterData();
        this.importformfields();
        if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
          this.optionalprops = this.dataItem.nodeData.optionalprops;
      for(let key in this.optionalprops ){
        this.dataItem.nodeData.input.push(this.optionalprops[key])
      }  
      this.dataItem.nodeData.optionalprops=[];
      this.dataItem.nodeData.input.forEach(async(inputparam,ind) => {
        if(inputparam.inputName == "filter"){
            if(inputparam.values.includes('rules') && inputparam.values.includes('condition')){
                let val =  JSON.parse(inputparam.values);
                if(val.rules.length > 0){
                  this.showTick=true;
                }else{
                    this.showTick=false;
                }
            } 
          
        }
        
        this.excludeparams.push(inputparam.inputName);
        await this.addInputparamsInNodedata(inputparam);
      }); 
      if(this.nodeName != 'CreatePDF'){
      await this.hideOptionalFields(this.enableDisable)  
      }    

        }else{
          if(this.reteservice.getFlow().flowType.toLowerCase() == 'masterdata' && this.dataItem.nodeData.category == 'MasterData'){
            this.reteservice.getworkflow(this.reteservice.getFlow().flowId,this.reteservice.getFlow().flowType).subscribe(async (res) => {
          this.defaultMasterData = res.formName
        });
       }
          this.getformparams();
        }
      //  if (this.dataItem.nodeData.filter) {
      //   this.filter = this.dataItem.nodeData.filter;
      //   this.filter.rules.forEach( element => {
      //     this.ruleOperationChanged(element);
      //   });
      //   this.nodeformdata.addControl('filter', this.fb.control(this.filter));
      //  } else {
      //   this.nodeformdata.addControl('filter', this.fb.control(this.filter));
      // }
    }
    if(this.reteservice.getDisableWfData()){
      this.readonlyOnDisable = this.reteservice.getDisableWfData()
      this.nodeformdata.disable();
    }
}
masterdataSelectedID:any;
async getMasterdataId(event:any){
  this.allMasterData.forEach((item)=>{
    if(item.masterdataName == event){
      this.masterdataSelectedID = item.masterdataId;
    }
  })
}
showTick:boolean=false;
openQueryBuilder(nodeData:any,formValue:any,index:any){  
  this.getMasterdataId(this.input?.at(0).get('values').value)
  let FormData={
    nodeData:nodeData,
    formData:formValue,
    masterdata:true,
    masterdataId:this.masterdataSelectedID
  }
  this.ref = this.dialogService.open(QueryBuildermodelComponent, {
    header: 'Query Builder',
    style:{"min-width":"50%","max-height":"75%","max-width":"75%","z-index":999},
    contentStyle: { overflow: 'auto', padding: '5px' },
    maximizable: false,
    modal:true,
    draggable:true,
    data: FormData
  });
  this.ref.onClose.subscribe((res:any) => {
    if(res == undefined){
     // this.reteservice.getRunflowToDialogClose.next(true)
    }else{
    if(res.save == true){
      if(res.qData.filter.rules.length > 0){
        this.showTick=true;
      }else{
          this.showTick=false;
      }
      this.input.at(index).get('values').setValue(JSON.stringify(res.qData.filter));
    }else{
      this.input.at(index).get('values').setValue(FormData.formData);
    }
  }
    // this.nodeformdata.get('filter').patchValue(res.filter);
  });
}

ruleOperationChanged(rule: any) {
  if(rule.operator === "between" || rule.operator === "not between"){
      if(!rule.value) {
      rule.value = [];
    }
  }
  else{
    if(!rule.value) {
      rule.value = null;
    }
    }
 }

getAllForms() {
  this.reteservice.getallformsforworkflow('form').subscribe( res => {
    const resp = res;
    if(resp.totalTeams !== 0) {
      this.allforms = resp.formHeaders;      
    } else {
     // this.errmsg = 'No Forms found!.';
    }
  });
}

getAllUsers() {
  this.usersservice.getusers('', 'asc', 0, 1000, '', null, null, null).subscribe(
      res => {
        const resp = res;
        if (resp.totalUsers !== 0) {
          this.allusers = resp.formUsers;
        } else {
          this.allusers = [];
        //  this.errmsg = 'No users found!.';
        }
      }
    )
}

getAllTeams() {
  this.teamsservice.getteams('', '', 'asc', 0, 100, '', null).subscribe(
    res => {
      const resp = res;
      if(resp.totalTeams !== 0) {
        this.allteams = resp.teams;
      } else {
        //this.errmsg = 'No Teams found!.';
        this.allteams=[];
      }
    }
  )
}

getMasterData(){
    this.reteservice.updateMasterDataRecord().subscribe((res)=>{
      this.allMasterData=res.masterdata;
    });
  
}

importformfields() {
    this.reteservice.importformfields( this.dataItem.formId)
      .subscribe(async (res) => {
        const response = res;
        if (response.error === '') {
          if (response.status === 'Success') {
            this.formfields = response.formFields;
            this.config = {
              fields: this.setExpressionParam(this.formfields)
            };
         }
        } else {
          //this.errmsg = 'Form does not have any fields to setup conditional queries.';
        }
      });
}

setExpressionParam(arrayexample): FieldMap {
    const arrayToObject = (array) =>
      array.reduce((obj, item) => {
        obj[item.key] = item;
        return obj;
      }, {});
    const fieldMap: FieldMap = arrayToObject(arrayexample);
    return fieldMap;
}
defaultMasterData:any;
async getformparams() {
//    if(this.reteservice.getFlow().flowType.toLowerCase() == 'masterdata' && this.dataItem.nodeData.category == 'MasterData'){
//       this.reteservice.getworkflow(this.reteservice.getFlow().flowId,this.reteservice.getFlow().flowType).subscribe(async (res) => {
//     this.defaultMasterData = res.formName
//   });
//  }
  this.reteservice.getformsfielsfor$values(this.dataItem.nodeData.wfName,this.dataItem.nodeData.wfNamespace,this.dataItem.formId,this.excludeparams.toString()).subscribe(async res => {
      const response = res;
      if (response.error === '') {
        if (response.status === 'Success') {
            if (response.prerequisites != "") {
              this.jsonLogicRule = response.prerequisites;
            }else{
              this.jsonLogicRule=[];
            }   
            response.inputs.forEach(async(inputparam,ind) => {
              // if (inputparam.type === 'keyvalue') {
              //     inputparam.valuepairs=[{key:'',value:''}]
              //     this.addkeyvalueType(inputparam);
              //  }
              // else
              //   {
            await  this.addInputparamsInNodedata(inputparam);
              // }
            })            
          }
          if(this.nodeName != 'CreatePDF'){
          await this.hideOptionalFields(this.enableDisable);
          }
        }  else{
          this.error=response.error 
          this.messageService.add({ severity: 'error', summary: 'ERROR', detail: response.error });
        } 
  });
}

async  addInputparamsInNodedata(inputParameter: any) {    
  if(inputParameter.required == 'Mandatory'){
    inputParameter.show = true;
  }else{
    inputParameter.show = false;
  }
   inputParameter.radiolistvalue=[];
  inputParameter.listvalue=[];
  if ((inputParameter.type === "list" || inputParameter.type === "radio") && inputParameter.listvalues && Array.isArray(inputParameter.listvalues) && !Array.isArray(inputParameter.listvalues[0]))
   { 
     inputParameter.listvalues = [inputParameter.listvalues];     
  }
  if (inputParameter.type === "list" && inputParameter.listvalues && inputParameter.inputName == 'searchcriteria') {
    this.searchOnArray=[];
    inputParameter.listvalue.push([]);
     if(inputParameter.listvalues[0] !== undefined){
        inputParameter.listvalues[0].forEach(data=>{  
          if(data == 'Users or Teams'){
            inputParameter.listvalue[0].push({"key":data,"value":'usersorteams'})
          }
          if(data == 'Start Date'){
            inputParameter.listvalue[0].push({"key":data,"value":'submitstartdate'})
          }
          if(data == 'End Date'){
            inputParameter.listvalue[0].push({"key":data,"value":'submitenddate'})
          }
          if(data == 'Status'){
            inputParameter.listvalue[0].push({"key":data,"value":'status'})
          }
          if(data == 'External Reference'){
            inputParameter.listvalue[0].push({"key":data,"value":'extreference'})
          }
          if(data == 'Form Name'){
            inputParameter.listvalue[0].push({"key":data,"value":'formname'})
          }
          if(data == 'Form Data'){
            inputParameter.listvalue[0].push({"key":data,"value":'formdata'})
          }
        });  
        if(inputParameter.values && Array.isArray(inputParameter.values) && !Array.isArray(inputParameter.values[0])){
          inputParameter.values = [inputParameter.values]
        }
        if(inputParameter.values[0] == undefined){
          this.searchOnArray = []
        }else{
        this.searchOnArray = inputParameter.values[0]; 
        }     
     }
  }
  if ((inputParameter.type === "user" || inputParameter.type === "team") && inputParameter.values && Array.isArray(inputParameter.values) && !Array.isArray(inputParameter.values[0])) 
  {
     this.searchuserteamVal="";
     inputParameter.values = [inputParameter.values]; 
    }
if(this.dataItem.nodeName === 'SearchForm' && inputParameter.inputName == 'completedbyusers'){
    if(inputParameter.values && inputParameter.values.length > 0){
      this.searchuserteamVal="user";
    }
}
if(this.dataItem.nodeName === 'SearchForm' && inputParameter.inputName == 'completedbyteams'){
    if(inputParameter.values && inputParameter.values.length > 0){
      this.searchuserteamVal="team";
    }
}
  if (inputParameter.type === "list" && inputParameter.listvalues && inputParameter.inputName !== 'searchcriteria') {
    inputParameter.listvalue.push([]);
     if(inputParameter.listvalues[0] !== undefined){
        inputParameter.listvalues[0].forEach(data=>{  
          inputParameter.listvalue[0].push({"key":data,"value":data.toLowerCase().replace(/\s+/g, '')})
        });  
      inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');
     }
  }

  if (inputParameter.type === "radio" && inputParameter.listvalues) {
    inputParameter.radiolistvalue.push([]);
    if(inputParameter.listvalues[0] !== undefined){
        inputParameter.listvalues[0].forEach(data=>{            
          inputParameter.radiolistvalue[0].push({"key":data,"value":data.toLowerCase().replace(/\s+/g, '')})
        });  
    }
    inputParameter.values = inputParameter.values.toLowerCase().replace(/\s+/g, '');      
  }
  if(inputParameter.type == "boolean" && inputParameter.defaultValue == false && (inputParameter.values == "False" || inputParameter.values == "false")){    
    inputParameter.defaultValue = false;
    inputParameter.values = false;
  }
  if(inputParameter.type == "boolean" && inputParameter.defaultValue == true && (inputParameter.values == "True" || inputParameter.values == "true")){
    inputParameter.defaultValue = true;
    inputParameter.values = true;
  }
  if(inputParameter.inputName === "recipienttype"){
      if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
        this.userTeamSelectValue=inputParameter.values;
      }else{
         setTimeout(() => {
        this.CreateFormRadio(inputParameter.values);
          }, 200);    
      }
  }
  if(inputParameter.inputName === "action"){
      if(this.dataItem.nodeData.input && Object.keys(this.dataItem.nodeData.input).length > 0){
       // this.userTeamSelectValue=inputParameter.values;
      }else{
         setTimeout(async () => {
       await this.getRadioValue(inputParameter.values);
          }, 50);    
      }
   
  }  
  if(this.reteservice.getFlow().flowType.toLowerCase() == 'masterdata' && this.dataItem.nodeData.category == 'MasterData'){
    if(inputParameter.type == "masterdataname" && this.dataItem.nodeData.input == undefined){       
          inputParameter.values = [this.defaultMasterData];
        }
      
  }

   this.input.push(this.fb.group(inputParameter));    
}
async hideOptionalFields(hide:any){
  this.input.controls.forEach(key => {
     if(key.value.required.toLowerCase() == 'optional'){ 
      if (key.value.show == true) {
        key.patchValue({ show: false });
      }else{
        key.patchValue({ show: true });
      }
   }else if(key.value.type == 'keyvalue'){
    if (key.value.show == true) {
      key.patchValue({ show: false });
    }else{
      key.patchValue({ show: true });
    }
   }else{
    key.patchValue({ show: false });
   }
  });    
}
shareRadio:any;
shareFormRadio(event:any){
    if(event.toLowerCase() === "private"){
      this.shareRadio=event;
     this.input.controls.forEach(key=>{
      if(key.value.inputName === "shareto"){
        key.patchValue({required: "Mandatory"});
        key.patchValue({values : ""});
      }
     });
    }else{
      this.shareRadio=event;
      this.input.controls.forEach(key=>{
        if(key.value.inputName === "shareto"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
       });
    }
}

async getRadioValue(event:any){
  if(event.toLowerCase() === "update"){
    this.input.controls.forEach(key=>{
      if(key.value.inputName === "firstname"){
        key.patchValue({required: "Mandatory"});
        key.patchValue({values : ""});
      }
     });
  }else{
    this.input.controls.forEach(key=>{
      if(key.value.inputName === "firstname"){
        key.patchValue({required: "Optional"});
        key.patchValue({values : ""});
      }
     });
  }
  
}

userTeamSelectValue:any;
CreateFormRadio(event:any){
   this.userTeamSelectValue="";
   if(event.toLowerCase() === "user"){
      this.userTeamSelectValue="user";
    this.input.controls.forEach(key=>{
     if(key.value.inputName === "user"){
       key.patchValue({required: "Mandatory"});
       key.patchValue({defaultSelect: "users"});
       key.patchValue({values : ""});
     }
     if(key.value.inputName === "team"){
      key.patchValue({required: "Optional"});
      key.patchValue({defaultSelect: "teams"});
      key.patchValue({values : ""});
    }
     });
    }else if(event.toLowerCase() === "team"){
        this.userTeamSelectValue="team";
     this.input.controls.forEach(key=>{
      if(key.value.inputName === "user"){
        key.patchValue({required: "Optional"});
        key.patchValue({defaultSelect: "users"});
        key.patchValue({values : ""});
      }
      if(key.value.inputName === "team"){
       key.patchValue({required: "Mandatory"});
       key.patchValue({defaultSelect: "teams"});
       key.patchValue({values : ""});
     }
      });
    }else if(event.toLowerCase() === "teams"){
       this.userTeamSelectValue="teams";
    this.input.controls.forEach(key=>{
     if(key.value.inputName === "completedbyusers" && this.dataItem.nodeName === 'SearchForm'){
       key.patchValue({required: "Optional"});
       key.patchValue({defaultSelect: "users"});
       key.patchValue({values : ""});
     }
     if(key.value.inputName === "completedbyteams" && this.dataItem.nodeName === 'SearchForm'){
      key.patchValue({required: "Mandatory"});
      key.patchValue({defaultSelect: "teams"});
      key.patchValue({values : ""});
    }
    if(key.value.inputName === "users" && this.dataItem.nodeName !== 'SearchForm'){
      key.patchValue({required: "Optional"});
      key.patchValue({defaultSelect: "users"});
      key.patchValue({values : ""});
    }
    if(key.value.inputName === "teams" && this.dataItem.nodeName !== 'SearchForm'){
     key.patchValue({required: "Mandatory"});
     key.patchValue({defaultSelect: "teams"});
     key.patchValue({values : ""});
   }
     });
    }else if(event.toLowerCase() === "users"){
       this.userTeamSelectValue="users";
    this.input.controls.forEach(key=>{
     if(key.value.inputName === "completedbyusers" && this.dataItem.nodeName === 'SearchForm'){
       key.patchValue({required: "Mandatory"});
       key.patchValue({defaultSelect: "users"});
       key.patchValue({values : ""});
     }
     if(key.value.inputName === "completedbyteams" && this.dataItem.nodeName === 'SearchForm'){
      key.patchValue({required: "Optional"});
      key.patchValue({defaultSelect: "teams"});
      key.patchValue({values : ""});
    }
     if(key.value.inputName === "users" && this.dataItem.nodeName !== 'SearchForm'){
      key.patchValue({required: "Mandatory"});
      key.patchValue({defaultSelect: "users"});
      key.patchValue({values : ""});
    }
    if(key.value.inputName === "teams" && this.dataItem.nodeName !== 'SearchForm'){
      key.patchValue({required: "Optional"});
      key.patchValue({defaultSelect: "teams"});
      key.patchValue({values : ""});
    }
     });
   }else{
     this.userTeamSelectValue="";
    }
}

searchuserteamVal:any;
searchFormRadio(event:any){
this.searchuserteamVal = '';
 if(event.toLowerCase() === "user"){
  this.searchuserteamVal="user";
  this.input.controls.forEach(key=>{
    if(key.value.inputName === "completedbyusers"){
      key.patchValue({required: "Mandatory"});
      key.patchValue({defaultSelect: "users"});
      key.patchValue({values : ""});
    }
    if(key.value.inputName === "completedbyteams"){
        key.patchValue({required: "Optional"});
        key.patchValue({defaultSelect: "teams"});
        key.patchValue({values : ""});
      }
  })
 }else if(event.toLowerCase() === "team"){
  this.searchuserteamVal="team";
  this.input.controls.forEach(key=>{
   if(key.value.inputName === "completedbyusers"){
     key.patchValue({required: "Optional"});
     key.patchValue({defaultSelect: "users"});
     key.patchValue({values : ""});
   }
   if(key.value.inputName === "completedbyteams"){
    key.patchValue({required: "Mandatory"});
    key.patchValue({defaultSelect: "teams"});
    key.patchValue({values : ""});
  }
});
 }else{
  this.searchuserteamVal="";
 }
}

searchOnArray:any=[];
searchOn(event:any){
  const previousSelection = this.searchOnArray.slice();
  this.searchOnArray=event;
  const removedItems = previousSelection.filter(item => !this.searchOnArray.includes(item));
this.searchOnRemove(removedItems[0])
}
searchOnRemove(event:any){
  if(event != undefined){ 
  this.input.controls.forEach(key=>{
       if(event == 'usersorteams'){
        if(key.value.inputName === "completedbyusers"){
          key.patchValue({required: "Optional"});
          key.patchValue({defaultSelect: "users"});
          key.patchValue({values : ""});
        }
        if(key.value.inputName === "completedbyteams"){
         key.patchValue({required: "Optional"});
         key.patchValue({defaultSelect: "teams"});
         key.patchValue({values : ""});
       }
       this.searchuserteamVal="";
      }
      if(event == 'submitstartdate'){
        if(key.value.inputName === "submitstartdate"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
      }
      if(event == 'submitenddate'){
        if(key.value.inputName === "submitenddate"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
      }
      if(event == 'status'){
        if(key.value.inputName === "status"){
          key.patchValue({required: "Optional"});
          key.patchValue({defaultSelect: "Completed"});
          key.patchValue({values : ""});
        }
      }
      if(event == 'extreference'){
        if(key.value.inputName === "extreference"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
      }
      if(event == 'formname'){
        if(key.value.inputName === "extreference"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
      }
      if(event == 'formdata'){
        if(key.value.inputName === "formdata"){
          key.patchValue({required: "Optional"});
          key.patchValue({values : ""});
        }
      }
  });
}
}
isFiledsSelected(fields: string): boolean {
  return this.searchOnArray.includes(fields);
}
//open overlay panel Expbuild system and send data
  openExprBuilder(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){
    this.expBuildData={};
    this.expBuildData={
      "event":event,
      "callerformfieldvalue":callerformfieldvalue,
      "callerformfieldtype":callerformfieldtype,
      "index":index,
      "keyvalindex":keyvalindex,
      "nodeName":this.dataItem.nodeName
    }   
      event.stopPropagation();
      event.preventDefault();
      this.expBuildVal=this.dataItem
      if (callerformfieldvalue !== null) {
        this.expBuildVal.exprbuildervalue = callerformfieldvalue;
      } else {
        this.expBuildVal.exprbuildervalue = '';
      }
  }

  openExpBuilderDialog(event: any, callerformfieldvalue: string, callerformfieldtype: string, index?: number, keyvalindex?: number){
    this.expBuildData={};
    this.expBuildData={
      "event":event,
      "callerformfieldvalue":callerformfieldvalue,
      "callerformfieldtype":callerformfieldtype,
      "index":index,
      "keyvalindex":keyvalindex,
      "nodeName":this.dataItem.nodeName
    }   
      event.stopPropagation();
      event.preventDefault();
      this.expBuildVal=this.dataItem
      if (callerformfieldvalue !== null) {
        this.expBuildVal.exprbuildervalue = callerformfieldvalue;
      } else {
        this.expBuildVal.exprbuildervalue = '';
      }
      this.ref = this.dialogService.open(ExpressionBuilderComponent, {
        header: 'Expression Builder',
        contentStyle: { overflow: 'auto', padding: '5px' },
        style: { "max-height": "75%", "max-width": "75%" ,"z-index":999},
        // baseZIndex: 10000,
        modal: true,
        maximizable: false,
        data: this.expBuildVal,
        draggable: true
      });
      this.ref.onClose.subscribe(async (res: any) => {
        if(res == undefined){
          this.reteservice.getRunflowToDialogClose.next(true)
        }else{
        let val = res.expBuildValue;
        if (this.expBuildData.callerformfieldtype === 'keyvalue') {
          const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
          control.at(this.expBuildData.keyvalindex).get('value').setValue(val);
        } else {
          this.input.at(this.expBuildData.index).get('values').setValue(val);
        }
      }
      });
  
  }

  //close expBuild overlay panel
  overlayCloseExpBuilder(data:any){
    let val=data.expBuildValue;
      if (this.expBuildData.callerformfieldtype === 'keyvalue') {
        const control = this.input.controls[this.expBuildData.index].get('valuepairs') as FormArray;
         control.at(this.expBuildData.keyvalindex).get('value').setValue(val);      
      } else {
         this.input.at(this.expBuildData.index).get('values').setValue(val);
      }
      document.getElementById('expBuildClose').click();
  }

validataFileds(){  
  let validate:any;
  let totalMandateFields:number=0;
  let mandateValue:number=0;
          this.input.value.forEach((val)=>{
              if(val.required === "Mandatory"){
                totalMandateFields = totalMandateFields + 1; 
                if(val.values == "" || val.values == null || val.values == 'null'){
                  // mandateValue = mandateValue - 1;
                  }else{
                    mandateValue = mandateValue + 1;
                  }                   
              }   
          });
          if (totalMandateFields == mandateValue && !this.duplicate) {
            validate = false;
          }else{
            validate = true;
          }
    return validate; 
}

async saveNodedata(){
  let nodeId;
  if(this.nodeDesc == true){
    this.focusIn();
  }
  nodeId =this.reteservice.getNodeButtonId();     
   document.getElementById(nodeId).click();
 await this.savedata();
  const data = this.editor.toJSON(); 
   this.reteservice.saveWfdata(this.editor['workflow'].wfId, data, true);      
}

//prepare node data to save
async savedata() { 
  let data:any;
  if(this.dataItem.nodeName === "ReadRows"){
    this.nodeformdata.value.filter=this.queryBuilddata;
     data = this.nodeformdata.value;
  }else{
     data = this.nodeformdata.value;
  }
  if (this.jsonLogicRule) {
    data.jsonLogicRule = this.jsonLogicRule;
  }
 // if(this.nodeNameChange){
   // this.reteservice.flowTechName.next("change");
  //}
  data.wfName = this.dataItem.nodeData.wfName;
  data.wfNamespace = this.dataItem.nodeData.wfNamespace;
  data.icon = this.dataItem.nodeData.icon;
  data.category = this.dataItem.nodeData.category;
  data.savedata = true;
  data.description=this.dataItem.nodeData.description;
  data.name=this.dataItem.nodeData.name;
  let newInput=data.input;
  data.inputs=newInput;
  delete data.input; 
  this.node.data = data;          
}

//remove node 
removenode(node: any) {
  this.editor.removeNode(node);
}

//popup node delete confirm
confirmNodeDelete(event:Event,node:any) {
this.confirmationService.confirm({
    target: event.target,
    message: 'Are you sure that you want to proceed?',
    icon: 'pi pi-exclamation-triangle',
    accept: () => {
      this.editor.removeNode(node);
      this.reteservice.popNodeId(node.id);
    },
    reject: () => {
    }
});  
}
openTestData(){
  this.reteservice.openTestDataPanel.next('open');
}

excludedNodes = ['UpdateRecord', 'UpdateRows','ReadRows','CreatePDF','ArchiveForm','SearchForm'];
isNodeNameExcluded(nodeName: string): boolean {
  return !this.excludedNodes.includes(nodeName);
}
}