.nodeIcon2{
    color:var(--cyan-500)!important;
    padding: 11px; 
    background:var(--cyan-100)!important;
  }
  .nodeIcon{
    // color: var(--purple-500)!important;
    // padding: 12px; 
    width: 50px;
    height: 47px;
   // font-size: 26px;
    background:var(--cyan-100)!important;
   }
  .p-inputtext {
    width: -webkit-fill-available;
  }
  .pmgImg{
    margin-top: 8px;
    margin-left: 10px;
    height: 30px !important;
    width: 30px !important;
  }
  ::ng-deep .selectBtn1 .p-button {
    font-size: 0.875rem !important;
    padding: 8px !important;
    width: 50% !important;
    // box-shadow: none !important;
    
  } 
  ::ng-deep .selectBtn2 .p-button {
    font-size: 0.875rem !important;
    padding: 8px !important;
    width: 33.3% !important;
    // box-shadow: none !important;
    
  } 
  ::ng-deep .warnMsg .p-inline-message{
    width: 100%;
  }
  // ::ng-deep  .panelOpenform .p-panel .p-panel-header .p-panel-header-icon{
  //   margin-right: 16px !important;
  // }
  // ::ng-deep  .panelOpenform .p-panel .p-panel-header .p-panel-header-icon:focus{
  //   box-shadow:0 0 0 0rem !important;
  // }
  // ::ng-deep  .panelOpenform .p-panel.p-panel-toggleable .p-panel-header{
  //   padding:0px;
  //   height: 48px;
  //   cursor: move;
  // }
  // ::ng-deep  .panelOpenform .p-panel .p-panel-header{
  //   background:  var(--cyan-50)!important;
  //   border-top-left-radius:0px;
  //   border-top-right-radius:0px;
  //   border: none;
  //   border-bottom: 1px solid #dee2e6;
  //   cursor: move;
  // }
  // ::ng-deep  .panelOpenform .p-panel .p-panel-content{
  //   padding: 0px 0px 10px 7px;
  //   // min-height: 190px !important;
  //   border: none;
  
  // }
  .selected {
    // box-shadow: 10px 5px 5px red;
    border: 2px solid rgb(145, 149, 139);

  // box-shadow:0 0 6px 6px rgb(145, 149, 139);
  }
  :host ::ng-deep .fieldDisplay {
    display: none !important;
  }