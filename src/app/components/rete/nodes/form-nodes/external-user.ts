import { Component, Output, Input } from 'rete';
import { strSocket, successSocket, errorSocket, formsSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { AngularComponentData } from 'rete-angular-render-plugin';
import { FormnodeComponent } from './formnode/formnode.component';
export class ExternalUserComponent extends Component {
 override data: AngularComponentData;
  defaultNodeData = {
    name: 'untitled',
    description: '',
    wfName: 'contact',
    wfNamespace: 'unvired.flow',
    icon: 'person',
    category: 'Forms'
  };
  constructor() {
    super('contact');
    this.data.render = 'angular';
    this.data.component = FormnodeComponent;
  }
builder(node) {
  const inp1 = new Input('contact', 'Input', formsSocket);
  const out1 = new Output('contact_success', 'Success', successSocket, false);
  const out2 = new Output('contact_error', 'Error', errorSocket, false);
  node.data = Object.keys(node.data).length === 0 && node.data.constructor === Object ? this.defaultNodeData  : node.data;
  return node.addInput(inp1)
              .addOutput(out1)
              // .addControl(new StrControl(this.editor, 'shortname'))
              .addOutput(out2);
}

worker(node, inputs, outputs) {
 //  outputs.transform = node.data.transformdata;
}
}
