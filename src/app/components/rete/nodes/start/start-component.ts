import { Component,  Output } from 'rete';
import { strSocket, successSocket, errorSocket, startSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { StartComponent as StartComp} from './start.component';
import { AngularComponent,AngularComponentData } from 'rete-angular-render-plugin';
export class StartComponent extends Component {
  
  override data: AngularComponentData;
  constructor() {
    super('Start');
     this.data.render = 'angular';
     this.data.component = StartComp;
  }

  builder(node) {
    const out1 = new Output('Start', 'Output', startSocket, false);
    node.meta = {
      category: 'standard',
      name: 'start',
      index: 0,
      icon: 'flag',
      description: 'Start of the workflow.'
    };
    return node.addOutput(out1);
  }

  worker(node, inputs, outputs) {
  //  outputs.start = node.data.start;
  }
}
