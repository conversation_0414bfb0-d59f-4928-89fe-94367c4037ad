import { Component, ChangeDetectorRef, AfterViewInit, ViewChild, ElementRef, Input } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
import { ReteService } from 'src/app/services/rete.service';
@Component({
  selector: 'app-start',
  templateUrl: './start.component.html',
  styleUrls: ['./start.component.scss'],
  providers: [NodeService]
})
export class StartComponent extends NodeComponent implements AfterViewInit{
  parentMessage:boolean;
  messages:any;
  messages2:any;
  readonlyOnDisable:boolean=false;
  wfActionType:any='';
  isNew:boolean=false;
  constructor(
    protected override service: NodeService,
    protected override cdr: ChangeDetectorRef,private reteservice: ReteService,
  ) { 
    super(service, cdr);
    this.readonlyOnDisable = this.reteservice.getDisableWfData();    
    if(this.reteservice.getflowActionType().flowType == ""){
      let data={
        'flowType':"Start",
        'flowDesc':"Start Node"
      }
      this.wfActionType = data;
    }else{
      this.wfActionType =  this.reteservice.getflowActionType();      
    }
    if(this.reteservice.getIsNewWf()){      
      this.isNew=true;
      this.parentMessage=true
    }else{
      this.isNew=false;
      this.parentMessage=false
    }
  }
  ngAfterViewInit(){

    }
    parentWillTakeAction(msg){
      this.isNew = false;
    }

  panelBtn(event: any, node: any) {
    if (event.collapsed == 'true' || event.collapsed == true) {      
      this.messages = [{ severity: 'info', detail:this.wfActionType.flowDesc}];
  //  setTimeout(() => {
  //       this.reteservice.arrangeNodes.next(true);
  //     }, 500);
    }else{
  //  setTimeout(() => {
  //       this.reteservice.arrangeNodes.next(true);
  //     }, 500);
    }
  }
  public copy() {
    this.reteservice.odataMessages.next({"type":'success','message':'Copied to clipboard'})
  }
}
