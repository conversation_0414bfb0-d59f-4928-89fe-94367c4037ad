<div class="nodes-input-socket" *ngFor="let input of inputs">
  <rete-socket rete-socket [io]="input" [socket]="input.socket"></rete-socket>
</div>
<div class="main" [ngClass]="[selected()]">
  <div class="node-container" >
    <p-panel toggleable="true" [collapsed]="true" expandIcon="pi pi-chevron-up" collapseIcon="pi pi-chevron-down"
    class="panelOpen" (onBeforeToggle)="panelBtn($event,node)">
   <ng-template pTemplate="header">
       <!-- <i class="pi pi-play nodeIcon" pTooltip="Start Node" tooltipPosition="top"></i> -->
       <span class="nodeIcon" pTooltip="Start Node" tooltipPosition="top"><img class="pmgImg" src="assets/icon/flag.png"></span>
      <span class="ml-5">
        <!-- <span class="spanText">{{node.meta['name']}}</span> -->
        <span class="spanText">{{wfActionType.flowType}}</span>
      </span>
    </ng-template>
    <ng-template pTemplate="content">
      <div class="startcardBody">
        <p-messages  severity="info" class="startMsg">
          <ng-template pTemplate>
            <div class="ml-2">{{wfActionType.flowDesc}}</div>
        </ng-template>
        </p-messages>
        <p-card class="startCard" *ngIf="wfActionType.workflowNodeInfo != undefined">
          <div class="grid" *ngIf="this.wfActionType.flowType == 'Webhook Flow'">
          <div class="col-10 startnode">
            {{wfActionType.workflowNodeInfo}}
           </div>
          <div class="col-2">
               <i class="pi pi-copy cursor mt-3" (click)="copy()" style="color: var(--primary-color) !important;font-size: 1rem" [cdkCopyToClipboard]="wfActionType.workflowNodeInfo"  pTooltip="Copy to clipboard"></i>
          </div>
        </div>
        <div class="startnode" *ngIf="this.wfActionType.flowType != 'Webhook Flow'">{{wfActionType.workflowNodeInfo}}</div>
      </p-card>
      </div>
    </ng-template>
  </p-panel>
  </div>
</div>
<div class="outputs">
  <div class="start-node-output" *ngFor="let output of outputs">
      <rete-socket rete-socket [io]="output" [socket]="output.socket" class="socket"></rete-socket>
  </div> 
</div>
<div *ngIf="selected() && !readonlyOnDisable && !isNew" style="text-align: center;height: 45px;"><app-add-nodes ></app-add-nodes></div>
<div *ngIf="!readonlyOnDisable && isNew" style="margin-left: 150px;height: 45px;"><app-add-nodes [childMessage]="parentMessage" (informParent)="parentWillTakeAction($event)"></app-add-nodes></div>

