
.outputs{
    display: block;
    margin-top: -12px !important;
    text-align: center !important;
  }
.start-node-output {
   // margin-left:155px;
}
.pmgImg{
  margin-top: 8px;
   margin-left: 10px;
  height: 30px !important;
  width: 30px !important;
}
.nodeIcon{
  // color: var(--orange-500)!important;
  // padding: 16px; 
  width: 50px;
  height: 47px;
  background: var(--gray-300)!important;
}
::ng-deep .startCard .p-card .p-card-content{
   padding: 0px !important;
}

::ng-deep .startCard .p-card .p-card-body{
   padding: 5px !important;
   text-align: center !important;  
}

::ng-deep .startnode{
  overflow:hidden;
  text-overflow: ellipsis !important;
  -webkit-text-stroke:thin !important;
}

.startcardBody{
    overflow-y: auto; 
    padding: 0px 9px 2px 4px !important;
 }
 .selected {
  // box-shadow: 10px 5px 5px red;
  border: 2px solid rgb(145, 149, 139);
  // box-shadow:0 0 6px 6px rgb(145, 149, 139);
}