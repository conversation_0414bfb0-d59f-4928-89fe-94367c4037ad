import { Component, OnInit, Inject, AfterViewInit, HostListener } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { UtilsService } from 'src/app/services/utils.service';
import { ReteService } from 'src/app/services/rete.service';
import { Message } from 'primeng/api';
import { MenuItem } from 'primeng/api';
import { Router } from '@angular/router';
@Component({
  selector: 'app-jsonata-editor',
  templateUrl: './jsonata-editor.component.html',
  styleUrls: ['./jsonata-editor.component.scss']
})
export class JsonataEditorComponent implements OnInit, AfterViewInit{
  checkForChange=false;
  isDiscardDialogOpen=false;
  public initialdata:any;
  errmsg: string;
  mapperstate: FormGroup;
  items:MenuItem[] | undefined;
  editorOptionsJSON = {
    minimap: { enabled: false },
    lineNumbers: 'off',
    language: 'json',
    theme: 'jsonataTheme',
    contextmenu: false,
    automaticLayout: true,
    scrollBeyondLastLine: false,
    // extraEditorClassName: 'editor-pane'
  };
  editorOptionsResult = {
    minimap: { enabled: false },
    lineNumbers: 'off',
    language: 'json',
    theme: 'jsonataTheme',
    readOnly: true,
    automaticLayout: true,
    contextmenu: false,
    extraEditorClassName: 'result-pane'
  };
  editorOptionsJSONata = {
    minimap: { enabled: false },
    lineNumbers: 'off',
    language:'jsonata',
    theme: 'jsonataTheme',
    // fontSize: 16,
    contextmenu: false,
    automaticLayout: true,
    scrollBeyondLastLine: false,
    // extraEditorClassName: 'editor-pane'
  };
  messages2: Message[] | undefined;
  code: string= '';
  jsonEditor: any;
  jsonataEditor: any;
  timer: any;
  previousNodes: any;
 
  constructor(public data: DynamicDialogConfig,public ref: DynamicDialogRef,private utilservice: UtilsService, private reteservice: ReteService,
    private confirmationService: ConfirmationService,private fb: FormBuilder,private router: Router) {
      window.addEventListener('message', this.receiveMessage.bind(this));
     this.loadJSONata();}
     receiveMessage(event) {
      if (event.origin !== window.location.origin) {
        return;
      }    
      if(event.data == "json"){
        this.getTestData();
        // const formatted = JSON.stringify((this.testData), null, 2);
        //  this.mapperstate.patchValue({ 'json': formatted });
        //  this.onChanges();
        //  setTimeout(() => {
        //   this.eval();
        // }, 500);
      }else{
  
      }
      
  
    }
     async ngOnInit(){
      this.items=[];
      this.openRunData(this.reteservice.getRunflowToDialog())
      this.mapperstate = this.fb.group({
        json: [],
        jsonata: [],
        result: ['']
      });
      const formatted = JSON.stringify(JSON.parse(this.data.data.json), null, 2);
      this.mapperstate.patchValue({ 'json': formatted });
      this.mapperstate.patchValue({jsonata:this.data.data.jsonata});      
      // setTimeout(() => {
        this.eval();
      // }, 500);
      this.onChanges();
    }

    ngAfterViewInit() {
      this.onChanges();
     // this.getTestData();
      this.eval();
    }
    
   async callPreviousNodes(){
      this.items=[];
      if(this.data.data.nodeinfo.getConnections().length > 0){
        const nodesfnd = await this.utilservice.getpreviousnodes(this.data.data.dataItem.nodeId, this.data.data.dataItem.nodeName, [], this.data.data.dataItem.editor.toJSON());
        this.previousNodes = this.utilservice.prepareFromfieldsFromPreviousNodes(nodesfnd, true);
        for(let key in this.previousNodes){
          let a ={
            label:this.previousNodes[key].name,
            value:this.previousNodes[key].key,
            command: () => {
              this.addValueIntoExpression(this.previousNodes[key].key);
          }
          }
          this.items.push(a)
        }        
      }
    }
    loadJSONata() {
      const head = document.getElementsByTagName('head')[0];
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://cdn.jsdelivr.net/npm/jsonata@1.8.4/jsonata.min.js';
      head.appendChild(script);
   }
    get json() {
      return this.mapperstate.get('json');
    }
    get jsonata() {
      return this.mapperstate.get('jsonata');
    }
    get result() {
      return this.mapperstate.get('result');
    }
   async onChanges() {
      this.json.valueChanges.subscribe(val => {
        clearTimeout(this.timer);
        this.timer = setTimeout(this.eval.bind(this), 500);
        this.clearMarkers();
      });
      this.jsonata.valueChanges.subscribe(val => {
        clearTimeout(this.timer);
        this.timer = setTimeout(this.eval.bind(this), 500);
        this.clearMarkers();
      });
    }
    jsonEditorOnInit(editor) {
      this.jsonEditor = editor;
      editor.decorations = [];
      
    }
    addValueIntoExpression(nodeResult: string) {
      this.jsonataEditor.trigger('keyboard', 'type', {text: nodeResult});
    }
    jsonataEditorOnInit(editor) {
      this.jsonataEditor = editor;
      editor.decorations = [];
      editor.addAction({
        id: 'jsonata-lambda',
        label: 'Lambda',
        keybindings: [(window as any).monaco.KeyCode.F11],
        run: function (ed) {
            ed.trigger('keyboard', 'type', { text: "λ" });
            return null;
        }
    });
  
    }
   async format(jsonvalue: any) {
      const formatted = JSON.stringify(JSON.parse(jsonvalue), null, 2);
      this.mapperstate.patchValue({ 'json': formatted });      
    }
   async eval() {
      let input, jsonataResult, bindings;
  
      if (typeof (window as any).jsonata === 'undefined') {
          this.timer = setTimeout(this.eval.bind(this), 500);
          return;
      }
  
      try {
          if (typeof this.json.value !== 'undefined' && this.json.value !== '') {
              input = JSON.parse(this.json.value);            
          } else {
              input = undefined;
          }
      } catch (err) {
          this.mapperstate.patchValue({ result: 'ERROR IN INPUT DATA: ' + err.message });
          const pos = err.message.indexOf('at position ');
          if (pos !== -1) {
              const start = parseInt(err.message.substr(pos + 12)) + 1;
              this.errorMarker(start, start + 1, this.jsonEditor, this.json.value);
          }
          return;
      }
  
      try {
          if (this.jsonata.value !== '') {
              jsonataResult = this.evalJsonata(input);
          } else {
              jsonataResult = '^^ Enter a JSONata expression in the box above ^^';
          }
          this.mapperstate.patchValue({result: jsonataResult});
      } catch (err) {
        this.mapperstate.patchValue({ result: err.message || String(err) });
          const end = err.position + 1;
          const start = end - (err.token ? err.token.length : 1);
          this.errorMarker(start, end, this.jsonataEditor, this.jsonata.value);
      }
  }
    errorMarker(start, end, editor, buffer) {
      const resolve = offset => {
          let line = 1;
          let column = 1;
          let position = 1;
          while (position < offset) {
              if (buffer.charAt(position) === '\n') {
                  line++;
                  column = 0;
              } else {
                  column++;
              }
              position++;
          }
          return { line, column };
      };
      const from = resolve(start);
      const to = resolve(end);
      editor.decorations = editor.deltaDecorations(editor.decorations, [
          { range: new (window as any).monaco.Range(from.line, from.column, to.line, to.column), options: { inlineClassName: 'jsonataErrorMarker' } },
          { range: new (window as any).monaco.Range(from.line, 1, to.line, 1), options: { isWholeLine: true, linesDecorationsClassName: 'jsonataErrorMargin' } },
      ]);
  }
  
  clearMarkers() {
      this.jsonataEditor.decorations = this.jsonataEditor.deltaDecorations(this.jsonataEditor.decorations, []);
      this.jsonEditor.decorations = this.jsonEditor.deltaDecorations(this.jsonEditor.decorations, []);
  }
    evalJsonata(input: any) {
      const expr = (window as any).jsonata(this.jsonata.value);
          expr.assign('trace', function (arg) {
      });    
      let pathresult = expr.evaluate(input);
          if (typeof pathresult === 'undefined') {
              pathresult = '** no match **';
          } else {
            // pathresult= pathresult.evaluate(input)
              pathresult =  JSON.stringify(pathresult, function (key, val) {
                   return (typeof val !== 'undefined' && val !== null && val.toPrecision) ? Number(val.toPrecision(13)) :
                       (val && (val._jsonata_lambda === true || val._jsonata_function === true)) ? '{function:' + (val.signature ? val.signature.definition : "") + '}' :
                        (typeof val === 'function') ? '<native function>#' + val.length : val;
              }, 2);
          }
          return pathresult;
  
    };
    savedata(){
     this.mapperstate.value.jsonata = this.mapperstate.value.jsonata.replace(/[\r]/g, '');
      this.ref.close(this.mapperstate.value);
    }
  
   close(event:any): void {
  if(this.data.data.jsonata !== this.mapperstate.value.jsonata){
    this.confirm(event);
  }else{
    this.ref.close(this.data.data)
  }
  
  }
  confirm(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.ref.close(this.data.data)
      },
      reject: () => {
      }
    });
  }
  showMsg:boolean=false;
  useTestData(event: Event){
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to Replace JSON ?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        if(Object.keys(this.testData).length > 0){
          this.showMsg=false;
        }else{
          this.showMsg=true
        }

          //const formatted = JSON.stringify(this.testData);
          const formatted = JSON.stringify((this.testData), null, 2);
         this.mapperstate.patchValue({ 'json': formatted });
         this.onChanges();
         setTimeout(() => {
          this.eval();
        }, 500);
        this.messages2 = [
          { severity: 'warn', summary: 'Waning', detail: 'System error. No test data found for this form' }
         
      ];
      },
      reject: () => {
      }
    });
  }
  testData:any;
  async getTestData(){
    this.reteservice.getWfTestData(this.data.data.formId, this.data.data.flowId).subscribe(res=>{
      if(res.error == ""){
        this.testData = res.testData
      }else{
        this.testData = {};

      }     
    });
  }
  goToPreview(msg:any) {
    //this.dispFormData = false;
    let data = {
      submission:{},
      formId: this.data.data.formId,
      type: 'form',
      wfId:this.data.data.flowId,
      iswftestdata: true,
      message:msg
    }
    const routerData = encodeURIComponent(JSON.stringify(data));
    let newRelativeUrl = this.router.createUrlTree([`/preview/${routerData}`]);
    let baseUrl = window.location.href.replace(this.router.url, '');
    
    window.open(baseUrl + newRelativeUrl, '_blank');

  }
  openPanel:boolean=false;
  openTestData(event:any){
    let data={
      "panelName":"testData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
  openRunData(event:any){
    let data={
      "panelName":"runData",
      "panel":event
    }
    this.reteservice.openSidePanel.next(data);
  }
}
