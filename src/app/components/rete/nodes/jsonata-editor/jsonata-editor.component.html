<!-- <a style="padding: 0.45625rem 0.45625rem;" class="help-icon p-button p-button-text p-button-sm p-button-rounded" (click)="useTestData($event)" pTooltip="Copy Test Data" tooltipPosition="left"><i class="pi pi-copy"></i></a> -->
<a style="padding: 0.45625rem 0.45625rem;" class="help-icon p-button p-button-text p-button-sm p-button-rounded" (click)="openTestData(openPanel = true)" pTooltip="Open Test Data" tooltipPosition="left"><i class="pi pi-list"></i></a>

<a  style="padding: 0.45625rem 0.45625rem;" class="help-icon2 p-button p-button-text p-button-sm p-button-rounded" (click)="goToPreview('json')" pTooltip="Edit Test Data" tooltipPosition="left"><i class="pi pi-pencil"></i></a>

<p-messages *ngIf="this.showMsg" [(value)]="messages2" [enableService]="false"></p-messages>
<div class="editors-container">
    <form [formGroup]="mapperstate" novalidate>
  <!-- <p-splitter [style]="{height: '450px' }" [panelSizes]="[50, 50]" [minSizes]="[10, 50]">
    <ng-template pTemplate>
        <div class="pane">
            <div class="pane-title">JSON</div>
            <ngx-monaco-editor formControlName="json" style="height: calc(100% - 15px);width: 100%;overflow: hidden;" [options]="editorOptionsJSON" (onInit)="jsonEditorOnInit($event)">
            </ngx-monaco-editor>
        </div>
    </ng-template>
    <ng-template pTemplate>
        <p-splitter layout="vertical" [panelSizes]="[50, 50]">
            <ng-template pTemplate>
                <div class="pane">
                    <div class="pane-title">Mapping Expression
                        <span style="float: right;">                     
                             <i class="pi pi-plus cursor" pTooltip="Previous Node Results" tooltipPosition="top" style="font-size: 1rem;margin-left:0px;" (click)="menu.toggle($event)"></i>
                      <p-menu #menu [model]="items" [popup]="true" appendTo="body"></p-menu>
                      <a class="pi pi-question-circle" style="font-size: 1rem;margin-left: 20px;text-decoration: none;color: black;margin-right: 20px;" href="https://docs.jsonata.org/simple" target="_blank"></a>
                    </span></div>
                    <ngx-monaco-editor formControlName="jsonata" style="height: calc(100% - 20px);width: 100%;overflow: hidden;" [options]="editorOptionsJSONata" (onInit)="jsonataEditorOnInit($event)">
                    </ngx-monaco-editor>
                  </div>
            </ng-template>
            <ng-template pTemplate>
                <div class="pane">
                    <div class="pane-title">Result</div>
                    <ngx-monaco-editor formControlName="result"  style="height: calc(90% - 20px);width: 100%;overflow: hidden;" [options]="editorOptionsResult">
                    </ngx-monaco-editor>
                  </div>
            </ng-template>
        </p-splitter>
    </ng-template>
</p-splitter> -->
<div>
    <p-splitter  [style]="{ height: '500px' }"  [panelSizes]="[50, 50]"  [minSizes]="[10, 0]">
            <ng-template pTemplate>
                <div style="flex-grow: 1;" class="flex align-items-center justify-content-center">
                    <div class="pane">
                    <div class="pane-title">JSON</div>
                        <ngx-monaco-editor formControlName="json" style="height: calc(100% - 15px);overflow: hidden;" [options]="editorOptionsJSON" (onInit)="jsonEditorOnInit($event)">
                        </ngx-monaco-editor>
                </div>
                </div>
            </ng-template>
            <ng-template pTemplate>
                <p-splitter layout="vertical" [panelSizes]="[50,50]">
                    <ng-template pTemplate>
                        <div style="flex-grow: 1;" class="flex align-items-center justify-content-center">
                            <div class="pane">
                                <div class="pane-title">Mapping Expression
                                    <span style="float: right;">                     
                                         <i class="pi pi-plus cursor" pTooltip="Previous Node Results" tooltipPosition="top" style="font-size: 1rem;margin-left:0px;" (click)="[menu.toggle($event),callPreviousNodes()]"></i>
                                  <p-menu #menu [model]="items" [popup]="true" appendTo="body"></p-menu>
                                  <a class="pi pi-question-circle" style="font-size: 1rem;margin-left: 20px;text-decoration: none;color: black;margin-right: 20px;" href="https://docs.jsonata.org/simple" target="_blank"></a>
                                </span></div>
                                <ngx-monaco-editor formControlName="jsonata" style="height: calc(100% - 20px);width: 100%;overflow: hidden;" [options]="editorOptionsJSONata" (onInit)="jsonataEditorOnInit($event)">
                                </ngx-monaco-editor>
                              </div>
                        </div>
                    </ng-template>
                    <ng-template pTemplate>
                        <p-splitter [panelSizes]="[40, 50]">
                            <ng-template pTemplate>
                                <div style="flex-grow: 1;"  class="flex align-items-center justify-content-center">
                                    <div class="pane">
                                        <div class="pane-title">Result</div>
                                        <ngx-monaco-editor formControlName="result"  style="height: calc(100% - 20px);width: 100%;overflow: hidden;" [options]="editorOptionsResult">
                                        </ngx-monaco-editor>
                                      </div>
                                </div>
                            </ng-template>
                        </p-splitter>
                    </ng-template>
                </p-splitter>
            </ng-template>
    </p-splitter>
</div>

<div  class="col-12 text-center">
            <p-button label="Close" styleClass="p-button-sm p-button-danger  mr-2" icon="pi pi-times" (click)="close($event)"></p-button>
            <p-button label="Save" styleClass="p-button-sm p-button-sm" icon="pi pi-save" (click)="savedata()"></p-button>
</div>
  </form>
  </div>
  <p-confirmPopup></p-confirmPopup>  
