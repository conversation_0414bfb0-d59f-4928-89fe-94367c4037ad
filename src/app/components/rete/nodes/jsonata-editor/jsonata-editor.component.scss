.pane-title {
    background-color: #f5f5f5;
    font-size: 16px;
    text-align: center;
    font-weight: 500;
  }
   .pane {
     width: 100% !important;
     height: 100% !important;
    border:rgb(212, 212, 212) solid 1px;
    border-radius: 0px;
    font-size: 13px;
    background-color: #e9e9e9;
  }
  .jsonataErrorMarker {
    color: red !important;
    text-decoration: underline;
    font-weight: bold;
  }
  
  .jsonataErrorMargin {
    background: red;
    width: 5px !important;
    margin-left: 3px;
  }
  .result-pane .monaco-editor-background
  {
      background-color: #ffffff !important;
  }
  .help-icon {
    position: absolute;
    top: 20px;
    right: 55px;
  }
  .help-icon2 {
    position: absolute;
    top: 20px;
    right: 85px;
  }