import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { NodeComponent, NodeService } from 'rete-angular-render-plugin';
@Component({
  selector: 'app-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss'],
  providers: [NodeService]
})
export class ErrorComponent extends NodeComponent {

  constructor(
    protected override service: NodeService,
    protected override cdr: ChangeDetectorRef,
  ) { 
    super(service, cdr);
  }

}
