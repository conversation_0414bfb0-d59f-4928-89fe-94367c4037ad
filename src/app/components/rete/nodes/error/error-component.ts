import { Component, Input } from 'rete';
import { strSocket, successSocket, errorSocket } from '../../sockets';
// import { StrControl } from '../controls/string-control';
import { ErrorComponent as ErrorComp} from './error.component';
import { AngularComponentData } from 'rete-angular-render-plugin';
export class ErrorComponent extends Component {
  override data: AngularComponentData;
  constructor() {
    super('Error');
    this.data.render = 'angular';
    this.data.component = ErrorComp;
  }

  builder(node) {
    const input = new Input('Error', 'Input', errorSocket, true);
    node.meta = {
      category: 'standard',
      name: 'error',
      index: 2,
      icon: 'error',
      description: 'Workflow or operation has resulted in an error.',
      wfName: 'flowresult',
      wfNamespace: 'unvired.operation'
    };
    return node.addInput(input);
  }

  worker(node, inputs, outputs) {
  //  outputs.error = node.data.error;
  }
}
