
 export const nodes = [

  {
    displayname: 'Transform Data',
    index: 3,
    name: 'untitled',
    icon: 'transform',
    wfName: 'mapformsubmission',
    wfNamespace: ' unvired.operation',
    description:
      'Add a data Transformation to be executed as part of this workflow.Use this if you need to map the form JSON data to another format for use in other operations like a Webhook, Database etc. The function will be executed just like any other operation and the transformed data will be passed on to the next step.'
  },
  {
    displayname: 'Send Email',
    index: 4,
    name: 'untitled',
    icon: 'pi pi-envelope',
    wfName: 'sendemail',
    wfNamespace: 'unvired.operation',
    description:
      'Send an email to one or more recipients as part of this workflow.  The email message content will be embedded in predefined templates and sent. Attachments can also be added.'
  },
  {
    displayname: 'File Cacher',
    index: 22,
    name: 'untitled',
    icon: 'cloud_sync',
    wfName: 'filecacher',
    wfNamespace: 'unvired.operation',
    description:
      ' Upload/Download a file to/from the attachment cache in the requested format'
  },
  {
    displayname: 'CSV Mapper',
    index: 23,
    name: 'untitled',
    icon: ' table',
    wfName: 'csvmapper',
    wfNamespace: 'unvired.operation',
    description:
      'Convert between JSON and CSV.  Operate on files or on strings'
  },
  {
    displayname: 'Condition',
    index: 5,
    name: 'untitled',
    query: {
      condition: 'and',
      rules: [],
    },
    config: {},
    icon: 'rule',
    wfName: 'rulesrequest',
    wfNamespace: 'unvired.operation',
    description:
      'Add a Business Rule be evaluated as part of this workflow.  Use this to check any conditions before executing the next step in the workflow. Link next operations for Then (condition succeeds) or Else (condition failed). The evaluation does not compute any results but only determines if it passes (then) or fails (else).',
  },
  {
    displayname: 'Database Operation',
    index: 6,
    name: 'untitled',
    icon: 'storage',
    wfName: 'dbrequest',
    wfNamespace: 'unvired.operation',
    description:
      'Execute any SQL statement on a SQL compliant database such as MySQL, MSSQL etc.',
  },
  {
    displayname: 'REST',
    index: 7,
    name: 'untitled',
    icon: 'http',
    wfName: 'restrequest',
    wfNamespace: 'unvired.operation',
    description:
      'Integrate with any Webhook or REST endpoint of any web application or SAAS such as SalesForce, Workday etc.',
  },
  {
    displayname: 'SAP',
    index: 8,
    name: 'untitled',
    icon: 'business',
    wfName: 'saprequest',
    wfNamespace: 'unvired.operation',
    description:
      'Integrate call to any BAPI or custom RFC of an SAP S/4 HANA or ECC system.',
  },
  {
    displayname: 'Active Directory',
    index: 9,
    name: 'untitled',
    icon: 'people',
    wfName: 'adsrequest',
    wfNamespace: 'unvired.operation',
    description:
      'Query a Microsoft Active Directory for user information etc.',
  },
  {
    displayname: 'Upload File',
    index: 10,
    name: 'untitled',
    icon: 'attachment',
    wfName: 'attachmentrequest',
    wfNamespace: 'unvired.operation',
    description: 'Upload a file to the forms platform cache.',
  },
  {
    displayname: 'Create Form',
    index: 11,
    name: 'untitled',
    icon: 'post_add',
    wfName: 'formcreate',
    wfNamespace: 'unvired.flow',
    description:
      'Create a new form submission and (optionally) initialize the required form fields.',
  },
  {
    displayname: 'Update Form',
    index: 12,
    name: 'untitled',
    icon: 'edit_road',
    wfName: 'formupdate',
    wfNamespace: 'unvired.flow',
    description: 'Update the form submission and change any form fields.',
  },
  {
    displayname: 'Create PDF',
    index: 13,
    name: 'untitled',
    icon: 'picture_as_pdf',
    wfName: 'formpdf',
    wfNamespace: 'unvired.flow',
    description:
      'Creates a PDF of the form with the data and returns a cached AttachmentID that can be used to attach to an email or upload to a different system like Sharepoint, OpenText etc.',
  },
  {
    displayname: 'FormAlert',
    index: 14,
    name: 'untitled',
    icon: 'announcement',
    wfName: 'formalert',
    wfNamespace: 'unvired.flow',
    description: 'Send an alert to a user or team.',
  },
  {
    displayname: 'Archive Form',
    index: 15,
    name: 'untitled',
    icon: 'link',
    wfName: 'formarchive',
    wfNamespace: 'unvired.flow',
    description: 'Archive the form and create an archive URL that can then be saved in external systems to view the archived data.',
  },
  {
    displayname: 'Share Form',
    index: 16,
    name: 'untitled',
    icon: 'link',
    wfName: 'formshare',
    wfNamespace: 'unvired.flow',
    description: 'Create a form share URL to allow external users to fill the form.',
  },
  {
    displayname: 'Read Form',
    index: 17,
    name: 'untitled',
    icon: 'read_more',
    wfName: 'formread',
    wfNamespace: 'unvired.flow',
    description: '',
  },
  {
    displayname: 'Search Form',
    index: 18,
    name: 'untitled',
    icon: 'plagiarism',
    wfName: 'formsearch',
    wfNamespace: 'unvired.flow',
    description: '',
  },
  {
    displayname: 'Calculate',
    index: 19,
    name: 'untitled',
    icon: 'calculate',
    expressions: [],
    wfName: 'calculaterequest',
    wfNamespace: 'unvired.operation',
    description:
      'Add expressions to be calculated as part of this workflow. Use this to calculate intermediate values and assign to variables.  These variables can be used in other expressions, rules or in input/output expressions to other operations.  Arithmetic (e.g. assets = bank + stocks) and String (e.g. name = concat(firstName,lastName) ) expressions are supported.',
  },
  {
    displayname: 'Execute PA',
    index: 20,
    name: 'untitled',
    icon: 'flash_on',
    wfName: 'parequest',
    library: '',
    function: '',
    inputs: [],
    wfNamespace: 'unvired.operation',
    description:
      'Add an Unvired Process Agent function to be executed as part of this workflow.  Use this if you need any specific custom logic that you have programmed to be executed as a step in the workflow.  The function will be executed just like any other operation and the results will be passed on to the next step.',
  },
  {
    displayname: 'Execute Script',
    index: 21,
    name: 'untitled',
    icon: 'dynamic_form',
    wfName: '',
    wfNamespace: '',
    description:
      'Add a custom Python script to be executed as part of this workflow. Use this if you need any specific custom logic that you have programmed to be executed as a step in the workflow. The script will be executed just like any other operation and the results will be passed on to the next step.',
  },
  // {
  //   displayname: 'Form Link',
  //   index: 22,
  //   name: 'untitled',
  //   icon: 'link',
  //   wfName: 'createformlink',
  //   wfNamespace: 'unvired.flow',
  //   description:
  //     'Create a form submission URL to display the last recorded data. The URL can be used in any backend system to link back to the original form submission for reference or audit purposes.',
  // },
  // {
  //   displayname: 'Elastic Search',
  //   index: 23,
  //   name: 'untitled',
  //   icon: 'analytics',
  //   wfName: 'esrequest',
  //   wfNamespace: 'unvired.operation',
  //   description:
  //     'Publish the provided data to an ElasticSearch index.  This step is a prerequisite to designing Kibana dashboards in the Forms Admin Portal.',
  // }
 ];
