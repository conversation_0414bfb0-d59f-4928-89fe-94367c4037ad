import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { ReteComponent } from './rete.component';
import { roleGuard } from 'src/app/auth/guards/role.guard';

const routes: Routes = [
  { 
    path: 'flow-editor/:data',
    data: { role: ['Admin', 'Developer'] },
    canActivate: [roleGuard],
    component: ReteComponent
  },
 // {path: 'workflow-editor/:data', component: ReteComponent, canDeactivate: [CanDeactivateGuard]}, // note: shifted this path to app.routinng to avoid sidebar and header
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReteRoutingModule { }
