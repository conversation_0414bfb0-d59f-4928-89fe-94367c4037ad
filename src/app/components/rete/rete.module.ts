import { NgModule } from '@angular/core';
import { SharedModule } from '../../shared/shared.module';
import { ReteModule } from 'rete-angular-render-plugin';
import { ReteRoutingModule } from './rete-routing.module';
 import { MonacoEditorModule } from 'ngx-monaco-editor';
//  import { QueryBuilderModule } from 'angular2-query-builder';
// Components
import { ReteComponent } from './rete.component';

// import { StringComponent } from './controls/string.component';
// import { ConditionNodeComponent } from './nodes/general-nodes/condition-node/condition-node.component';
// import { RunWorkflowPanelComponent } from './run-workflow-panel/run-workflow-panel.component';
import { JsonDataComponent } from './json-data.component';

// Pipes
import { HumanizePipe} from './humanize';

import { StartComponent } from './nodes/start/start.component';
import { MyNodeComponent } from './nodes/general-nodes/node/node.component';
import { ErrorComponent } from './nodes/error/error.component';
import { SuccessComponent } from './nodes/success/success.component';
import { IntegrationnodeComponent } from './nodes/integration-nodes/integrationnode/integrationnode.component';
import { ExpressionBuilderComponent } from './nodes/expression-builder/expression-builder.component';
import { AddEditSystemComponent } from './nodes/integration-nodes/add-edit-system/add-edit-system.component';
import { AddNodesComponent } from './add-nodes/add-nodes.component';
import { ScriptnodeComponent } from './nodes/script-nodes/scriptnode/scriptnode.component';
import { ScriptEditorDialogComponent } from './nodes/script-nodes/script-editor-dialog/script-editor-dialog.component';
import { JsonataEditorComponent } from './nodes/jsonata-editor/jsonata-editor.component';
import { FormnodeComponent } from './nodes/form-nodes/formnode/formnode.component';
import { QueryBuildermodelComponent } from './nodes/query-buildermodel/query-buildermodel.component';
import { NgxAngularQueryBuilderModule } from 'ngx-angular-query-builder';
import { QueryExpBuilderComponent } from './nodes/query-exp-builder/query-exp-builder.component';
// import { IntegrationNodeDetailComponent } from './nodes/integration-nodes/integration-node-detail/integration-node-detail.component';
// import { AddEditSystemComponent } from './nodes/integration-nodes/add-edit-system/add-edit-system.component';
// import { NewExpressionBuilderComponent } from './nodes/new-expression-builder/new-expression-builder.component';
// import { NewJsonataEditorComponent } from './nodes/new-jsonata-editor/new-jsonata-editor.component';
// import { ScriptEditorDiloagComponent } from './nodes/script-nodes/script-editor-diloag/script-editor-diloag.component';
// import { QueryBuildermodelComponent } from './nodes/query-buildermodel/query-buildermodel.component';
// import { SapDataMappingComponent } from './nodes/integration-nodes/sap-data-mapping/sap-data-mapping.component';
// import { RunflowsComponent } from './nodes/runflows/runflows.component';

@NgModule({
  declarations: [
    ReteComponent,
    HumanizePipe,
    JsonDataComponent,
    StartComponent,
    MyNodeComponent,
    ErrorComponent,
    SuccessComponent,
    IntegrationnodeComponent,
    ExpressionBuilderComponent,
    AddEditSystemComponent,
    AddNodesComponent,
    ScriptnodeComponent,
    ScriptEditorDialogComponent,
    JsonataEditorComponent,
    FormnodeComponent,
    QueryBuildermodelComponent,
    QueryExpBuilderComponent,
  ],
  imports: [
    ReteModule,
    ReteRoutingModule,
    SharedModule,
    MonacoEditorModule,
    // QueryBuilderModule
    NgxAngularQueryBuilderModule,
  ],
  providers:[
  ]
})
export class ReteEditorModule {}
