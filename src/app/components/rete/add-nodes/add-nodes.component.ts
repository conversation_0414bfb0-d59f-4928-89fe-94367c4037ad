import { Component,OnInit,ViewChild,ElementRef,  Input, Output, EventEmitter,AfterViewInit} from '@angular/core';
import { NodeEditor, Engine, Connection, Component as comp } from 'rete';
/** Integration nodes import */
import { DatabaseOperationsComponent } from '../nodes/integration-nodes/database-ops-component';
import { RESTComponent } from '../nodes/integration-nodes/REST-component';                     
import { StoredProcedureComponent } from '../nodes/integration-nodes/stored-procedure-component';
import { ActiveDirectoryComponent } from '../nodes/integration-nodes/active-directory-component';
import { ODataComponent } from '../nodes/integration-nodes/OData-component';
import { SAPComponent } from '../nodes/integration-nodes/SAP-component';
import {FTPServicecomponent}from '../nodes/integration-nodes/FTP-Service-component';
/** General nodes import */
import { ConditionComponent } from '../nodes/general-nodes/condition-component';
import { SendEmailComponent } from '../nodes/general-nodes/send-emails-component';
import { CalculateComponent } from '../nodes/general-nodes/calculate-component';
import { InjectComponent } from '../nodes/general-nodes/inject-component';
import { DataMapperComponent } from '../nodes/general-nodes/data-mapper-component';
import { CSVMapperComponent } from '../nodes/general-nodes/CSV-mapper-component';
import { ApprovalComponent } from '../nodes/general-nodes/approval-component';
import { DownloadFileComponent } from '../nodes/general-nodes/download-file-component';
import { SubWorkflowComponent } from '../nodes/general-nodes/sub-workflow-component';
import { ForLoopComponent } from '../nodes/general-nodes/for-loop-component';
import { FileCatcherComponent } from '../nodes/general-nodes/File-Cacher-component';
import { combineComponent } from '../nodes/general-nodes/combine-nodes-component';

/** Form nodes import */
import { CreatePDFComponent } from '../nodes/form-nodes/create-pdf-component';
import { CreateFormComponent } from '../nodes/form-nodes/create-form-component';
import { UpdateFormComponent } from '../nodes/form-nodes/update-form-component';
import { MasterDataUpdateComponent } from '../nodes/form-nodes/masterdata-update-component';
import{ MasterDataRecordComponent} from '../nodes/form-nodes/masterdata-record-component';
import { MasterDataReadComponent } from '../nodes/form-nodes/masterdata-read-component';
import { SendAlertComponent } from '../nodes/form-nodes/send-alert-component';
import { ArchiveFormComponent } from '../nodes/form-nodes/archive-form-component';
import { ShareFormComponent } from '../nodes/form-nodes/share-form-component';
import { ExternalUserComponent } from '../nodes/form-nodes/external-user';
import { ReadFormComponent } from '../nodes/form-nodes/read-form-component';
import { SearchFormComponent } from '../nodes/form-nodes/search-form-component';
import { AssignFormComponent } from '../nodes/form-nodes/assign-form-component';
/** Script nodes import */
import { ExecuteFunctionComponent } from '../nodes/script-nodes/execute-pa-component';
import { ExecuteJavaScriptComponent } from '../nodes/script-nodes/execute-javascript-component';
import { ExecutePythonScriptComponent } from '../nodes/script-nodes/execute-pythonscript-component';
import { MyNodeComponent } from '../nodes/general-nodes/node/node.component';
import { ActivatedRoute } from '@angular/router';
import { nodes } from '../nodes';
import { Location } from '@angular/common';
import { MenuItem } from 'primeng/api';
import { ReteService } from 'src/app/services/rete.service';
import { Listbox } from 'primeng/listbox';
// import { NodeConfigDialogComponent } from '../../rete/nodes/general-nodes/node-config-dialog/node-config-dialog.component';

@Component({
  selector: 'app-add-nodes',
  templateUrl: './add-nodes.component.html',
  styleUrls: ['./add-nodes.component.scss']
})
export class AddNodesComponent implements OnInit, AfterViewInit {
  @ViewChild('nodeEditor', { static: true }) el: ElementRef;
  @ViewChild('listbox') listbox: Listbox;
  @Input() childMessage: boolean;
  @Output() informParent = new EventEmitter();
  constructor(private reteservice: ReteService,
    private route: ActivatedRoute,
    public location: Location,
   ){  

   
    this.components = [
      new RESTComponent(),
      new DatabaseOperationsComponent(),
      new StoredProcedureComponent(),
      new ActiveDirectoryComponent(),
      new ODataComponent(),
      new SAPComponent(),
      new CreateFormComponent(),
      new UpdateFormComponent(),
      new AssignFormComponent(),
      new ReadFormComponent(),
      new SearchFormComponent(),
      new ShareFormComponent(),
      new ExternalUserComponent(),
      new ArchiveFormComponent(),
      new CreatePDFComponent(),
      new SendAlertComponent(),
      new MasterDataUpdateComponent(),
      new MasterDataReadComponent(),
      new ExecuteFunctionComponent(),
      new ExecuteJavaScriptComponent(),
      new ExecutePythonScriptComponent(),
      new ApprovalComponent(),
      new DataMapperComponent(),
      new CSVMapperComponent(),
      new ConditionComponent(),
      new CalculateComponent(),
      new SendEmailComponent(),
      new InjectComponent(),
      new SubWorkflowComponent(),
      new ForLoopComponent(),
      new MasterDataRecordComponent(),
      new FTPServicecomponent(),
      new FileCatcherComponent(),
      new combineComponent()
    ];  
  }
  showMatIcons:boolean=false;

  public components = [];
  public allNodesList=[];
  public formsMasterData=[];
  public scriptNodeList=[];
  public othersNodeList=[];
  public formsNodeList=[];
  public serviceNodeList=[];
  public editor = null;
  activeItem: MenuItem;
  formType: string
  dismissable:boolean=false;
 async ngOnInit() {    
    if(this.childMessage == true){
      document.getElementById('closePanel').click();
    }
   this.formType=this.reteservice.getformtype();
   this.editor=this.reteservice.getEditorData();
  }
  async ngAfterViewInit() {
    this.getNodes(0);  
   }
  allnode:any=[];

 async getAllOthers(){
    this.allnode=[];
    this.othersNodeList=[
      {
        label: 'Code',
        items: [
          {
            icon:'java',
            component: new ExecuteFunctionComponent(),
            name: 'Execute Java Code',
            disp:true,
            desc:"Execute custom Java function as part of the workflow"                 
          },
           {
             icon:'javascript',
             component: new ExecuteJavaScriptComponent(),
             name: 'Execute JavaScript Code',
             disp:true,
             desc:"Execute custom JavaScript function as part of the workflow"
           },
            {
              icon:'python',
             component: new ExecutePythonScriptComponent(),
             name: 'Execute Python Code',
             disp:true,
             desc:"Execute custom Python function as part of the workflow" 
          }
        ]
      },
      {
        label: 'Others',
        items: [
          {
            icon:'response',
            component: new InjectComponent(),
            name: 'Response', 
            disp:false,
            desc:"Set the respose of the flow execution"
           },
           {
            icon:'jsonFile',
            component:new DataMapperComponent(),
            name: 'JSON Parser',
            disp:false,
            desc:"Parse JSON data"
           },
           {
            icon:'csv',
            component:new CSVMapperComponent(),
            name: 'CSV Parser',
            disp:false,
            desc:"Parse CSV files "
           },
           {
            icon:'calculate',
            component: new CalculateComponent(),
            name: 'Calculate',
            disp:false,
            desc:"String, Number, Date and other expressions",
           },
           {
            icon:'if',
            component:  new ConditionComponent(),
            name: 'Condition',
            disp:false,
            desc:"If - Then - Else construct"
           },
          {
           icon:'filestore',
           component: new FileCatcherComponent(),
           name: 'File Store',
           disp:false,
           desc:"Store/Retrieve files from a cache"
          },                   
          {
            icon:'email',
            component:  new SendEmailComponent(),
            name: 'Email',
            disp:false,
            desc:"Send emails as part of the flow"
           },
           {
            icon:'loop',
            component:  new ForLoopComponent(),
            name: 'For Loop',
            disp:false,
            desc:"Loop construct for repititive execution of flow steps"
           },
              
           {
            icon:'flow',
            component:new SubWorkflowComponent(),
            name: 'Sub Flow',
            disp:false,
            desc:"Execute another flow as part of the main flow"
           },
          {
            icon:'approval',
            component:  new ApprovalComponent(),
            name: 'Approval Workflow',
            disp:false,
            desc:"Execute workflow approvals for the form submission"
           },
           {
            component: new combineComponent(),
            icon:'combine',
            name: 'merge',
            disp:false,
           }     
        ]
      }
    ]
    for(let key in this.othersNodeList){
      this.allnode.push(this.othersNodeList[key])
    }
    this.showMatIcons=true;
  }

  getFormsData(){
    this.allnode=[];
    this.formsNodeList=[
      {
        label: 'Master Data',
        items: [
          {
            icon:'masterdata',
            component: new MasterDataRecordComponent(),
            name: 'Update Record',
            disp:false,
            desc:"Update a master data record"
           },
           {
             icon:'updaterows',
             component: new MasterDataUpdateComponent(),
             name: 'Update Rows',
             disp:false,
             desc:"Bulk update master data"
            },
            {
             icon:'readrows',
             component: new MasterDataReadComponent(),
             name: 'Read Rows',
             disp:false,
             desc:"Filter and Read master data"
            }
        ]
      },{
      label: 'Forms',
      items: [
        {
          icon:'create',
          component:  new CreateFormComponent(),
          name: 'Create',
          disp:false,
          desc:"Create a form and prefill it"
         },
         {
           icon:'update',
           component: new UpdateFormComponent(),
           name: 'Update',
           disp:false,
           desc:"Update form submission"
          },
          {
           icon:'assign',
           component: new AssignFormComponent(),
           name: 'Assign',
           disp:false,
           desc:"Assign form to user(s)"
          },
          {
           icon:'read',
           component:  new ReadFormComponent(),
           name: 'Read',
           disp:false,
           desc:"Read a form submission"
          },
          {
            icon:'search',
            component:  new SearchFormComponent(),
            name: 'Search',
            disp:false,
            desc:"Search for form submissions"
           },
           {
            icon:'share',
            component:  new ShareFormComponent(),
            name: 'Share',
            disp:false,
            desc:"Share a form with external user(s)"
           },
           {
             icon:'contact',
             component: new ExternalUserComponent(),
             name: 'Contact',
             disp:false,
             desc:"Create contacts for form sharing"
            },
            {
              icon:'archive',
              component: new ArchiveFormComponent(),
              name: 'Archive',
              disp:false,
              desc:"Archive form submission"
             },
             {
              icon:'pdf',
              component: new CreatePDFComponent(),
              name: 'Create PDF',
              disp:false,
              desc:"Create a PDF of the form submission"
             },
             {
               icon:'alert',
               component: new SendAlertComponent(),
               name: 'Send Alert',
               disp:false,
               desc:"Send an alert to user(s) about their form submission"
              }
      ]
    }
]
    for(let key in this.formsNodeList){
      this.allnode.push(this.formsNodeList[key])
    }
    this.showMatIcons=true;      
  }

  getServiceData(){
    this.allnode=[];
    this.serviceNodeList=[
        {
          icon:'http',
          component: new RESTComponent(),
          name: 'HTTP',
          disp:false,
          desc:"Send an HTTP REST request"
         },
         {
           icon:'odata',
           component:  new ODataComponent(),
           name: 'OData',
           disp:false,
           desc:"Send an Odata REST request"
          },
          {
           icon:'sap',
           component:   new SAPComponent(),
           name: 'SAP',
           disp:false,
           desc:"Call SAP BAPI or RFC"
          },
         {
           icon:'sql',
           component:  new DatabaseOperationsComponent(),
           name: 'SQL Query',
           disp:false,
           desc:"Execute SQL Query"
          },
          {
           icon:'stored-procedure',
           component:  new StoredProcedureComponent(),
           name: 'Stored Procedure',
           disp:false,
           desc:"Execute Stored Procedure"
          },
          {
           icon:'ads',
           component:  new ActiveDirectoryComponent(),
           name: 'Active Directory',
           disp:false,
           desc:"Lookup users in Active Directory"
          },
           {
             icon:'ftp',
             component: new FTPServicecomponent(),
             name: 'FTP',
             disp:false,
             desc:"Connect to an FTP server."
            }
    ]
    for(let key in this.serviceNodeList){
      this.allnode.push(this.serviceNodeList[key])
    }
    this.showMatIcons=true;   
  }

  getAllNodes(){
    this.allnode=[];
    this.allNodesList=[
      {
        label: 'Integrations',
                  items: [
                    {
                      icon:'http',
                      component: new RESTComponent(),
                      name: 'HTTP',
                      disp:false,
                      desc:"Send an HTTP REST request"
                     },
                     {
                       icon:'odata',
                       component:  new ODataComponent(),
                       name: 'OData',
                       disp:false,
                       desc:"Send an Odata REST request"
                      },
                      {
                       icon:'sap',
                       component:   new SAPComponent(),
                       name: 'SAP',
                       disp:false,
                       desc:"Call SAP BAPI or RFC"
                      },
                     {
                       icon:'sql',
                       component:  new DatabaseOperationsComponent(),
                       name: 'SQL Query',
                       disp:false,
                       desc:"Execute SQL Query"
                      },
                      {
                       icon:'stored-procedure',
                       component:  new StoredProcedureComponent(),
                       name: 'Stored Procedure',
                       disp:false,
                       desc:"Execute Stored Procedure"
                      },
                      {
                       icon:'ads',
                       component:  new ActiveDirectoryComponent(),
                       name: 'Active Directory',
                       disp:false,
                       desc:"Lookup users in Active Directory"
                      },
                       {
                         icon:'ftp',
                         component: new FTPServicecomponent(),
                         name: 'FTP',
                         disp:false,
                         desc:"Connect to an FTP server."
                        },
                  ]
      }, {
        label: 'Master Data',
                  items: [
                    {
                      icon:'masterdata',
                      component: new MasterDataRecordComponent(),
                      name: 'Update Record',
                      disp:false,
                      desc:"Update a master data record"
                     },
                     {
                       icon:'updaterows',
                       component: new MasterDataUpdateComponent(),
                       name: 'Update Rows',
                       disp:false,
                       desc:"Bulk update master data"
                      },
                      {
                       icon:'readrows',
                       component: new MasterDataReadComponent(),
                       name: 'Read Rows',
                       disp:false,
                       desc:"Filter and Read master data"
                      }
                  ]
      },
      {
      label: 'Forms',
                items: [
                  {
                    icon:'create',
                    component:  new CreateFormComponent(),
                    name: 'Create',
                    disp:false,
                    desc:"Create a form and prefill it"
                   },
                   {
                     icon:'update',
                     component: new UpdateFormComponent(),
                     name: 'Update',
                     disp:false,
                     desc:"Update form submission"
                    },
                    {
                     icon:'assign',
                     component: new AssignFormComponent(),
                     name: 'Assign',
                     disp:false,
                     desc:"Assign form to user(s)"
                    },
                    {
                     icon:'read',
                     component:  new ReadFormComponent(),
                     name: 'Read',
                     disp:false,
                     desc:"Read a form submission"
                    },
                    {
                      icon:'search',
                      component:  new SearchFormComponent(),
                      name: 'Search',
                      disp:false,
                      desc:"Search for form submissions"
                     },
                     {
                      icon:'share',
                      component:  new ShareFormComponent(),
                      name: 'Share',
                      disp:false,
                      desc:"Share a form with external user(s)"
                     },
                     {
                       icon:'contact',
                       component: new ExternalUserComponent(),
                       name: 'Contact',
                       disp:false,
                       desc:"Create contacts for form sharing"
                      },
                      {
                        icon:'archive',
                        component: new ArchiveFormComponent(),
                        name: 'Archive',
                        disp:false,
                        desc:"Archive form submission"
                       },
                       {
                        icon:'pdf',
                        component: new CreatePDFComponent(),
                        name: 'Create PDF',
                        disp:false,
                        desc:"Create a PDF of the form submission"
                       },
                       {
                         icon:'alert',
                         component: new SendAlertComponent(),
                         name: 'Send Alert',
                         disp:false,
                         desc:"Send an alert to user(s) about their form submission"
                        }
                ]
    },
    {
      label: 'Code',
                items: [
                  {
                    icon:'java',
                    component: new ExecuteFunctionComponent(),
                    name: 'Execute Java Code',
                    disp:true,
                    desc:"Execute custom Java function as part of the workflow"                 
                  },
                   {
                     icon:'javascript',
                     component: new ExecuteJavaScriptComponent(),
                     name: 'Execute JavaScript Code',
                     disp:true,
                     desc:"Execute custom JavaScript function as part of the workflow"
                   },
                    {
                      icon:'python',
                     component: new ExecutePythonScriptComponent(),
                     name: 'Execute Python Code',
                     disp:true,
                     desc:"Execute custom Python function as part of the workflow" 
                  }
                ]
    },  {
      label: 'Others',
                items: [
                  {
                    icon:'response',
                    component: new InjectComponent(),
                    name: 'Response', 
                    disp:false,
                    desc:"Set the respose of the flow execution"
                   },
                   {
                    icon:'jsonFile',
                    component:new DataMapperComponent(),
                    name: 'JSON Parser',
                    disp:false,
                    desc:"Parse JSON data"
                   },
                   {
                    icon:'csv',
                    component:new CSVMapperComponent(),
                    name: 'CSV Parser',
                    disp:false,
                    desc:"Parse CSV files "
                   },
                   {
                    icon:'calculate',
                    component: new CalculateComponent(),
                    name: 'Calculate',
                    disp:false,
                    desc:"String, Number, Date and other expressions",
                   },
                   {
                    icon:'if',
                    component:  new ConditionComponent(),
                    name: 'Condition',
                    disp:false,
                    desc:"If - Then - Else construct"
                   },
                  {
                   icon:'filestore',
                   component: new FileCatcherComponent(),
                   name: 'File Store',
                   disp:false,
                   desc:"Store/Retrieve files from a cache"
                  },                   
                  {
                    icon:'email',
                    component:  new SendEmailComponent(),
                    name: 'Email',
                    disp:false,
                    desc:"Send emails as part of the flow"
                   },
                   {
                    icon:'loop',
                    component:  new ForLoopComponent(),
                    name: 'For Loop',
                    disp:false,
                    desc:"Loop construct for repititive execution of flow steps"
                   },
                      
                   {
                    icon:'flow',
                    component:new SubWorkflowComponent(),
                    name: 'Sub Flow',
                    disp:false,
                    desc:"Execute another flow as part of the main flow"
                   },
                  {
                    icon:'approval',
                    component:  new ApprovalComponent(),
                    name: 'Approval Workflow',
                    disp:false,
                    desc:"Execute workflow approvals for the form submission"
                   },
                   {
                    component: new combineComponent(),
                    icon:'combine',
                    name: 'Merge',
                    disp:false,
                   }     
                ]
    }
      
    ]
    for(let key in this.allNodesList){
      this.allnode.push(this.allNodesList[key])
    }
    this.showMatIcons=true;   
  }

  async addNodeComponent(component: comp) {
    const area = this.editor.view.area;
    let connectionFrom;
    if(this.editor.selected.list[0] == undefined){
      connectionFrom = this.editor.nodes[0]
    }else{
      connectionFrom = this.editor.selected.list[0];
    }    
    const existingNodePosition = connectionFrom.position;
    const nodeDistance = 111;
      const node = await component.createNode({});
      const { container } = this.editor.view.area;
      const [hw, hh] = [container.clientWidth / 2, container.clientHeight / 16];
      const { x, y, k } = this.editor.view.area.transform;
     //node.position = [(hw - x-(-200)) / k, (hh - y+100) / k]
      node.position[0] = existingNodePosition[0]; // Keep the same X position
      node.position[1] = existingNodePosition[1] + nodeDistance; // Set the Y position below the existing node      
      node['new'] = true;     
      this.editor.addNode(node);
      const nodeView = this.editor.view.nodes.get(node);
      const nodeElement = nodeView.el;
      nodeElement.style.zIndex=this.reteservice.getNodeIndex();
      const newNodeErrorOutput = `${node.name}_error`;
      const newNodeOutputSuccess = node.name === 'approvalworkflow' ? `${node.name}_approved` : `${node.name}_success`;
      const newNodeStart = `${node.name}`;
      const connectionFromNodeSuccess = connectionFrom.name === 'approvalworkflow' ? `${connectionFrom.name}_approved` : `${connectionFrom.name}_success`;
      const connectionFromNodeError = `${connectionFrom.name}_error`;
      let nextNode;
      let nextNodeInput;      
      if(connectionFrom.name == 'Start'){//for START node
        if(newNodeStart && node.inputs.get(newNodeStart)) {
          this.editor.nodes.forEach((nodes,index)=>{
            if(nodes.name == 'Start'){                    
              this.editor.nodes[index].outputs.forEach(output => {
                 output.connections.map(connection => {
                   if(connection.input.key != 'Error'){
                     nextNode = connection.input.node;
                     nextNodeInput = nextNode.name;
                     }
                     this.editor.removeConnection(connection)
                    })
                  });
                    this.editor.connect(this.editor.nodes[0].outputs.get("Start"),node.inputs.get(newNodeStart));
                  if(nextNodeInput && nextNode.inputs.get(nextNodeInput) && node.name != 'condition'){ 
                     this.editor.nodes.forEach((nod,ind)=>{
                       if(nod.id == node.id){
                         this.editor.connect(this.editor.nodes[ind].outputs.get(newNodeOutputSuccess),nextNode.inputs.get(nextNodeInput));
                        }
                     })
                   }
                   this.getNodesUntilFromtoEnd(node).then(nodes => {
                    for(let key in nodes){
                    this.getConnectionsByNodeId(nodes[key])
                    }
                  });
                   if(newNodeErrorOutput && node.outputs.get(newNodeErrorOutput)) {
                     this.editor.connect(node.outputs.get(newNodeErrorOutput), this.editor.nodes[2].inputs.get("Error"));
                   }
              }
              //if (nodes.id != node.id) { // Skip the newly added node
              //  nodes.position[1] = nodes.position[1] + nodeDistance;
           // }
           if(nodes.name == 'Error'){
            this.changeNodePosition(3,[nodes.position[0],nodes.position[1] + 111])
          }
          if(nodes.name == 'Success'){
            this.changeNodePosition(2,[nodes.position[0],nodes.position[1] + 111])
          }
           })
         } 
      }else if(connectionFrom.name == 'condition' || connectionFrom.name == 'ForLoop'){//for LOOP node & CONDITION node
        if(connectionFrom.name == 'ForLoop'){//for LOOP node
          if(newNodeErrorOutput && node.outputs.get(newNodeErrorOutput)) {
                   this.editor.connect(node.outputs.get(newNodeErrorOutput), this.editor.nodes[2].inputs.get("Error"));
           }
        }
        if(connectionFrom.name == 'condition'){//for CONDITION node
          let count = 0;
          this.editor.nodes.forEach((nodes,index)=>{
            if((nodes.name == connectionFrom.name) && (nodes.id == connectionFrom.id)){ 
          this.editor.nodes[index].outputs.forEach(output => {
            if(output.connections.length > 0){
                output.connections.map(connection => {
              if(connection.input.key != 'Error'){
                count = count+1;
                nextNode = connection.input.node;
                this.getNodesUntilEnd(nextNode).then(nodes => {
                  for(let key in nodes){
                    this.getConnectionsByNodeId(nodes[key])
                    }
                });
                }
               })
              }
             });
            }
            });
            if(count != 0){
              this.editor.nodes.forEach((nodes,index)=>{
              if(nodes.name == 'Error' ){
                this.changeNodePosition(3,[nodes.position[0],nodes.position[1] + 111])
              }
              if(nodes.name == 'Success'){
                this.changeNodePosition(2,[nodes.position[0],nodes.position[1] + 111])
              }
            });
            }

          if(newNodeErrorOutput && node.outputs.get(newNodeErrorOutput)) {
            this.editor.connect(node.outputs.get(newNodeErrorOutput), this.editor.nodes[2].inputs.get("Error"));
    }
        }
       }else{ //for all node except CONDITION ,LOOP and START
        if(newNodeStart && node.inputs.get(newNodeStart)) {
         // let nodeLen=0;
          this.editor.nodes.forEach((nodes,index)=>{    
            if((nodes.name == connectionFrom.name) && (nodes.id == connectionFrom.id)){     
              this.editor.nodes[index].outputs.forEach(output => {
                output.connections.map(connection => {
                  if(connection.input.key != 'Error'){
                    nextNode = connection.input.node;
                    nextNodeInput = nextNode.name;
                    }
                    this.editor.removeConnection(connection)
                    })
                    
                  });
                  this.getNodesUntilEnd(nextNode).then(nodes => {
                    // nodeLen = nodes.length;
                    for(let key in nodes){
                      this.getConnectionsByNodeId(nodes[key])
                      }
                  });
            
                   this.editor.connect(this.editor.nodes[index].outputs.get(connectionFromNodeSuccess),node.inputs.get(newNodeStart));
                   this.editor.connect(this.editor.nodes[index].outputs.get(connectionFromNodeError),this.editor.nodes[2].inputs.get("Error"));
                    if(nextNodeInput && nextNode.inputs.get(nextNodeInput) && node.name != 'condition'){
                         this.editor.nodes.forEach((nod,ind)=>{
                         if(nod.id == node.id){
                         this.editor.connect(this.editor.nodes[ind].outputs.get(newNodeOutputSuccess),nextNode.inputs.get(nextNodeInput));
                        }
                    })
                  }
                }
                if(nodes.name == 'Error' ){//&& nodeLen > 0
                  this.changeNodePosition(3,[nodes.position[0],nodes.position[1] + 111])
                }
                if(nodes.name == 'Success'){
                  this.changeNodePosition(2,[nodes.position[0],nodes.position[1] + 111])
                }
              })
           } 
            if(newNodeErrorOutput && node.outputs.get(newNodeErrorOutput)) {
              this.editor.connect(node.outputs.get(newNodeErrorOutput), this.editor.nodes[2].inputs.get("Error"));
            }
      }
    document.getElementById('closePanel').click();
    this.informParent.emit(true);    
  }

  getNodes(index:any){
    switch (index) {
      case 0:
        this.getAllNodes();
        this.listbox.filterViewChild.nativeElement.autofocus = true;
        break;
        case 1:
        this.getServiceData();
         break;
         case 2:
          this.getFormsData();
           break;
            case 3:
              this.getAllOthers();
              break;
    }
  }

  async changeNodePosition(nodeId: number, newPosition: [number, number]) {
    const area = this.editor.view.area;
    const node = this.editor.nodes.find(n => n.id === nodeId);
    if (node) {
      this.editor.view.nodes.get(node).translate(...newPosition)
      node.update();
      area.update();
    }
  }

  async getConnectionsByNodeId(nodeData){    
    let connections = nodeData
       this.editor.nodes.forEach((nodes,index)=>{       
        if(nodes.id == connections.id){
          this.changeNodePosition(connections.id,[nodes.position[0],nodes.position[1] + 111])
        }
       });
  }

  async getNodesUntilEnd(nodeData) {
    let result = [];
    if(nodeData.name == 'Success'){

    }else{
      result = [nodeData];
    }
    const traverseNodes = (currentNode) => {
        currentNode.outputs.forEach((output, index) => { 
        output.connections.forEach(connection => {
          if(connection.input.key != 'Success' && connection.input.key != 'Error'){
            result.push(connection.input.node);
            traverseNodes(connection.input.node);
          }
       });
      });
    };
      traverseNodes(nodeData);
      return result;
  }
  async getNodesUntilFromtoEnd(nodeData) {
    const result = [];
    const traverseNodes = (currentNode) => {
        currentNode.outputs.forEach((output, index) => {          
        output.connections.forEach(connection => {
          if(connection.input.key != 'Success' && connection.input.key != 'Error'){
            result.push(connection.input.node);
            traverseNodes(connection.input.node);
          }
       });
      });
    };
      traverseNodes(nodeData);
      return result;
  }

}
// this.editor.trigger('arrange', { node: this.editor.nodes[0] });