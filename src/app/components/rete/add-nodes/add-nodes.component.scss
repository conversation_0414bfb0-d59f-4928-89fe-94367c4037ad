.tab{
    height: 40px;
    margin-top: 5px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    transition: 0.3s;
    padding-top: 10px;
    cursor: pointer;
}
.listScreen{
    max-height: 232px !important;
    overflow-x: hidden;
    overflow-y: auto;
}

::ng-deep p-tabView  .p-tabview .p-tabview-panels{
padding: 0px !important;
}
::ng-deep .tabview1 .p-tabview .p-tabview-nav li{
    width: 100px !important;
  }
  ::ng-deep .tabview1 .p-tabview .p-tabview-nav li .p-tabview-nav-link{
    justify-content: center !important;
  }

  ::ng-deep .spanDescText { 
    display:block;
    width:300px;
    overflow:hidden;
    text-overflow: ellipsis !important;
  }
  // ::ng-deep .listBoxSearch .p-listbox .p-listbox-header .p-listbox-filter{
  //      border-color: var(--primary-color) !important;
  //      box-shadow: inset 0 0 0 0.2rem #C7D2FE;
  // }
  .pmgImg{
    height: 30px !important;
    width: 30px !important;
  }