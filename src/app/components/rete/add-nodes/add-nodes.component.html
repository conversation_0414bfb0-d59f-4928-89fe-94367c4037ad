
<p-button (click)="op.toggle($event)" icon="pi pi-plus" styleClass="p-button-rounded p-button-text p-button-raised p-button-outlined" class="nodeBtn1" id="closePanel"></p-button>
<p-overlayPanel #op [dismissable]="true" [showCloseIcon]="true" header="All" [style]="{'width': '400px'}">
    <div><b>Add Node</b></div>
        <p-tabView styleClass="tabview-custom" class="tabview1" (onChange)="getNodes($event.index)" [activeIndex]=0>
            <p-tabPanel header="All">
                <ng-template pTemplate="header">
                    <span>All</span>
                </ng-template>
                <p-listbox #listbox  class="listBoxSearch" [options]="allnode" optionLabel="name" [group]="true" filterBy="name,desc" [listStyle]="{ 'max-height': '250px'}"  [filter]="true"
                    (onClick)="addNodeComponent($event.value.component)" [style]="{ 'padding': '0px', 'border-radius':'0px' }">
                    <ng-template let-list pTemplate="item">
                        <div class="flex align-items-center">
                            <span class="nodeIcon"><img class="pmgImg" src="assets/icon/{{list.icon}}.png"></span>
                            <span class="ml-3 ">
                                <div class="font-normal text-primary ">{{list.name}}</div>
                                <div>{{list.desc}}</div>
                            </span>   
                        </div>
                    </ng-template>
                </p-listbox>
            </p-tabPanel>
            <p-tabPanel header="Integrations">
                <ng-template pTemplate="header">
                    <span>Integrations</span>
                </ng-template>
                <p-listbox class="listBoxSearch" #listbox [options]="allnode" optionLabel="name" filterBy="name,desc" [listStyle]="{ 'max-height': '250px'}" [filter]="true"
                (onClick)="addNodeComponent($event.value.component)" [style]="{ 'padding': '0px', 'border-radius':'0px' }">
                    <ng-template let-list pTemplate="item">
                        <div class="flex align-items-center">
                            <span class="nodeIcon"><img class="pmgImg" src="assets/icon/{{list.icon}}.png"></span>
                            <span class="ml-3">
                                <div class="font-normal text-primary ">{{list.name}}</div>
                                <div>{{list.desc}}</div>
                            </span>   
                        </div>
                    </ng-template>
                </p-listbox>
            </p-tabPanel>
            <p-tabPanel header="Forms">
                <ng-template pTemplate="header">
                    <span>Forms</span>
                </ng-template>
                <p-listbox class="listBoxSearch" #listbox [options]="allnode" optionLabel="name" filterBy="name,desc" [group]="true" [listStyle]="{ 'max-height': '250px' }" [filter]="true"
                (onClick)="addNodeComponent($event.value.component)" [style]="{ 'padding': '0px', 'border-radius':'0px' }">
                    <ng-template let-list pTemplate="group">
                        <div class="flex align-items-center">
                            <span class="font-bold text-xl">{{list.label}}</span>    
                        </div>
                    </ng-template>
                    <ng-template let-list pTemplate="item">
                        <div class="flex align-items-center">
                            <span class="nodeIcon"><img class="pmgImg" src="assets/icon/{{list.icon}}.png"></span>
                            <span class="ml-3">
                                <div class="font-normal text-primary ">{{list.name}}</div>
                                <div>{{list.desc}}</div>
                                
                            </span>  
                        </div>
                    </ng-template>
                </p-listbox>
            </p-tabPanel>
            <p-tabPanel header="Others"> 
                <ng-template pTemplate="header">
                    <span>Others</span>
                </ng-template>
                <p-listbox class="listBoxSearch" [options]="allnode" #listbox optionLabel="name" [group]="true" filterBy="name,desc" [listStyle]="{ 'max-height': '250px' }" [filter]="true"
                (onClick)="addNodeComponent($event.value.component)" [style]="{ 'padding': '0px', 'border-radius':'0px' }">
                    <ng-template let-list pTemplate="group">
                        <div class="flex align-items-center">
                            <span class="font-bold text-xl">{{list.label}}</span>    
                        </div>
                    </ng-template>
                    <ng-template let-list pTemplate="item">
                        <div class="flex align-items-center">
                            <span class="nodeIcon"><img class="pmgImg" src="assets/icon/{{list.icon}}.png"></span>
                            <span class="ml-3">
                                <div class="font-normal text-primary ">{{list.name}}</div>
                                <div>{{list.desc}}</div>
                                
                            </span>   
                        </div>
                    </ng-template>
                </p-listbox>
            </p-tabPanel>



        </p-tabView>
 </p-overlayPanel> 