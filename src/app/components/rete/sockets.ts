import { Socket } from 'rete';
// export const conditionErrorSocket = new Socket('ConditionError');
export const numSocket = new Socket('Number Value');
export const strSocket = new Socket('Input');
export const generalSocket = new Socket('GeneralInput');
export const conditionInputSocket = new Socket('ConditionInput');
export const conditionElseSocket = new Socket('Else');
export const conditionThenSocket = new Socket('Then');
export const ApprovalApprovedSocket = new Socket('Approved');
export const ApprovalRejectedSocket = new Socket('Rejected');
export const ApprovalSendbackSocket = new Socket('ReturnToRequestor');
export const ApprovalNextSocket = new Socket('NextApprovar');
export const startSocket = new Socket('Start');
export const formsSocket = new Socket('FormsOperation');
export const integrationSocket = new Socket('Integration');
export const successSocket = new Socket('Success');
export const errorSocket = new Socket('Error');
export const injectSocket = new Socket('response');
export const combineSocket = new Socket('Combine');//new node still need to connect

startSocket.combineWith(strSocket);
startSocket.combineWith(successSocket);
startSocket.combineWith(errorSocket);
startSocket.combineWith(formsSocket);
startSocket.combineWith(integrationSocket);
startSocket.combineWith(generalSocket);
startSocket.combineWith(injectSocket);
// startSocket.combineWith(combineSocket);

successSocket.combineWith(strSocket);
successSocket.combineWith(formsSocket);
successSocket.combineWith(conditionInputSocket);
successSocket.combineWith(integrationSocket);
successSocket.combineWith(generalSocket);
successSocket.combineWith(injectSocket);
successSocket.combineWith(combineSocket);

ApprovalApprovedSocket.combineWith(strSocket);
ApprovalApprovedSocket.combineWith(successSocket);
ApprovalApprovedSocket.combineWith(errorSocket);
ApprovalApprovedSocket.combineWith(formsSocket);
ApprovalApprovedSocket.combineWith(conditionInputSocket);
ApprovalApprovedSocket.combineWith(integrationSocket);
ApprovalApprovedSocket.combineWith(generalSocket);
ApprovalApprovedSocket.combineWith(injectSocket);
ApprovalApprovedSocket.combineWith(combineSocket);

ApprovalRejectedSocket.combineWith(strSocket);
ApprovalRejectedSocket.combineWith(successSocket);
ApprovalRejectedSocket.combineWith(errorSocket);
ApprovalRejectedSocket.combineWith(formsSocket);
ApprovalRejectedSocket.combineWith(conditionInputSocket);
ApprovalRejectedSocket.combineWith(integrationSocket);
ApprovalRejectedSocket.combineWith(generalSocket);
ApprovalRejectedSocket.combineWith(injectSocket);
ApprovalApprovedSocket.combineWith(combineSocket);

ApprovalSendbackSocket.combineWith(strSocket);
ApprovalSendbackSocket.combineWith(successSocket);
ApprovalSendbackSocket.combineWith(errorSocket);
ApprovalSendbackSocket.combineWith(formsSocket);
ApprovalSendbackSocket.combineWith(conditionInputSocket);
ApprovalSendbackSocket.combineWith(integrationSocket);
ApprovalSendbackSocket.combineWith(generalSocket);
ApprovalSendbackSocket.combineWith(injectSocket);
ApprovalApprovedSocket.combineWith(combineSocket);

ApprovalNextSocket.combineWith(strSocket);
ApprovalNextSocket.combineWith(successSocket);
ApprovalNextSocket.combineWith(errorSocket);
ApprovalNextSocket.combineWith(formsSocket);
ApprovalNextSocket.combineWith(conditionInputSocket);
ApprovalNextSocket.combineWith(integrationSocket);
ApprovalNextSocket.combineWith(generalSocket);
ApprovalNextSocket.combineWith(injectSocket);
ApprovalApprovedSocket.combineWith(combineSocket);

errorSocket.combineWith(strSocket);
errorSocket.combineWith(formsSocket);
errorSocket.combineWith(conditionInputSocket);
errorSocket.combineWith(integrationSocket);
errorSocket.combineWith(injectSocket);
errorSocket.combineWith(generalSocket);
conditionElseSocket.combineWith(combineSocket);

conditionThenSocket.combineWith(integrationSocket);
conditionThenSocket.combineWith(formsSocket);
conditionThenSocket.combineWith(successSocket);
conditionThenSocket.combineWith(errorSocket);
conditionThenSocket.combineWith(strSocket);
conditionThenSocket.combineWith(generalSocket);
conditionThenSocket.combineWith(injectSocket);
conditionElseSocket.combineWith(combineSocket);

conditionElseSocket.combineWith(integrationSocket);
conditionElseSocket.combineWith(successSocket);
conditionElseSocket.combineWith(formsSocket);
conditionElseSocket.combineWith(errorSocket);
conditionElseSocket.combineWith(strSocket);
conditionElseSocket.combineWith(generalSocket);
conditionElseSocket.combineWith(injectSocket);
conditionElseSocket.combineWith(combineSocket);

strSocket.combineWith(conditionInputSocket);
strSocket.combineWith(integrationSocket);
strSocket.combineWith(formsSocket);

generalSocket.combineWith(successSocket);
generalSocket.combineWith(conditionInputSocket);
generalSocket.combineWith(integrationSocket);
generalSocket.combineWith(formsSocket);

formsSocket.combineWith(strSocket);
formsSocket.combineWith(successSocket);
formsSocket.combineWith(errorSocket);
formsSocket.combineWith(startSocket);
formsSocket.combineWith(integrationSocket);





// injectSocket.combineWith(successSocket);
// injectSocket.combineWith(startSocket);
// successSocket.combineWith(InjectSocket);
// errorSocket.combineWith(InjectSocket);
// conditionErrorSocket.combineWith(successSocket);
// conditionErrorSocket.combineWith(errorSocket);
// conditionErrorSocket.combineWith(strSocket);
