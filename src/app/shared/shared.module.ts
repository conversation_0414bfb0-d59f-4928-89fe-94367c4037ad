import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { ImageCropperModule } from 'ngx-image-cropper';
//import { QueryBuilderModule } from "angular2-query-builder";
import {HttpClientModule} from '@angular/common/http';
// Angular CDK Components
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CdkTreeModule} from '@angular/cdk/tree';
import { ClipboardModule } from '@angular/cdk/clipboard';
// PrimeNg Components
import {InputTextModule} from 'primeng/inputtext';
import {DropdownModule} from 'primeng/dropdown';
import {ButtonModule} from 'primeng/button';
import {MessagesModule} from 'primeng/messages';
import {MessageModule} from 'primeng/message';
import {SidebarModule} from 'primeng/sidebar';
import {AvatarModule} from 'primeng/avatar';
import {TabViewModule} from 'primeng/tabview';
import {TabMenuModule} from 'primeng/tabmenu';
import {TooltipModule} from 'primeng/tooltip';
import {InputSwitchModule} from 'primeng/inputswitch';
import {InputNumberModule} from 'primeng/inputnumber';
import {AccordionModule} from 'primeng/accordion';
import {DialogModule} from 'primeng/dialog';
import {ToastModule} from 'primeng/toast';
import {TableModule} from 'primeng/table';
import {FileUploadModule} from 'primeng/fileupload';
import {ScrollerModule} from 'primeng/scroller';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { ChipsModule } from 'primeng/chips';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { RadioButtonModule } from 'primeng/radiobutton';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToolbarModule } from 'primeng/toolbar';
import { SelectButtonModule } from 'primeng/selectbutton';
import { DataViewModule } from 'primeng/dataview';
import { TagModule } from 'primeng/tag';
import { SkeletonModule } from 'primeng/skeleton';
import { MenuModule } from 'primeng/menu';
import { BadgeModule } from 'primeng/badge';
import { DividerModule } from 'primeng/divider';
import { DynamicDialogModule } from 'primeng/dynamicdialog';
import { CardModule } from 'primeng/card';
import { ProgressBarModule } from 'primeng/progressbar';
import { CalendarModule } from 'primeng/calendar';
import { PaginatorModule } from 'primeng/paginator';
import { DynamicDialogRef } from 'primeng/dynamicdialog';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ConfirmDialogModule} from 'primeng/confirmdialog';
import { SplitButtonModule } from 'primeng/splitbutton';
import { SlideMenuModule } from 'primeng/slidemenu';
import { MenubarModule } from 'primeng/menubar';
import { TreeTableModule } from 'primeng/treetable';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { TreeModule } from 'primeng/tree';
import { ListboxModule } from 'primeng/listbox';
import { SplitterModule } from 'primeng/splitter';
import { SpeedDialModule } from 'primeng/speeddial';
import { PasswordModule } from 'primeng/password';
import { PanelModule } from 'primeng/panel';
import { MegaMenuModule } from 'primeng/megamenu';
import { ScrollTopModule } from 'primeng/scrolltop';
import { FieldsetModule } from 'primeng/fieldset';
import { AutoFocusModule } from 'primeng/autofocus';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { BlockUIModule } from 'primeng/blockui';
import { ColorPickerModule } from 'primeng/colorpicker';
import { ImageModule } from 'primeng/image';

// Pipes
import { FilterPipe } from './pipes/filter.pipe';
import { SafePipe } from './pipes/safe.pipe';

//Cpmponents
import { PageNotFoundComponent } from './components/page-not-found/page-not-found.component';
import { MonacoEditorModule } from 'ngx-monaco-editor';
import { NgJsonEditorModule } from 'ang-jsoneditor';
import { AutoCompleteModule } from 'primeng/autocomplete';
// Pipes
// import { FilterPipe } from './pipes/filter.pipe';
// import { UsersFilterPipe } from './pipes/usersfilter.pipe';
// import { TeamsFilterPipe } from './pipes/teamsfilter.pipe';
// import { FormsFilterPipe } from './pipes/formsfilter.pipe';
// import { DataPropertyGetterPipe } from './components/table/data-property-getter-pipe/data-property-getter.pipe';
// Dialogs
// import { CreateFormComponent } from './dialogs/create-form/create-form.component';
// import { SelectListComponent } from './dialogs/select-list/select-list.component';
// import { ImportFormsComponent } from './dialogs/import-forms/import-forms.component';
// import { AssignTeamsToFormComponent } from './dialogs/assign-teams-to-form/assign-teams-to-form.component';
// import { GetFormsComponent } from './dialogs/get-forms/get-forms.component';
// import { GetFormsetsComponent } from './dialogs/get-fomsets/get-fomsets.component';
// import { CreateScheduleComponent } from './dialogs/create-schedule/create-schedule.component';
// import { PublishFormComponent } from './dialogs/publish-form/publish-form.component';
// Components
// import { TableComponent } from './components/table/table.component';
// import { BlockCopyPasteDirective } from './directives/blockcopypaste.directive';
// import { SelectDashboardComponent } from './dialogs/select-dashboard/select-dashboard.component';
// import { FormUsersComponent } from './dialogs/form-users/form-users.component';
// import { FormConfigComponent } from './dialogs/form-config/form-config.component';
// import { JsonViewComponent } from './dialogs/json-view/json-view.component';
// import { PrettyjsonPipe } from './pipes/prettyjson.pipe';
// import { DashboardlistDialogComponent } from './components/dashboardlist-dialog/dashboardlist-dialog.component';
// import { CreateWorkflowDialogComponent } from '../rete/create-workflow-dialog/create-workflow-dialog.component';

@NgModule({
  declarations: [
    FilterPipe,
    PageNotFoundComponent,
    SafePipe,
    // UsersFilterPipe,
    // TeamsFilterPipe,
    // FormsFilterPipe,
    // TableComponent,
    // IconpickerComponent,
    // CreateFormComponent,
    // SelectListComponent,
    // ImportFormsComponent,
    // GetFormsComponent,
    // AssignTeamsToFormComponent,
    // GetFormsetsComponent,
    // CreateScheduleComponent,
    // PublishFormComponent,
    // BlockCopyPasteDirective,
    // DataPropertyGetterPipe,
    // SelectDashboardComponent,
    // FormUsersComponent,
    // FormConfigComponent,
    // JsonViewComponent,
    // PrettyjsonPipe,
    // DashboardlistDialogComponent,
    // CreateWorkflowDialogComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    DragDropModule,
    CdkTreeModule,
    ClipboardModule,
    //QueryBuilderModule,
    InputTextModule,
    DropdownModule,
    ButtonModule,
    MessagesModule,
    MessageModule,
    SidebarModule,
    AvatarModule,
    TabViewModule,
    TabMenuModule,
    TooltipModule,
    InputSwitchModule,
    InputNumberModule,
    AccordionModule,
    DialogModule,
    ToastModule,
    TableModule,
    HttpClientModule,
    FileUploadModule,
    ScrollerModule,
    RadioButtonModule,
    ScrollPanelModule,
    DynamicDialogModule ,
    TreeModule,
    InputTextareaModule,
    ListboxModule,
    SplitterModule,
    SpeedDialModule,
    MenuModule,
    ConfirmPopupModule,
    DividerModule,
    PasswordModule,
    MultiSelectModule,
    SelectButtonModule,
    TreeTableModule,
    SplitButtonModule,
    SlideMenuModule,
    MenubarModule,
    OverlayPanelModule,
    ChipsModule,
    DataViewModule,
    TagModule,
    SkeletonModule,
    BadgeModule,
    ProgressBarModule,
    CalendarModule,
    CheckboxModule,
    ToolbarModule,
    CardModule,
    PaginatorModule,
    BreadcrumbModule,
    ConfirmDialogModule,
    PanelModule,
    MonacoEditorModule,
    MegaMenuModule,
    ScrollTopModule,
    NgJsonEditorModule,
    FieldsetModule,
    AutoFocusModule,
    AutoCompleteModule,
    ImageCropperModule,
    ProgressSpinnerModule,
    BlockUIModule,
    ColorPickerModule,
    ImageModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
   // QueryBuilderModule,
    DragDropModule,
    CdkTreeModule,
    FilterPipe,
    SafePipe,
    // UsersFilterPipe,
    // TeamsFilterPipe,
    // FormsFilterPipe,
    ClipboardModule,
    // DataPropertyGetterPipe,
    InputTextModule,
    DropdownModule,
    ButtonModule,
    MessagesModule,
    MessageModule,
    SidebarModule,
    AvatarModule,
    TabViewModule,
    TabMenuModule,
    TooltipModule,
    InputSwitchModule,
    InputNumberModule,
    AccordionModule,
    DialogModule,
    ToastModule,
    TableModule,
    FileUploadModule,
    ScrollerModule,
    ConfirmDialogModule,
    CheckboxModule,
    BreadcrumbModule,
    ToolbarModule,
    CardModule,
    PaginatorModule,
    RadioButtonModule,
    ScrollPanelModule,
    DynamicDialogModule,
    TreeModule,
    InputTextareaModule,
    ListboxModule,
    SplitterModule,
    SpeedDialModule,
    MenuModule,
    ConfirmPopupModule,
    DividerModule,
    PasswordModule,
    MultiSelectModule,
    SelectButtonModule,
    TreeTableModule,
    SplitButtonModule,
    SlideMenuModule,
    MenubarModule,
    OverlayPanelModule,
    ChipsModule,
    DataViewModule,
    TagModule,
    SkeletonModule,
    BadgeModule,
    ProgressBarModule,
    CalendarModule,
    PanelModule,
    PageNotFoundComponent,
    MonacoEditorModule,
    MegaMenuModule,
    ScrollTopModule,
    NgJsonEditorModule,
    FieldsetModule,
    AutoFocusModule,
    AutoCompleteModule,
    ImageCropperModule,
    ProgressSpinnerModule,
    BlockUIModule,
    ColorPickerModule,
    ImageModule
  ],
  providers: [
    ConfirmationService,
    MessageService,
    DynamicDialogRef
    ],
})
export class SharedModule {}
