import { AbstractControl, ValidatorFn, ValidationErrors  } from '@angular/forms';

export function whiteSpaceValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const isWhitespace = control.value ? (control.value as string).indexOf(' ') >= 0 : null;
        const isValid = !isWhitespace;
        return isValid ? null : { 'whitespace': true };
    };
}