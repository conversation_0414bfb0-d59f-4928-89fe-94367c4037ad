import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-sso-redirect',
  template: `
    <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
      <div style="text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
        <div style="margin-bottom: 20px;">
          <i class="pi pi-spin pi-spinner" style="font-size: 2rem; color: #007ad9;"></i>
        </div>
        <h3 style="margin: 0 0 10px 0; color: #333;">Processing SSO Authentication</h3>
        <p style="margin: 0; color: #666;">Please wait while we complete your login...</p>
      </div>
    </div>
  `,
  styles: []
})
export class SsoRedirectComponent implements OnInit {

  constructor(private route: ActivatedRoute) { }

  ngOnInit(): void {
    // Handle SSO redirect parameters
    this.route.queryParams.subscribe(params => {
      console.log('SSO Redirect params:', params);
      
      const error = params['error'];
      const token = params['token'];
      
      let result;
      if (error) {
        result = { 
          success: false, 
          error: `SSO Error: ${error}`,
          details: params
        };
      } else if (token) {
        result = { 
          success: true, 
          token: token,
          details: params
        };
      } else {
        result = { 
          success: false, 
          error: 'No authentication parameters received',
          details: params
        };
      }
      
      console.log('SSO Result:', result);
      
      // Store result in localStorage for parent window to read
      localStorage.setItem('sso_result', JSON.stringify(result));
      
      // Close this tab after a short delay
      setTimeout(() => {
        window.close();
      }, 1000);
    });
  }
}
