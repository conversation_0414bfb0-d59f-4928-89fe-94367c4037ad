import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthenticationService } from 'src/app/services/authentication.service';

export const roleGuard: CanActivateFn = (route, state) => {
  const authservice = inject(AuthenticationService);
  const router = inject(Router);
  
  const user = authservice.decode();
    if (route.data['role'].includes(user.ROLE)) {
      return true;
    }
    router.navigate(['/']);
    return false;
};
