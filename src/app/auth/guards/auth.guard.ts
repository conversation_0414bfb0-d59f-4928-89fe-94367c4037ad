import { inject } from '@angular/core';
import { CanActivateChildFn, CanActivateFn, Router } from '@angular/router';

export const authGuardChild: CanActivateChildFn = (childRoute, state) => {
  const router = inject(Router);
  const idToken = localStorage.getItem('token');
    if (idToken) {
      return true;
    }
    router.navigate(['/login'], { queryParams: { returnUrl: state.url }});
    return false;
};

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const idToken = localStorage.getItem('token');
    if (idToken) {
      return true;
    }
    router.navigate(['/login'], { queryParams: { returnUrl: state.url }});
    return false;
};
