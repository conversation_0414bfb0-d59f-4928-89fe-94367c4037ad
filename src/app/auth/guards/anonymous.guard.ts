import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

export const anonymousGuard: CanActivateFn = (route, state) => {

  const router = inject(Router);
  const idToken = localStorage.getItem('token');
  const defaultPage = localStorage.getItem('defaultPage');
    if (!idToken) {
      return true;
    } 
    router.navigate([defaultPage || '/home']);
    return false;
};
