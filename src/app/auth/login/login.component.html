<p-toast></p-toast>
<div class="bg-color">
    <!-- Language dropdown at top right of page -->
    <div class="language-dropdown-container">
        <div class="language-dropdown">
            <div class="language-dropdown-with-icon">
                <i class="pi pi-globe language-icon"></i>
                <p-dropdown class="p-inputtext-sm" styleClass="w-full" id="Language" [options]="languages" [(ngModel)]="currentLanguage" (onChange)="selectLanguage($event)" [ngModelOptions]="{standalone: true}" optionLabel="display" optionValue="key" [showClear]="false"></p-dropdown>
            </div>
        </div>
    </div>

    <div class="main-container">
        <div class="child-container">

            <div class="flex flex-column align-items-center">
                <img src="assets/images/product_logo_small_square.png" class="w-7rem">
                <div *ngIf="!localEmail" class="text-900 text-3xl font-medium lg:my-3 md:my-3">{{isSignUp ? 'Sign Up' : (isSSOMode ? 'SSO Login' : 'Login')}}</div>
                <span *ngIf="localName" class="text-900 text-3xl font-medium lg:mt-2 md:mt-2">Welcome back {{localName}}!</span>
                <span *ngIf="loginForm.get('domain')?.value" class="text-600 font-medium lg:mt-4 md:mt-4">{{loginForm.get('domain').value}}</span>
                <span *ngIf="localEmail" class="text-600 font-medium lg:mt-1 md:mt-1">{{localEmail}}</span>
            </div>
            <form [formGroup]="loginForm" novalidate> 
                <div class="flex flex-column justify-content-center align-items-center">
                    <div class="col-offset-2"></div>
                    <div class="col-8 pt-0">
                        <div *ngIf="setDomain === ' ' && !(isSSOMode && getLocalDomain)" class="w-full mt-4">
                            <span class="p-float-label">
                                <input pInputText id='domain' formControlName="domain" style="text-transform: uppercase;"  class="p-inputtext-sm w-full" type="email" required="true">
                                <label htmlFor="domain" class="_required">{{DomainPlaceholder}}</label>
                            </span>
                            <span *ngIf="loginForm.get('domain').hasError('whitespace')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Whitespaces are not allowed.</small></span>
                        </div>
                        <div *ngIf="showADSdomain" class=" mt-4">
                            <span class="p-float-label">
                                <p-dropdown class="p-inputtext-sm" styleClass="w-full" id="adsdomain" [options]="adsdomain" formControlName="adsdomainname" optionValue="adsdomain"></p-dropdown>
                                <label htmlFor="adsdomain" class="_required">ADS Domain</label>
                            </span>
                        </div>
                        <div *ngIf="!localEmail && !isSSOMode" class="w-full mt-4">
                            <span class="p-float-label">
                                <input pInputText id='email' class="p-inputtext-sm w-full" formControlName="email" type="text" [required]="!isSSOMode">
                                <label htmlFor="email" [class]="!isSSOMode ? '_required' : ''">{{EmailPlaceholder}}</label>
                            </span>
                            <span *ngIf="loginForm.get('email').hasError('email')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Invalid email id.</small></span>
                        </div>
                        <div *ngIf="!isSSOMode" class="mt-4">
                            <span class="p-input-icon-right p-float-label w-full">
                                <i [class]="showPassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showPassword=!showPassword;"></i>
                                <input id="password" pInputText formControlName="password" class="w-full p-inputtext-sm" [type]="showPassword ? 'text' : 'password'" [required]="!isSSOMode">
                                <label htmlFor="password" [class]="!isSSOMode ? '_required' : ''">Password</label>
                            </span>
                            <small class="forgot-password-link" (click)="forgetPassword()">Forgot password? <i *ngIf="forgetPasswordLoading" class="pi pi-spin pi-spinner"></i></small>
                        </div>
                        <!-- Centered Login Button -->
                        <div class="flex justify-content-center mt-5">
                            <button *ngIf="!isSignUp" class="bg-blue p-button-sm p-button-rounded custom-button" pButton type="submit" label="Login" (click)="login()" [disabled]="!isLoginButtonEnabled"></button>
                            <button *ngIf="isSignUp" class="bg-blue p-button-sm p-button-rounded custom-button" pButton type="button" label="Sign Up" (click)="signUpRequest()" [disabled]="this.loginForm.get('email').hasError('required') || this.loginForm.get('domain').hasError('required')"></button>
                        </div>

                        <!-- SSO Login and Sign Up Links -->
                        <div class="flex justify-content-center align-items-center mt-5">
                            <!-- Login mode: SSO toggle and Sign Up on same line -->
                            <div *ngIf="!isSignUp" class="flex justify-content-between align-items-center w-8">
                                <!-- Left side: SSO Login toggle (hidden when using cached login info) -->
                                <div class="flex-1 text-left">
                                    <span *ngIf="!isSSOMode && !getLocalDomain"
                                          (click)="showSSOToggle ? toggleToSSOMode() : null"
                                          [class]="showSSOToggle ? 'button-link' : 'button-link-disabled'">SSO Login</span>
                                    <span *ngIf="isSSOMode && !getLocalDomain"
                                          (click)="toggleToEmailMode()"
                                          class="button-link">Email Login</span>
                                </div>

                                <!-- Right side: Sign Up link -->
                                <div class="flex-1 text-right">
                                    <span *ngIf="!getLocalDomain" (click)="goToSignUpPage()" class="button-link">Sign Up</span>
                                </div>
                            </div>

                            <!-- Sign Up mode: Centered login link with label -->
                            <div *ngIf="isSignUp" class="text-center">
                                <span class="text-600 mr-2">Already have an account?</span>
                                <span (click)="goToLoginPage()" class="button-link">Login</span>
                            </div>
                        </div>

                        <!-- Use Different Account link (centered, separate line) -->
                        <div *ngIf="getLocalDomain" class="text-center mt-2">
                            <span (click)="useDifferentAcc()" class="button-link">Use Different Account</span>
                        </div>
                    </div>
                    <div class="col-offset-2"></div>
                </div>
            </form>
        </div>
    </div>
</div>