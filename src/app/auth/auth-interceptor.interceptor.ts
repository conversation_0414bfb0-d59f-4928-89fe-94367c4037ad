import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpSentEvent,
  HttpHeaderResponse,
  HttpProgressEvent,
  HttpResponse,
  HttpUserEvent,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { tap } from 'rxjs/operators';
import { PopupService } from '../services/popup.service';
import { LoaderService } from '../services/loader.service';
import { MessageService } from 'primeng/api';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor( private popupservice: PopupService,private loaderService: LoaderService, private messageService: MessageService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpSentEvent | HttpHeaderResponse | HttpProgressEvent | HttpResponse<any> | HttpUserEvent<any>> {
    const idToken = localStorage.getItem('token');
        if (idToken) {
          const cloned = req.clone({
              headers: req.headers.set('Authorization', 'Bearer ' + idToken)
          });
          this.showLoader();
          return next.handle(cloned).pipe(
            tap({ next:(event: HttpEvent<any>) => {
              if (event instanceof HttpResponse) {
                // do stuff with response if you want
                 const jwt = event.headers.get('jwttoken');
                 this.onEnd();
                 if (jwt) {
                   localStorage.setItem('token', jwt);
                 }
              }
            },
            error: (error => {
              if (error instanceof HttpErrorResponse && error.status === 401) {
                this.onEnd();
                // Login again in case of Auth error on token expiry
                 const login_type = localStorage.getItem('login_type');
                 if (login_type === 'SSO') {
                   localStorage.removeItem('token');
                   localStorage.removeItem('theme');
                   this.popupservice.openSSO('login');
                 } else {
                   localStorage.removeItem('token');
                   localStorage.removeItem('theme');
                   location.reload();
                 }
              } else if (error instanceof HttpErrorResponse && !(error.status === 200 || error.status === 201 || error.status === 204)) {
                console.log('interceptor error',error)
                this.onEnd();
                this.messageService.add({ severity: 'error', summary: 'Error: ' + error.status, detail: "Something went wrong!" });
                return error;
              } else {
                this.onEnd();
                return error;
              }
            })
        }));
        } else {
          return next.handle(req);
        }
  }

  private onEnd(): void {
    this.hideLoader();
  }
  private showLoader(): void {
    this.loaderService.show();
  }
  private hideLoader(): void {
    this.loaderService.hide();
  }
  
}
