import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { AuthenticationService } from 'src/app/services/authentication.service';
import { MustMatch } from 'src/app/shared/directives/must-match.directive';
import { whiteSpaceValidator } from 'src/app/shared/directives/white-space-validator.directive';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {
  public resetpasswordform: FormGroup;
  public resetToken: string;
  public user: string;
  public usertype: string;
  public showpassword: boolean;
  public cnf_showpassword: boolean;
  public showdomainfield: boolean;
  public domain: string;
  public displaymsg: string;
  public errmsg: string;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authservice: AuthenticationService,
    private route: ActivatedRoute,
    private messageService: MessageService
  ) { }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.resetToken = params['resetToken'];
      this.user = params['user'];
      this.domain = params['domain'];
      this.usertype = params['usertype'];
    });
    this.resetpasswordform = this.fb.group({
      password: ['', [Validators.required, whiteSpaceValidator()]],
      confirmpassword: ['', Validators.required]
      }, {
        validator: MustMatch('password', 'confirmpassword')
      });
  }

  ressetpassword() {
    this.authservice.resetpassword(this.resetToken, this.user, this.domain, this.resetpasswordform.get('confirmpassword').value)
    .subscribe(
      (res: any) => {
        if (res.status === 204) {
          this.usertype = 'app'
            if (this.usertype === 'app') {
              this.displaymsg = 'Your TurboForms password has been reset. You can open the TurboForms app and login now.';
              this.messageService.add({ severity:'success', summary:'Success', detail: "Your TurboForms password has been reset. You can open the TurboForms app and login now." }); 
              setTimeout(() => {
                window.close();
              }, 2000);
            } else {
              this.router.navigate(['login']);
            }
        }
      },
     (error) => {
        this.errmsg = error.error.error;
        this.messageService.add({ severity:'error', summary:'Error', detail: this.errmsg }); 
      });
  }
}
