<p-toast></p-toast>
<div class="bg-color">
    <div class="main-container">
        <div class="child-container">
            <div class="col-12 text-center pb-0">
                <img src="assets/images/product_logo_small_square.png" class="w-7rem">
                <div class="text-900 text-3xl font-medium lg:my-4 md:my-4">Reset Password</div>
            </div>
            <form [formGroup]="resetpasswordform" novalidate> 
                <div class="flex flex-column justify-content-center align-items-center">
                    <div class="col-offset-2"></div>
                    <div class="col-8 pt-0">
                        <div class="mt-4">
                            <span class="p-input-icon-right p-float-label w-full">
                                <i [class]="showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="showpassword =! showpassword;"></i>
                                <input id="password" pInputText formControlName="password" class="w-full p-inputtext-sm" [type]="showpassword ? 'text' : 'password'" required="true">
                                <label htmlFor="password" class="_required">Password</label>
                            </span>
                        </div>
                        <div class="mt-4">
                            <span class="p-input-icon-right p-float-label w-full">
                                <i [class]="cnf_showpassword ? 'pi pi-eye' : 'pi pi-eye-slash'" (click)="cnf_showpassword =! cnf_showpassword;"></i>
                                <input id="cnf-password" pInputText formControlName="confirmpassword" class="w-full p-inputtext-sm" [type]="cnf_showpassword ? 'text' : 'password'" required="true">
                                <label htmlFor="cnf-password" class="_required">Confirm</label>
                            </span>
                            <span *ngIf="resetpasswordform.get('confirmpassword').hasError('mustMatch')" class="error"><i class="pi pi-exclamation-circle mr-1"></i><small>Passwords must match.</small></span>
                        </div>
                        <div class="flex flex-row justify-content-center mt-5">
                            <button class="bg-blue p-button-sm p-button-rounded custom-button" pButton type="button" [disabled]="!resetpasswordform.valid" label="Reset" (click)="ressetpassword()"></button>
                        </div>
                    </div>
                    <div class="col-offset-2"></div>
                </div>
            </form>
        </div>
    </div>
</div>