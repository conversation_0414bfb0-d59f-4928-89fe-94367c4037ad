/*

███████╗ ██████╗ ███╗   ███╗ █████╗ ███╗   ██╗████████╗██╗ ██████╗    ██╗   ██╗██╗
██╔════╝██╔═══██╗████╗ ████║██╔══██╗████╗  ██║╚══██╔══╝██║██╔════╝    ██║   ██║██║
█████╗  ██║   ██║██╔████╔██║███████║██╔██╗ ██║   ██║   ██║██║         ██║   ██║██║
██╔══╝  ██║   ██║██║╚██╔╝██║██╔══██║██║╚██╗██║   ██║   ██║██║         ██║   ██║██║
██║     ╚██████╔╝██║ ╚═╝ ██║██║  ██║██║ ╚████║   ██║   ██║╚██████╗    ╚██████╔╝██║
╚═╝      ╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═╝ ╚═════╝     ╚═════╝ ╚═╝

  Import this file into your LESS project to use Fomantic-UI without build tools
*/

// stylelint-disable no-invalid-position-at-import-rule

/* Global */
& {
	@import 'definitions/globals/reset';
}
& {
	@import 'definitions/globals/site';
}

/* Elements */
& {
	@import 'definitions/elements/button';
}
& {
	@import 'definitions/elements/container';
}
& {
	@import 'definitions/elements/divider';
}
& {
	@import 'definitions/elements/emoji';
}
& {
	@import 'definitions/elements/flag';
}
& {
	@import 'definitions/elements/header';
}
& {
	@import 'definitions/elements/icon';
}
& {
	@import 'definitions/elements/image';
}
& {
	@import 'definitions/elements/input';
}
& {
	@import 'definitions/elements/label';
}
& {
	@import 'definitions/elements/list';
}
& {
	@import 'definitions/elements/loader';
}
& {
	@import 'definitions/elements/placeholder';
}
& {
	@import 'definitions/elements/rail';
}
& {
	@import 'definitions/elements/reveal';
}
& {
	@import 'definitions/elements/segment';
}
& {
	@import 'definitions/elements/step';
}
& {
	@import 'definitions/elements/text';
}

/* Collections */
& {
	@import 'definitions/collections/breadcrumb';
}
& {
	@import 'definitions/collections/form';
}
& {
	@import 'definitions/collections/grid';
}
& {
	@import 'definitions/collections/menu';
}
& {
	@import 'definitions/collections/message';
}
& {
	@import 'definitions/collections/table';
}

/* Views */
& {
	@import 'definitions/views/ad';
}
& {
	@import 'definitions/views/card';
}
& {
	@import 'definitions/views/comment';
}
& {
	@import 'definitions/views/feed';
}
& {
	@import 'definitions/views/item';
}
& {
	@import 'definitions/views/statistic';
}

/* Modules */
& {
	@import 'definitions/modules/accordion';
}
& {
	@import 'definitions/modules/calendar';
}
& {
	@import 'definitions/modules/checkbox';
}
& {
	@import 'definitions/modules/dimmer';
}
& {
	@import 'definitions/modules/dropdown';
}
& {
	@import 'definitions/modules/embed';
}
& {
	@import 'definitions/modules/flyout';
}
& {
	@import 'definitions/modules/modal';
}
& {
	@import 'definitions/modules/nag';
}
& {
	@import 'definitions/modules/popup';
}
& {
	@import 'definitions/modules/progress';
}
& {
	@import 'definitions/modules/slider';
}
& {
	@import 'definitions/modules/rating';
}
& {
	@import 'definitions/modules/search';
}
& {
	@import 'definitions/modules/shape';
}
& {
	@import 'definitions/modules/sidebar';
}
& {
	@import 'definitions/modules/sticky';
}
& {
	@import 'definitions/modules/tab';
}
& {
	@import 'definitions/modules/toast';
}
& {
	@import 'definitions/modules/transition';
}
