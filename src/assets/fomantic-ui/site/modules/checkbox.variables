/*******************************
    User Variable Overrides
*******************************/

/*******************************
            Checkbox
*******************************/

@checkboxSize: 20px;
@checkboxLineHeight: @checkboxSize;
@checkboxBorder: 1.5px solid @checkboxBorderColor;
@checkboxColor: @textColor;
@selectedBorderColor: @primaryColor;
@checkboxBorderRadius: @4px;
@bulletScale: (6 / 15);

/* Label */
@labelDistance: unit((28 / 14), em); /* 26px @ 14/em */

/* Radio */

/* Uses px to avoid rounding issues with circles */

@radioTop: 0;
@bulletTop: @radioTop;
@radioSize: @checkboxSize;

/* Checkmark */
@checkboxCheckFontSize: 12px;

/* Hover */
@checkboxHoverBackground: @checkboxBackground;
@checkboxHoverBorderColor: @selectedBorderColor;
@labelHoverColor: @hoveredTextColor;

/* Pressed */
@checkboxPressedBackground: @offWhite;
@checkboxPressedBorderColor: @selectedBorderColor;
@checkboxPressedColor: @white;
@labelPressedColor: @selectedTextColor;

/* Focus */
@checkboxFocusBackground: @white;
@checkboxFocusBorderColor: @focusedFormMutedBorderColor;
@checkboxFocusCheckColor: @white;
@labelFocusColor: @selectedTextColor;

/* Active */
@labelActiveColor: @selectedTextColor;
@checkboxActiveBackground: @primaryColor;
@checkboxActiveBorderColor: @selectedBorderColor;
@checkboxActiveCheckColor: @white;
@checkboxActiveCheckOpacity: 1;

/* Active Focus */
@checkboxActiveFocusBackground: @primaryColor;
@checkboxActiveFocusBorderColor: @checkboxFocusBorderColor;
@checkboxActiveFocusCheckColor: @white;
