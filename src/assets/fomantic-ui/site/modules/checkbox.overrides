/*******************************
         Site Overrides
*******************************/

.ui.radio.checkbox label:empty {
	min-height: auto;
	padding-left: 0;
}

.ui.checkbox {
	&:not(.un-survey-checkbox) {
		padding: 10px;
		border: 1px solid @fieldBorderColor;
		border-radius: @absoluteBorderRadius;
		transition: all 0.4s;

		&:has(input:checked) {
			border-color: @primaryColor;
		}

		&:hover {
			background-color: rgba(@lightBrown, 0.12);
		}

		&:has(:disabled) {
			pointer-events: none;
		}

		// &.radio {
		// 	border-radius: @absoluteBorderRadius + @6px;
		// }
	}

	&.un-survey-checkbox {
		@media only screen and (min-width: @largestMobileScreen) {
			font-size: 0;
		}

		@media only screen and (max-width: @largestMobileScreen) {
		padding: 10px;
		border: 1px solid @fieldBorderColor;
		border-radius: @absoluteBorderRadius;
		transition: all 0.4s;

		&:has(input:checked) {
			border-color: @primaryColor;
		}

		&:hover {
			background-color: rgba(@lightBrown, 0.12);
		}

		&:has(:disabled) {
			pointer-events: none;
		}
		}
	}
}

.ui.checkbox input[type='checkbox'],
.ui.checkbox input[type='radio'] {
	inset: 0;
	width: auto;
	height: auto;
	z-index: 99;
}

.ui.checkbox.label-is-hidden label {
	padding-left: unit((20 / 14), em);
}

.ui.form .field.error .ui.checkbox.checkbox:not(.toggle):not(.slider) label::before,
.ui.form .fields.error .field .ui.checkbox.checkbox:not(.toggle):not(.slider) label::before {
	border: @checkboxBorder;
}

.ui.checkbox {
	&.un-survey-checkbox {
		input:checked ~ label::before {
			background: @checkboxActiveBackground !important;
			border-color: @checkboxActiveBorderColor !important;
		}
		input:checked ~ label::after {
			color: @checkboxActiveCheckColor !important;
		}
	}
}
