/*******************************
         Site Overrides
*******************************/

@borderRadius: @8px;
@footerBackground: @white;

/* Standard */
.ui.table {
	font-weight: @headerFontWeight;
	vertical-align: baseline;
	&.table-bordered > tr > th,
	&.table-bordered > thead > tr > th,
	&.table-bordered > tr > td,
	&.table-bordered > tbody > tr > td {
		&:not(:first-child) {
			border-left: @rowBorder;
		}
	}
}

.ui.table > thead > tr > th {
	font-size: 13px;
}

/* -------------------
	Survey Table
-------------------- */

.un-survey-table {
	/* Responsive */
	@media only screen and (max-width: @largestMobileScreen) {
		&.ui.table:not(.unstackable) > {
			thead {
				display: none !important;
			}

			thead > tr,
			tfoot > tr,
			tr,
			tbody > tr {
				display: flex !important;
				flex-wrap: wrap;
				padding: 16px;
				gap: 16px;

				&:not(:first-child) {
					border-top: 1px solid @borderColor;
				}

				& > :first-child {
					min-width: 100%;
				}

				td {
					padding: 0;
				}
			}
		}
	}
}

// /* -------------------
// 	Data grid Table
// -------------------- */

// .datagrid-table {
// 	/* Responsive */
// 	@media only screen and (max-width: @largestMobileScreen) {
// 		&.ui.table:not(.unstackable) > {
// 			// thead {
// 			// 	display: none !important;
// 			// }

// 			tbody {
// 				display: table-row-group !important;
// 			}

// 			tfoot {
// 				display: table-footer-group !important;
// 			}

// 			thead > tr,
// 			tfoot > tr,
// 			tr,
// 			tbody > tr {
// 				display: table-row !important;
// 				// flex-wrap: wrap;
// 				// padding: 16px;
// 				// gap: 16px 32px;

// 				// &:not(:first-child) {
// 				// 	border-top: 1px solid @borderColor;
// 				// }

// 				// & > :first-child {
// 				// 	min-width: 100%;
// 				// }

// 				td {
// 					display: table-cell !important;
// 				}
// 			}
// 		}
// 	}
// }

/* -------------------
	Table layout fixed reverted
-------------------- */

.formio-component .table {
	table-layout: auto !important;
}

.formio-component-table .table {
	&:not(.datagrid-table) {
		table-layout: fixed !important;
	}
}

.formio-component-datagrid .ui.table.datagrid-table td {
	.ui.fluid.input > input {
		width: auto !important;
	}
}
