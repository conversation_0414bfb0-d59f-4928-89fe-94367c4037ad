/*******************************
     User Global Variables
*******************************/

/* -------------------
      Site Colors
-------------------- */
@red: #f04438;
@orange: #f28787;
@yellow: #b6630a;
@olive: #a0d7e9;
@green: #109e10;
@teal: #008080;
@blue: #285fe7;
@violet: #b8b0e5;
@purple: #b413ec;
@pink: #ff1493;
@brown: #eac43e;
@grey: #0f1419;
@black: #231c16;

/* ---  Light Colors  --- */
@lightBrown: #909090;
@darkGrey: #6f6f6f;
@lightGrey: #d6dde2;
@lighterGrey: #fafbfc;
@lightBlack: #4f5051;
@lightBlue: #24abe2;
@lightGreen: #4dd1af;

/* ---   Neutrals  --- */
@fullBlack: #000;
@offWhite: #f9fafb;
@darkWhite: #f3f4f5;
@midWhite: #dcddde;
@white: #fff;

/* --- Dark Colors --- */
// @darkBlack: #231c16;

/* Negative */
@negativeColor: @red;
@negativeBackgroundColor: #fff;
@negativeBorderColor: #fda29b;
@negativeHeaderColor: #912d2b;
@negativeTextColor: @red;

/* Positive */
@positiveColor: @green;
@positiveBackgroundColor: #fcfff5;
@positiveBorderColor: #a3c293;
@positiveHeaderColor: #1a531b;
@positiveTextColor: #2c662d;

/* --- Colored Backgrounds --- */
@greyBackground: @lighterGrey;
// @darkGreyBackground: #f9fafb;
// @primaryBackground: #dff0ff;
@secondaryBackground: @offWhite;

/* --- Colored Text --- */
@primaryTextColor: @grey;
@secondaryTextColor: @lightBlack;
@textColor: @primaryTextColor;

/* -------------------
    Brand Colors
-------------------- */
@primaryColor: @blue;
@secondaryColor: @grey;
// @pageBackground: red;

/* -------------------
     Neutral Text
-------------------- */
@selectedTextColor: @primaryTextColor;

// Global font family
// @pageFont: "Inter", sans-serif;
// @headerFont: "Inter", sans-serif;
@fontName: 'Inter';
@fontDisplay: swap;

// Global font path
@fontPath: '../../../fomantic-ui/fonts';

/* This is the single variable that controls them all */
@emSize: 14px;

/* The size of page text */
@fontSize: 14px;

/* -------------------
    Border Radius
-------------------- */
@absoluteBorderRadius: @6px;

/*
  Sizes are all expressed in terms of 14px/em (default em)
  This ensures these "ratios" remain constant despite changes in EM
*/

@miniSize: (11 / 14);
@tinySize: (12 / 14);
@smallSize: (13 / 14);
@mediumSize: (14 / 14);
@largeSize: (16 / 14);
@bigSize: (18 / 14);
@hugeSize: (20 / 14);
@massiveSize: (24 / 14);
/* --------------
  Page Heading
--------------- */

@headerFontWeight: @bold;
@headerLineHeight: unit((18 / 14), em);

@h1: unit((28 / 14), rem);
@h2: unit((24 / 14), rem);
@h3: unit((18 / 14), rem);
@h4: unit((15 / 14), rem);
@h5: unit((14 / 14), rem);
@h6: unit((12 / 14), rem);

/* --------------
   Form Input
--------------- */

/* This adjusts the default form input across all elements */
@inputBackground: @white;
@inputVerticalPadding: @relative14px;
@inputHorizontalPadding: @relative16px;
@inputPadding: @inputVerticalPadding @inputHorizontalPadding;

/* -------------------
      Borders
-------------------- */
@solidBorderColor: #e4e8eb;
@borderColor: @solidBorderColor;
@fieldBorderColor: #d6dde2;
@checkboxBorderColor: #d0d5dd;
@selectedBorderColor: @primaryColor;

/* Input Text Color */
@inputColor: @black;
@inputPlaceholderColor: @lightBrown;
@inputPlaceholderFocusColor: @lightBrown;

/* -------------------
    Focused Input
-------------------- */

/* Used on inputs, textarea etc */
@focusedFormBorderColor: @primaryColor;

/* Used on dropdowns, other larger blocks */
@focusedFormMutedBorderColor: @primaryColor;

/* -------------------
       Links
-------------------- */

@linkColor: @primaryColor;

/*  custom variables */

@uvFormBgColor: @pageBackground;
@uvFormBgImage: '';

/* -------------------
    Accordion
-------------------- */
@spaceCommonPad: 20px;
@spaceCommonPadResponsive: 16px;
@dataGridResponsiveMinWidth: 350px;
@actionsText: 'Action';

// @media only screen and (max-width: @largestMobileScreen) {
// 	@spaceCommonPad: 50px;
// }

/* -------------------
    popup custom
-------------------- */
@popupContentWidth: 465px;
@alertHeight: 52px;

/* -------------------
    Table sticky
-------------------- */
@tableStickyColumnWidth: 65px;

/* Placeholder state */
@inputErrorPlaceholderColor: if(iscolor(@formErrorColor), lighten(@formErrorColor, 0.8), @formErrorColor);
@inputErrorPlaceholderFocusColor: if(iscolor(@formErrorColor), lighten(@formErrorColor, 0.8), @formErrorColor);
