/*******************************
         Site Overrides
*******************************/

.formio-wrapper {
	--form-wrapper-padding: 40px;
	display: block;
	background-color: @uvFormBgColor;
	background-image: @uvFormBgImage;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	min-height: 100vh;

	@media only screen and (max-width: 767.98px) {
		--form-wrapper-padding: 25px;
	}
}

.un-form-wrapper {
	padding: var(--form-wrapper-padding);
	// padding-bottom: 90px;
}

fieldset {
	padding: 0;
	border: 0;
	margin: 0;
}

/* -------------------
	styles that are not available in the
	SemanticUI theme start
-------------------- */
.address-autocomplete-container .address-autocomplete-remove-value-icon {
	font-size: 1em;
	color: @lightBlack;
	transform: translateY(2px) translateX(0);
}

/* -------------------
       Data Grid
-------------------- */

@dataGridResponsiveActionColumnMinWidth: unit((50 / 14), em);

.formio-component-datagrid,
.formio-component-dataMap {
	background-color: @white;
	padding-bottom: 3px;
	// padding: @spaceCommonPad;
	// border: 1px solid @solidBorderColor;
	// border-radius: @8px;

	// @media only screen and (max-width: @largestMobileScreen) {
	// 	padding: @spaceCommonPadResponsive;
	// }

	.delete-icon-text {
		display: none;
		margin-left: 4px;
		@media only screen and (max-width: @largestMobileScreen) {
			display: block;
		}
	}

	// .ui.table {
	// 	.formio-component-datagrid {
	// 		overflow: visible;
	// 	}
	// }

	.ui.table.datagrid-table {
		border: 1px solid @borderColor !important;
		padding: 0 !important;
		vertical-align: top;

		@media only screen and (min-width: 768px) and (max-width: 1200px) {
			overflow-x: auto;
			.ui.input input.form-control,
			.ui.form select,
			.ui.selection.dropdown {
				min-width: 140px;
			}
			.ui.input textarea.form-control {
				min-width: 180px;
			}

			.ui.grid {
				flex-wrap: nowrap;
			}
			.ui.grid > [class*='column'] {
				width: auto !important;
			}
		}

		& > thead {
			@media only screen and (max-width: @largestMobileScreen) {
				display: none !important;
			}
		}

		& > tbody {
			& > tr:has(> :nth-child(2)) {
				@media only screen and (max-width: @largestMobileScreen) {
					position: relative;

					td:not(.un-remove-row-column) {
						border-left: 0 !important;
						// min-width: calc(100% - @dataGridResponsiveActionColumnMinWidth);
						// max-width: calc(100% - @dataGridResponsiveActionColumnMinWidth);
					}

					tfoot td:not(.un-remove-row-column) {
						min-width: 100%;
						max-width: 100%;
					}
				}

				& > td.un-remove-row-column {
					// min-width: @dataGridResponsiveActionColumnMinWidth;

					@media only screen and (max-width: @largestMobileScreen) {
						// position: absolute;
						// top: 0;
						// right: 0;
						// bottom: 0;
						display: flex !important;
						flex-direction: column;
						gap: 10px;
						// border-top: 1px solid @borderColor !important;
						border-left: 0 !important;
						padding-top: 12px !important;
						padding-bottom: 24px !important;
						border-bottom: 1px solid #e4e8eb !important;
						// max-width: @dataGridResponsiveActionColumnMinWidth;
						// background-color: @white !important;
						// padding: @12px !important;
						// border-radius: 0 @12px 0 0;
						// z-index: 99;

						.ui.button {
							padding: 8px !important;
							font-size: 12px;
							margin: 0;
						}
					}
				}
			}
		}

		tr {
			& + tr {
				td {
					&:first-child {
						@media only screen and (max-width: @largestMobileScreen) {
							padding-top: 24px !important;
						}
					}
				}
			}
		}

		td {
			padding: unit((12 / 14), em) unit((8 / 14), em) !important;
			border: 0 !important;
			border-top: 1px solid @borderColor !important;

			.field.row.formio-component.formio-component-columns {
				// overflow: hidden;
				// & > .ui.grid {
				// 	margin-right: -(2rem / 2);
				// }

				.ui.grid {
					margin: unit((-12 / 14), em) unit((-8 / 14), em);
				}

				.ui.grid > .column:not(.row),
				.ui.grid > .row > .column {
					padding-left: unit((8 / 14), em) !important;
					padding-right: unit((8 / 14), em) !important;
				}
			}

			// &:first-child {
			// 	padding-left: unit((10 / 14), em) !important;
			// }

			// &:nth-last-child(2) {
			// 	padding-right: unit((10 / 14), em) !important;
			// }

			@media only screen and (max-width: @largestMobileScreen) {
				padding: unit((12 / 14), em) unit((18 / 14), em) !important;
				border-top: 0 !important;

				&:last-child {
					padding-bottom: unit((18 / 14)) !important;
				}
			}

			&:not(:first-child) {
				border-left: 1px solid @borderColor !important;
			}

			// &.un-remove-row-column {
			// 	padding-left: unit((16 / 14), em) !important;
			// 	padding-right: unit((16 / 14), em) !important;
			// }
		}

		// tbody {
		// 	td {
		// 		&:last-child:not(:first-child) {
		// 			@media only screen and (max-width: @largestMobileScreen) {
		// 				position: sticky !important;
		// 				right: 0;
		// 				// min-width: @tableStickyColumnWidth;
		// 				background-color: @white !important;
		// 				padding: 10px !important;
		// 				border-radius: 0 @12px 0 0;
		// 				z-index: 1;
		// 			}
		// 		}
		// 	}
		// }

		th {
			padding: unit((12 / 14), em) unit((8 / 14), em) !important;
			border: 0 !important;
			border-bottom: 1px solid @borderColor !important;

			// &:first-child {
			// 	padding-left: unit((10 / 14), em) !important;
			// }

			// &:nth-last-child(2) {
			// 	padding-right: unit((10 / 14), em) !important;
			// }

			@media only screen and (max-width: @largestMobileScreen) {
				padding: unit((12 / 14), em) unit((18 / 14), em) !important;
			}

			// &:last-child {
			// 	@media only screen and (max-width: @largestMobileScreen) {
			// 		position: sticky;
			// 		right: 0;
			// 		// min-width: @tableStickyColumnWidth;
			// 		background-color: @white !important;
			// 		padding: 10px !important;
			// 		border-radius: 0 @12px 0 0;
			// 		z-index: 1;

			// 		&::before {
			// 			content: @actionsText;
			// 		}
			// 	}
			// }

			&:not(:first-child) {
				border-left: 1px solid @borderColor !important;
			}

			&.un-remove-row-column {
				width: calc(unit((36 / 14), em) + 8px);
				min-width: calc(unit((36 / 14), em) + 8px);
				max-width: calc(unit((36 / 14), em) + 8px);
				// padding-left: unit((16 / 14), em) !important;
				// padding-right: unit((16 / 14), em) !important;
				// @media only screen and (max-width: @largestMobileScreen) {
				// 	width: auto;
				// 	min-width: auto;
				// 	max-width: 100%;
				// }
			}
		}

		// tfoot {
		// 	td {
		// 		position: sticky !important;
		// 		left: 0;
		// 		background-color: @white !important;
		// 		z-index: 1;
		// 	}
		// }

		.formio-datagrid-remove {
			position: static;
			opacity: 1;
			visibility: visible;
			margin: 0;
		}

		&:not(thead) {
			tbody > tr:first-child > td {
				border-top: 0 !important;

				// @media only screen and (max-width: @largestMobileScreen) {
				// 	& + td:not(.un-remove-row-column) {
				// 		border-top: 1px solid @borderColor !important;
				// 	}
				// }
			}
		}

		.table-header-custom-mob {
			display: none;

			@media only screen and (max-width: @largestMobileScreen) {
				display: block;
				font-size: @relativeMedium;
				font-weight: 600;
				margin: 0 0 12px 0;
				order: -1;
			}
		}
	}
}

/* -------------------
		Signature Pad
-------------------- */
.signature-pad-body {
	// background-color: @white;
	border-radius: @6px;
	border: 1px solid @fieldBorderColor;

	&:focus-visible {
		outline: none;
	}

	.signature-pad-refresh {
		position: absolute;
		top: 12px;
		right: 12px;
	}

	.signature-pad-canvas {
		background-color: @white;
		width: 100%;
		border-radius: @6px;
		border: 0;
	}
}

.click-to-sign-box .click-to-sign-label {
	font-weight: 400;
	font-size: @relativeMedium;
	color: @lightBlack;
	margin: 11px 0 4px 0;
}

.formio-component-signature {
	.signature-pad-footer {
		font-size: @relativeMedium;
		color: @darkGrey;
		font-weight: 400;
		text-align: left;
	}
	.field-has-signature {
		.signature-info-box {
			opacity: 0;
			visibility: hidden;
		}
	}
}

.signature-info-box {
	position: absolute;
	inset: 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
	pointer-events: none;
	transition: all 0.2s ease-in-out;
}

.signature-info-box {
	&.field-has-signature {
		opacity: 0;
		visibility: hidden;
	}
}

.signature-info-box .sign-click-btn {
	cursor: pointer;
	pointer-events: all;
}

/* -------------------
		File upload
-------------------- */
.formio-component-file {
	.un-file-selector {
		a {
			font-weight: 600;
			text-decoration: none;
		}

		&.fileSelector {
			padding: @spaceCommonPad;
			border: 2px dashed @fieldBorderColor;
			border-radius: @6px;

			@media only screen and (max-width: @largestMobileScreen) {
				padding: @spaceCommonPadResponsive;
			}
		}
	}

	.un-upload-text {
		color: @primaryTextColor;
		font-size: @relativeMedium;
		font-weight: 400;
		line-height: 20px;
		margin: 4px 0 0 0;
	}

	.un-upload-icon-box {
		display: inline-flex;
		align-items: center;
		min-width: 40px;
		justify-content: center;
		border-radius: @8px;
		color: @primaryTextColor;
		border: 1px solid @borderColor !important;
		margin-bottom: @relative8px;
		box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
		aspect-ratio: 1;
	}
}

.formio-component-datagrid .ui.table.datagrid-table {
	.formio-component-file .un-file-selector.fileSelector {
		@media only screen and (min-width: 768px) and (max-width: 1200px) {
			min-width: 180px;
		}
	}
}

.un-file-uploades {
	--un-file-upload-width: 6;
	--un-file-upload-gap: 12px;
	display: grid;
	grid-template-columns: repeat(
		auto-fit,
		minmax(calc(100% / var(--un-file-upload-width)), calc(100% / var(--un-file-upload-width)))
	);
	margin-left: calc(-1 * var(--un-file-upload-gap));

	@media only screen and (max-width: 1200px) {
		--un-file-upload-width: 5;
	}

	@media only screen and (max-width: 991px) {
		--un-file-upload-width: 4;
	}

	@media only screen and (max-width: 767px) {
		--un-file-upload-width: 3;
	}

	@media only screen and (max-width: 575px) {
		--un-file-upload-width: 2;
	}

	@media only screen and (max-width: 475px) {
		--un-file-upload-width: 1;
	}

	.un-file-upload-item {
		position: relative;
		margin-bottom: 10px;
		margin-left: var(--un-file-upload-gap);
	}

	.un-file-upload-item-actions {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		justify-content: flex-end;
		row-gap: 3px;
		position: absolute;
		top: 10px;
		right: 10px;
		max-width: 100%;

		.ui.button {
			background-color: #fff;
		}
	}

	.un-file-upload-item-image image {
		max-width: 100%;
	}

	.un-file-upload-item-image {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 100%;
		border: 1px solid @borderColor;
		border-radius: 8px;
		overflow: hidden;
	}

	.un-file-upload-item-image .fileImageData {
		width: auto !important;
		max-width: 100%;
		// min-width: 100%;
		min-height: 100px;
		max-height: 145px;
		object-fit: cover;
	}
}

.datagrid-table {
	td {
		.un-file-uploades {
			--un-file-upload-width: 1;

			@media only screen and (max-width: 767px) {
				--un-file-upload-width: 3;
			}

			@media only screen and (max-width: 575px) {
				--un-file-upload-width: 2;
			}

			@media only screen and (max-width: 475px) {
				--un-file-upload-width: 1;
			}
		}

		&.td-column-count-1 {
			.un-file-uploades {
				--un-file-upload-width: 5;

				@media only screen and (max-width: 1200px) {
					--un-file-upload-width: 3;
				}

				@media only screen and (max-width: 767px) {
					--un-file-upload-width: 3;
				}

				@media only screen and (max-width: 575px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 475px) {
					--un-file-upload-width: 1;
				}
			}
		}

		&.td-column-count-2 {
			.un-file-uploades {
				--un-file-upload-width: 4;

				@media only screen and (max-width: 1200px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 767px) {
					--un-file-upload-width: 3;
				}

				@media only screen and (max-width: 575px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 475px) {
					--un-file-upload-width: 1;
				}
			}
		}

		&.td-column-count-medium {
			.un-file-uploades {
				--un-file-upload-width: 3;

				@media only screen and (max-width: 1366px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 991px) {
					--un-file-upload-width: 1;
				}

				@media only screen and (max-width: 767px) {
					--un-file-upload-width: 3;
				}

				@media only screen and (max-width: 575px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 475px) {
					--un-file-upload-width: 1;
				}
			}
		}

		&.td-column-count-5 {
			.un-file-uploades {
				--un-file-upload-width: 2;

				@media only screen and (max-width: 1300px) {
					--un-file-upload-width: 1;
				}

				@media only screen and (max-width: 767px) {
					--un-file-upload-width: 3;
				}

				@media only screen and (max-width: 575px) {
					--un-file-upload-width: 2;
				}

				@media only screen and (max-width: 475px) {
					--un-file-upload-width: 1;
				}
			}
		}

		&.td-column-count-high {
			.un-file-upload-item {
				max-width: 280px;
				@media only screen and (max-width: 767px) {
					max-width: 100%;
				}
			}
		}
	}
}

.upload-camera-btns {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	gap: 8px;

	@media only screen and (max-width: 475px) {
		flex-direction: column;
	}
}

/* -------------------
		Edit Grid
-------------------- */

.formio-component-editgrid {
	background-color: @white;
	padding: @spaceCommonPad;
	border: 1px solid @solidBorderColor;
	border-radius: @8px;

	@media only screen and (max-width: @largestMobileScreen) {
		padding: @spaceCommonPadResponsive;
	}
}

.editgrid-actions {
	display: flex;
	gap: 8px;
	justify-content: flex-end;
}

/* -------------------
		Form submission
-------------------- */
.field.formio-component-submit .error {
	.help-block {
		color: @red;
		font-size: @relativeMedium;
		font-weight: 400;
		line-height: 1.3;
		margin: 0;
	}
}

.ui.form > .field.formio-component-submit {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	align-items: center;
	column-gap: 8px;
	position: sticky;
	bottom: 0;
	background-color: @white;
	padding: 16px var(--form-wrapper-padding);
	border-top: 1px solid @borderColor;
	margin: 0 calc(var(--form-wrapper-padding) * -1) calc(var(--form-wrapper-padding) * -1)
		calc(var(--form-wrapper-padding) * -1) !important;
	z-index: 99;

	.un-submit-btn {
		align-self: flex-end;
		margin-right: 0;

		.icon.refresh {
			margin-left: 8px !important;
		}
	}

	.submit-fail::after {
		display: none;
	}

	.error {
		min-width: 0;
		flex: 1 1 0;
	}

	[ref='messageContainer'] {
		min-width: 100%;
		display: block;
	}
}

/* -------------------
		Tags/Choices
-------------------- */

.formio-component-tags {
	.choices {
		width: 100%;
	}

	&:has(.choices + .floating-label) .choices {
		margin-bottom: 0;
	}
}

.ui {
	.choices__inner {
		border-radius: @6px;
		border: 1px solid @fieldBorderColor;
		background: @white;
		padding: 12px 12px 6px 14px;
	}
	.is-focused .choices__inner,
	.is-open .choices__inner {
		border-color: @focusedFormBorderColor;
	}
}

.ui .ui.input .choices__inner .choices__input {
	padding: 2px 6px;
	min-width: 50px !important;
	line-height: 1.5;
	border: 0;
}

.choices .choices__list--multiple .choices__item {
	border-radius: @6px;
	border: 1px solid @checkboxBorderColor;
	background-color: @white;
	padding: 2px 8px;
	font-size: @relativeSmall;
	font-style: normal;
	font-weight: 500;
	line-height: 16px;
	color: @textColor;
}

.ui .choices[data-type*='select-multiple'] .choices__button,
.ui .choices[data-type*='text'] .choices__button {
	border: 0;
	font-size: 0;
	text-indent: 0;
	padding: 0;
	width: 15px;
	margin-left: 6px;
	margin-right: -2px;

	&::before {
		content: '\f00d';
		font-family: 'Icons';
		font-size: 13px;
		color: rgba(@lightBlack, 0.7);
		text-indent: 0px;
		line-height: 1;
	}
}

.ui .choices__inner:has(.form-control.error) {
	border-color: @formErrorBorder !important;
}

.choices__list--multiple .choices__item.is-highlighted {
	border: 1px solid @fieldBorderColor !important;
	background: @white !important;
	filter: contrast(0.9);
}

.choices .choices__item.choices__item--selectable {
	padding-right: 15px;
	line-height: normal;
}

@media (min-width: 640px) {
	.choices__list.choices__list--dropdown .choices__item--selectable,
	.choices__list[aria-expanded] .choices__item--selectable {
		padding-right: 10px;
	}
}

/* -------------------
	Field wrapper
-------------------- */

.field-wrapper {
	gap: 15px;

	.field-label {
		flex: 1 1 auto !important;
		max-width: 30%;
		margin: 0 !important;

		@media only screen and (max-width: @largestMobileScreen) {
			max-width: 125px;
		}
	}

	.filed-content {
		flex: 1 1 0 !important;
	}
}

/* -------------------
		Summary custom
-------------------- */

.un-order-header {
	display: flex;
	justify-content: space-between;

	.un-order-header-title {
		color: @textColor;
		font-size: unit((16 / 14), rem);
		font-weight: 600;
		line-height: 1.3;
		margin: 0;
	}
}

.un-order-lists {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	column-gap: 8px;
	row-gap: 24px;

	@media only screen and (max-width: 767px) {
		grid-template-columns: repeat(2, 1fr);
	}

	@media only screen and (max-width: 475px) {
		grid-template-columns: repeat(1, 1fr);
	}

	&:not(:empty) {
		margin-top: 16px;
	}

	.un-order-list-item-label {
		color: @primaryTextColor;
		font-size: unit((12 / 14), rem);
		font-weight: 400;
		line-height: 1.3;
		margin-bottom: 4px;
	}

	.un-order-list-item-value {
		color: @textColor;
		font-size: unit((14 / 14), rem);
		font-weight: 500;
		line-height: 1.3;
		margin: 0;
	}
}

/* -------------------
		Form submit error
-------------------- */
.formio-error-wrapper {
	background: transparent !important;
	color: @red !important;
	padding: 0 !important;
	border: 0;

	.ui.input input {
		color: @red !important;
		border-color: @formErrorBorder !important;
	}

	.ui.label.pointing {
		font-size: unit((14 / 14), rem) !important;
		font-weight: 400 !important;
		padding: 0 !important;
		border: 0 !important;
		background-color: transparent !important;
	}
}

/* -------------------
		Calendar
-------------------- */
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
	background-color: @primaryColor !important;
	color: @white !important;
}

/* -------------------
		popup
-------------------- */

.un-popup {
	position: fixed;
	inset: 0;
	max-height: 100vh;
	min-height: 100vh;
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease-in-out;

	&.un-popup-active {
		opacity: 1;
		visibility: visible;
	}

	.un-popup-overlay {
		position: absolute;
		inset: 0;
		background: rgba(0, 0, 0, 0.65);
	}

	.un-popup-content {
		position: absolute;
		border-radius: @12px;
		background-color: @white;
		min-width: @popupContentWidth;
		max-width: @popupContentWidth;
		padding: 32px;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.un-popup-close-btn {
		position: absolute;
		top: 15px;
		right: 15px;
	}

	.un-message-title {
		color: @textColor;
		font-size: unit((20 / 14), rem);
		font-weight: 600;
		line-height: 1.3;
		margin: 0;
	}

	.un-message-description {
		color: @primaryTextColor;
		font-size: unit((14 / 14), rem);
		font-weight: 400;
		line-height: 1.3;
		max-width: 300px;
		margin: 12px 0 32px 0;
	}

	.un-popup-message-icon {
		width: 64px;
		display: flex;
		align-items: center;
		justify-content: center;
		aspect-ratio: 1;
		background: rgba(@selectedBorderColor, 0.4);
		color: @primaryColor;
		box-shadow: inset 0px 0 0 5px @white;
		border-radius: 50%;
		border: 1.5px solid rgba(@selectedBorderColor, 0.4);
		margin: 8px 0 20px 0;

		.icon {
			font-size: unit((24 / 14), rem);
			margin: 0;
		}
	}

	.un-popup-btn-ok {
		margin: 0;
	}
}

/* -------------------
		Alert
-------------------- */

formio-alerts {
	position: fixed;
	top: 15px;
	right: 15px;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	max-width: 70%;
	z-index: 999;
}

.alert:not(.field) {
	position: relative;
	// position: fixed;
	// top: 15px;
	// right: 15px;
	display: inline-flex;
	align-items: center;
	background-color: @white;
	font-size: unit((13 / 14), rem);
	font-weight: 400;
	line-height: 1.3;
	min-width: 260px;
	min-height: @alertHeight;
	border: 1px solid transparent;
	border-radius: @8px;
	padding: 16px;
	margin: 0;

	&.uv-upload-alert {
		margin-top: 8px;
	}

	@media only screen and (max-width: @largestMobileScreen) {
		min-width: auto;
	}

	& + .alert {
		// margin-top: calc(@alertHeight + 12px);
		margin-top: 8px;
	}

	&::before,
	&::after {
		content: '';
		pointer-events: none;
	}

	&::before {
		display: flex;
		min-width: 20px;
		font-family: 'Icons';
		font-size: unit((16 / 14), rem);
		aspect-ratio: 1;
		text-align: center;
		margin-right: 8px;
	}

	&::after {
		position: absolute;
		inset: 0;
		border-radius: @8px;
		// z-index: -1;
	}

	&.alert-success {
		color: @green;
		border-color: rgba(@green, 0.5);

		&::before {
			content: '\f058';
		}

		&::after {
			background-color: rgba(@green, 20%);
		}
	}

	&.alert-warning {
		color: @yellow;
		border-color: rgba(@yellow, 0.5);

		&::before {
			content: '\f071';
		}

		&::after {
			background-color: rgba(@yellow, 20%);
		}
	}

	&.alert-danger {
		color: @red;
		border-color: rgba(@red, 0.5);

		&::before {
			content: '\f06a';
		}

		&::after {
			background-color: rgba(@red, 20%);
		}
	}
}

/* -------------------
		Table
-------------------- */

.formio-component.formio-component-table {
	@media only screen and (min-width: 768px) and (max-width: 1200px) {
		overflow-x: auto !important;
	}

	.ui.table {
		background-color: @white;
		border-radius: @8px;
		vertical-align: top;

		@media only screen and (min-width: 768px) and (max-width: 1200px) {
			.ui.input input.form-control,
			.ui.form select,
			.ui.selection.dropdown {
				min-width: 140px;
			}
			.ui.input textarea.form-control {
				min-width: 180px;
			}
		}
	}
}

/* -------------------
	Autocomplete responsive issue
-------------------- */

.formio-select-autocomplete-input {
	width: auto !important;
}

/* -------------------
	styles that are not available in the
	SemanticUI theme end
-------------------- */

/* -------------------
  Modals
-------------------- */
.formio-dialog.formio-dialog-theme-default .formio-dialog-content {
	font-family: inherit !important;
	.ui.input .form-control.error {
		border-color: @formErrorBorder !important;
	}
}

.formio-dialog.formio-dialog-theme-default .formio-dialog-content {
	padding: @spaceCommonPad !important;
	padding-top: calc(@spaceCommonPad + 25px) !important;
}

.formio-dialog.formio-dialog-theme-default .formio-dialog-close:before {
	color: @lightBlack !important;
}

.formio-dialog.formio-dialog-theme-default .formio-dialog-button {
	border-radius: 0.57142857rem !important;
	padding: 0.71428571em 1.14285714em 0.71428571em !important;
	font-size: 0.78571429rem !important;
	font-weight: 600 !important;
	line-height: 1.4 !important;
}

.formio-component-modal-wrapper-signature .open-modal-button.button {
	font-size: 1em;
	padding: 0.71428571em 1.14285714em 0.71428571em;
}

.formio-component-htmlelement ol,
.formio-component-htmlelement ul {
	padding-left: 20px;
	margin: 0;
}

.formio-dialog-content {
	[ref='dialogHeader'] {
		color: @primaryTextColor;
		font-size: 16px;
		font-weight: 700;
		line-height: 1.3;
		margin: 0px 0 25px 0;
	}

	[ref='dialogHeader'] + div .btn[ref='dialogCancelButton'] {
		color: @primaryTextColor;
		box-shadow: 0 0 0 1px @primaryColor inset;
	}

	[ref='dialogHeader'] + div .btn[ref='dialogYesButton'] {
		background-color: @primaryColor;
		color: #fff;
		text-shadow: none;
		background-image: none;
		border: 0;
	}

	[ref='dialogHeader'] + div {
		display: flex;
		gap: 8px;
	}

	[ref='dialogHeader'] + div .btn {
		font-size: 1rem;
		color: @primaryTextColor;
		font-weight: 600;
		border-radius: 0.57142857rem;
		text-transform: none;
		text-shadow: none !important;
		border: 1px solid transparent;
		padding: 0.71428571em 1.14285714em;
		cursor: pointer;
		transition: opacity 0.1s ease, background-color 0.1s ease, color 0.1s ease, box-shadow 0.1s ease,
			background 0.1s ease;
	}

	[ref='dialogHeader'] + div .btn[ref='dialogCancelButton']:hover {
		color: @primaryColor;
	}

	[ref='dialogHeader'] + div .btn[ref='dialogYesButton']:hover {
		background-color: darken(@primaryColor, 10%);
		color: #fff;
	}
}

// custom label
.un-column-custom-label-day .floating-label::before {
	content: 'Day';
}
.un-column-custom-label-year .floating-label::before {
	content: 'Year';
}
