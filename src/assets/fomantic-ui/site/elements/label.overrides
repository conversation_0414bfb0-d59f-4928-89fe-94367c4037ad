/*******************************
         Site Overrides
*******************************/

@color: @grey;

.ui.form .field .ui.input .ui.label {
	background-color: @white;
	// padding: @verticalPadding @horizontalPadding;
	border: 1px solid @fieldBorderColor;
	transition: 0.1s ease, border-color 0.1s ease;

	&[ref='prefix'] {
		border-right: 0 !important;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}

	&[ref='suffix'] {
		border-left: 0 !important;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
}

.ui[class*='left labeled'].input > .ui.label {
	border-right: 0 !important;
}
.ui[class*='right labeled'].input > .ui.label {
	border-left: 0 !important;
}

.ui.form .field .ui.input:has(input:focus) > .ui.label {
	border-color: @focusedFormBorderColor;
}

.ui.form .field .ui.input:has(input.error) > .ui.label {
	border-color: @formErrorBorder;
}

// .ui.form:not(.initial) .field:has(input:invalid) .ui.label,
// .ui.form:not(.initial) .field:has(input:invalid:focus) .ui.label {
// 	border-color: @formErrorBorder;
// }

/* Icon */
.ui.ui[class*='left icon'].label > .icon,
.ui.label > .icon {
	&:last-child {
		margin-right: 0;
	}
}
