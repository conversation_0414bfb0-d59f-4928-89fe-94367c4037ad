/*******************************
         Site Overrides
*******************************/

.un-wizard-header {
	background-color: @stepsBackground;
}

/*******************************
            Plural
*******************************/

.ui.steps {
	counter-reset: un-step-counter;
	width: 100%;
	// align-items: center;
	// justify-content: center;
	padding: @stepContentHorizontalPadding;
	gap: @stepGap;
	overflow-x: auto;
}

.ui.steps:not(.unstackable) {
	flex-wrap: nowrap;
}

.ui.steps .step {
	counter-increment: un-step-counter;
	position: relative;
	flex: 0 0 auto;
	width: auto;

	&:first-child {
		margin-left: auto;
	}

	&:last-child {
		margin-right: auto;
	}

	& + .step::before {
		content: '';
		display: block;
		width: 24px;
		height: 1px;
		background-color: @stepsBorderColor;
		opacity: 0.2;
		margin-right: @stepGap;
		pointer-events: none;
	}

	.title {
		display: flex;
		align-items: center;
		gap: 8px;
		transition: all 0.4s;
	}

	.step-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		min-width: @32px;
		aspect-ratio: 1;
		display: flex;
		border-radius: 50%;
		border: 1px solid rgba(@stepsBorderColor, 0.2);

		&::before {
			content: '' counter(un-step-counter) '';
			font-size: @relativeMedium;
			font-weight: 500;
		}

		.icon {
			margin: 0;
		}
	}

	&.completed {
		.step-icon {
			background-color: transparent;
			color: @positiveColor;
			border-color: @positiveColor;

			&::before {
				display: none;
			}
		}
	}

	&.active {
		.step-icon {
			background-color: @stepActiveColor;
			color: @stepsBackground;
			border-color: @stepActiveColor;
		}
	}
}

.un-wizard-wrapper {
	display: flex;
	flex-direction: column;
	min-height: 100dvh;
}

.un-wizard-body {
	min-width: 0;
	flex: 1 1 auto;
	padding: var(--form-wrapper-padding);
}

.ui.un-wizard-footer {
	display: flex;
	gap: @relative8px;
	position: sticky;
	bottom: 0;
	width: 100%;
	background-color: @white;
	padding: @relative16px var(--form-wrapper-padding) !important;
	border-top: 1px solid @borderColor;
	z-index: 99;

	.un-wizard-footer-step-cancel {
		margin-right: auto;
	}

	button {
		margin-right: 0;

		.icon {
			margin-left: 6px;
		}
	}
}

.un-wizard-header-content {
	display: @stepHeaderContentDisplay;
	flex-direction: column;
	align-items: center;
	padding: @stepContentVerticalPaddingTop @stepContentHorizontalPadding @stepContentVerticalPaddingBottom
		@stepContentHorizontalPadding;

	@media only screen and (max-width: @largestMobileScreen) {
		padding-top: @stepContentVerticalPaddingTopResponsive;
	}

	.un-wizard-header-title {
		color: @wizardHeaderTitleColor;
		font-weight: 700;
		line-height: 1.3;
		margin: 0;

		@media only screen and (max-width: @largestMobileScreen) {
			font-size: @relativeMassive;
		}
	}

	.un-wizard-header-logo {
		max-width: unit((120 / 14), rem);
		// max-height: unit((90 / 14), rem);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: unit((32 / 14), rem);

		@media only screen and (max-width: @largestMobileScreen) {
			margin-bottom: unit((28 / 14), rem);
		}

		img {
			width: 100%;
			max-height: 100%;
			max-width: 100%;
			object-fit: cover;
			object-position: center;
		}
	}

	.un-wizard-header-description {
		font-size: @relativeMedium;
		color: @wizardHeaderDescriptionColor;
		font-weight: 400;
		line-height: 1.3;
		margin-top: @12px;
	}
}

/* Mobile (Default) */
@media only screen and (max-width: (@largestMobileScreen)) {
	.ui.steps {
		gap: 15px;
		padding-bottom: unit((15px / 14), rem);
	}

	.ui.steps .step .title {
		font-size: 0;
		gap: 0;
	}

	.ui.steps .step .step-icon {
		font-size: 14px;
	}

	.ui.steps .step + .step::before {
		width: 32px;
	}
}
