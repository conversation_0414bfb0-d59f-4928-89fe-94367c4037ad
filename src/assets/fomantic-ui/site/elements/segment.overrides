/*******************************
         Site Overrides
*******************************/

@borderRadius: @8px;

& when (@variationSegmentAttached) {
	.ui.tab.segment[class*='bottom attached']:last-child {
		margin-bottom: 0;
	}
}

.ui.segment {
	background: @white;
	padding: @spaceCommonPad;

	@media only screen and (max-width: @largestMobileScreen) {
		padding: @spaceCommonPadResponsive;
	}

	&.attached.un-panel-body {
		border: 0 !important;
	}

	&[class*='attached'] {
		padding: @spaceCommonPad 0 0 0;

		@media only screen and (max-width: @largestMobileScreen) {
			padding: @spaceCommonPadResponsive 0 0 0;
		}
	}

	// .formio-component-datagrid {
	// 	background: transparent;
	// 	padding: 0;
	// 	border: 0;
	// }

	&.un-segment-disabled {
		background: rgba(@lightGrey, 0.3);
		margin-top: 0;
		pointer-events: none;
	}
}
