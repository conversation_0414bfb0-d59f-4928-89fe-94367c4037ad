/*!
 * # Fomantic-UI - Dimmer
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "module";
@element: "dimmer";

@import (multiple) "../../theme.config";

/*******************************
            Dimmer
*******************************/

.dimmable:not(body) {
    position: @dimmablePosition;
}

.ui.dimmer {
    display: none;
    position: @dimmerPosition;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: @textAlign;
    vertical-align: @verticalAlign;
    padding: @padding;
    background: @backgroundColor;
    opacity: @hiddenOpacity;
    line-height: @lineHeight;
    animation-fill-mode: both;
    animation-duration: @duration;
    transition: @transition;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    user-select: none;
    will-change: opacity;
    z-index: @zIndex;
}

/* Dimmer Content */
.ui.dimmer > .content {
    user-select: text;
    color: @textColor;
}

/* Loose Coupling */
.ui.segment > .ui.dimmer:not(.page) {
    border-radius: inherit;
}

/* Scrollbars */
& when (@useCustomScrollbars) {
    .ui.dimmer:not(.inverted)::-webkit-scrollbar-track {
        background: @trackInvertedBackground;
    }
    .ui.dimmer:not(.inverted)::-webkit-scrollbar-thumb {
        background: @thumbInvertedBackground;
    }
    .ui.dimmer:not(.inverted)::-webkit-scrollbar-thumb:window-inactive {
        background: @thumbInvertedInactiveBackground;
    }
    .ui.dimmer:not(.inverted)::-webkit-scrollbar-thumb:hover {
        background: @thumbInvertedHoverBackground;
    }
    & when (@supportIE) {
        .ui.dimmer:not(.inverted) {
            /* IE11 */
            scrollbar-face-color: @thumbInvertedBackgroundHex;
            scrollbar-shadow-color: @thumbInvertedBackgroundHex;
            scrollbar-track-color: @trackInvertedBackgroundHex;
            scrollbar-arrow-color: @trackInvertedBackgroundHex;
        }
    }
    @supports (-moz-appearance: none) {
        .ui.dimmer:not(.inverted) {
            /* firefox: first color thumb, second track */
            scrollbar-color: @thumbInvertedBackground @trackInvertedBackground;
        }
    }
}

/*******************************
            States
*******************************/

/* Animating */
.animating.dimmable:not(body),
.dimmed.dimmable:not(body) {
    overflow: @overflow;
}

/* Animating / Active / Visible */
.dimmed.dimmable > .ui.animating.dimmer,
.dimmed.dimmable > .ui.visible.dimmer,
.ui.active.dimmer {
    display: flex;
    opacity: @visibleOpacity;
}

& when (@variationDimmerDisabled) {
    /* Disabled */
    .ui.disabled.dimmer {
        width: 0 !important;
        height: 0 !important;
    }
}

/*******************************
           Variations
*******************************/

& when (@variationDimmerLegacy) {
    /* --------------
        Legacy
    --------------- */

    /* Animating / Active / Visible */
    .dimmed.dimmable > .ui.animating.legacy.dimmer,
    .dimmed.dimmable > .ui.visible.legacy.dimmer,
    .ui.active.legacy.dimmer {
        display: block;
    }
}

& when (@variationDimmerAligned) {
    /* --------------
        Alignment
    --------------- */

    .ui[class*="top aligned"].dimmer {
        justify-content: flex-start;
    }
    .ui[class*="bottom aligned"].dimmer {
        justify-content: flex-end;
    }
}

& when (@variationDimmerPage) {
    /* --------------
          Page
    --------------- */

    .ui.page.dimmer {
        position: @pageDimmerPosition;
        transform-style: @transformStyle;
        perspective: @perspective;
        transform-origin: center center;
        &.modals {
            // https://github.com/fomantic/Fomantic-UI/pull/1491
            // stylelint-disable-next-line property-no-vendor-prefix
            -moz-perspective: none;
        }
    }
}

body.animating.in.dimmable,
body.dimmed.dimmable {
    overflow: hidden;
}

body.dimmable > .dimmer {
    position: fixed;
}

& when (@variationDimmerBlurring) {
    /* --------------
        Blurring
    --------------- */

    @supports (not (-webkit-backdrop-filter: none)) and (not (backdrop-filter: none)) {
        .blurring.dimmable > :not(.dimmer) {
            filter: @blurredStartFilter;
            transition: @blurredTransition;
        }
        .blurring.dimmed.dimmable > :not(.dimmer):not(.popup) {
            filter: @blurredEndFilter;
        }
    }

    /* Dimmer Color */
    .blurring.dimmable > .dimmer {
        background: @blurredBackgroundColor;
        -webkit-backdrop-filter: @blurredEndFilter;
        backdrop-filter: @blurredEndFilter;
    }
    .blurring.dimmable > .inverted.dimmer {
        background: @blurredInvertedBackgroundColor;
    }
}
& when (@variationDimmerAligned) {
    /* --------------
        Aligned
    --------------- */

    .ui.dimmer > .top.aligned.content > * {
        vertical-align: top;
    }
    .ui.dimmer > .bottom.aligned.content > * {
        vertical-align: bottom;
    }
}

& when (@variationDimmerShades) {
    /* --------------
        Shades
    --------------- */

    .medium.medium.medium.medium.medium.dimmer {
        background: @mediumBackgroundColor;
    }
    .light.light.light.light.light.dimmer {
        background: @lightBackgroundColor;
    }
    .very.very.very.very.light.dimmer {
        background: @veryLightBackgroundColor;
    }
}

& when (@variationDimmerInverted) {
    /* --------------
        Inverted
    --------------- */

    .ui.inverted.dimmer {
        background: @invertedBackgroundColor;
    }
    .ui.inverted.dimmer > .content,
    .ui.inverted.dimmer > .content > * {
        color: @invertedTextColor;
    }

    & when (@variationDimmerShades) {
        /* --------------
         Inverted Shades
        --------------- */

        .medium.medium.medium.medium.medium.inverted.dimmer {
            background: @mediumInvertedBackgroundColor;
        }
        .light.light.light.light.light.inverted.dimmer {
            background: @lightInvertedBackgroundColor;
        }
        .very.very.very.very.light.inverted.dimmer {
            background: @veryLightInvertedBackgroundColor;
        }
    }
}

& when (@variationDimmerSimple) {
    /* --------------
         Simple
    --------------- */

    /* Displays without javascript */
    .ui.simple.dimmer {
        display: block;
        overflow: hidden;
        opacity: 0;
        width: 0;
        height: 0;
        z-index: -100;
        background: @simpleStartBackgroundColor;
    }
    .dimmed.dimmable > .ui.simple.dimmer {
        overflow: visible;
        opacity: 1;
        width: 100%;
        height: 100%;
        background: @simpleEndBackgroundColor;
        z-index: @simpleZIndex;
    }

    .ui.simple.inverted.dimmer {
        background: @simpleInvertedStartBackgroundColor;
    }
    .dimmed.dimmable > .ui.simple.inverted.dimmer {
        background: @simpleInvertedEndBackgroundColor;
    }
}

& when (@variationDimmerPartially) {
    /* --------------
         Partially
    ---------------- */

    .ui[class*="top dimmer"],
    .ui[class*="center dimmer"],
    .ui[class*="bottom dimmer"] {
        height: auto;
    }
    .ui[class*="bottom dimmer"] {
        top: auto;
        bottom: 0;
    }
    .ui[class*="center dimmer"] {
        top: 50%;
        transform: translateY(-50%);
    }

    .ui.segment > .ui.ui[class*="top dimmer"] {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }
    .ui.segment > .ui.ui[class*="center dimmer"] {
        border-radius: 0;
    }
    .ui.segment > .ui.ui[class*="bottom dimmer"] {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }

    .ui[class*="center dimmer"].transition[class*="fade up"].in {
        animation-name: fadeInUpCenter;
    }
    .ui[class*="center dimmer"].transition[class*="fade down"].in {
        animation-name: fadeInDownCenter;
    }
    .ui[class*="center dimmer"].transition[class*="fade up"].out {
        animation-name: fadeOutUpCenter;
    }
    .ui[class*="center dimmer"].transition[class*="fade down"].out {
        animation-name: fadeOutDownCenter;
    }
    .ui[class*="center dimmer"].bounce.transition {
        animation-name: bounceCenter;
    }
    @keyframes fadeInUpCenter {
        0% {
            opacity: 0;
            transform: translateY(-40%);
        }
        100% {
            opacity: 1;
            transform: translateY(-50%);
        }
    }
    @keyframes fadeInDownCenter {
        0% {
            opacity: 0;
            transform: translateY(-60%);
        }
        100% {
            opacity: 1;
            transform: translateY(-50%);
        }
    }
    @keyframes fadeOutUpCenter {
        0% {
            opacity: 1;
            transform: translateY(-50%);
        }
        100% {
            opacity: 0;
            transform: translateY(-45%);
        }
    }
    @keyframes fadeOutDownCenter {
        0% {
            opacity: 1;
            transform: translateY(-50%);
        }
        100% {
            opacity: 0;
            transform: translateY(-55%);
        }
    }
    @keyframes bounceCenter {
        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(-50%);
        }
        40% {
            transform: translateY(calc(-50% - 30px));
        }
        60% {
            transform: translateY(calc(-50% - 15px));
        }
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
