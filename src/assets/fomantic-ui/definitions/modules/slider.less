/*!
 * # Fomantic-UI - Slider
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
             Theme
 *******************************/

@type: "module";
@element: "slider";

@import (multiple) "../../theme.config";

.ui.slider:not(.vertical):not(.checkbox) {
    width: 100%;
    padding: @padding;
}

.ui.slider:not(.checkbox) {
    position: relative;
}

.ui.slider:not(.checkbox):focus {
    outline: 0;
}

.ui.slider .inner {
    position: relative;
    z-index: 2;
}

.ui.slider:not(.vertical) .inner {
    height: @height;
}

.ui.slider .inner:hover {
    cursor: @hoverPointer;
}

.ui.slider .inner .track {
    position: absolute;
    border-radius: @trackBorderRadius;
    background-color: @trackColor;
}

.ui.slider:not(.vertical) .inner .track {
    width: 100%;
    height: @trackHeight;
    top: @trackPositionTop;
    left: 0;
}

.ui.slider .inner .track-fill {
    position: absolute;
    border-radius: @trackFillBorderRadius;
    background-color: @trackFillColor;
}

.ui.slider:not(.vertical) .inner .track-fill {
    height: @trackFillHeight;
    top: @trackPositionTop;
    left: 0;
}

.ui.slider .inner .thumb {
    position: absolute;
    left: 0;
    top: 0;
    height: @thumbHeight;
    width: @thumbHeight;
    background: @thumbBackground;
    border-radius: @thumbBorderRadius;
    box-shadow: @thumbShadow;
    transition: @thumbTransition;
}

.ui.slider:not(.disabled) .inner .thumb:hover {
    cursor: @thumbHoverPointer;
    background: @thumbHoverBackground;
}

.ui.slider:not(.disabled):focus .inner .thumb {
    background: @thumbHoverBackground;
}

/*******************************
            States
*******************************/

& when (@variationSliderDisabled) {
    /* --------------
         Disabled
    --------------- */

    .ui.disabled.slider:not(.checkbox) {
        opacity: @disabledOpactiy;
    }

    .ui.disabled.slider .inner:hover {
        cursor: auto;
    }

    .ui.disabled.slider .inner .track-fill {
        background: @disabledTrackFillColor;
    }
}

& when (@variationSliderReversed) {
    /* --------------
        Reversed
    --------------- */

    .ui.reversed.slider .inner .track-fill {
        left: auto;
        right: 0;
    }

    .ui.reversed.slider:not(.vertical) .inner .thumb {
        left: auto;
        right: 0;
    }

    .ui.reversed.vertical.slider .inner .thumb {
        left: @thumbVerticalSliderOffset;
    }

    & when (@variationSliderLabeled) {
        .ui.labeled.reversed.slider > .labels .label {
            transform: translate(-100%, -100%);
        }
    }
}

/*******************************
           Variations
*******************************/

& when (@variationSliderVertical) {
    /* --------------
        Vertical
    --------------- */

    .ui.vertical.slider {
        height: 100%;
        width: @height;
        padding: @verticalPadding;
    }

    .ui.vertical.slider .inner {
        height: 100%;
    }

    .ui.vertical.slider .inner .track {
        height: 100%;
        width: @trackHeight;
        left: @trackPositionTop;
        top: 0;
    }

    .ui.vertical.slider .inner .track-fill {
        width: @trackFillHeight;
        left: @trackPositionTop;
        top: 0;
    }
    & when (@variationSliderReversed) {
        /* Vertical Reversed */
        .ui.vertical.reversed.slider .inner .thumb {
            top: auto;
            bottom: 0;
        }

        .ui.vertical.reversed.slider .inner .track-fill {
            top: auto;
            bottom: 0;
        }
    }
}

& when (@variationSliderLabeled) {
    /* --------------
        Labeled
    --------------- */

    .ui.labeled.slider > .labels {
        height: @labelHeight;
        width: auto;
        margin: 0;
        padding: 0;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
    }

    .ui.labeled.slider:not(.vertical) > .labels {
        transform: translateY(-50%);
    }

    .ui.labeled.slider > .labels .label {
        display: inline-flex;
        padding: @labelPadding;
        position: absolute;
        transform: translate(-50%, -100%);
        white-space: nowrap;
    }

    .ui.bottom.aligned.labeled.slider > .labels .label {
        bottom: 0;
        transform: translate(-50%, 100%);
    }
    & when (@variationSliderTicked) {
        .ui.labeled.ticked.slider > .labels .label::after {
            content: " ";
            height: @labelHeight;
            width: @labelWidth;
            background: @labelColor;
            position: absolute;
            top: 100%;
            left: 50%;
        }
        .ui.bottom.aligned.labeled.ticked.slider > .labels .label::after {
            top: auto;
            bottom: 100%;
        }
        .ui.labeled.ticked.slider > .labels .halftick.label::after {
            height: (@labelHeight / 2);
        }
    }

    & when (@variationSliderVertical) {
        /* Vertical Labels */

        .ui.labeled.vertical.slider > .labels {
            width: @labelHeight;
            height: auto;
            left: 50%;
            top: 0;
            bottom: 0;
            transform: translateX(-50%);
        }

        .ui.labeled.vertical.slider > .labels .label {
            transform: translate(-100%, -50%);
        }

        .ui.labeled.vertical.slider > .labels .label::after {
            width: @labelHeight;
            height: @labelWidth;
            left: 100%;
            top: 50%;
        }
        .ui.labeled.vertical.slider > .labels .halftick.label::after {
            width: (@labelHeight / 2);
            height: @labelWidth;
        }

        & when (@variationSliderReversed) {
            /* Vertical Reversed Labels */
            .ui.labeled.vertical.reversed.slider > .labels .label {
                transform: translate(-100%, 50%);
            }
        }
    }
}

/* --------------
    Hover
--------------- */

.ui.hover.slider .inner .thumb {
    opacity: @hoverVarOpacity;
    transition: @hoverOpacityTransition;
}

.ui.hover.slider:not(.disabled):hover .inner .thumb,
.ui.hover.slider:not(.disabled):focus .inner .thumb {
    opacity: @hoverVarHoverOpacity;
}

& when (@variationSliderInverted) {
    /* --------------
        Inverted
    --------------- */

    .ui.inverted.slider .inner .track-fill {
        background-color: @invertedTrackFillColor;
    }

    .ui.inverted.slider .inner .track {
        background-color: @transparentWhite;
    }
}

/* --------------
     Colors
--------------- */
& when not (@variationSliderColors = false) {
    each(@variationSliderColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];
        @h: @colors[@@color][hover];
        @lh: @colors[@@color][lightHover];

        /* Standard */
        .ui.@{color}.slider .inner .track-fill {
            background-color: @c;
        }
        & when (@variationSliderInverted) {
            .ui.@{color}.inverted.slider .inner .track-fill {
                background-color: @l;
            }
        }

        & when (@variationSliderBasic) {
            /* Basic */
            .ui.@{color}.slider.basic .inner .thumb {
                background-color: @c;
            }
            .ui.@{color}.slider.basic .inner .thumb:hover,
            .ui.@{color}.slider.basic:focus .inner .thumb {
                background-color: @h;
            }
            & when (@variationSliderInverted) {
                /* Basic Inverted */
                .ui.@{color}.inverted.slider.basic .inner .thumb {
                    background-color: @l;
                }
                .ui.@{color}.inverted.slider.basic .inner .thumb:hover,
                .ui.@{color}.inverted.slider.basic:focus .inner .thumb {
                    background-color: @lh;
                }
            }
        }

    });
}

& when (@variationSliderBasic) {
    /* --------------
         Basic
    --------------- */

    /* Standard */
    .ui.slider.basic .inner .thumb {
        background-color: @trackFillColor;
    }
    .ui.slider.basic .inner .thumb:hover,
    .ui.slider.basic:focus .inner .thumb {
        background-color: @trackFillColorFocus;
    }

    & when (@variationSliderInverted) {
        /* --------------
          Basic Inverted
        --------------- */

        /* Standard */
        .ui.inverted.slider.basic .inner .thumb {
            background-color: @invertedTrackFillColor;
        }
        .ui.inverted.slider.basic .inner .thumb:hover,
        .ui.inverted.slider.basic:focus .inner .thumb {
            background-color: @invertedTrackFillColorFocus;
        }
    }
}

/* --------------
     Sizing
--------------- */

& when not (@variationSliderSizes = false) {
    each(@variationSliderSizes, {
        @h: @{value}Height;
        @th: @{value}TrackHeight;
        @tp: @{value}TrackPositionTop;
        @lh: @{value}LabelHeight;
        .ui.slider.@{value} .inner .thumb {
            height: @@h;
            width: @@h;
        }
        .ui.slider.@{value}:not(.vertical) .inner {
            height: @@h;
        }
        .ui.slider.@{value}:not(.vertical) .inner .track,
        .ui.slider.@{value}:not(.vertical) .inner .track-fill {
            height: @@th;
            top: @@tp;
        }
        & when (@variationSliderLabeled) {
            .ui.@{value}.labeled.slider:not(.vertical) > .labels,
            .ui.@{value}.labeled.slider:not(.vertical) > .labels .label::after {
                height: @@lh;
            }
            .ui.@{value}.labeled.slider:not(.vertical) > .labels .halftick.label::after {
                height: (@@lh / 2);
            }
        }
        & when (@variationSliderVertical) {
            /* Small Vertical */
            .ui.slider.@{value}.vertical .inner {
                width: @@h;
            }
            .ui.slider.@{value}.vertical .inner .track,
            .ui.slider.@{value}.vertical .inner .track-fill {
                width: @@th;
                left: @@tp;
            }
            & when (@variationSliderLabeled) {
                .ui.@{value}.labeled.vertical.slider> .labels,
                .ui.@{value}.labeled.vertical.slider> .labels .label::after {
                    width: @@lh;
                }
                .ui.@{value}.labeled.vertical.slider> .labels .halftick.label::after {
                    width: (@@lh / 2);
                }
            }
        }
    });
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
