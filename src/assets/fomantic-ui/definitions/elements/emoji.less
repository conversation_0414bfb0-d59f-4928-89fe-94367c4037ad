/*!
 * # Fomantic-UI - Emoji
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "emoji";

@import (multiple) "../../theme.config";

/*******************************
             Emoji
*******************************/

em[data-emoji] {
    opacity: @opacity;
    speak: none;
    backface-visibility: hidden;
}

em[data-emoji]::before {
    content: "\00A0\00A0\00A0\00A0\00A0\00A0\00A0";
    display: inline-block;
    line-height: @emojiLineHeight;
    background-repeat: no-repeat;
    background-position: center center;
    & when not (@emojiFileType = "svg") {
        background-size: contain;
    }
}

/*******************************
             States
*******************************/

& when (@variationEmojiDisabled) {
    em[data-emoji].disabled {
        opacity: @disabledOpacity;
    }
}

/*******************************
           Variations
*******************************/

& when (@variationEmojiLoading) {
    em[data-emoji].loading::before {
        animation: loader @loadingDuration linear infinite;
    }
}

& when (@variationEmojiLink) {
    /* -------------------
             Link
    -------------------- */

    em[data-emoji].link:not(.disabled) {
        cursor: pointer;
    }
}

/* rtl:begin:ignore */

each(@size-map, {
    em[data-emoji].@{key} {
        font-size: 1.5em * @value;
        vertical-align: middle;
    }
});

each(@emoji-map, {
    & when (@variationEmojiColons) {
        em[data-emoji=":@{value}:"]::before {
            background-image: url("@{emojiPath}@{key}.@{emojiFileType}");
        }
        em[data-emoji="@{value}"]::before:extend(em[data-emoji=":@{value}:"]::before) when (@variationEmojiNoColons) {}
    }
    em[data-emoji="@{value}"]::before when (@variationEmojiNoColons) and not (@variationEmojiColons) {
        background-image: url("@{emojiPath}@{key}.@{emojiFileType}");
    }
});

/* rtl:end:ignore */

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
