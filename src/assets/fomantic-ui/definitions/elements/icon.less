/*!
 * # Fomantic-UI - Icon
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "icon";

@import (multiple) "../../theme.config";

/*******************************
             Icon
*******************************/
& when (@importIcons) {
    each(@fonts, {
        @font-face {
            each(@value, {
                @{key}: @value;
            });
        }
    });
}

i.icon {
    display: inline-block;
    opacity: @opacity;
    margin: 0 @distanceFromText 0 0;
    width: @width;
    height: @height;
    font-family: @fontName;
    font-style: normal;
    font-weight: @normal;
    text-decoration: inherit;
    text-align: center;
    speak: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
}

i.icon::before {
    background: none;
}

/*******************************
             Types
*******************************/

& when (@variationIconLoading) {
    /* --------------
        Loading
    --------------- */

    i.loading.icon {
        height: 1em;
        line-height: 1;
    }
    i.loading.icon,
    i.loading.icons {
        animation: loader @loadingDuration linear infinite;
    }
}

/*******************************
             States
*******************************/

i.icon:hover,
i.icons:hover,
i.icon:active,
i.icons:active,
i.emphasized.icon:not(.disabled),
i.emphasized.icons:not(.disabled) {
    opacity: 1;
}

& when (@variationIconDisabled) {
    i.disabled.icon,
    i.disabled.icons {
        opacity: @disabledOpacity;
        cursor: default;
        pointer-events: none;
    }
}

/*******************************
           Variations
*******************************/

& when (@variationIconFitted) {
    /* -------------------
            Fitted
    -------------------- */

    i.fitted.icons,
    i.fitted.icon {
        width: auto;
        margin: 0 !important;
    }
}

& when (@variationIconLink) {
    /* -------------------
             Link
    -------------------- */

    i.link.icon:not(.disabled),
    i.link.icons:not(.disabled) {
        cursor: pointer;
        opacity: @linkOpacity;
        transition: opacity @defaultDuration @defaultEasing;
    }
    i.link.icon:hover,
    i.link.icons:hover {
        opacity: 1;
    }
}

& when (@variationIconCircular) {
    /* -------------------
          Circular
    -------------------- */

    i.circular.icon {
        border-radius: 500em !important;
        line-height: 1 !important;
        padding: @circularPadding !important;
        box-shadow: @circularShadow;
        width: @circularSize !important;
        height: @circularSize !important;
        &.colored when (@variationIconColored) {
            box-shadow: @coloredBoxShadow;
        }
    }
    & when (@variationIconInverted) {
        i.circular.inverted.icon {
            border: none;
            box-shadow: none;
        }
    }
}

& when (@variationIconFlipped) {
    /* -------------------
          Flipped
    -------------------- */

    i.flipped.icon,
    i.horizontally.flipped.icon {
        transform: scale(-1, 1);
    }
    i.vertically.flipped.icon {
        transform: scale(1, -1);
    }

    & when (@variationIconGroups) {
        .icons i.flipped.icon:not(.corner):not(:first-child),
        .icons i.horizontally.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(-1, 1);
        }
        .icons i.vertically.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(1, -1);
        }
    }
}

& when (@variationIconRotated) {
    /* -------------------
          Rotated
    -------------------- */

    i.rotated.icon,
    i.right.rotated.icon,
    i.clockwise.rotated.icon {
        transform: rotate(90deg);
    }

    i.left.rotated.icon,
    i.counterclockwise.rotated.icon {
        transform: rotate(-90deg);
    }

    i.halfway.rotated.icon {
        transform: rotate(180deg);
    }

    & when (@variationIconGroups) {
        .icons i.rotated.rotated.icon:not(.corner):not(:first-child),
        .icons i.right.rotated.icon:not(.corner):not(:first-child),
        .icons i.clockwise.rotated.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) rotate(90deg);
        }
        .icons i.left.rotated.icon:not(.corner):not(:first-child),
        .icons i.counterclockwise.rotated.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) rotate(-90deg);
        }
        .icons i.halfway.rotated.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) rotate(180deg);
        }
    }
}

& when (@variationIconFlipped) and (@variationIconRotated) {
    /* --------------------------
          Flipped & Rotated
    --------------------------- */

    i.rotated.flipped.icon,
    i.right.rotated.flipped.icon,
    i.clockwise.rotated.flipped.icon {
        transform: scale(-1, 1) rotate(90deg);
    }

    i.left.rotated.flipped.icon,
    i.counterclockwise.rotated.flipped.icon {
        transform: scale(-1, 1) rotate(-90deg);
    }

    i.halfway.rotated.flipped.icon {
        transform: scale(-1, 1) rotate(180deg);
    }

    i.rotated.vertically.flipped.icon,
    i.right.rotated.vertically.flipped.icon,
    i.clockwise.rotated.vertically.flipped.icon {
        transform: scale(1, -1) rotate(90deg);
    }

    i.left.rotated.vertically.flipped.icon,
    i.counterclockwise.rotated.vertically.flipped.icon {
        transform: scale(1, -1) rotate(-90deg);
    }

    i.halfway.rotated.vertically.flipped.icon {
        transform: scale(1, -1) rotate(180deg);
    }

    & when (@variationIconGroups) {
        .icons i.rotated.flipped.icon:not(.corner):not(:first-child),
        .icons i.right.rotated.flipped.icon:not(.corner):not(:first-child),
        .icons i.clockwise.rotated.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(-1, 1) rotate(90deg);
        }
        .icons i.left.rotated.flipped.icon:not(.corner):not(:first-child),
        .icons i.counterclockwise.rotated.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(-1, 1) rotate(-90deg);
        }
        .icons i.halfway.rotated.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(-1, 1) rotate(180deg);
        }
        .icons i.rotated.vertically.flipped.icon:not(.corner):not(:first-child),
        .icons i.right.rotated.vertically.flipped.icon:not(.corner):not(:first-child),
        .icons i.clockwise.rotated.vertically.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(1, -1) rotate(90deg);
        }
        .icons i.left.rotated.vertically.flipped.icon:not(.corner):not(:first-child),
        .icons i.counterclockwise.rotated.vertically.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(1, -1) rotate(-90deg);
        }
        .icons i.halfway.rotated.vertically.flipped.icon:not(.corner):not(:first-child) {
            transform: translateX(-50%) translateY(-50%) scale(1, -1) rotate(180deg);
        }
    }
}

& when (@variationIconBordered) {
    /* -------------------
          Bordered
    -------------------- */

    i.bordered.icon {
        line-height: 1;
        vertical-align: baseline;
        width: @borderedSize;
        height: @borderedSize;
        padding: @borderedVerticalPadding @borderedHorizontalPadding !important;
        box-shadow: @borderedShadow;
        &.colored when (@variationIconColored) {
            box-shadow: @coloredBoxShadow;
        }
    }
    & when (@variationIconInverted) {
        i.bordered.inverted.icon {
            border: none;
            box-shadow: none;
        }
    }
}

& when (@variationIconInverted) {
    /* -------------------
          Inverted
    -------------------- */

    /* Inverted Shapes */
    i.inverted.bordered.icon,
    i.inverted.circular.icon {
        background-color: @black;
        color: @white;
    }

    i.inverted.icon {
        color: @white;
    }
}

/* -------------------
       Colors
-------------------- */
& when not (@variationIconColors = false) {
    each(@variationIconColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];

        i.@{color}.icon.icon.icon.icon.icon.icon {
            color: @c;
        }
        & when (@variationIconInverted) {
            i.inverted.@{color}.icon.icon.icon.icon.icon.icon {
                color: @l;
            }
            & when (@variationIconBordered) or (@variationIconCircular) {
                i.inverted.bordered.@{color}.icon.icon.icon.icon.icon.icon,
                i.inverted.circular.@{color}.icon.icon.icon.icon.icon.icon,
                i.inverted.bordered.@{color}.icons,
                i.inverted.circular.@{color}.icons {
                    background-color: @c;
                    color: @white;
                }
            }
        }
    });
}

/* -------------------
        Sizes
-------------------- */

i.icon,
i.icons {
    font-size: @medium;
    line-height: @lineHeight;
    font-style: normal;
}
& when not (@variationIconSizes = false) {
    each(@variationIconSizes, {
        @s: @@value;
        i.@{value}.@{value}.@{value}.icon,
        i.@{value}.@{value}.@{value}.icons {
            font-size: @s;
            vertical-align: middle;
        }
    });
}

& when (@variationIconGroups) or (@variationIconCorner) {
    /*******************************
                Groups
    *******************************/

    i.icons {
        display: inline-block;
        position: relative;
        line-height: 1;
        min-width: @width;
        min-height: @height;
        margin: 0 @distanceFromText 0 0;
        text-align: center;
    }

    i.icons .icon {
        position: absolute;
        top: 50%;
        left: 50%;
        margin: 0;
        &:not(.corner):not(.rotated):not(.flipped) {
            transform: translateX(-50%) translateY(-50%);
        }
    }

    i.icons .icon:first-child {
        position: static;
        width: auto;
        height: auto;
        vertical-align: top;
    }
    i.icons:not(.bordered):not(.circular) .icon:first-child:not(.rotated):not(.flipped) {
        transform: none;
    }

    & when (@variationIconCorner) {
        /* Corner Icon */
        i.icons .corner.icon {
            top: auto;
            left: auto;
            right: @cornerOffset;
            bottom: @cornerOffset;
            font-size: @cornerIconSize;
            text-shadow: @cornerIconShadow;
            &:not(.rotated):not(.flipped) {
                transform: none;
            }
        }
        i.icons .icon.corner[class*="top right"] {
            top: @cornerOffset;
            left: auto;
            right: @cornerOffset;
            bottom: auto;
        }
        i.icons .icon.corner[class*="top left"] {
            top: @cornerOffset;
            left: @cornerOffset;
            right: auto;
            bottom: auto;
        }
        i.icons .icon.corner[class*="bottom left"] {
            top: auto;
            left: @cornerOffset;
            right: auto;
            bottom: @cornerOffset;
        }
        i.icons .icon.corner[class*="bottom right"] {
            top: auto;
            left: auto;
            right: @cornerOffset;
            bottom: @cornerOffset;
        }
        & when (@variationIconInverted) {
            i.icons .inverted.corner.icon {
                text-shadow: @cornerIconInvertedShadow;
            }
        }
    }
}

& when ((@variationIconGroups) or (@variationIconCorner)) and ((@variationIconBordered) or (@variationIconCircular)) {
    /*************************************************
       Bordered/circular with corner or group icons
    *************************************************/
    i.bordered.icons,
    i.circular.icons {
        width: @borderedSize;
        height: @borderedSize;
        box-shadow: @borderedShadow;
        vertical-align: middle;
        &.colored when (@variationIconColored) {
            box-shadow: @coloredBoxShadow;
        }
    }
    i.circular.icons {
        border-radius: 500em;
    }
    i.bordered.icons i.icon:first-child,
    i.circular.icons i.icon:first-child {
        position: absolute;
        transform: translateX(-50%) translateY(-50%);
    }

    & when (@variationIconInverted) {
        /* Inverted Icon */
        i.bordered.inverted.icons,
        i.circular.inverted.icons {
            border: none;
            box-shadow: none;
            background-color: @black;
            color: @white;
        }
    }

    & when (@variationIconCorner) {
        /* Corner Icon */
        i.bordered.icons .icon.corner,
        i.circular.icons .icon.corner,
        i.bordered.icons .icon.corner[class*="bottom right"],
        i.circular.icons .icon.corner[class*="bottom right"] {
            top: auto;
            left: auto;
            right: @borderedGroupCornerOffset;
            bottom: @borderedGroupCornerOffset;
        }
        i.bordered.icons .icon.corner[class*="top right"],
        i.circular.icons .icon.corner[class*="top right"] {
            top: @borderedGroupCornerOffset;
            left: auto;
            right: @borderedGroupCornerOffset;
            bottom: auto;
        }
        i.bordered.icons .icon.corner[class*="top left"],
        i.circular.icons .icon.corner[class*="top left"] {
            top: @borderedGroupCornerOffset;
            left: @borderedGroupCornerOffset;
            right: auto;
            bottom: auto;
        }
        i.bordered.icons .icon.corner[class*="bottom left"],
        i.circular.icons .icon.corner[class*="bottom left"] {
            top: auto;
            left: @borderedGroupCornerOffset;
            right: auto;
            bottom: @borderedGroupCornerOffset;
        }
    }
}

.generateIcons(@map, @fontFamily: false, @pseudo: before) {
    each(@map, {
        @escapedKey: replace(@key, "^([0-9])", "\3$1 ");
        @normalizedKey: replace(@escapedKey, "_", @iconClassSeparator, "g");
        @unorderedKey: e(%(".%s::%s",@normalizedKey, @pseudo));
        @orderedKey: e(%('[%s*="%s"]::%s', @iconForcedAttribute, @normalizedKey, @pseudo));
        @selectorKey: if(@iconForcedOrder or @iconClassSeparator = " ", @orderedKey, @unorderedKey);
        i.icon@{selectorKey} {
            content: "@{value}";
            & when not (@fontFamily = false) {
                font-family: @fontFamily;
            }
        }
    });
}

& when (@variationIconDeprecated) {
    /* Deprecated *In/Out Naming Conflict) */
    .generateIcons(@icon-deprecated-map);
}

& when (@variationIconSolid) {
    /*******************************
            Solid Icons
    *******************************/

    /* Icons */
    .generateIcons(@icon-map);
    & when (@variationIconAliases) {
        /* Aliases */
        .generateIcons(@icon-aliases-map);
    }
}

& when (@variationIconOutline) {
    /*******************************
             Outline Icons
    *******************************/

    i.icon.outline {
        font-family: @outlineFontName;
    }

    /* Icons */
    .generateIcons(@icon-outline-map);
    & when (@variationIconAliases) {
        /* Aliases */
        .generateIcons(@icon-outline-aliases-map);
    }
}

& when (@variationIconThin) {
    /*******************************
           Thin Icons
    *******************************/

    /* Icons */
    i.icon.thin {
        font-family: @thinFontName;
    }
    .generateIcons(@icon-thin-map);
    & when (@variationIconAliases) {
        /* Aliases */
        .generateIcons(@icon-thin-aliases-map);
    }
}

& when (@variationIconBrand) {
    /*******************************
              Brand Icons
    *******************************/

    /* Icons */
    .generateIcons(@icon-brand-map, @brandFontName);
    & when (@variationIconAliases) {
        /* Aliases */
        .generateIcons(@icon-brand-aliases-map, @brandFontName);
    }
}

& when (@variationIconDuotone) {
    /*******************************
              Duotone Icons
    *******************************/

    /* Make duotone icons use the proper font */
    i.icon.duotone {
        font-family: @duotoneFontName;

        /* To position the secondary layer on top of the first layer */
        position: relative;
    }

    /* Set the default opacity levels and colors for each layer */
    i.icon.duotone::before {
        color: @duotonePrimaryColor;
        opacity: @duotonePrimaryOpacity;
    }

    i.icon.duotone::after {
        /* Position secondary layer to the left, centered horizontally and aligned vertically to flex with different line heights */
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
        color: @duotoneSecondaryColor;
        opacity: @duotoneSecondaryOpacity;
    }

    .generateIcons(@icon-duotone-map);
    .generateIcons(@icon-duotone-secondary-map, false, after);

    & when (@variationIconAliases) {
        /* Aliases */
        .generateIcons(@icon-duotone-aliases-map);
        .generateIcons(@icon-duotone-secondary-aliases-map, false, after);
    }

    /*
     * Colors for duotone icons, in the form `primary-secondary`(e.g. `black-grey duotone icon`).
     */
    & when not (@variationIconColors = false) {
        each(@variationIconColors, {
            @color: @value;
            @c: @colors[@@color][color];
            @l: @colors[@@color][light];

            i.icon.duotone[class*="@{color}-"]::before,
            i.icon.duotone[class*="-@{color}"]::after {
                color: @c;
            }

            & when (@variationIconInverted) {
                i.icon.inverted.duotone[class*="@{color}-"]::before,
                i.icon.inverted.duotone[class*="-@{color}"]::after {
                    color: @l;
                }
            }
        });
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
