/*!
 * # Fomantic-UI - Container
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "container";

@import (multiple) "../../theme.config";

/*******************************
            Container
*******************************/

/* All Sizes */
.ui.container {
    display: block;
    max-width: @maxWidth;
}

/* Mobile */
@media only screen and (max-width: @largestMobileScreen) {
    .ui.ui.ui.container:not(.fluid) {
        width: @mobileWidth;
        margin-left: @mobileGutter;
        margin-right: @mobileGutter;
    }
    & when (@variationContainerGrid) or (@variationContainerRelaxed) {
        .ui.ui.ui.grid.container {
            width: @mobileGridWidth;
        }
        & when (@variationContainerRelaxed) {
            .ui.ui.ui.relaxed.grid.container {
                width: @mobileRelaxedGridWidth;
            }
            & when (@variationContainerVeryRelaxed) {
                .ui.ui.ui.very.relaxed.grid.container {
                    width: @mobileVeryRelaxedGridWidth;
                }
            }
        }
    }
}

/* Tablet */
@media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
    .ui.ui.ui.container:not(.fluid) {
        width: @tabletWidth;
        margin-left: @tabletGutter;
        margin-right: @tabletGutter;
    }
    & when (@variationContainerGrid) or (@variationContainerRelaxed) {
        .ui.ui.ui.grid.container {
            width: @tabletGridWidth;
        }
        & when (@variationContainerRelaxed) {
            .ui.ui.ui.relaxed.grid.container {
                width: @tabletRelaxedGridWidth;
            }
            & when (@variationContainerVeryRelaxed) {
                .ui.ui.ui.very.relaxed.grid.container {
                    width: @tabletVeryRelaxedGridWidth;
                }
            }
        }
    }
}

/* Small Monitor */
@media only screen and (min-width: @computerBreakpoint) and (max-width: @largestSmallMonitor) {
    .ui.ui.ui.container:not(.fluid) {
        width: @computerWidth;
        margin-left: @computerGutter;
        margin-right: @computerGutter;
    }
    & when (@variationContainerGrid) or (@variationContainerRelaxed) {
        .ui.ui.ui.grid.container {
            width: @computerGridWidth;
        }
        & when (@variationContainerRelaxed) {
            .ui.ui.ui.relaxed.grid.container {
                width: @computerRelaxedGridWidth;
            }
            & when (@variationContainerVeryRelaxed) {
                .ui.ui.ui.very.relaxed.grid.container {
                    width: @computerVeryRelaxedGridWidth;
                }
            }
        }
    }
}

/* Large Monitor */
@media only screen and (min-width: @largeMonitorBreakpoint) {
    .ui.ui.ui.container:not(.fluid) {
        width: @largeMonitorWidth;
        margin-left: @largeMonitorGutter;
        margin-right: @largeMonitorGutter;
    }
    & when (@variationContainerGrid) or (@variationContainerRelaxed) {
        .ui.ui.ui.grid.container {
            width: @largeMonitorGridWidth;
        }
        & when (@variationContainerRelaxed) {
            .ui.ui.ui.relaxed.grid.container {
                width: @largeMonitorRelaxedGridWidth;
            }
            & when (@variationContainerVeryRelaxed) {
                .ui.ui.ui.very.relaxed.grid.container {
                    width: @largeMonitorVeryRelaxedGridWidth;
                }
            }
        }
    }
}

/*******************************
             Types
*******************************/

& when (@variationContainerText) {
    /* Text Container */
    .ui.text.container {
        font-family: @textFontFamily;
        max-width: @textWidth;
        line-height: @textLineHeight;
        font-size: @textSize;
    }
}

& when (@variationContainerWide) {
    /* Wide Container */
    @media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
        .ui.ui.ui.wide.container {
            width: @tabletWideWidth;
        }
    }
    @media only screen and (min-width: @computerBreakpoint) and (max-width: @largestSmallMonitor) {
        .ui.ui.ui.wide.container {
            width: @computerWideWidth;
        }
    }
    @media only screen and (min-width: @largeMonitorBreakpoint) {
        .ui.ui.ui.wide.container {
            width: @largeMonitorWideWidth;
        }
    }
}

& when (@variationContainerFluid) {
    /* Fluid */
    .ui.fluid.container {
        width: 100%;
    }
}

/*******************************
           Variations
*******************************/
& when (@variationContainerAligned) {
    .ui[class*="left aligned"].container {
        text-align: left;
    }
    .ui[class*="center aligned"].container {
        text-align: center;
    }
    .ui[class*="right aligned"].container {
        text-align: right;
    }
}
& when (@variationContainerJustified) {
    .ui.justified.container {
        text-align: justify;
        hyphens: auto;
    }
}
& when (@variationContainerScrolling) {
    /* --------------
      Scrolling
    --------------- */
    .ui.scrolling.container {
        overflow: auto;
        overscroll-behavior: @overscrollBehavior;
    }
    @media only screen and (max-width: @largestMobileScreen) {
        & when (@variationContainerScrollingShort) {
            .ui.scrolling.container.short {
                max-height: @scrollingMobileMaxHeight * 0.75;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingMobileMaxHeight * 0.75;
                }
            }
        }
        & when (@variationContainerScrollingVeryShort) {
            .ui.scrolling.container[class*="very short"] {
                max-height: @scrollingMobileMaxHeight * 0.5;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingMobileMaxHeight * 0.5;
                }
            }
        }
        .ui.scrolling.container {
            max-height: @scrollingMobileMaxHeight;
        }
        & when (@variationContainerScrollingLong) {
            .ui.scrolling.container.long {
                max-height: @scrollingMobileMaxHeight * 2;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingMobileMaxHeight * 2;
                }
            }
        }
        & when (@variationContainerScrollingVeryLong) {
            .ui.scrolling.container[class*="very long"] {
                max-height: @scrollingMobileMaxHeight * 3;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingMobileMaxHeight * 3;
                }
            }
        }
    }

    @media only screen and (min-width: @tabletBreakpoint) {
        & when (@variationContainerScrollingShort) {
            .ui.scrolling.container.short {
                max-height: @scrollingTabletMaxHeight * 0.75;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingTabletMaxHeight * 0.75;
                }
            }
        }
        & when (@variationContainerScrollingVeryShort) {
            .ui.scrolling.container[class*="very short"] {
                max-height: @scrollingTabletMaxHeight * 0.5;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingTabletMaxHeight * 0.5;
                }
            }
        }
        .ui.scrolling.container {
            max-height: @scrollingTabletMaxHeight;
            &.resizable when (@variationContainerResizable) {
                height: @scrollingTabletMaxHeight;
            }
        }
        & when (@variationContainerScrollingLong) {
            .ui.scrolling.container.long {
                max-height: @scrollingTabletMaxHeight * 2;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingTabletMaxHeight * 2;
                }
            }
        }
        & when (@variationContainerScrollingVeryLong) {
            .ui.scrolling.container[class*="very long"] {
                max-height: @scrollingTabletMaxHeight * 3;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingTabletMaxHeight * 3;
                }
            }
        }
    }
    @media only screen and (min-width: @computerBreakpoint) {
        & when (@variationContainerScrollingShort) {
            .ui.scrolling.container.short {
                max-height: @scrollingComputerMaxHeight * 0.75;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingComputerMaxHeight * 0.75;
                }
            }
        }
        & when (@variationContainerScrollingVeryShort) {
            .ui.scrolling.container[class*="very short"] {
                max-height: @scrollingComputerMaxHeight * 0.5;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingComputerMaxHeight * 0.5;
                }
            }
        }
        .ui.scrolling.container {
            max-height: @scrollingComputerMaxHeight;
            &.resizable when (@variationContainerResizable) {
                height: @scrollingComputerMaxHeight;
            }
        }
        & when (@variationContainerScrollingLong) {
            .ui.scrolling.container.long {
                max-height: @scrollingComputerMaxHeight * 2;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingComputerMaxHeight * 2;
                }
            }
        }
        & when (@variationContainerScrollingVeryLong) {
            .ui.scrolling.container[class*="very long"] {
                max-height: @scrollingComputerMaxHeight * 3;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingComputerMaxHeight * 3;
                }
            }
        }
    }
    @media only screen and (min-width: @widescreenMonitorBreakpoint) {
        & when (@variationContainerScrollingShort) {
            .ui.scrolling.container.short {
                max-height: @scrollingWidescreenMaxHeight * 0.75;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingWidescreenMaxHeight * 0.75;
                }
            }
        }
        & when (@variationContainerScrollingVeryShort) {
            .ui.scrolling.container[class*="very short"] {
                max-height: @scrollingWidescreenMaxHeight * 0.5;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingWidescreenMaxHeight * 0.5;
                }
            }
        }
        .ui.scrolling.container {
            max-height: @scrollingWidescreenMaxHeight;
            &.resizable when (@variationContainerResizable) {
                height: @scrollingWidescreenMaxHeight;
            }
        }
        & when (@variationContainerScrollingLong) {
            .ui.scrolling.container.long {
                max-height: @scrollingWidescreenMaxHeight * 2;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingWidescreenMaxHeight * 2;
                }
            }
        }
        & when (@variationContainerScrollingVeryLong) {
            .ui.scrolling.container[class*="very long"] {
                max-height: @scrollingWidescreenMaxHeight * 3;
                &.resizable when (@variationContainerResizable) {
                    height: @scrollingWidescreenMaxHeight * 3;
                }
            }
        }
    }
    & when (@variationContainerResizable) {
        .ui.resizable.scrolling.container {
            resize: @resizableDirection;
            max-height: none;
        }
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
