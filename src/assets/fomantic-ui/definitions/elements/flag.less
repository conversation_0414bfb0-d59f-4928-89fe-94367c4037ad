/*!
 * # Fomantic-UI - Flag
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "flag";

@import (multiple) "../../theme.config";

/*******************************
             Flag
*******************************/

i.flag:not(.icon) {
    speak: none;
    backface-visibility: hidden;
}

i.flag:not(.icon)::before {
    content: "\00A0\00A0\00A0\00A0\00A0\00A0\00A0";
    display: inline-block;
    line-height: @flagLineHeight;
    background-repeat: no-repeat;
    background-position: center center;
    & when not (@flagFileType = "svg") {
        background-size: contain;
    }
}

each(@size-map, {
    i.flag.@{key} {
        font-size: 1.5em * @value;
        vertical-align: middle;
    }
});

/* rtl:begin:ignore */

// for simplicity, class and alias names have to be unique and different to countrycode otherwise false
each(@flags, {
    @unicode: replace(@key, "@", "");
    @cc: replace(@flags[@@unicode][countrycode], "_", ".", "g");
    @cls: replace(@flags[@@unicode][class], "_", ".", "g");
    @alias: replace(@flags[@@unicode][aliasClass], "_", ".", "g");
    @alias2: replace(@flags[@@unicode][aliasClass2], "_", ".", "g");
    & when (@cls = false){
        i.flag.@{cc}::before {
            background-image: url("@{flagPath}@{unicode}.@{flagFileType}");
        }
    }
    & when not (@cls = false) and (@alias = false) {
        i.flag.@{cc}::before,
        i.flag.@{cls}::before {
            background-image: url("@{flagPath}@{unicode}.@{flagFileType}");
        }
    }
    & when not (@cls = false) and not (@alias = false) and (@alias2 = false) {
        i.flag.@{cc}::before,
        i.flag.@{cls}::before,
        i.flag.@{alias}::before {
            background-image: url("@{flagPath}@{unicode}.@{flagFileType}");
        }
    }
    & when not (@alias2 = false) {
        i.flag.@{cc}::before,
        i.flag.@{cls}::before,
        i.flag.@{alias}::before,
        i.flag.@{alias2}::before {
            background-image: url("@{flagPath}@{unicode}.@{flagFileType}");
        }
    }
});

/* rtl:end:ignore */

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
