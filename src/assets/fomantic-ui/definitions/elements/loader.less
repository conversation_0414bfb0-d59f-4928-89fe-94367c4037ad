/*!
 * # Fomantic-UI - Loader
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "element";
@element: "loader";

@import (multiple) "../../theme.config";

/*******************************
            Loader
*******************************/

/* Standard Size */
.ui.loader {
    display: none;
    position: absolute;
    top: @loaderTopOffset;
    left: @loaderLeftOffset;
    margin: 0;
    text-align: center;
    z-index: 1000;
    transform: translateX(-50%) translateY(-50%);
}

/* Static Shape */
.ui.loader::before {
    position: absolute;
    content: "";
    top: 0;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: @circularRadius;
    border: @loaderLineWidth solid @loaderFillColor;
}

/* Active Shape */
.ui.loader::after {
    position: absolute;
    content: "";
    top: 0;
    left: 50%;
    width: 100%;
    height: 100%;
    animation: loader @loaderSpeed infinite linear;
    border: @loaderLineWidth solid @shapeBorderColor;
    border-radius: @circularRadius;
    box-shadow: 0 0 0 1px transparent;
}

& when (@variationLoaderSpeeds) {
    /* Speeds */

    .ui.ui.fast.loading::after,
    .ui.ui.fast.loading .input > i.icon::after,
    .ui.ui.fast.loading > i.icon::after,
    .ui.fast.loader::after {
        animation-duration: @loaderSpeedFast;
    }

    .ui.ui.slow.loading::after,
    .ui.ui.slow.loading .input > i.icon::after,
    .ui.ui.slow.loading > i.icon::after,
    .ui.slow.loader::after {
        animation-duration: @loaderSpeedSlow;
    }
}

/* Active Animation */
@keyframes loader {
    100% {
        transform: rotate(360deg);
    }
}

/* -------------------
      Coupling
-------------------- */

/* Show inside active dimmer */
.ui.dimmer > .loader {
    display: block;
}

/* Black Dimmer */
.ui.dimmer > .ui.loader {
    color: @invertedLoaderTextColor;
}
.ui.dimmer > .ui.loader:not(.elastic)::before {
    border-color: @invertedLoaderFillColor;
}

/* White Dimmer (Inverted) */
.ui.inverted.dimmer > .ui.loader {
    color: @loaderTextColor;
}
.ui.inverted.dimmer > .ui.loader:not(.elastic)::before {
    border-color: @loaderFillColor;
}

/*******************************
             Types
*******************************/

& when (@variationLoaderText) {
    /* -------------------
            Text
    -------------------- */

    .ui.ui.ui.ui.text.loader {
        width: auto;
        height: auto;
        text-align: center;
        font-style: normal;
    }
}

/*******************************
            States
*******************************/
& when (@variationLoaderIndeterminate) {
    .ui.indeterminate.loader::after {
        animation-direction: @indeterminateDirection;
        animation-duration: @indeterminateSpeed;
    }
}

.ui.loader.active,
.ui.loader.visible {
    display: block;
}
.ui.loader.disabled,
.ui.loader.hidden {
    display: none;
}

/*******************************
            Variations
*******************************/

/* -------------------
        Sizes
-------------------- */

.ui.loader {
    width: @medium;
    height: @medium;
    font-size: @mediumFontSize;
}
.ui.loader::before,
.ui.loader::after {
    width: @medium;
    height: @medium;
    margin: @mediumOffset;
}
& when (@variationLoaderText) {
    .ui.text.loader {
        min-width: @medium;
        padding-top: (@medium + @textDistance);
    }
}
& when not (@variationLoaderSizes = false) {
    each(@variationLoaderSizes, {
        @o: @{value}Offset;
        @f: @{value}FontSize;
        @s: @@value;
        .ui.@{value}.loader {
            width: @s;
            height: @s;
            font-size: @@f;
        }
        .ui.@{value}.loader::before,
        .ui.@{value}.loader::after {
            width: @s;
            height: @s;
            margin: @@o;
        }
        & when (@variationLoaderText) {
            .ui.@{value}.text.loader {
                min-width: @s;
                padding-top: (@s + @textDistance);
            }
        }
    });
}

/* -------------------
       Colors
-------------------- */
& when not (@variationLoaderColors = false) {
    each(@variationLoaderColors, {
        @color: @value;
        @c: @colors[@@color][color];
        @l: @colors[@@color][light];

        .ui.ui.@{color}.elastic.loader::before,
        .ui.@{color}.basic.elastic.loading.button::before,
        .ui.@{color}.basic.elastic.loading.button::after,
        .ui.ui.ui.@{color}.elastic.loading:not(.segment):not(.segments):not(.card)::before,
        .ui.ui.ui.@{color}.elastic.loading .input > i.icon::before,
        .ui.ui.ui.ui.@{color}.elastic.loading > i.icon::before,
        .ui.ui.ui.ui.@{color}.loading:not(.usual):not(.button)::after,
        .ui.ui.ui.ui.@{color}.loading .input > i.icon::after,
        .ui.ui.ui.ui.@{color}.loading > i.icon::after,
        .ui.ui.ui.@{color}.loader::after {
            color: @c;
        }
        .ui.inverted.@{color}.elastic.loader::before,
        .ui.ui.ui.inverted.@{color}.elastic.loading:not(.segment):not(.segments):not(.card)::before,
        .ui.ui.ui.inverted.@{color}.elastic.loading .input > i.icon::before,
        .ui.ui.ui.inverted.@{color}.elastic.loading > i.icon::before,
        .ui.ui.ui.ui.inverted.@{color}.loading:not(.usual)::after,
        .ui.ui.ui.ui.inverted.@{color}.loading .input > i.icon::after,
        .ui.ui.ui.ui.inverted.@{color}.loading > i.icon::after,
        .ui.ui.ui.inverted.@{color}.loader::after {
            color: @l;
        }
    });
}

.ui.ui.elastic.loader::before,
.ui.ui.ui.elastic.loading::before,
.ui.ui.ui.elastic.loading .input > i.icon::before,
.ui.ui.ui.elastic.loading > i.icon::before,
.ui.ui.ui.ui.loading:not(.usual)::after,
.ui.ui.ui.ui.loading .input > i.icon::after,
.ui.ui.ui.ui.loading > i.icon::after,
.ui.ui.ui.loader::after {
    border-color: currentColor;
}
.ui.ui.ui.ui.elastic.loading.button:not(.inverted):not(.basic)::before {
    color: @invertedLoaderLineColor;
}
.ui.elastic.basic.loading.button::before,
.ui.elastic.basic.loading.button::after {
    color: @loaderLineColor;
}
.ui.ui.ui.ui.double.loading.button::after {
    border-bottom-color: currentColor;
}

& when (@variationLoaderInline) {
    /* -------------------
           Inline
    -------------------- */

    .ui.inline.loader {
        position: relative;
        vertical-align: @inlineVerticalAlign;
        margin: @inlineMargin;
        left: 0;
        top: 0;
        transform: none;
    }

    .ui.inline.loader.active,
    .ui.inline.loader.visible {
        display: inline-block;
    }

    /* Centered Inline */
    .ui.centered.inline.loader.active,
    .ui.centered.inline.loader.visible {
        display: block;
        margin-left: auto;
        margin-right: auto;
    }
}

.ui.ui.ui.ui.ui.ui.loading::after,
.ui.ui.ui.ui.ui.ui.loading .input > i.icon::after,
.ui.ui.ui.ui.ui.ui.loading > i.icon::after,
.ui.ui.ui.ui.ui.loader::after {
    border-left-color: transparent;
    border-right-color: transparent;
}
.ui.ui.ui.ui.ui.ui.ui.loading:not(.double)::after,
.ui.ui.ui.ui.ui.ui.ui.loading:not(.double) .input > i.icon::after,
.ui.ui.ui.ui.ui.ui.ui.loading:not(.double) > i.icon::after,
.ui.ui.ui.ui.ui.ui.loader:not(.double)::after {
    border-bottom-color: transparent;
}
.ui.ui.ui.ui.ui.ui.loading.card::after,
.ui.ui.ui.ui.ui.ui.loading.segments::after,
.ui.ui.ui.ui.ui.ui.loading.segment::after,
.ui.ui.ui.ui.ui.ui.loading.form::after {
    border-left-color: @loaderFillColor;
    border-right-color: @loaderFillColor;
}
.ui.ui.ui.ui.ui.ui.loading.card:not(.double)::after,
.ui.ui.ui.ui.ui.ui.loading.segments:not(.double)::after,
.ui.ui.ui.ui.ui.ui.loading.segment:not(.double)::after,
.ui.ui.ui.ui.ui.ui.loading.form:not(.double)::after {
    border-bottom-color: @loaderFillColor;
}

& when (@variationLoaderElastic) {
    /* -------------------
           Elastic
    -------------------- */

    .ui.dimmer > .ui.elastic.loader {
        color: @invertedLoaderLineColor;
    }
    .ui.inverted.dimmer > .ui.elastic.loader {
        color: @loaderLineColor;
    }
    .ui.ui.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::after,
    .ui.ui.elastic.loading .input > i.icon::after,
    .ui.ui.elastic.loading > i.icon::after,
    .ui.ui.elastic.loader::after {
        animation: loader 1s infinite cubic-bezier(0.27, 1.05, 0.92, 0.61);
        animation-delay: 0.3s;
    }
    .ui.ui.ui.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::before,
    .ui.ui.ui.elastic.loading .input > i.icon::before,
    .ui.ui.ui.elastic.loading > i.icon::before,
    .ui.ui.elastic.loader::before {
        animation: elastic-loader 1s infinite cubic-bezier(0.27, 1.05, 0.92, 0.61);
        // https://github.com/fomantic/Fomantic-UI/pull/363
        // stylelint-disable-next-line property-no-vendor-prefix
        -moz-animation: currentcolor-elastic-loader 1s infinite cubic-bezier(0.27, 1.05, 0.92, 0.61);
        border-right-color: transparent;
    }
    & when (@variationLoaderInline) {
        .ui.elastic.inline.loader:empty {
            animation: loader 8s infinite linear;
        }
    }
    & when (@variationLoaderSpeeds) {
        .ui.ui.slow.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::after,
        .ui.ui.slow.elastic.loading .input > i.icon::after,
        .ui.ui.slow.elastic.loading > i.icon::after,
        .ui.ui.slow.elastic.loader::after {
            animation-duration: 1.5s;
            animation-delay: 0.45s;
        }
        .ui.ui.ui.slow.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::before,
        .ui.ui.ui.slow.elastic.loading .input > i.icon::before,
        .ui.ui.ui.slow.elastic.loading > i.icon::before,
        .ui.ui.slow.elastic.loader::before {
            animation-duration: 1.5s;
        }
        .ui.ui.fast.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::after,
        .ui.ui.fast.elastic.loading .input > i.icon::after,
        .ui.ui.fast.elastic.loading > i.icon::after,
        .ui.ui.fast.elastic.loader::after {
            animation-duration: 0.66s;
            animation-delay: 0.2s;
        }
        .ui.ui.ui.fast.elastic.loading:not(.form):not(.segment):not(.segments):not(.card)::before,
        .ui.ui.ui.fast.elastic.loading .input > i.icon::before,
        .ui.ui.ui.fast.elastic.loading > i.icon::before,
        .ui.ui.fast.elastic.loader::before {
            animation-duration: 0.66s;
        }
    }
    @keyframes elastic-loader {
        0%,
        1% {
            border-left-color: transparent;
            border-bottom-color: transparent;
        }
        1.1%,
        50% {
            border-left-color: inherit;
        }
        10%,
        35.1% {
            border-bottom-color: transparent;
        }
        10.1%,
        35% {
            border-bottom-color: inherit;
        }
        50.1% {
            border-left-color: transparent;
        }
        100% {
            border-left-color: transparent;
            border-bottom-color: transparent;
            transform: rotate(360deg);
        }
    }

    @keyframes currentcolor-elastic-loader {
        0%,
        1% {
            border-left-color: transparent;
            border-bottom-color: transparent;
        }
        1.1%,
        50% {
            border-left-color: currentColor;
        }
        10%,
        35.1% {
            border-bottom-color: transparent;
        }
        10.1%,
        35% {
            border-bottom-color: currentColor;
        }
        50.1% {
            border-left-color: transparent;
        }
        100% {
            border-left-color: transparent;
            border-bottom-color: transparent;
            transform: rotate(360deg);
        }
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
