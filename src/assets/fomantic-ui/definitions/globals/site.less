/*!
 * # Fomantic-UI - Site
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: 'global';
@element: 'site';

@import (multiple) '../../theme.config';

/*******************************
             Page
*******************************/

.loadFonts();

html,
body {
	height: 100%;
}

html {
	font-size: @emSize;
}

body {
	margin: 0;
	padding: 0;
	overflow-x: @pageOverflowX;
	min-width: @pageMinWidth;
	// background: @pageBackground;
	font-family: @pageFont;
	font-size: @fontSize;
	line-height: @lineHeight;
	color: @textColor;
}

/*******************************
             Headers
*******************************/

h1,
h2,
h3,
h4,
h5 {
	font-family: @headerFont;
	line-height: @headerLineHeight;
	margin: @headerMargin;
	font-weight: @headerFontWeight;
	padding: 0;
}

h1 {
	min-height: 1rem;
	font-size: @h1;
}
h2 {
	font-size: @h2;
}
h3 {
	font-size: @h3;
}
h4 {
	font-size: @h4;
}
h5 {
	font-size: @h5;
}

h1:first-child,
h2:first-child,
h3:first-child,
h4:first-child,
h5:first-child {
	margin-top: 0;
}

h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child {
	margin-bottom: 0;
}

/*******************************
             Text
*******************************/

p {
	margin: @paragraphMargin;
	line-height: @paragraphLineHeight;
}
p:first-child {
	margin-top: 0;
}
p:last-child {
	margin-bottom: 0;
}

/* -------------------
        Links
-------------------- */

a {
	color: @linkColor;
	text-decoration: @linkUnderline;
}
a:hover {
	color: @linkHoverColor;
	text-decoration: @linkHoverUnderline;
}

/*******************************
         Scrollbars
*******************************/

& when (@useCustomScrollbars) {
	/* Force Simple Scrollbars */
	body ::-webkit-scrollbar {
		-webkit-appearance: none;
		width: @customScrollbarWidth;
		height: @customScrollbarHeight;
	}
	body ::-webkit-scrollbar-track {
		background: @trackBackground;
		border-radius: @trackBorderRadius;
	}
	body ::-webkit-scrollbar-thumb {
		cursor: pointer;
		border-radius: @thumbBorderRadius;
		background: @thumbBackground;
		transition: @thumbTransition;
	}
	body ::-webkit-scrollbar-thumb:window-inactive {
		background: @thumbInactiveBackground;
	}
	body ::-webkit-scrollbar-thumb:hover {
		background: @thumbHoverBackground;
	}
	& when (@supportIE) {
		body .ui {
			/* IE11 */
			scrollbar-face-color: @thumbBackgroundHex;
			scrollbar-shadow-color: @thumbBackgroundHex;
			scrollbar-track-color: @trackBackgroundHex;
			scrollbar-arrow-color: @trackBackgroundHex;
		}
	}
	@supports (-moz-appearance: none) {
		body .ui {
			/* firefox: first color thumb, second track */
			scrollbar-color: @thumbBackground @trackBackground;
			scrollbar-width: thin;
		}
	}

	/* Inverted UI */
	body .ui.inverted:not(.dimmer)::-webkit-scrollbar-track {
		background: @trackInvertedBackground;
	}
	body .ui.inverted:not(.dimmer)::-webkit-scrollbar-thumb {
		background: @thumbInvertedBackground;
	}
	body .ui.inverted:not(.dimmer)::-webkit-scrollbar-thumb:window-inactive {
		background: @thumbInvertedInactiveBackground;
	}
	body .ui.inverted:not(.dimmer)::-webkit-scrollbar-thumb:hover {
		background: @thumbInvertedHoverBackground;
	}
	& when (@supportIE) {
		body .ui.inverted:not(.dimmer) {
			/* IE11 */
			scrollbar-face-color: @thumbInvertedBackgroundHex;
			scrollbar-shadow-color: @thumbInvertedBackgroundHex;
			scrollbar-track-color: @trackInvertedBackgroundHex;
			scrollbar-arrow-color: @trackInvertedBackgroundHex;
		}
	}
	@supports (-moz-appearance: none) {
		body .ui.inverted:not(.dimmer) {
			/* firefox: first color thumb, second track */
			scrollbar-color: @thumbInvertedBackground @trackInvertedBackground;
		}
	}
}

/*******************************
          Highlighting
*******************************/

/* Site */
::selection {
	background-color: @highlightBackground;
	color: @highlightColor;
}

/* Form */
textarea::selection,
input::selection {
	background-color: @inputHighlightBackground;
	color: @inputHighlightColor;
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) '../../overrides.less';
