/*!
 * # Fomantic-UI - Comment
 * https://github.com/fomantic/Fomantic-UI/
 *
 *
 * Released under the MIT license
 * https://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: "view";
@element: "comment";

@import (multiple) "../../theme.config";

/*******************************
            Standard
*******************************/

/* --------------
    Comments
--------------- */

.ui.comments {
    margin: @margin;
    max-width: @maxWidth;
}

.ui.comments:first-child {
    margin-top: 0;
}
.ui.comments:last-child {
    margin-bottom: 0;
}

/* --------------
     Comment
--------------- */

.ui.comments .comment {
    position: relative;
    background: @commentBackground;
    margin: @commentMargin;
    padding: @commentPadding;
    border: @commentBorder;
    border-top: @commentDivider;
    line-height: @commentLineHeight;
}
.ui.comments .comment:first-child {
    margin-top: @firstCommentMargin;
    padding-top: @firstCommentPadding;
}

/* --------------------
    Nested Comments
--------------------- */

.ui.comments .comment > .comments {
    margin: @nestedCommentsMargin;
    padding: @nestedCommentsPadding;
}
.ui.comments .comment > .comments::before {
    position: absolute;
    top: 0;
    left: 0;
}
.ui.comments .comment > .comments .comment {
    border: @nestedCommentBorder;
    border-top: @nestedCommentDivider;
    background: @nestedCommentBackground;
}
& when (@variationCommentAvatar) {
    /* --------------
         Avatar
    --------------- */

    .ui.comments .comment .avatar {
        display: @avatarDisplay;
        width: @avatarWidth;
        height: @avatarHeight;
        float: @avatarFloat;
        margin: @avatarMargin;
    }
    .ui.comments .comment img.avatar,
    .ui.comments .comment .avatar img {
        display: block;
        margin: 0 auto;
        width: 100%;
        height: 100%;
        border-radius: @avatarBorderRadius;
    }
}

/* --------------
     Content
--------------- */

.ui.comments .comment > .content {
    display: block;
}
& when (@variationCommentAvatar) {
    /* If there is an avatar move content over */
    .ui.comments .comment > .avatar ~ .content {
        margin-left: @contentMargin;
    }
}

& when (@variationCommentAuthor) {
    /* --------------
         Author
    --------------- */

    .ui.comments .comment .author {
        font-size: @authorFontSize;
        color: @authorColor;
        font-weight: @authorFontWeight;
    }
    .ui.comments .comment a.author {
        cursor: pointer;
    }
    .ui.comments .comment a.author:hover {
        color: @authorHoverColor;
    }
}

& when (@variationCommentMeta) {
    /* --------------
         Metadata
    --------------- */

    .ui.comments .comment .metadata {
        display: @metadataDisplay;
        margin-left: @metadataSpacing;
        color: @metadataColor;
        font-size: @metadataFontSize;
    }
    .ui.comments .comment .metadata > * {
        display: inline-block;
        margin: 0 @metadataContentSpacing 0 0;
    }
    .ui.comments .comment .metadata > :last-child {
        margin-right: 0;
    }
}

/* --------------------
     Comment Text
--------------------- */

.ui.comments .comment .text {
    margin: @textMargin;
    font-size: @textFontSize;
    word-wrap: @textWordWrap;
    color: @textColor;
    line-height: @textLineHeight;
}

& when (@variationCommentActions) {
    /* --------------------
         User Actions
    --------------------- */

    .ui.comments .comment .actions {
        font-size: @actionFontSize;
    }
    .ui.comments .comment .actions a {
        cursor: pointer;
        display: inline-block;
        margin: 0 @actionContentDistance 0 0;
        color: @actionLinkColor;
    }
    .ui.comments .comment .actions a:last-child {
        margin-right: 0;
    }
    .ui.comments .comment .actions a.active,
    .ui.comments .comment .actions a:hover {
        color: @actionLinkHoverColor;
    }
}

& when (@variationCommentReply) {
    /* --------------------
          Reply Form
    --------------------- */

    .ui.comments > .reply.form {
        margin-top: @replyDistance;
    }
    .ui.comments .comment .reply.form {
        width: 100%;
        margin-top: @commentReplyDistance;
    }
    .ui.comments .reply.form textarea {
        font-size: @replyFontSize;
        height: @replyHeight;
    }
}

/*******************************
            State
*******************************/

.ui.collapsed.comments,
.ui.comments .collapsed.comments,
.ui.comments .collapsed.comment {
    display: none;
}

/*******************************
           Variations
*******************************/

& when (@variationCommentThreaded) {
    /* --------------------
            Threaded
    --------------------- */

    .ui.threaded.comments .comment > .comments {
        margin: @threadedCommentMargin;
        padding: @threadedCommentPadding;
        box-shadow: @threadedCommentBoxShadow;
    }
}

& when (@variationCommentMinimal) {
    /* --------------------
            Minimal
    --------------------- */

    .ui.minimal.comments .comment .actions {
        opacity: 0;
        position: @minimalActionPosition;
        top: @minimalActionTop;
        right: @minimalActionRight;
        left: @minimalActionLeft;
        transition: @minimalTransition;
        transition-delay: @minimalTransitionDelay;
    }
    .ui.minimal.comments .comment > .content:hover > .actions {
        opacity: 1;
    }
}

/* -------------------
        Sizes
-------------------- */

.ui.comments {
    font-size: @medium;
}
& when not (@variationCommentSizes = false) {
    each(@variationCommentSizes, {
        @s: @@value;
        .ui.@{value}.comments {
            font-size: @s;
        }
    });
}

& when (@variationCommentInverted) {
    /* -------------------
            Inverted
    -------------------- */
    .ui.inverted.comments .comment {
        background-color: @black;
    }

    .ui.inverted.comments .comment .author,
    .ui.inverted.comments .comment .text {
        color: @invertedTextColor;
    }

    .ui.inverted.comments .comment .metadata,
    .ui.inverted.comments .comment .actions a {
        color: @invertedLightTextColor;
    }

    .ui.inverted.comments .comment a.author:hover,
    .ui.inverted.comments .comment .actions a.active,
    .ui.inverted.comments .comment .actions a:hover {
        color: @invertedHoveredTextColor;
    }
    & when (@variationCommentThreaded) {
        .ui.inverted.threaded.comments .comment > .comments {
            box-shadow: -1px 0 0 @solidWhiteBorderColor;
        }
    }
}
& when (@variationCommentDisabled) {
    .ui.disabled.comments,
    .ui.comments .disabled.comment {
        opacity: @disabledOpacity;
        pointer-events: @disabledPointerEvents;
    }
}

// stylelint-disable no-invalid-position-at-import-rule
@import (multiple, optional) "../../overrides.less";
