/*******************************
            Header
*******************************/

/* -------------------
       Element
-------------------- */

@textTransform: none;
@fontFamily: @headerFont;
@fontWeight: @headerFontWeight;
@lineHeight: @headerLineHeight;
@lineHeightOffset: @headerLineHeightOffset;

@topMargin: @headerTopMargin;
@bottomMargin: @headerBottomMargin;
@margin: @topMargin 0 @bottomMargin;

@firstMargin: -@lineHeightOffset;
@lastMargin: 0;
@horizontalPadding: 0;
@verticalPadding: 0;

/* Sub Heading */
@subHeadingDistance: @2px;
@subHeadingFontSize: @relativeTiny;
@subHeadingFontWeight: @bold;
@subHeadingTextTransform: uppercase;
@subHeadingColor: "";

@miniSubHeadingSize: @relativeMini;
@tinySubHeadingSize: @relativeMini;
@smallSubHeadingSize: @relativeMini;
@largeSubHeadingSize: @relativeSmall;
@bigSubHeadingSize: @relativeMedium;
@hugeSubHeadingSize: @relativeMedium;
@massiveSubHeadingSize: @relativeLarge;

/* Sub Header */
@subHeaderMargin: 0;
@subHeaderLineHeight: 1.2em;
@subHeaderColor: @mutedTextColor;

/* Icon */
@iconOpacity: 1;
@iconSize: 1.5em;
@iconOffset: 0;
@iconMargin: 0.75rem;
@iconAlignment: middle;

/* Image */
@imageWidth: 2.5em;
@imageHeight: auto;
@imageOffset: @lineHeightOffset;
@imageMargin: @iconMargin;
@imageAlignment: middle;

/* Label */
@labelSize: "";
@labelDistance: 0.5rem;
@labelVerticalAlign: middle;

/* Content */
@contentAlignment: top;
@contentIconAlignment: middle;
@contentImageAlignment: middle;

/* Paragraph after Header */
@nextParagraphDistance: 0;

/* -------------------
      Variations
-------------------- */

/* Sizing */
@massiveFontSize: unit((32 / 14), em);
@hugeFontSize: unit(@h1, em);
@bigFontSize: unit((26 / 14), em);
@largeFontSize: unit(@h2, em);
@mediumFontSize: unit(@h3, em);
@smallFontSize: unit(@h4, em);
@tinyFontSize: unit(@h5, em);
@miniFontSize: unit(@h6, em);

/* Sub Header */
@h1SubHeaderFontSize: @large;
@h2SubHeaderFontSize: @large;
@h3SubHeaderFontSize: @medium;
@h4SubHeaderFontSize: @medium;
@h5SubHeaderFontSize: @small;
@h6SubHeaderFontSize: @small;

@massiveSubHeaderFontSize: @huge;
@hugeSubHeaderFontSize: @h1SubHeaderFontSize;
@bigSubHeaderFontSize: @h1SubHeaderFontSize;
@largeSubHeaderFontSize: @h2SubHeaderFontSize;
@subHeaderFontSize: @h3SubHeaderFontSize;
@smallSubHeaderFontSize: @h4SubHeaderFontSize;
@tinySubHeaderFontSize: @h5SubHeaderFontSize;
@miniSubHeaderFontSize: @h6SubHeaderFontSize;

/* Icon Header */
@iconHeaderSize: 3em;
@iconHeaderOpacity: 1;
@iconHeaderMargin: 0.5rem;
@circularHeaderIconSize: 2em;
@squareHeaderIconSize: 2em;
@cornerIconHeaderSize: calc(@iconHeaderSize * 0.45);

/* No Line Height Offset */
@iconHeaderTopMargin: 2rem;
@iconHeaderBottomMargin: @bottomMargin;
@iconHeaderFirstMargin: 0;

/* Divided */
@dividedBorderWidth: 1px;
@dividedBorder: @dividedBorderWidth solid @borderColor;
@dividedColoredBorderWidth: 2px;

@dividedBorderPadding: @3px;
@dividedSubHeaderPadding: @3px;
@dividedIconPadding: 0;

/* Block */
@blockBackground: @darkWhite;
@blockBoxShadow: none;
@blockBorderWidth: 1px;
@blockBorder: @blockBorderWidth solid @solidBorderColor;
@blockHorizontalPadding: @medium;
@blockVerticalPadding: @mini;
@blockBorderRadius: @defaultBorderRadius;

@miniBlock: @mini;
@tinyBlock: @tiny;
@smallBlock: @small;
@mediumBlock: @medium;
@largeBlock: @large;
@bigBlock: @big;
@hugeBlock: @huge;
@massiveBlock: @massive;

/* Attached */
@attachedOffset: -1px;
@attachedBoxShadow: none;
@attachedBorder: 1px solid @solidBorderColor;
@attachedVerticalPadding: @blockVerticalPadding;
@attachedHorizontalPadding: @blockHorizontalPadding;
@attachedBackground: @white;
@attachedBorderRadius: @blockBorderRadius;

@miniAttachedSize: @relativeMini;
@tinyAttachedSize: @relativeTiny;
@smallAttachedSize: @relativeSmall;
@mediumAttachedSize: @relativeMedium;
@largeAttachedSize: @relativeLarge;
@bigAttachedSize: @relativeBig;
@hugeAttachedSize: @relativeHuge;
@massiveAttachedSize: @relativeMassive;

/* Inverted */
@invertedColor: @white;
@invertedSubHeaderColor: @invertedMutedTextColor;
@invertedDividedBorderColor: @whiteBorderColor;
@invertedBlockBackground: @lightBlack @subtleGradient;
@invertedAttachedBackground: @black;

/* Floated */
@floatedMargin: 0.5em;
