@placeholderMaxWidth: 30rem;

/* Key Content Sizing */
@placeholderLineMargin: @relative12px;
@placeholderHeaderLineHeight: @relative9px;
@placeholderLineHeight: @relative7px;
@placeholderParagraphLineHeight: @placeholderLineHeight;

@placeholderSpacing: @relative20px;

/* Interval between consecutive placeholders */
@placeholderAnimationInterval: 0.15s;

/* Repeated Placeholder */
@consecutivePlaceholderSpacing: 2rem;

/* Image */
@placeholderImageHeight: 100px;

/* Header Image */
@placeholderImageWidth: 3em;
@placeholderImageTextIndent: @10px;

/* Paragraph */
@placeholderHeaderLineOneOutdent: 20%;
@placeholderHeaderLineTwoOutdent: 60%;

@placeholderLineOneOutdent: @placeholderFullLineOutdent;
@placeholderLineTwoOutdent: @placeholderMediumLineOutdent;
@placeholderLineThreeOutdent: @placeholderVeryLongLineOutdent;
@placeholderLineFourOutdent: @placeholderLongLineOutdent;
@placeholderLineFiveOutdent: @placeholderShortLineOutdent;

/* Glow Gradient */
@placeholderLoadingAnimationDuration: 2s;
@placeholderLoadingGradientWidth: 1200px;
// prettier-ignore
@placeholderLoadingGradient: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.08) 0,
    rgba(0, 0, 0, 0.15) 15%,
    rgba(0, 0, 0, 0.08) 30%
);
// prettier-ignore
@placeholderInvertedLoadingGradient: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.08) 0,
    rgba(255, 255, 255, 0.14) 15%,
    rgba(255, 255, 255, 0.08) 30%
);

/* Variations */
@placeholderFullLineOutdent: 0;
@placeholderVeryLongLineOutdent: 10%;
@placeholderLongLineOutdent: 35%;
@placeholderMediumLineOutdent: 50%;
@placeholderShortLineOutdent: 65%;
@placeholderVeryShortLineOutdent: 80%;
