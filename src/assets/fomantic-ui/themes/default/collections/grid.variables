/*******************************
             Grid
*******************************/

/* Inherited From Site */

// @mobileBreakpoint
// @tabletBreakpoint
// @computerBreakpoint
// @largeMonitorBreakpoint
// @widescreenMonitorBreakpoint

/*******************************
            Grid
*******************************/

@minWidth: 320px;

@gutterWidth: 2rem;
@rowSpacing: 2rem;

@tableWidth: e(%("calc(100%% + %d)", @gutterWidth));
@columnMaxImageWidth: 100%;

@consecutiveGridDistance: (@rowSpacing / 2);

/*******************************
           Variations
*******************************/

/* --------------
     Relaxed
--------------- */

@relaxedGutterWidth: 3rem;
@veryRelaxedGutterWidth: 5rem;

/* --------------
     Divided
--------------- */

@dividedBorder: -1px 0 0 0 @borderColor;
@verticallyDividedBorder: 0 -1px 0 0 @borderColor;

@dividedInvertedBorder: -1px 0 0 0 @whiteBorderColor;
@verticallyDividedInvertedBorder: 0 -1px 0 0 @whiteBorderColor;

/* --------------
    Celled
--------------- */

@celledMargin: 1em 0;
@celledWidth: 1px;
@celledBorderColor: @solidBorderColor;

@celledPadding: 1em;
@celledRelaxedPadding: 1.5em;
@celledVeryRelaxedPadding: 2em;

@celledGridDivider: 0 0 0 @celledWidth @celledBorderColor;
@celledRowDivider: 0 (-@celledWidth) 0 0 @celledBorderColor;
@celledColumnDivider: (-@celledWidth) 0 0 0 @celledBorderColor;

/* --------------
    Stackable
--------------- */

@stackableRowSpacing: @rowSpacing;
@stackableGutter: @gutterWidth;
@stackableMobileGutter: 0;
@stackableMobileBorder: 1px solid @borderColor;
@stackableInvertedMobileBorder: 1px solid @whiteBorderColor;

/* --------------
    Compact
--------------- */
@compactGutterWidth: (@gutterWidth / 2);
@compactRowSpacing: (@rowSpacing / 2);
@compactCelledRelaxedPadding: (@celledRelaxedPadding / 2);
@compactCelledVeryRelaxedPadding: (@celledVeryRelaxedPadding / 2);

/* ------------------
    Very Compact
------------------ */
@veryCompactGutterWidth: (@compactGutterWidth / 2);
@veryCompactRowSpacing: (@compactRowSpacing / 2);
@veryCompactCelledRelaxedPadding: (@compactCelledRelaxedPadding / 2);
@veryCompactCelledVeryRelaxedPadding: (@compactCelledVeryRelaxedPadding / 2);

/*******************************
             Legacy
*******************************/

/* --------------
     Page
--------------- */

/* Legacy (DO NOT USE)
 */
@mobileWidth: auto;
@mobileMargin: 0;
@mobileGutter: 0;

@tabletWidth: auto;
@tabletMargin: 0;
@tabletGutter: 2em;

@computerWidth: auto;
@computerMargin: 0;
@computerGutter: 3%;

@largeMonitorWidth: auto;
@largeMonitorMargin: 0;
@largeMonitorGutter: 15%;

@widescreenMonitorWidth: auto;
@widescreenMargin: 0;
@widescreenMonitorGutter: 23%;
