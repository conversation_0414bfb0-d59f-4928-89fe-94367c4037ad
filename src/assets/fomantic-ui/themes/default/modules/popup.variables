/*******************************
             Popup
*******************************/

/* -------------------
       Element
-------------------- */

@zIndex: 1900;
@background: @white;

@maxWidth: 250px;
@borderColor: @solidBorderColor;
@borderWidth: 1px;
@boxShadow: @floatingShadow;
@color: @textColor;

@verticalPadding: 0.833em;
@horizontalPadding: 1em;
@fontWeight: @normal;
@fontStyle: @normal;
@borderRadius: @defaultBorderRadius;

/* -------------------
       Parts
-------------------- */

/* Placement */
@arrowSize: @relative10px;
@arrowWidth: 1em;
@arrowDistanceFromEdge: 1em;
@boxArrowOffset: 0;
@popupDistanceAway: @arrowSize;

/* Header */
@headerFontFamily: @headerFont;
@headerFontWeight: @bold;
@headerFontSize: @relativeLarge;
@headerDistance: @relative7px;
@headerLineHeight: 1.2;

/* Content Border */
@border: @borderWidth solid @borderColor;

/* Arrow */
@arrowBackground: @background;
@arrowZIndex: 1901;
@arrowJitter: 0.05em;
@arrowOffset: -(@arrowSize / 2) + @arrowJitter;

@arrowStroke: @borderWidth;
@arrowColor: if(iscolor(@borderColor), darken(@borderColor, 10), @borderColor);

/* Arrow color by position */
@arrowTopBackground: @arrowBackground;
@arrowCenterBackground: @arrowBackground;
@arrowBottomBackground: @arrowBackground;

@arrowBoxShadow: @arrowStroke @arrowStroke 0 0 @arrowColor;
@leftArrowBoxShadow: @arrowStroke -@arrowStroke 0 0 @arrowColor;
@rightArrowBoxShadow: -@arrowStroke @arrowStroke 0 0 @arrowColor;
@bottomArrowBoxShadow: -@arrowStroke -@arrowStroke 0 0 @arrowColor;

/* -------------------
       Types
-------------------- */

/* Tooltip */
@tooltipBackground: @background;
@tooltipBorderRadius: @borderRadius;
@tooltipPadding: @verticalPadding @horizontalPadding;
@tooltipFontWeight: @fontWeight;
@tooltipFontStyle: @fontStyle;
@tooltipColor: @color;
@tooltipBorder: @border;
@tooltipBoxShadow: @boxShadow;
@tooltipMaxWidth: none;
@tooltipFontSize: @medium;
@tooltipLineHeight: @lineHeight;
@tooltipDistanceAway: @relative7px;
@tooltipZIndex: 1900;
@tooltipDuration: 0.2s;
@tooltipEasing: @defaultEasing;
@tooltipDelay: 0.04s;
@tooltipScaleInit: 0.8;

/* Inverted */
@tooltipInvertedBackground: @invertedBackground;
@tooltipInvertedColor: @invertedColor;
@tooltipInvertedBorder: @invertedBorder;
@tooltipInvertedBoxShadow: @invertedBoxShadow;

/* Arrow */
@tooltipArrowVerticalOffset: -@2px;
@tooltipArrowHorizontalOffset: -@1px;
@tooltipArrowBoxShadow: @arrowBoxShadow;
@tooltipArrowBackground: @arrowBackground;
@tooltipArrowTopBackground: @arrowTopBackground;
@tooltipArrowCenterBackground: @arrowCenterBackground;
@tooltipArrowBottomBackground: @arrowBottomBackground;

/* -------------------
       Coupling
-------------------- */

/* Grid Inside Popup */
@nestedGridMargin: -0.7rem -0.875rem; /* (padding * @medium) */
@nestedGridWidth: e("calc(100% + 1.75rem)");

/* -------------------
       States
-------------------- */

@loadingOpacity: 0.3;
@loadingColoredPercent: 30%;
@loadingPointerEvents: none;
@loadingZIndex: -1;
@loaderSize: 2em;
@loaderLineZIndex: 101;

/* backward compatible just in case */
@invisibleZIndex: @loadingZIndex;

/* -------------------
       Variations
-------------------- */

@multilineWhiteSpace: pre-line;

/* Wide */
@wideWidth: 350px;
@veryWideWidth: 550px;

/* Inverted */
@invertedBackground: @black;
@invertedColor: @white;
@invertedBorder: none;
@invertedBoxShadow: none;

@invertedHeaderBackground: none;
@invertedHeaderColor: @white;
@invertedArrowColor: @invertedBackground;

/* Arrow color by position */
@invertedArrowTopBackground: @invertedBackground;
@invertedArrowCenterBackground: @invertedBackground;
@invertedArrowBottomBackground: @invertedBackground;
