/*******************************
            Input
*******************************/

/* -------------------
      Element
-------------------- */

@inputFont: @pageFont;
@verticalPadding: @inputVerticalPadding;
@horizontalPadding: @inputHorizontalPadding;

@lineHeight: @inputLineHeight;
@lineHeightOffset: ((@lineHeight - 1em) / 2);

@padding: (@verticalPadding - @lineHeightOffset) @horizontalPadding;

@textAlign: left;
@background: @inputBackground;
@borderWidth: 1px;
@border: @borderWidth solid @borderColor;
@boxShadow: none;

@borderRadius: @defaultBorderRadius;
@transition:
    box-shadow @defaultDuration @defaultEasing,
    border-color @defaultDuration @defaultEasing;

/* -------------------
        Types
-------------------- */

/* Icon Input */
@iconWidth: (@verticalPadding * 2) + @glyphWidth;
@iconOpacity: 0.5;
@iconFocusOpacity: 1;
@iconOffset: -0.5em;

@iconDistance: 0;
@iconMargin: @iconWidth + @iconDistance;
@iconTransition: opacity 0.3s @defaultEasing;

@transparentIconWidth: @glyphWidth;
@transparentIconMargin: 2em;

@textareaIconHeight: 3em;
@transparentTextareaIconHeight: 1.3em;

/* Circular Icon Input */
@circularIconVerticalOffset: 0.35em;
@circularIconHorizontalOffset: 0.5em;

/* Labeled Input */
@labelCornerTop: @borderWidth;
@labelCornerRight: @borderWidth;
@labelCornerSize: @relative9px;
@labelSize: 1em;
@labelVerticalPadding: (@verticalPadding - @lineHeightOffset);

@labeledMargin: 2.5em;
@labeledIconInputMargin: 3.25em;
@labeledAndIconMargin: 4em;
@labeledIconMargin: 1.25em;

@labeledDropdownZIndex: 10;

/* -------------------
        States
-------------------- */

/* Placeholder */
@placeholderColor: @inputPlaceholderColor;
@placeholderFocusColor: @inputPlaceholderFocusColor;

/* Down */
@downBorderColor: rgba(0, 0, 0, 0.3);
@downBackground: #fafafa;
@downColor: @textColor;
@downBoxShadow: none;

/* Focus */
@focusBorderColor: @focusedFormBorderColor;
@focusBackground: @background;
@focusColor: @hoveredTextColor;
@focusBoxShadow: none;

/* Loader */
@invertedLoaderFillColor: rgba(0, 0, 0, 0.15);

/* file */
@fileButtonMargin: 1em;
@fileButtonTextColor: @mutedTextColor;
@fileButtonTextColorHover: @hoveredTextColor;
@fileButtonBackground: #e0e1e2;
@fileButtonBackgroundHover: #cacbcd;
@fileButtonFontWeight: @bold;

@fileButtonInvertedBackground: #e0e1e2;
@fileButtonInvertedBackgroundHover: @white;
@fileButtonInvertedTextColor: @mutedTextColor;

/* -------------------
      Variations
-------------------- */

/* Inverted */
@transparentInvertedPlaceholderColor: @invertedUnselectedTextColor;
@transparentInvertedColor: @white;

@miniInputSize: @relativeMini;
@tinyInputSize: @relativeTiny;
@smallInputSize: @relativeSmall;
@largeInputSize: @relativeLarge;
@bigInputSize: @relativeBig;
@hugeInputSize: @relativeHuge;
@massiveInputSize: @relativeMassive;
