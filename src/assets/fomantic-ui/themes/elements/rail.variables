/*******************************
            Rail
*******************************/

/* -------------------
       Element
-------------------- */

@width: 300px;
@height: 100%;

@distance: 4rem;
@splitDistance: (@distance / 2);

/* -------------------
      Variations
-------------------- */

/* Close */
@closeDistance: 2em;
@veryCloseDistance: 1em;

@splitCloseDistance: (@closeDistance / 2);
@splitVeryCloseDistance: (@veryCloseDistance / 2);

@closeWidth: e(%("calc(%d + %d)", @width, @splitCloseDistance));
@veryCloseWidth: e(%("calc(%d + %d)", @width, @splitVeryCloseDistance));

/* Dividing */
@dividingBorder: 1px solid @borderColor;
@dividingDistance: 5rem;
@splitDividingDistance: (@dividingDistance / 2);
@dividingWidth: @width + @splitDividingDistance;
