/*******************************
        Import Directives
*******************************/

/* ------------------
       Theme
------------------- */

@theme: if(@@element = "default", "", @@element);

/* --------------------
   Site Variables
--------------------- */

/* Default site.variables */
@import "@{themesFolder}/default/globals/site.variables";

/* Packaged site.variables */
@import (optional) "@{themesFolder}/@{site}/globals/site.variables";

/* Component's site.variables */
@import (optional) "@{themesFolder}/@{theme}/globals/site.variables";

/* Site theme site.variables */
@import (optional) "@{siteFolder}/globals/site.variables";

/* -------------------
 Component Variables
--------------------- */

/* Default */
@import "@{themesFolder}/default/@{type}s/@{element}.variables";

/* Packaged Theme */
@import (optional) "@{themesFolder}/@{theme}/@{type}s/@{element}.variables";

/* Site Theme */
@import (optional) "@{siteFolder}/@{type}s/@{element}.variables";

/* -------------------------
    Central Color Map
------------------------- */

/* Default */
@import "@{themesFolder}/default/globals/colors.less";

/* Packaged colors.less */
@import (optional) "@{themesFolder}/@{site}/globals/colors.less";

/* Packaged Theme */
@import (optional) "@{themesFolder}/@{theme}/globals/colors.less";

/* Site Theme */
@import (optional) "@{siteFolder}/globals/colors.less";

/*******************************
             Mix-ins
*******************************/

/* ------------------
       Fonts
------------------- */

.loadFonts() {
    & when (@importGoogleFonts) {
        @import (css) url("@{googleFontUrl}");
    }
    & when (@importFonts) and not (@fontName = "") {
        each(@fonts, {
            @font-face {
                each(@value, {
                    @{key}: @value;
                });
            }
        });
    }
}

/* ------------------
     Overrides
------------------- */

// Keep empty mixin in case of custom components using it so they dont break
.loadUIOverrides() {
    /* loadUIOverrides mixin is deprecated and will be removed in 2.10.0
       Replace mixin call by
          @import (multiple, optional) "../../overrides.less";
       instead
    */
}
