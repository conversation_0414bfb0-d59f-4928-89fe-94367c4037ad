/**
 * Browser Image Compression
 * v1.0.17
 * by <PERSON> <<EMAIL>>
 * https://github.com/Donaldcwl/browser-image-compression
 */

 !function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(e="undefined"!=typeof globalThis?globalThis:e||self).imageCompression=r()}(this,(function(){"use strict";function _mergeNamespaces(e,r){return r.forEach((function(r){Object.keys(r).forEach((function(t){if("default"!==t&&!(t in e)){var a=Object.getOwnPropertyDescriptor(r,t);Object.defineProperty(e,t,a.get?a:{enumerable:!0,get:function(){return r[t]}})}}))})),Object.freeze(e)}function ownKeys$1(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,a)}return t}function _objectSpread2(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys$1(Object(t),!0).forEach((function(r){_defineProperty(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys$1(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function _defineProperty(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function _slicedToArray(e,r){return _arrayWithHoles(e)||_iterableToArrayLimit(e,r)||_unsupportedIterableToArray(e,r)||_nonIterableRest()}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _iterableToArrayLimit(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var a,i,s=[],c=!0,l=!1;try{for(t=t.call(e);!(c=(a=t.next()).done)&&(s.push(a.value),!r||s.length!==r);c=!0);}catch(e){l=!0,i=e}finally{try{c||null==t.return||t.return()}finally{if(l)throw i}}return s}}function _unsupportedIterableToArray(e,r){if(e){if("string"==typeof e)return _arrayLikeToArray(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,r):void 0}}function _arrayLikeToArray(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function createCommonjsModule(e){var r={exports:{}};return e(r,r.exports),r.exports}var check=function(e){return e&&e.Math==Math&&e},global$1=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof commonjsGlobal&&commonjsGlobal)||function(){return this}()||Function("return this")(),isPure=!1,setGlobal=function(e,r){try{Object.defineProperty(global$1,e,{value:r,configurable:!0,writable:!0})}catch(t){global$1[e]=r}return r},SHARED="__core-js_shared__",store$1=global$1[SHARED]||setGlobal(SHARED,{}),sharedStore=store$1,shared=createCommonjsModule((function(e){(e.exports=function(e,r){return sharedStore[e]||(sharedStore[e]=void 0!==r?r:{})})("versions",[]).push({version:"3.18.0",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),requireObjectCoercible=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},toObject=function(e){return Object(requireObjectCoercible(e))},hasOwnProperty={}.hasOwnProperty,has$1=Object.hasOwn||function hasOwn(e,r){return hasOwnProperty.call(toObject(e),r)},id=0,postfix=Math.random(),uid=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++id+postfix).toString(36)},isCallable=function(e){return"function"==typeof e},aFunction=function(e){return isCallable(e)?e:void 0},getBuiltIn=function(e,r){return arguments.length<2?aFunction(global$1[e]):global$1[e]&&global$1[e][r]},engineUserAgent=getBuiltIn("navigator","userAgent")||"",process$3=global$1.process,Deno=global$1.Deno,versions=process$3&&process$3.versions||Deno&&Deno.version,v8=versions&&versions.v8,match,version;v8?(match=v8.split("."),version=match[0]<4?1:match[0]+match[1]):engineUserAgent&&(match=engineUserAgent.match(/Edge\/(\d+)/),(!match||match[1]>=74)&&(match=engineUserAgent.match(/Chrome\/(\d+)/),match&&(version=match[1])));var engineV8Version=version&&+version,fails=function(e){try{return!!e()}catch(e){return!0}},nativeSymbol=!!Object.getOwnPropertySymbols&&!fails((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&engineV8Version&&engineV8Version<41})),useSymbolAsUid=nativeSymbol&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,WellKnownSymbolsStore=shared("wks"),Symbol$1=global$1.Symbol,createWellKnownSymbol=useSymbolAsUid?Symbol$1:Symbol$1&&Symbol$1.withoutSetter||uid,wellKnownSymbol=function(e){return has$1(WellKnownSymbolsStore,e)&&(nativeSymbol||"string"==typeof WellKnownSymbolsStore[e])||(nativeSymbol&&has$1(Symbol$1,e)?WellKnownSymbolsStore[e]=Symbol$1[e]:WellKnownSymbolsStore[e]=createWellKnownSymbol("Symbol."+e)),WellKnownSymbolsStore[e]},TO_STRING_TAG$4=wellKnownSymbol("toStringTag"),test$1={};test$1[TO_STRING_TAG$4]="z";var toStringTagSupport="[object z]"===String(test$1),descriptors=!fails((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),isObject=function(e){return"object"==typeof e?null!==e:isCallable(e)},document$3=global$1.document,EXISTS$1=isObject(document$3)&&isObject(document$3.createElement),documentCreateElement=function(e){return EXISTS$1?document$3.createElement(e):{}},ie8DomDefine=!descriptors&&!fails((function(){return 7!=Object.defineProperty(documentCreateElement("div"),"a",{get:function(){return 7}}).a})),anObject=function(e){if(isObject(e))return e;throw TypeError(String(e)+" is not an object")},isSymbol=useSymbolAsUid?function(e){return"symbol"==typeof e}:function(e){var r=getBuiltIn("Symbol");return isCallable(r)&&Object(e)instanceof r},tryToString=function(e){try{return String(e)}catch(e){return"Object"}},aCallable=function(e){if(isCallable(e))return e;throw TypeError(tryToString(e)+" is not a function")},getMethod=function(e,r){var t=e[r];return null==t?void 0:aCallable(t)},ordinaryToPrimitive=function(e,r){var t,a;if("string"===r&&isCallable(t=e.toString)&&!isObject(a=t.call(e)))return a;if(isCallable(t=e.valueOf)&&!isObject(a=t.call(e)))return a;if("string"!==r&&isCallable(t=e.toString)&&!isObject(a=t.call(e)))return a;throw TypeError("Can't convert object to primitive value")},TO_PRIMITIVE=wellKnownSymbol("toPrimitive"),toPrimitive=function(e,r){if(!isObject(e)||isSymbol(e))return e;var t,a=getMethod(e,TO_PRIMITIVE);if(a){if(void 0===r&&(r="default"),t=a.call(e,r),!isObject(t)||isSymbol(t))return t;throw TypeError("Can't convert object to primitive value")}return void 0===r&&(r="number"),ordinaryToPrimitive(e,r)},toPropertyKey=function(e){var r=toPrimitive(e,"string");return isSymbol(r)?r:String(r)},$defineProperty=Object.defineProperty,f$5=descriptors?$defineProperty:function defineProperty(e,r,t){if(anObject(e),r=toPropertyKey(r),anObject(t),ie8DomDefine)try{return $defineProperty(e,r,t)}catch(e){}if("get"in t||"set"in t)throw TypeError("Accessors not supported");return"value"in t&&(e[r]=t.value),e},objectDefineProperty={f:f$5},createPropertyDescriptor=function(e,r){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:r}},createNonEnumerableProperty=descriptors?function(e,r,t){return objectDefineProperty.f(e,r,createPropertyDescriptor(1,t))}:function(e,r,t){return e[r]=t,e},functionToString=Function.toString;isCallable(sharedStore.inspectSource)||(sharedStore.inspectSource=function(e){return functionToString.call(e)});var inspectSource=sharedStore.inspectSource,WeakMap$1=global$1.WeakMap,nativeWeakMap=isCallable(WeakMap$1)&&/native code/.test(inspectSource(WeakMap$1)),keys$2=shared("keys"),sharedKey=function(e){return keys$2[e]||(keys$2[e]=uid(e))},hiddenKeys$1={},OBJECT_ALREADY_INITIALIZED="Object already initialized",WeakMap=global$1.WeakMap,set$2,get$1,has,enforce=function(e){return has(e)?get$1(e):set$2(e,{})},getterFor=function(e){return function(r){var t;if(!isObject(r)||(t=get$1(r)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return t}};if(nativeWeakMap||sharedStore.state){var store=sharedStore.state||(sharedStore.state=new WeakMap),wmget=store.get,wmhas=store.has,wmset=store.set;set$2=function(e,r){if(wmhas.call(store,e))throw new TypeError(OBJECT_ALREADY_INITIALIZED);return r.facade=e,wmset.call(store,e,r),r},get$1=function(e){return wmget.call(store,e)||{}},has=function(e){return wmhas.call(store,e)}}else{var STATE=sharedKey("state");hiddenKeys$1[STATE]=!0,set$2=function(e,r){if(has$1(e,STATE))throw new TypeError(OBJECT_ALREADY_INITIALIZED);return r.facade=e,createNonEnumerableProperty(e,STATE,r),r},get$1=function(e){return has$1(e,STATE)?e[STATE]:{}},has=function(e){return has$1(e,STATE)}}var internalState={set:set$2,get:get$1,has:has,enforce:enforce,getterFor:getterFor},FunctionPrototype$1=Function.prototype,getDescriptor=descriptors&&Object.getOwnPropertyDescriptor,EXISTS=has$1(FunctionPrototype$1,"name"),PROPER=EXISTS&&"something"===function something(){}.name,CONFIGURABLE=EXISTS&&(!descriptors||descriptors&&getDescriptor(FunctionPrototype$1,"name").configurable),functionName={EXISTS:EXISTS,PROPER:PROPER,CONFIGURABLE:CONFIGURABLE},redefine=createCommonjsModule((function(e){var r=functionName.CONFIGURABLE,t=internalState.get,a=internalState.enforce,i=String(String).split("String");(e.exports=function(e,t,s,c){var l,u=!!c&&!!c.unsafe,p=!!c&&!!c.enumerable,d=!!c&&!!c.noTargetGet,h=c&&void 0!==c.name?c.name:t;isCallable(s)&&("Symbol("===String(h).slice(0,7)&&(h="["+String(h).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!has$1(s,"name")||r&&s.name!==h)&&createNonEnumerableProperty(s,"name",h),(l=a(s)).source||(l.source=i.join("string"==typeof h?h:""))),e!==global$1?(u?!d&&e[t]&&(p=!0):delete e[t],p?e[t]=s:createNonEnumerableProperty(e,t,s)):p?e[t]=s:setGlobal(t,s)})(Function.prototype,"toString",(function toString(){return isCallable(this)&&t(this).source||inspectSource(this)}))})),toString$1={}.toString,classofRaw=function(e){return toString$1.call(e).slice(8,-1)},TO_STRING_TAG$3=wellKnownSymbol("toStringTag"),CORRECT_ARGUMENTS="Arguments"==classofRaw(function(){return arguments}()),tryGet=function(e,r){try{return e[r]}catch(e){}},classof=toStringTagSupport?classofRaw:function(e){var r,t,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=tryGet(r=Object(e),TO_STRING_TAG$3))?t:CORRECT_ARGUMENTS?classofRaw(r):"Object"==(a=classofRaw(r))&&isCallable(r.callee)?"Arguments":a},objectToString=toStringTagSupport?{}.toString:function toString(){return"[object "+classof(this)+"]"};toStringTagSupport||redefine(Object.prototype,"toString",objectToString,{unsafe:!0});var $propertyIsEnumerable={}.propertyIsEnumerable,getOwnPropertyDescriptor$4=Object.getOwnPropertyDescriptor,NASHORN_BUG=getOwnPropertyDescriptor$4&&!$propertyIsEnumerable.call({1:2},1),f$4=NASHORN_BUG?function propertyIsEnumerable(e){var r=getOwnPropertyDescriptor$4(this,e);return!!r&&r.enumerable}:$propertyIsEnumerable,objectPropertyIsEnumerable={f:f$4},split="".split,indexedObject=fails((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==classofRaw(e)?split.call(e,""):Object(e)}:Object,toIndexedObject=function(e){return indexedObject(requireObjectCoercible(e))},$getOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,f$3=descriptors?$getOwnPropertyDescriptor:function getOwnPropertyDescriptor(e,r){if(e=toIndexedObject(e),r=toPropertyKey(r),ie8DomDefine)try{return $getOwnPropertyDescriptor(e,r)}catch(e){}if(has$1(e,r))return createPropertyDescriptor(!objectPropertyIsEnumerable.f.call(e,r),e[r])},objectGetOwnPropertyDescriptor={f:f$3},ceil=Math.ceil,floor$7=Math.floor,toInteger=function(e){return isNaN(e=+e)?0:(e>0?floor$7:ceil)(e)},min$6=Math.min,toLength=function(e){return e>0?min$6(toInteger(e),9007199254740991):0},max$2=Math.max,min$5=Math.min,toAbsoluteIndex=function(e,r){var t=toInteger(e);return t<0?max$2(t+r,0):min$5(t,r)},createMethod$5=function(e){return function(r,t,a){var i,s=toIndexedObject(r),c=toLength(s.length),l=toAbsoluteIndex(a,c);if(e&&t!=t){for(;c>l;)if((i=s[l++])!=i)return!0}else for(;c>l;l++)if((e||l in s)&&s[l]===t)return e||l||0;return!e&&-1}},arrayIncludes={includes:createMethod$5(!0),indexOf:createMethod$5(!1)},indexOf=arrayIncludes.indexOf,objectKeysInternal=function(e,r){var t,a=toIndexedObject(e),i=0,s=[];for(t in a)!has$1(hiddenKeys$1,t)&&has$1(a,t)&&s.push(t);for(;r.length>i;)has$1(a,t=r[i++])&&(~indexOf(s,t)||s.push(t));return s},enumBugKeys=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],hiddenKeys=enumBugKeys.concat("length","prototype"),f$2=Object.getOwnPropertyNames||function getOwnPropertyNames(e){return objectKeysInternal(e,hiddenKeys)},objectGetOwnPropertyNames={f:f$2},f$1=Object.getOwnPropertySymbols,objectGetOwnPropertySymbols={f:f$1},ownKeys=getBuiltIn("Reflect","ownKeys")||function ownKeys(e){var r=objectGetOwnPropertyNames.f(anObject(e)),t=objectGetOwnPropertySymbols.f;return t?r.concat(t(e)):r},copyConstructorProperties=function(e,r){for(var t=ownKeys(r),a=objectDefineProperty.f,i=objectGetOwnPropertyDescriptor.f,s=0;s<t.length;s++){var c=t[s];has$1(e,c)||a(e,c,i(r,c))}},replacement=/#|\.prototype\./,isForced=function(e,r){var t=data[normalize(e)];return t==POLYFILL||t!=NATIVE&&(isCallable(r)?fails(r):!!r)},normalize=isForced.normalize=function(e){return String(e).replace(replacement,".").toLowerCase()},data=isForced.data={},NATIVE=isForced.NATIVE="N",POLYFILL=isForced.POLYFILL="P",isForced_1=isForced,getOwnPropertyDescriptor$3=objectGetOwnPropertyDescriptor.f,_export=function(e,r){var t,a,i,s,c,l=e.target,u=e.global,p=e.stat;if(t=u?global$1:p?global$1[l]||setGlobal(l,{}):(global$1[l]||{}).prototype)for(a in r){if(s=r[a],i=e.noTargetGet?(c=getOwnPropertyDescriptor$3(t,a))&&c.value:t[a],!isForced_1(u?a:l+(p?".":"#")+a,e.forced)&&void 0!==i){if(typeof s==typeof i)continue;copyConstructorProperties(s,i)}(e.sham||i&&i.sham)&&createNonEnumerableProperty(s,"sham",!0),redefine(t,a,s,e)}},nativePromiseConstructor=global$1.Promise,redefineAll=function(e,r,t){for(var a in r)redefine(e,a,r[a],t);return e},aPossiblePrototype=function(e){if("object"==typeof e||isCallable(e))return e;throw TypeError("Can't set "+String(e)+" as a prototype")},objectSetPrototypeOf=Object.setPrototypeOf||("__proto__"in{}?function(){var e,r=!1,t={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(t,[]),r=t instanceof Array}catch(e){}return function setPrototypeOf(t,a){return anObject(t),aPossiblePrototype(a),r?e.call(t,a):t.__proto__=a,t}}():void 0),defineProperty$5=objectDefineProperty.f,TO_STRING_TAG$2=wellKnownSymbol("toStringTag"),setToStringTag=function(e,r,t){e&&!has$1(e=t?e:e.prototype,TO_STRING_TAG$2)&&defineProperty$5(e,TO_STRING_TAG$2,{configurable:!0,value:r})},SPECIES$6=wellKnownSymbol("species"),setSpecies=function(e){var r=getBuiltIn(e),t=objectDefineProperty.f;descriptors&&r&&!r[SPECIES$6]&&t(r,SPECIES$6,{configurable:!0,get:function(){return this}})},anInstance=function(e,r,t){if(e instanceof r)return e;throw TypeError("Incorrect "+(t?t+" ":"")+"invocation")},iterators={},ITERATOR$8=wellKnownSymbol("iterator"),ArrayPrototype$1=Array.prototype,isArrayIteratorMethod=function(e){return void 0!==e&&(iterators.Array===e||ArrayPrototype$1[ITERATOR$8]===e)},functionBindContext=function(e,r,t){if(aCallable(e),void 0===r)return e;switch(t){case 0:return function(){return e.call(r)};case 1:return function(t){return e.call(r,t)};case 2:return function(t,a){return e.call(r,t,a)};case 3:return function(t,a,i){return e.call(r,t,a,i)}}return function(){return e.apply(r,arguments)}},ITERATOR$7=wellKnownSymbol("iterator"),getIteratorMethod=function(e){if(null!=e)return getMethod(e,ITERATOR$7)||getMethod(e,"@@iterator")||iterators[classof(e)]},getIterator=function(e,r){var t=arguments.length<2?getIteratorMethod(e):r;if(aCallable(t))return anObject(t.call(e));throw TypeError(String(e)+" is not iterable")},iteratorClose=function(e,r,t){var a,i;anObject(e);try{if(!(a=getMethod(e,"return"))){if("throw"===r)throw t;return t}a=a.call(e)}catch(e){i=!0,a=e}if("throw"===r)throw t;if(i)throw a;return anObject(a),t},Result=function(e,r){this.stopped=e,this.result=r},iterate=function(e,r,t){var a,i,s,c,l,u,p,d=t&&t.that,h=!(!t||!t.AS_ENTRIES),y=!(!t||!t.IS_ITERATOR),A=!(!t||!t.INTERRUPTED),g=functionBindContext(r,d,1+h+A),stop=function(e){return a&&iteratorClose(a,"normal",e),new Result(!0,e)},callFn=function(e){return h?(anObject(e),A?g(e[0],e[1],stop):g(e[0],e[1])):A?g(e,stop):g(e)};if(y)a=e;else{if(!(i=getIteratorMethod(e)))throw TypeError(String(e)+" is not iterable");if(isArrayIteratorMethod(i)){for(s=0,c=toLength(e.length);c>s;s++)if((l=callFn(e[s]))&&l instanceof Result)return l;return new Result(!1)}a=getIterator(e,i)}for(u=a.next;!(p=u.call(a)).done;){try{l=callFn(p.value)}catch(e){iteratorClose(a,"throw",e)}if("object"==typeof l&&l&&l instanceof Result)return l}return new Result(!1)},ITERATOR$6=wellKnownSymbol("iterator"),SAFE_CLOSING=!1;try{var called=0,iteratorWithReturn={next:function(){return{done:!!called++}},return:function(){SAFE_CLOSING=!0}};iteratorWithReturn[ITERATOR$6]=function(){return this},Array.from(iteratorWithReturn,(function(){throw 2}))}catch(e){}var checkCorrectnessOfIteration=function(e,r){if(!r&&!SAFE_CLOSING)return!1;var t=!1;try{var a={};a[ITERATOR$6]=function(){return{next:function(){return{done:t=!0}}}},e(a)}catch(e){}return t},empty=[],construct=getBuiltIn("Reflect","construct"),constructorRegExp=/^\s*(?:class|function)\b/,exec=constructorRegExp.exec,INCORRECT_TO_STRING=!constructorRegExp.exec((function(){})),isConstructorModern=function(e){if(!isCallable(e))return!1;try{return construct(Object,empty,e),!0}catch(e){return!1}},isConstructorLegacy=function(e){if(!isCallable(e))return!1;switch(classof(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return INCORRECT_TO_STRING||!!exec.call(constructorRegExp,inspectSource(e))},isConstructor=!construct||fails((function(){var e;return isConstructorModern(isConstructorModern.call)||!isConstructorModern(Object)||!isConstructorModern((function(){e=!0}))||e}))?isConstructorLegacy:isConstructorModern,aConstructor=function(e){if(isConstructor(e))return e;throw TypeError(tryToString(e)+" is not a constructor")},SPECIES$5=wellKnownSymbol("species"),speciesConstructor=function(e,r){var t,a=anObject(e).constructor;return void 0===a||null==(t=anObject(a)[SPECIES$5])?r:aConstructor(t)},html=getBuiltIn("document","documentElement"),engineIsIos=/(?:ipad|iphone|ipod).*applewebkit/i.test(engineUserAgent),engineIsNode="process"==classofRaw(global$1.process),set$1=global$1.setImmediate,clear=global$1.clearImmediate,process$2=global$1.process,MessageChannel=global$1.MessageChannel,Dispatch=global$1.Dispatch,counter=0,queue={},ONREADYSTATECHANGE="onreadystatechange",location,defer,channel,port;try{location=global$1.location}catch(e){}var run=function(e){if(queue.hasOwnProperty(e)){var r=queue[e];delete queue[e],r()}},runner=function(e){return function(){run(e)}},listener=function(e){run(e.data)},post=function(e){global$1.postMessage(String(e),location.protocol+"//"+location.host)};set$1&&clear||(set$1=function setImmediate(e){for(var r=[],t=arguments.length,a=1;t>a;)r.push(arguments[a++]);return queue[++counter]=function(){(isCallable(e)?e:Function(e)).apply(void 0,r)},defer(counter),counter},clear=function clearImmediate(e){delete queue[e]},engineIsNode?defer=function(e){process$2.nextTick(runner(e))}:Dispatch&&Dispatch.now?defer=function(e){Dispatch.now(runner(e))}:MessageChannel&&!engineIsIos?(channel=new MessageChannel,port=channel.port2,channel.port1.onmessage=listener,defer=functionBindContext(port.postMessage,port,1)):global$1.addEventListener&&isCallable(global$1.postMessage)&&!global$1.importScripts&&location&&"file:"!==location.protocol&&!fails(post)?(defer=post,global$1.addEventListener("message",listener,!1)):defer=ONREADYSTATECHANGE in documentCreateElement("script")?function(e){html.appendChild(documentCreateElement("script"))[ONREADYSTATECHANGE]=function(){html.removeChild(this),run(e)}}:function(e){setTimeout(runner(e),0)});var task$1={set:set$1,clear:clear},engineIsIosPebble=/ipad|iphone|ipod/i.test(engineUserAgent)&&void 0!==global$1.Pebble,engineIsWebosWebkit=/web0s(?!.*chrome)/i.test(engineUserAgent),getOwnPropertyDescriptor$2=objectGetOwnPropertyDescriptor.f,macrotask=task$1.set,MutationObserver=global$1.MutationObserver||global$1.WebKitMutationObserver,document$2=global$1.document,process$1=global$1.process,Promise$1=global$1.Promise,queueMicrotaskDescriptor=getOwnPropertyDescriptor$2(global$1,"queueMicrotask"),queueMicrotask=queueMicrotaskDescriptor&&queueMicrotaskDescriptor.value,flush,head,last,notify$1,toggle,node,promise,then;queueMicrotask||(flush=function(){var e,r;for(engineIsNode&&(e=process$1.domain)&&e.exit();head;){r=head.fn,head=head.next;try{r()}catch(e){throw head?notify$1():last=void 0,e}}last=void 0,e&&e.enter()},engineIsIos||engineIsNode||engineIsWebosWebkit||!MutationObserver||!document$2?!engineIsIosPebble&&Promise$1&&Promise$1.resolve?(promise=Promise$1.resolve(void 0),promise.constructor=Promise$1,then=promise.then,notify$1=function(){then.call(promise,flush)}):notify$1=engineIsNode?function(){process$1.nextTick(flush)}:function(){macrotask.call(global$1,flush)}:(toggle=!0,node=document$2.createTextNode(""),new MutationObserver(flush).observe(node,{characterData:!0}),notify$1=function(){node.data=toggle=!toggle}));var microtask=queueMicrotask||function(e){var r={fn:e,next:void 0};last&&(last.next=r),head||(head=r,notify$1()),last=r},PromiseCapability=function(e){var r,t;this.promise=new e((function(e,a){if(void 0!==r||void 0!==t)throw TypeError("Bad Promise constructor");r=e,t=a})),this.resolve=aCallable(r),this.reject=aCallable(t)},f=function(e){return new PromiseCapability(e)},newPromiseCapability$1={f:f},promiseResolve=function(e,r){if(anObject(e),isObject(r)&&r.constructor===e)return r;var t=newPromiseCapability$1.f(e);return(0,t.resolve)(r),t.promise},hostReportErrors=function(e,r){var t=global$1.console;t&&t.error&&(1===arguments.length?t.error(e):t.error(e,r))},perform=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},engineIsBrowser="object"==typeof window,task=task$1.set,SPECIES$4=wellKnownSymbol("species"),PROMISE="Promise",getInternalState$4=internalState.get,setInternalState$5=internalState.set,getInternalPromiseState=internalState.getterFor(PROMISE),NativePromisePrototype=nativePromiseConstructor&&nativePromiseConstructor.prototype,PromiseConstructor=nativePromiseConstructor,PromiseConstructorPrototype=NativePromisePrototype,TypeError$1=global$1.TypeError,document$1=global$1.document,process=global$1.process,newPromiseCapability=newPromiseCapability$1.f,newGenericPromiseCapability=newPromiseCapability,DISPATCH_EVENT=!!(document$1&&document$1.createEvent&&global$1.dispatchEvent),NATIVE_REJECTION_EVENT=isCallable(global$1.PromiseRejectionEvent),UNHANDLED_REJECTION="unhandledrejection",REJECTION_HANDLED="rejectionhandled",PENDING=0,FULFILLED=1,REJECTED=2,HANDLED=1,UNHANDLED=2,SUBCLASSING=!1,Internal,OwnPromiseCapability,PromiseWrapper,nativeThen,FORCED$6=isForced_1(PROMISE,(function(){var e=inspectSource(PromiseConstructor),r=e!==String(PromiseConstructor);if(!r&&66===engineV8Version)return!0;if(engineV8Version>=51&&/native code/.test(e))return!1;var t=new PromiseConstructor((function(e){e(1)})),FakePromise=function(e){e((function(){}),(function(){}))};return(t.constructor={})[SPECIES$4]=FakePromise,!(SUBCLASSING=t.then((function(){}))instanceof FakePromise)||!r&&engineIsBrowser&&!NATIVE_REJECTION_EVENT})),INCORRECT_ITERATION=FORCED$6||!checkCorrectnessOfIteration((function(e){PromiseConstructor.all(e).catch((function(){}))})),isThenable=function(e){var r;return!(!isObject(e)||!isCallable(r=e.then))&&r},notify=function(e,r){if(!e.notified){e.notified=!0;var t=e.reactions;microtask((function(){for(var a=e.value,i=e.state==FULFILLED,s=0;t.length>s;){var c,l,u,p=t[s++],d=i?p.ok:p.fail,h=p.resolve,y=p.reject,A=p.domain;try{d?(i||(e.rejection===UNHANDLED&&onHandleUnhandled(e),e.rejection=HANDLED),!0===d?c=a:(A&&A.enter(),c=d(a),A&&(A.exit(),u=!0)),c===p.promise?y(TypeError$1("Promise-chain cycle")):(l=isThenable(c))?l.call(c,h,y):h(c)):y(a)}catch(e){A&&!u&&A.exit(),y(e)}}e.reactions=[],e.notified=!1,r&&!e.rejection&&onUnhandled(e)}))}},dispatchEvent=function(e,r,t){var a,i;DISPATCH_EVENT?((a=document$1.createEvent("Event")).promise=r,a.reason=t,a.initEvent(e,!1,!0),global$1.dispatchEvent(a)):a={promise:r,reason:t},!NATIVE_REJECTION_EVENT&&(i=global$1["on"+e])?i(a):e===UNHANDLED_REJECTION&&hostReportErrors("Unhandled promise rejection",t)},onUnhandled=function(e){task.call(global$1,(function(){var r,t=e.facade,a=e.value;if(isUnhandled(e)&&(r=perform((function(){engineIsNode?process.emit("unhandledRejection",a,t):dispatchEvent(UNHANDLED_REJECTION,t,a)})),e.rejection=engineIsNode||isUnhandled(e)?UNHANDLED:HANDLED,r.error))throw r.value}))},isUnhandled=function(e){return e.rejection!==HANDLED&&!e.parent},onHandleUnhandled=function(e){task.call(global$1,(function(){var r=e.facade;engineIsNode?process.emit("rejectionHandled",r):dispatchEvent(REJECTION_HANDLED,r,e.value)}))},bind=function(e,r,t){return function(a){e(r,a,t)}},internalReject=function(e,r,t){e.done||(e.done=!0,t&&(e=t),e.value=r,e.state=REJECTED,notify(e,!0))},internalResolve=function(e,r,t){if(!e.done){e.done=!0,t&&(e=t);try{if(e.facade===r)throw TypeError$1("Promise can't be resolved itself");var a=isThenable(r);a?microtask((function(){var t={done:!1};try{a.call(r,bind(internalResolve,t,e),bind(internalReject,t,e))}catch(r){internalReject(t,r,e)}})):(e.value=r,e.state=FULFILLED,notify(e,!1))}catch(r){internalReject({done:!1},r,e)}}};if(FORCED$6&&(PromiseConstructor=function Promise(e){anInstance(this,PromiseConstructor,PROMISE),aCallable(e),Internal.call(this);var r=getInternalState$4(this);try{e(bind(internalResolve,r),bind(internalReject,r))}catch(e){internalReject(r,e)}},PromiseConstructorPrototype=PromiseConstructor.prototype,Internal=function Promise(e){setInternalState$5(this,{type:PROMISE,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:PENDING,value:void 0})},Internal.prototype=redefineAll(PromiseConstructorPrototype,{then:function then(e,r){var t=getInternalPromiseState(this),a=newPromiseCapability(speciesConstructor(this,PromiseConstructor));return a.ok=!isCallable(e)||e,a.fail=isCallable(r)&&r,a.domain=engineIsNode?process.domain:void 0,t.parent=!0,t.reactions.push(a),t.state!=PENDING&&notify(t,!1),a.promise},catch:function(e){return this.then(void 0,e)}}),OwnPromiseCapability=function(){var e=new Internal,r=getInternalState$4(e);this.promise=e,this.resolve=bind(internalResolve,r),this.reject=bind(internalReject,r)},newPromiseCapability$1.f=newPromiseCapability=function(e){return e===PromiseConstructor||e===PromiseWrapper?new OwnPromiseCapability(e):newGenericPromiseCapability(e)},isCallable(nativePromiseConstructor)&&NativePromisePrototype!==Object.prototype)){nativeThen=NativePromisePrototype.then,SUBCLASSING||(redefine(NativePromisePrototype,"then",(function then(e,r){var t=this;return new PromiseConstructor((function(e,r){nativeThen.call(t,e,r)})).then(e,r)}),{unsafe:!0}),redefine(NativePromisePrototype,"catch",PromiseConstructorPrototype.catch,{unsafe:!0}));try{delete NativePromisePrototype.constructor}catch(e){}objectSetPrototypeOf&&objectSetPrototypeOf(NativePromisePrototype,PromiseConstructorPrototype)}_export({global:!0,wrap:!0,forced:FORCED$6},{Promise:PromiseConstructor}),setToStringTag(PromiseConstructor,PROMISE,!1),setSpecies(PROMISE),PromiseWrapper=getBuiltIn(PROMISE),_export({target:PROMISE,stat:!0,forced:FORCED$6},{reject:function reject(e){var r=newPromiseCapability(this);return r.reject.call(void 0,e),r.promise}}),_export({target:PROMISE,stat:!0,forced:FORCED$6},{resolve:function resolve(e){return promiseResolve(this,e)}}),_export({target:PROMISE,stat:!0,forced:INCORRECT_ITERATION},{all:function all(e){var r=this,t=newPromiseCapability(r),a=t.resolve,i=t.reject,s=perform((function(){var t=aCallable(r.resolve),s=[],c=0,l=1;iterate(e,(function(e){var u=c++,p=!1;s.push(void 0),l++,t.call(r,e).then((function(e){p||(p=!0,s[u]=e,--l||a(s))}),i)})),--l||a(s)}));return s.error&&i(s.value),t.promise},race:function race(e){var r=this,t=newPromiseCapability(r),a=t.reject,i=perform((function(){var i=aCallable(r.resolve);iterate(e,(function(e){i.call(r,e).then(t.resolve,a)}))}));return i.error&&a(i.value),t.promise}});var inheritIfRequired=function(e,r,t){var a,i;return objectSetPrototypeOf&&isCallable(a=r.constructor)&&a!==t&&isObject(i=a.prototype)&&i!==t.prototype&&objectSetPrototypeOf(e,i),e},objectKeys=Object.keys||function keys(e){return objectKeysInternal(e,enumBugKeys)},objectDefineProperties=descriptors?Object.defineProperties:function defineProperties(e,r){anObject(e);for(var t,a=objectKeys(r),i=a.length,s=0;i>s;)objectDefineProperty.f(e,t=a[s++],r[t]);return e},GT=">",LT="<",PROTOTYPE$1="prototype",SCRIPT="script",IE_PROTO$1=sharedKey("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(e){return LT+SCRIPT+GT+e+LT+"/"+SCRIPT+GT},NullProtoObjectViaActiveX=function(e){e.write(scriptTag("")),e.close();var r=e.parentWindow.Object;return e=null,r},NullProtoObjectViaIFrame=function(){var e,r=documentCreateElement("iframe"),t="java"+SCRIPT+":";return r.style.display="none",html.appendChild(r),r.src=String(t),(e=r.contentWindow.document).open(),e.write(scriptTag("document.F=Object")),e.close(),e.F},activeXDocument,NullProtoObject=function(){try{activeXDocument=new ActiveXObject("htmlfile")}catch(e){}NullProtoObject="undefined"!=typeof document?document.domain&&activeXDocument?NullProtoObjectViaActiveX(activeXDocument):NullProtoObjectViaIFrame():NullProtoObjectViaActiveX(activeXDocument);for(var e=enumBugKeys.length;e--;)delete NullProtoObject[PROTOTYPE$1][enumBugKeys[e]];return NullProtoObject()};hiddenKeys$1[IE_PROTO$1]=!0;var objectCreate=Object.create||function create(e,r){var t;return null!==e?(EmptyConstructor[PROTOTYPE$1]=anObject(e),t=new EmptyConstructor,EmptyConstructor[PROTOTYPE$1]=null,t[IE_PROTO$1]=e):t=NullProtoObject(),void 0===r?t:objectDefineProperties(t,r)},toString=function(e){if("Symbol"===classof(e))throw TypeError("Cannot convert a Symbol value to a string");return String(e)},whitespaces="\t\n\v\f\r                　\u2028\u2029\ufeff",whitespace="["+whitespaces+"]",ltrim=RegExp("^"+whitespace+whitespace+"*"),rtrim=RegExp(whitespace+whitespace+"*$"),createMethod$4=function(e){return function(r){var t=toString(requireObjectCoercible(r));return 1&e&&(t=t.replace(ltrim,"")),2&e&&(t=t.replace(rtrim,"")),t}},stringTrim={start:createMethod$4(1),end:createMethod$4(2),trim:createMethod$4(3)},getOwnPropertyNames$1=objectGetOwnPropertyNames.f,getOwnPropertyDescriptor$1=objectGetOwnPropertyDescriptor.f,defineProperty$4=objectDefineProperty.f,trim=stringTrim.trim,NUMBER="Number",NativeNumber=global$1[NUMBER],NumberPrototype=NativeNumber.prototype,BROKEN_CLASSOF=classofRaw(objectCreate(NumberPrototype))==NUMBER,toNumber=function(e){if(isSymbol(e))throw TypeError("Cannot convert a Symbol value to a number");var r,t,a,i,s,c,l,u,p=toPrimitive(e,"number");if("string"==typeof p&&p.length>2)if(43===(r=(p=trim(p)).charCodeAt(0))||45===r){if(88===(t=p.charCodeAt(2))||120===t)return NaN}else if(48===r){switch(p.charCodeAt(1)){case 66:case 98:a=2,i=49;break;case 79:case 111:a=8,i=55;break;default:return+p}for(c=(s=p.slice(2)).length,l=0;l<c;l++)if((u=s.charCodeAt(l))<48||u>i)return NaN;return parseInt(s,a)}return+p};if(isForced_1(NUMBER,!NativeNumber(" 0o1")||!NativeNumber("0b1")||NativeNumber("+0x1"))){for(var NumberWrapper=function Number(e){var r=arguments.length<1?0:e,t=this;return t instanceof NumberWrapper&&(BROKEN_CLASSOF?fails((function(){NumberPrototype.valueOf.call(t)})):classofRaw(t)!=NUMBER)?inheritIfRequired(new NativeNumber(toNumber(r)),t,NumberWrapper):toNumber(r)},keys$1=descriptors?getOwnPropertyNames$1(NativeNumber):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),j$1=0,key$1;keys$1.length>j$1;j$1++)has$1(NativeNumber,key$1=keys$1[j$1])&&!has$1(NumberWrapper,key$1)&&defineProperty$4(NumberWrapper,key$1,getOwnPropertyDescriptor$1(NativeNumber,key$1));NumberWrapper.prototype=NumberPrototype,NumberPrototype.constructor=NumberWrapper,redefine(global$1,NUMBER,NumberWrapper)}var FUNCTION_NAME_EXISTS=functionName.EXISTS,defineProperty$3=objectDefineProperty.f,FunctionPrototype=Function.prototype,FunctionPrototypeToString=FunctionPrototype.toString,nameRE=/^\s*function ([^ (]*)/,NAME$1="name";descriptors&&!FUNCTION_NAME_EXISTS&&defineProperty$3(FunctionPrototype,NAME$1,{configurable:!0,get:function(){try{return FunctionPrototypeToString.call(this).match(nameRE)[1]}catch(e){return""}}}),_export({global:!0},{globalThis:global$1});var $assign=Object.assign,defineProperty$2=Object.defineProperty,objectAssign=!$assign||fails((function(){if(descriptors&&1!==$assign({b:1},$assign(defineProperty$2({},"a",{enumerable:!0,get:function(){defineProperty$2(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},r={},t=Symbol();return e[t]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),7!=$assign({},e)[t]||"abcdefghijklmnopqrst"!=objectKeys($assign({},r)).join("")}))?function assign(e,r){for(var t=toObject(e),a=arguments.length,i=1,s=objectGetOwnPropertySymbols.f,c=objectPropertyIsEnumerable.f;a>i;)for(var l,u=indexedObject(arguments[i++]),p=s?objectKeys(u).concat(s(u)):objectKeys(u),d=p.length,h=0;d>h;)l=p[h++],descriptors&&!c.call(u,l)||(t[l]=u[l]);return t}:$assign;_export({target:"Object",stat:!0,forced:Object.assign!==objectAssign},{assign:objectAssign});var path=global$1;path.Object.assign;var regexpFlags=function(){var e=anObject(this),r="";return e.global&&(r+="g"),e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.dotAll&&(r+="s"),e.unicode&&(r+="u"),e.sticky&&(r+="y"),r},$RegExp$2=global$1.RegExp,UNSUPPORTED_Y$2=fails((function(){var e=$RegExp$2("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET=fails((function(){var e=$RegExp$2("^r","gy");return e.lastIndex=2,null!=e.exec("str")})),regexpStickyHelpers={UNSUPPORTED_Y:UNSUPPORTED_Y$2,BROKEN_CARET:BROKEN_CARET},$RegExp$1=global$1.RegExp,regexpUnsupportedDotAll=fails((function(){var e=$RegExp$1(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),$RegExp=global$1.RegExp,regexpUnsupportedNcg=fails((function(){var e=$RegExp("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),getInternalState$3=internalState.get,nativeExec=RegExp.prototype.exec,nativeReplace=shared("native-string-replace",String.prototype.replace),patchedExec=nativeExec,UPDATES_LAST_INDEX_WRONG=(re1=/a/,re2=/b*/g,nativeExec.call(re1,"a"),nativeExec.call(re2,"a"),0!==re1.lastIndex||0!==re2.lastIndex),re1,re2,UNSUPPORTED_Y$1=regexpStickyHelpers.UNSUPPORTED_Y||regexpStickyHelpers.BROKEN_CARET,NPCG_INCLUDED=void 0!==/()??/.exec("")[1],PATCH=UPDATES_LAST_INDEX_WRONG||NPCG_INCLUDED||UNSUPPORTED_Y$1||regexpUnsupportedDotAll||regexpUnsupportedNcg;PATCH&&(patchedExec=function exec(e){var r,t,a,i,s,c,l,u=this,p=getInternalState$3(u),d=toString(e),h=p.raw;if(h)return h.lastIndex=u.lastIndex,r=patchedExec.call(h,d),u.lastIndex=h.lastIndex,r;var y=p.groups,A=UNSUPPORTED_Y$1&&u.sticky,g=regexpFlags.call(u),E=u.source,v=0,m=d;if(A&&(-1===(g=g.replace("y","")).indexOf("g")&&(g+="g"),m=d.slice(u.lastIndex),u.lastIndex>0&&(!u.multiline||u.multiline&&"\n"!==d.charAt(u.lastIndex-1))&&(E="(?: "+E+")",m=" "+m,v++),t=new RegExp("^(?:"+E+")",g)),NPCG_INCLUDED&&(t=new RegExp("^"+E+"$(?!\\s)",g)),UPDATES_LAST_INDEX_WRONG&&(a=u.lastIndex),i=nativeExec.call(A?t:u,m),A?i?(i.input=i.input.slice(v),i[0]=i[0].slice(v),i.index=u.lastIndex,u.lastIndex+=i[0].length):u.lastIndex=0:UPDATES_LAST_INDEX_WRONG&&i&&(u.lastIndex=u.global?i.index+i[0].length:a),NPCG_INCLUDED&&i&&i.length>1&&nativeReplace.call(i[0],t,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)})),i&&y)for(i.groups=c=objectCreate(null),s=0;s<y.length;s++)c[(l=y[s])[0]]=i[l[1]];return i});var regexpExec=patchedExec;_export({target:"RegExp",proto:!0,forced:/./.exec!==regexpExec},{exec:regexpExec});var SPECIES$3=wellKnownSymbol("species"),RegExpPrototype$1=RegExp.prototype,fixRegexpWellKnownSymbolLogic=function(e,r,t,a){var i=wellKnownSymbol(e),s=!fails((function(){var r={};return r[i]=function(){return 7},7!=""[e](r)})),c=s&&!fails((function(){var r=!1,t=/a/;return"split"===e&&((t={}).constructor={},t.constructor[SPECIES$3]=function(){return t},t.flags="",t[i]=/./[i]),t.exec=function(){return r=!0,null},t[i](""),!r}));if(!s||!c||t){var l=/./[i],u=r(i,""[e],(function(e,r,t,a,i){var c=r.exec;return c===regexpExec||c===RegExpPrototype$1.exec?s&&!i?{done:!0,value:l.call(r,t,a)}:{done:!0,value:e.call(t,r,a)}:{done:!1}}));redefine(String.prototype,e,u[0]),redefine(RegExpPrototype$1,i,u[1])}a&&createNonEnumerableProperty(RegExpPrototype$1[i],"sham",!0)},MATCH$1=wellKnownSymbol("match"),isRegexp=function(e){var r;return isObject(e)&&(void 0!==(r=e[MATCH$1])?!!r:"RegExp"==classofRaw(e))},createMethod$3=function(e){return function(r,t){var a,i,s=toString(requireObjectCoercible(r)),c=toInteger(t),l=s.length;return c<0||c>=l?e?"":void 0:(a=s.charCodeAt(c))<55296||a>56319||c+1===l||(i=s.charCodeAt(c+1))<56320||i>57343?e?s.charAt(c):a:e?s.slice(c,c+2):i-56320+(a-55296<<10)+65536}},stringMultibyte={codeAt:createMethod$3(!1),charAt:createMethod$3(!0)},charAt$1=stringMultibyte.charAt,advanceStringIndex=function(e,r,t){return r+(t?charAt$1(e,r).length:1)},regexpExecAbstract=function(e,r){var t=e.exec;if(isCallable(t)){var a=t.call(e,r);return null!==a&&anObject(a),a}if("RegExp"===classofRaw(e))return regexpExec.call(e,r);throw TypeError("RegExp#exec called on incompatible receiver")},UNSUPPORTED_Y=regexpStickyHelpers.UNSUPPORTED_Y,arrayPush=[].push,min$4=Math.min,MAX_UINT32=4294967295,SPLIT_WORKS_WITH_OVERWRITTEN_EXEC=!fails((function(){var e=/(?:)/,r=e.exec;e.exec=function(){return r.apply(this,arguments)};var t="ab".split(e);return 2!==t.length||"a"!==t[0]||"b"!==t[1]}));fixRegexpWellKnownSymbolLogic("split",(function(e,r,t){var a;return a="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,t){var a=toString(requireObjectCoercible(this)),i=void 0===t?MAX_UINT32:t>>>0;if(0===i)return[];if(void 0===e)return[a];if(!isRegexp(e))return r.call(a,e,i);for(var s,c,l,u=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),d=0,h=new RegExp(e.source,p+"g");(s=regexpExec.call(h,a))&&!((c=h.lastIndex)>d&&(u.push(a.slice(d,s.index)),s.length>1&&s.index<a.length&&arrayPush.apply(u,s.slice(1)),l=s[0].length,d=c,u.length>=i));)h.lastIndex===s.index&&h.lastIndex++;return d===a.length?!l&&h.test("")||u.push(""):u.push(a.slice(d)),u.length>i?u.slice(0,i):u}:"0".split(void 0,0).length?function(e,t){return void 0===e&&0===t?[]:r.call(this,e,t)}:r,[function split(r,t){var i=requireObjectCoercible(this),s=null==r?void 0:getMethod(r,e);return s?s.call(r,i,t):a.call(toString(i),r,t)},function(e,i){var s=anObject(this),c=toString(e),l=t(a,s,c,i,a!==r);if(l.done)return l.value;var u=speciesConstructor(s,RegExp),p=s.unicode,d=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(UNSUPPORTED_Y?"g":"y"),h=new u(UNSUPPORTED_Y?"^(?:"+s.source+")":s,d),y=void 0===i?MAX_UINT32:i>>>0;if(0===y)return[];if(0===c.length)return null===regexpExecAbstract(h,c)?[c]:[];for(var A=0,g=0,E=[];g<c.length;){h.lastIndex=UNSUPPORTED_Y?0:g;var v,m=regexpExecAbstract(h,UNSUPPORTED_Y?c.slice(g):c);if(null===m||(v=min$4(toLength(h.lastIndex+(UNSUPPORTED_Y?g:0)),c.length))===A)g=advanceStringIndex(c,g,p);else{if(E.push(c.slice(A,g)),E.length===y)return E;for(var I=1;I<=m.length-1;I++)if(E.push(m[I]),E.length===y)return E;g=A=v}}return E.push(c.slice(A)),E}]}),!SPLIT_WORKS_WITH_OVERWRITTEN_EXEC,UNSUPPORTED_Y),fixRegexpWellKnownSymbolLogic("match",(function(e,r,t){return[function match(r){var t=requireObjectCoercible(this),a=null==r?void 0:getMethod(r,e);return a?a.call(r,t):new RegExp(r)[e](toString(t))},function(e){var a=anObject(this),i=toString(e),s=t(r,a,i);if(s.done)return s.value;if(!a.global)return regexpExecAbstract(a,i);var c=a.unicode;a.lastIndex=0;for(var l,u=[],p=0;null!==(l=regexpExecAbstract(a,i));){var d=toString(l[0]);u[p]=d,""===d&&(a.lastIndex=advanceStringIndex(i,toLength(a.lastIndex),c)),p++}return 0===p?null:u}]}));var UNSCOPABLES=wellKnownSymbol("unscopables"),ArrayPrototype=Array.prototype;null==ArrayPrototype[UNSCOPABLES]&&objectDefineProperty.f(ArrayPrototype,UNSCOPABLES,{configurable:!0,value:objectCreate(null)});var addToUnscopables=function(e){ArrayPrototype[UNSCOPABLES][e]=!0},correctPrototypeGetter=!fails((function(){function F(){}return F.prototype.constructor=null,Object.getPrototypeOf(new F)!==F.prototype})),IE_PROTO=sharedKey("IE_PROTO"),ObjectPrototype$2=Object.prototype,objectGetPrototypeOf=correctPrototypeGetter?Object.getPrototypeOf:function(e){var r=toObject(e);if(has$1(r,IE_PROTO))return r[IE_PROTO];var t=r.constructor;return isCallable(t)&&r instanceof t?t.prototype:r instanceof Object?ObjectPrototype$2:null},ITERATOR$5=wellKnownSymbol("iterator"),BUGGY_SAFARI_ITERATORS$1=!1,IteratorPrototype$2,PrototypeOfArrayIteratorPrototype,arrayIterator;[].keys&&(arrayIterator=[].keys(),"next"in arrayIterator?(PrototypeOfArrayIteratorPrototype=objectGetPrototypeOf(objectGetPrototypeOf(arrayIterator)),PrototypeOfArrayIteratorPrototype!==Object.prototype&&(IteratorPrototype$2=PrototypeOfArrayIteratorPrototype)):BUGGY_SAFARI_ITERATORS$1=!0);var NEW_ITERATOR_PROTOTYPE=null==IteratorPrototype$2||fails((function(){var e={};return IteratorPrototype$2[ITERATOR$5].call(e)!==e}));NEW_ITERATOR_PROTOTYPE&&(IteratorPrototype$2={}),isCallable(IteratorPrototype$2[ITERATOR$5])||redefine(IteratorPrototype$2,ITERATOR$5,(function(){return this}));var iteratorsCore={IteratorPrototype:IteratorPrototype$2,BUGGY_SAFARI_ITERATORS:BUGGY_SAFARI_ITERATORS$1},IteratorPrototype$1=iteratorsCore.IteratorPrototype,returnThis$1=function(){return this},createIteratorConstructor=function(e,r,t){var a=r+" Iterator";return e.prototype=objectCreate(IteratorPrototype$1,{next:createPropertyDescriptor(1,t)}),setToStringTag(e,a,!1),iterators[a]=returnThis$1,e},PROPER_FUNCTION_NAME$3=functionName.PROPER,CONFIGURABLE_FUNCTION_NAME$1=functionName.CONFIGURABLE,IteratorPrototype=iteratorsCore.IteratorPrototype,BUGGY_SAFARI_ITERATORS=iteratorsCore.BUGGY_SAFARI_ITERATORS,ITERATOR$4=wellKnownSymbol("iterator"),KEYS="keys",VALUES="values",ENTRIES="entries",returnThis=function(){return this},defineIterator=function(e,r,t,a,i,s,c){createIteratorConstructor(t,r,a);var l,u,p,getIterationMethod=function(e){if(e===i&&g)return g;if(!BUGGY_SAFARI_ITERATORS&&e in y)return y[e];switch(e){case KEYS:return function keys(){return new t(this,e)};case VALUES:return function values(){return new t(this,e)};case ENTRIES:return function entries(){return new t(this,e)}}return function(){return new t(this)}},d=r+" Iterator",h=!1,y=e.prototype,A=y[ITERATOR$4]||y["@@iterator"]||i&&y[i],g=!BUGGY_SAFARI_ITERATORS&&A||getIterationMethod(i),E="Array"==r&&y.entries||A;if(E&&(l=objectGetPrototypeOf(E.call(new e)))!==Object.prototype&&l.next&&(objectGetPrototypeOf(l)!==IteratorPrototype&&(objectSetPrototypeOf?objectSetPrototypeOf(l,IteratorPrototype):isCallable(l[ITERATOR$4])||redefine(l,ITERATOR$4,returnThis)),setToStringTag(l,d,!0)),PROPER_FUNCTION_NAME$3&&i==VALUES&&A&&A.name!==VALUES&&(CONFIGURABLE_FUNCTION_NAME$1?createNonEnumerableProperty(y,"name",VALUES):(h=!0,g=function values(){return A.call(this)})),i)if(u={values:getIterationMethod(VALUES),keys:s?g:getIterationMethod(KEYS),entries:getIterationMethod(ENTRIES)},c)for(p in u)(BUGGY_SAFARI_ITERATORS||h||!(p in y))&&redefine(y,p,u[p]);else _export({target:r,proto:!0,forced:BUGGY_SAFARI_ITERATORS||h},u);return y[ITERATOR$4]!==g&&redefine(y,ITERATOR$4,g,{name:i}),iterators[r]=g,u},ARRAY_ITERATOR="Array Iterator",setInternalState$4=internalState.set,getInternalState$2=internalState.getterFor(ARRAY_ITERATOR),es_array_iterator=defineIterator(Array,"Array",(function(e,r){setInternalState$4(this,{type:ARRAY_ITERATOR,target:toIndexedObject(e),index:0,kind:r})}),(function(){var e=getInternalState$2(this),r=e.target,t=e.kind,a=e.index++;return!r||a>=r.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==t?{value:a,done:!1}:"values"==t?{value:r[a],done:!1}:{value:[a,r[a]],done:!1}}),"values");iterators.Arguments=iterators.Array,addToUnscopables("keys"),addToUnscopables("values"),addToUnscopables("entries");var arrayBufferNative="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,defineProperty$1=objectDefineProperty.f,Int8Array$3=global$1.Int8Array,Int8ArrayPrototype=Int8Array$3&&Int8Array$3.prototype,Uint8ClampedArray=global$1.Uint8ClampedArray,Uint8ClampedArrayPrototype=Uint8ClampedArray&&Uint8ClampedArray.prototype,TypedArray=Int8Array$3&&objectGetPrototypeOf(Int8Array$3),TypedArrayPrototype=Int8ArrayPrototype&&objectGetPrototypeOf(Int8ArrayPrototype),ObjectPrototype$1=Object.prototype,isPrototypeOf=ObjectPrototype$1.isPrototypeOf,TO_STRING_TAG$1=wellKnownSymbol("toStringTag"),TYPED_ARRAY_TAG=uid("TYPED_ARRAY_TAG"),TYPED_ARRAY_CONSTRUCTOR$1=uid("TYPED_ARRAY_CONSTRUCTOR"),NATIVE_ARRAY_BUFFER_VIEWS$1=arrayBufferNative&&!!objectSetPrototypeOf&&"Opera"!==classof(global$1.opera),TYPED_ARRAY_TAG_REQIRED=!1,NAME,Constructor,Prototype,TypedArrayConstructorsList={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},BigIntArrayConstructorsList={BigInt64Array:8,BigUint64Array:8},isView=function isView(e){if(!isObject(e))return!1;var r=classof(e);return"DataView"===r||has$1(TypedArrayConstructorsList,r)||has$1(BigIntArrayConstructorsList,r)},isTypedArray=function(e){if(!isObject(e))return!1;var r=classof(e);return has$1(TypedArrayConstructorsList,r)||has$1(BigIntArrayConstructorsList,r)},aTypedArray$m=function(e){if(isTypedArray(e))return e;throw TypeError("Target is not a typed array")},aTypedArrayConstructor$2=function(e){if(isCallable(e)&&(!objectSetPrototypeOf||isPrototypeOf.call(TypedArray,e)))return e;throw TypeError(tryToString(e)+" is not a typed array constructor")},exportTypedArrayMethod$n=function(e,r,t){if(descriptors){if(t)for(var a in TypedArrayConstructorsList){var i=global$1[a];if(i&&has$1(i.prototype,e))try{delete i.prototype[e]}catch(e){}}TypedArrayPrototype[e]&&!t||redefine(TypedArrayPrototype,e,t?r:NATIVE_ARRAY_BUFFER_VIEWS$1&&Int8ArrayPrototype[e]||r)}},exportTypedArrayStaticMethod=function(e,r,t){var a,i;if(descriptors){if(objectSetPrototypeOf){if(t)for(a in TypedArrayConstructorsList)if((i=global$1[a])&&has$1(i,e))try{delete i[e]}catch(e){}if(TypedArray[e]&&!t)return;try{return redefine(TypedArray,e,t?r:NATIVE_ARRAY_BUFFER_VIEWS$1&&TypedArray[e]||r)}catch(e){}}for(a in TypedArrayConstructorsList)!(i=global$1[a])||i[e]&&!t||redefine(i,e,r)}};for(NAME in TypedArrayConstructorsList)Constructor=global$1[NAME],Prototype=Constructor&&Constructor.prototype,Prototype?createNonEnumerableProperty(Prototype,TYPED_ARRAY_CONSTRUCTOR$1,Constructor):NATIVE_ARRAY_BUFFER_VIEWS$1=!1;for(NAME in BigIntArrayConstructorsList)Constructor=global$1[NAME],Prototype=Constructor&&Constructor.prototype,Prototype&&createNonEnumerableProperty(Prototype,TYPED_ARRAY_CONSTRUCTOR$1,Constructor);if((!NATIVE_ARRAY_BUFFER_VIEWS$1||!isCallable(TypedArray)||TypedArray===Function.prototype)&&(TypedArray=function TypedArray(){throw TypeError("Incorrect invocation")},NATIVE_ARRAY_BUFFER_VIEWS$1))for(NAME in TypedArrayConstructorsList)global$1[NAME]&&objectSetPrototypeOf(global$1[NAME],TypedArray);if((!NATIVE_ARRAY_BUFFER_VIEWS$1||!TypedArrayPrototype||TypedArrayPrototype===ObjectPrototype$1)&&(TypedArrayPrototype=TypedArray.prototype,NATIVE_ARRAY_BUFFER_VIEWS$1))for(NAME in TypedArrayConstructorsList)global$1[NAME]&&objectSetPrototypeOf(global$1[NAME].prototype,TypedArrayPrototype);if(NATIVE_ARRAY_BUFFER_VIEWS$1&&objectGetPrototypeOf(Uint8ClampedArrayPrototype)!==TypedArrayPrototype&&objectSetPrototypeOf(Uint8ClampedArrayPrototype,TypedArrayPrototype),descriptors&&!has$1(TypedArrayPrototype,TO_STRING_TAG$1))for(NAME in TYPED_ARRAY_TAG_REQIRED=!0,defineProperty$1(TypedArrayPrototype,TO_STRING_TAG$1,{get:function(){return isObject(this)?this[TYPED_ARRAY_TAG]:void 0}}),TypedArrayConstructorsList)global$1[NAME]&&createNonEnumerableProperty(global$1[NAME],TYPED_ARRAY_TAG,NAME);var arrayBufferViewCore={NATIVE_ARRAY_BUFFER_VIEWS:NATIVE_ARRAY_BUFFER_VIEWS$1,TYPED_ARRAY_CONSTRUCTOR:TYPED_ARRAY_CONSTRUCTOR$1,TYPED_ARRAY_TAG:TYPED_ARRAY_TAG_REQIRED&&TYPED_ARRAY_TAG,aTypedArray:aTypedArray$m,aTypedArrayConstructor:aTypedArrayConstructor$2,exportTypedArrayMethod:exportTypedArrayMethod$n,exportTypedArrayStaticMethod:exportTypedArrayStaticMethod,isView:isView,isTypedArray:isTypedArray,TypedArray:TypedArray,TypedArrayPrototype:TypedArrayPrototype},NATIVE_ARRAY_BUFFER_VIEWS=arrayBufferViewCore.NATIVE_ARRAY_BUFFER_VIEWS,ArrayBuffer$1=global$1.ArrayBuffer,Int8Array$2=global$1.Int8Array,typedArrayConstructorsRequireWrappers=!NATIVE_ARRAY_BUFFER_VIEWS||!fails((function(){Int8Array$2(1)}))||!fails((function(){new Int8Array$2(-1)}))||!checkCorrectnessOfIteration((function(e){new Int8Array$2,new Int8Array$2(null),new Int8Array$2(1.5),new Int8Array$2(e)}),!0)||fails((function(){return 1!==new Int8Array$2(new ArrayBuffer$1(2),1,void 0).length})),toIndex=function(e){if(void 0===e)return 0;var r=toInteger(e),t=toLength(r);if(r!==t)throw RangeError("Wrong length or index");return t},abs=Math.abs,pow$1=Math.pow,floor$6=Math.floor,log=Math.log,LN2=Math.LN2,pack=function(e,r,t){var a,i,s,c=new Array(t),l=8*t-r-1,u=(1<<l)-1,p=u>>1,d=23===r?pow$1(2,-24)-pow$1(2,-77):0,h=e<0||0===e&&1/e<0?1:0,y=0;for((e=abs(e))!=e||e===1/0?(i=e!=e?1:0,a=u):(a=floor$6(log(e)/LN2),e*(s=pow$1(2,-a))<1&&(a--,s*=2),(e+=a+p>=1?d/s:d*pow$1(2,1-p))*s>=2&&(a++,s/=2),a+p>=u?(i=0,a=u):a+p>=1?(i=(e*s-1)*pow$1(2,r),a+=p):(i=e*pow$1(2,p-1)*pow$1(2,r),a=0));r>=8;c[y++]=255&i,i/=256,r-=8);for(a=a<<r|i,l+=r;l>0;c[y++]=255&a,a/=256,l-=8);return c[--y]|=128*h,c},unpack=function(e,r){var t,a=e.length,i=8*a-r-1,s=(1<<i)-1,c=s>>1,l=i-7,u=a-1,p=e[u--],d=127&p;for(p>>=7;l>0;d=256*d+e[u],u--,l-=8);for(t=d&(1<<-l)-1,d>>=-l,l+=r;l>0;t=256*t+e[u],u--,l-=8);if(0===d)d=1-c;else{if(d===s)return t?NaN:p?-1/0:1/0;t+=pow$1(2,r),d-=c}return(p?-1:1)*t*pow$1(2,d-r)},ieee754={pack:pack,unpack:unpack},arrayFill=function fill(e){for(var r=toObject(this),t=toLength(r.length),a=arguments.length,i=toAbsoluteIndex(a>1?arguments[1]:void 0,t),s=a>2?arguments[2]:void 0,c=void 0===s?t:toAbsoluteIndex(s,t);c>i;)r[i++]=e;return r},getOwnPropertyNames=objectGetOwnPropertyNames.f,defineProperty=objectDefineProperty.f,PROPER_FUNCTION_NAME$2=functionName.PROPER,CONFIGURABLE_FUNCTION_NAME=functionName.CONFIGURABLE,getInternalState$1=internalState.get,setInternalState$3=internalState.set,ARRAY_BUFFER="ArrayBuffer",DATA_VIEW="DataView",PROTOTYPE="prototype",WRONG_LENGTH="Wrong length",WRONG_INDEX="Wrong index",NativeArrayBuffer=global$1[ARRAY_BUFFER],$ArrayBuffer=NativeArrayBuffer,$DataView=global$1[DATA_VIEW],$DataViewPrototype=$DataView&&$DataView[PROTOTYPE],ObjectPrototype=Object.prototype,RangeError$1=global$1.RangeError,packIEEE754=ieee754.pack,unpackIEEE754=ieee754.unpack,packInt8=function(e){return[255&e]},packInt16=function(e){return[255&e,e>>8&255]},packInt32=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},unpackInt32=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},packFloat32=function(e){return packIEEE754(e,23,4)},packFloat64=function(e){return packIEEE754(e,52,8)},addGetter=function(e,r){defineProperty(e[PROTOTYPE],r,{get:function(){return getInternalState$1(this)[r]}})},get=function(e,r,t,a){var i=toIndex(t),s=getInternalState$1(e);if(i+r>s.byteLength)throw RangeError$1(WRONG_INDEX);var c=getInternalState$1(s.buffer).bytes,l=i+s.byteOffset,u=c.slice(l,l+r);return a?u:u.reverse()},set=function(e,r,t,a,i,s){var c=toIndex(t),l=getInternalState$1(e);if(c+r>l.byteLength)throw RangeError$1(WRONG_INDEX);for(var u=getInternalState$1(l.buffer).bytes,p=c+l.byteOffset,d=a(+i),h=0;h<r;h++)u[p+h]=d[s?h:r-h-1]};if(arrayBufferNative){var INCORRECT_ARRAY_BUFFER_NAME=PROPER_FUNCTION_NAME$2&&NativeArrayBuffer.name!==ARRAY_BUFFER;if(fails((function(){NativeArrayBuffer(1)}))&&fails((function(){new NativeArrayBuffer(-1)}))&&!fails((function(){return new NativeArrayBuffer,new NativeArrayBuffer(1.5),new NativeArrayBuffer(NaN),INCORRECT_ARRAY_BUFFER_NAME&&!CONFIGURABLE_FUNCTION_NAME})))INCORRECT_ARRAY_BUFFER_NAME&&CONFIGURABLE_FUNCTION_NAME&&createNonEnumerableProperty(NativeArrayBuffer,"name",ARRAY_BUFFER);else{$ArrayBuffer=function ArrayBuffer(e){return anInstance(this,$ArrayBuffer),new NativeArrayBuffer(toIndex(e))};for(var ArrayBufferPrototype=$ArrayBuffer[PROTOTYPE]=NativeArrayBuffer[PROTOTYPE],keys=getOwnPropertyNames(NativeArrayBuffer),j=0,key;keys.length>j;)(key=keys[j++])in $ArrayBuffer||createNonEnumerableProperty($ArrayBuffer,key,NativeArrayBuffer[key]);ArrayBufferPrototype.constructor=$ArrayBuffer}objectSetPrototypeOf&&objectGetPrototypeOf($DataViewPrototype)!==ObjectPrototype&&objectSetPrototypeOf($DataViewPrototype,ObjectPrototype);var testView=new $DataView(new $ArrayBuffer(2)),$setInt8=$DataViewPrototype.setInt8;testView.setInt8(0,2147483648),testView.setInt8(1,2147483649),!testView.getInt8(0)&&testView.getInt8(1)||redefineAll($DataViewPrototype,{setInt8:function setInt8(e,r){$setInt8.call(this,e,r<<24>>24)},setUint8:function setUint8(e,r){$setInt8.call(this,e,r<<24>>24)}},{unsafe:!0})}else $ArrayBuffer=function ArrayBuffer(e){anInstance(this,$ArrayBuffer,ARRAY_BUFFER);var r=toIndex(e);setInternalState$3(this,{bytes:arrayFill.call(new Array(r),0),byteLength:r}),descriptors||(this.byteLength=r)},$DataView=function DataView(e,r,t){anInstance(this,$DataView,DATA_VIEW),anInstance(e,$ArrayBuffer,DATA_VIEW);var a=getInternalState$1(e).byteLength,i=toInteger(r);if(i<0||i>a)throw RangeError$1("Wrong offset");if(i+(t=void 0===t?a-i:toLength(t))>a)throw RangeError$1(WRONG_LENGTH);setInternalState$3(this,{buffer:e,byteLength:t,byteOffset:i}),descriptors||(this.buffer=e,this.byteLength=t,this.byteOffset=i)},descriptors&&(addGetter($ArrayBuffer,"byteLength"),addGetter($DataView,"buffer"),addGetter($DataView,"byteLength"),addGetter($DataView,"byteOffset")),redefineAll($DataView[PROTOTYPE],{getInt8:function getInt8(e){return get(this,1,e)[0]<<24>>24},getUint8:function getUint8(e){return get(this,1,e)[0]},getInt16:function getInt16(e){var r=get(this,2,e,arguments.length>1?arguments[1]:void 0);return(r[1]<<8|r[0])<<16>>16},getUint16:function getUint16(e){var r=get(this,2,e,arguments.length>1?arguments[1]:void 0);return r[1]<<8|r[0]},getInt32:function getInt32(e){return unpackInt32(get(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function getUint32(e){return unpackInt32(get(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function getFloat32(e){return unpackIEEE754(get(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function getFloat64(e){return unpackIEEE754(get(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function setInt8(e,r){set(this,1,e,packInt8,r)},setUint8:function setUint8(e,r){set(this,1,e,packInt8,r)},setInt16:function setInt16(e,r){set(this,2,e,packInt16,r,arguments.length>2?arguments[2]:void 0)},setUint16:function setUint16(e,r){set(this,2,e,packInt16,r,arguments.length>2?arguments[2]:void 0)},setInt32:function setInt32(e,r){set(this,4,e,packInt32,r,arguments.length>2?arguments[2]:void 0)},setUint32:function setUint32(e,r){set(this,4,e,packInt32,r,arguments.length>2?arguments[2]:void 0)},setFloat32:function setFloat32(e,r){set(this,4,e,packFloat32,r,arguments.length>2?arguments[2]:void 0)},setFloat64:function setFloat64(e,r){set(this,8,e,packFloat64,r,arguments.length>2?arguments[2]:void 0)}});setToStringTag($ArrayBuffer,ARRAY_BUFFER),setToStringTag($DataView,DATA_VIEW);var arrayBuffer={ArrayBuffer:$ArrayBuffer,DataView:$DataView},floor$5=Math.floor,isInteger=function isInteger(e){return!isObject(e)&&isFinite(e)&&floor$5(e)===e},toPositiveInteger=function(e){var r=toInteger(e);if(r<0)throw RangeError("The argument can't be less than 0");return r},toOffset=function(e,r){var t=toPositiveInteger(e);if(t%r)throw RangeError("Wrong offset");return t},aTypedArrayConstructor$1=arrayBufferViewCore.aTypedArrayConstructor,typedArrayFrom=function from(e){var r,t,a,i,s,c,l=aConstructor(this),u=toObject(e),p=arguments.length,d=p>1?arguments[1]:void 0,h=void 0!==d,y=getIteratorMethod(u);if(y&&!isArrayIteratorMethod(y))for(c=(s=getIterator(u,y)).next,u=[];!(i=c.call(s)).done;)u.push(i.value);for(h&&p>2&&(d=functionBindContext(d,arguments[2],2)),t=toLength(u.length),a=new(aTypedArrayConstructor$1(l))(t),r=0;t>r;r++)a[r]=h?d(u[r],r):u[r];return a},isArray=Array.isArray||function isArray(e){return"Array"==classofRaw(e)},SPECIES$2=wellKnownSymbol("species"),arraySpeciesConstructor=function(e){var r;return isArray(e)&&(r=e.constructor,(isConstructor(r)&&(r===Array||isArray(r.prototype))||isObject(r)&&null===(r=r[SPECIES$2]))&&(r=void 0)),void 0===r?Array:r},arraySpeciesCreate=function(e,r){return new(arraySpeciesConstructor(e))(0===r?0:r)},push=[].push,createMethod$2=function(e){var r=1==e,t=2==e,a=3==e,i=4==e,s=6==e,c=7==e,l=5==e||s;return function(u,p,d,h){for(var y,A,g=toObject(u),E=indexedObject(g),v=functionBindContext(p,d,3),m=toLength(E.length),I=0,R=h||arraySpeciesCreate,P=r?R(u,m):t||c?R(u,0):void 0;m>I;I++)if((l||I in E)&&(A=v(y=E[I],I,g),e))if(r)P[I]=A;else if(A)switch(e){case 3:return!0;case 5:return y;case 6:return I;case 2:push.call(P,y)}else switch(e){case 4:return!1;case 7:push.call(P,y)}return s?-1:a||i?i:P}},arrayIteration={forEach:createMethod$2(0),map:createMethod$2(1),filter:createMethod$2(2),some:createMethod$2(3),every:createMethod$2(4),find:createMethod$2(5),findIndex:createMethod$2(6),filterReject:createMethod$2(7)},typedArrayConstructor=createCommonjsModule((function(e){var r=objectGetOwnPropertyNames.f,t=arrayIteration.forEach,a=internalState.get,i=internalState.set,s=objectDefineProperty.f,c=objectGetOwnPropertyDescriptor.f,l=Math.round,u=global$1.RangeError,p=arrayBuffer.ArrayBuffer,d=arrayBuffer.DataView,h=arrayBufferViewCore.NATIVE_ARRAY_BUFFER_VIEWS,y=arrayBufferViewCore.TYPED_ARRAY_CONSTRUCTOR,A=arrayBufferViewCore.TYPED_ARRAY_TAG,g=arrayBufferViewCore.TypedArray,E=arrayBufferViewCore.TypedArrayPrototype,v=arrayBufferViewCore.aTypedArrayConstructor,m=arrayBufferViewCore.isTypedArray,fromList=function(e,r){for(var t=0,a=r.length,i=new(v(e))(a);a>t;)i[t]=r[t++];return i},addGetter=function(e,r){s(e,r,{get:function(){return a(this)[r]}})},isArrayBuffer=function(e){var r;return e instanceof p||"ArrayBuffer"==(r=classof(e))||"SharedArrayBuffer"==r},isTypedArrayIndex=function(e,r){return m(e)&&!isSymbol(r)&&r in e&&isInteger(+r)&&r>=0},I=function getOwnPropertyDescriptor(e,r){return r=toPropertyKey(r),isTypedArrayIndex(e,r)?createPropertyDescriptor(2,e[r]):c(e,r)},R=function defineProperty(e,r,t){return r=toPropertyKey(r),!(isTypedArrayIndex(e,r)&&isObject(t)&&has$1(t,"value"))||has$1(t,"get")||has$1(t,"set")||t.configurable||has$1(t,"writable")&&!t.writable||has$1(t,"enumerable")&&!t.enumerable?s(e,r,t):(e[r]=t.value,e)};descriptors?(h||(objectGetOwnPropertyDescriptor.f=I,objectDefineProperty.f=R,addGetter(E,"buffer"),addGetter(E,"byteOffset"),addGetter(E,"byteLength"),addGetter(E,"length")),_export({target:"Object",stat:!0,forced:!h},{getOwnPropertyDescriptor:I,defineProperty:R}),e.exports=function(e,c,v){var I=e.match(/\d+$/)[0]/8,R=e+(v?"Clamped":"")+"Array",P="get"+e,S="set"+e,b=global$1[R],T=b,_=T&&T.prototype,O={},addElement=function(e,r){s(e,r,{get:function(){return function(e,r){var t=a(e);return t.view[P](r*I+t.byteOffset,!0)}(this,r)},set:function(e){return function(e,r,t){var i=a(e);v&&(t=(t=l(t))<0?0:t>255?255:255&t),i.view[S](r*I+i.byteOffset,t,!0)}(this,r,e)},enumerable:!0})};h?typedArrayConstructorsRequireWrappers&&(T=c((function(e,r,t,a){return anInstance(e,T,R),inheritIfRequired(isObject(r)?isArrayBuffer(r)?void 0!==a?new b(r,toOffset(t,I),a):void 0!==t?new b(r,toOffset(t,I)):new b(r):m(r)?fromList(T,r):typedArrayFrom.call(T,r):new b(toIndex(r)),e,T)})),objectSetPrototypeOf&&objectSetPrototypeOf(T,g),t(r(b),(function(e){e in T||createNonEnumerableProperty(T,e,b[e])})),T.prototype=_):(T=c((function(e,r,t,a){anInstance(e,T,R);var s,c,l,h=0,y=0;if(isObject(r)){if(!isArrayBuffer(r))return m(r)?fromList(T,r):typedArrayFrom.call(T,r);s=r,y=toOffset(t,I);var A=r.byteLength;if(void 0===a){if(A%I)throw u("Wrong length");if((c=A-y)<0)throw u("Wrong length")}else if((c=toLength(a)*I)+y>A)throw u("Wrong length");l=c/I}else l=toIndex(r),s=new p(c=l*I);for(i(e,{buffer:s,byteOffset:y,byteLength:c,length:l,view:new d(s)});h<l;)addElement(e,h++)})),objectSetPrototypeOf&&objectSetPrototypeOf(T,g),_=T.prototype=objectCreate(E)),_.constructor!==T&&createNonEnumerableProperty(_,"constructor",T),createNonEnumerableProperty(_,y,T),A&&createNonEnumerableProperty(_,A,R),O[R]=T,_export({global:!0,forced:T!=b,sham:!h},O),"BYTES_PER_ELEMENT"in T||createNonEnumerableProperty(T,"BYTES_PER_ELEMENT",I),"BYTES_PER_ELEMENT"in _||createNonEnumerableProperty(_,"BYTES_PER_ELEMENT",I),setSpecies(R)}):e.exports=function(){}}));typedArrayConstructor("Uint8",(function(e){return function Uint8Array(r,t,a){return e(this,r,t,a)}}));var min$3=Math.min,arrayCopyWithin=[].copyWithin||function copyWithin(e,r){var t=toObject(this),a=toLength(t.length),i=toAbsoluteIndex(e,a),s=toAbsoluteIndex(r,a),c=arguments.length>2?arguments[2]:void 0,l=min$3((void 0===c?a:toAbsoluteIndex(c,a))-s,a-i),u=1;for(s<i&&i<s+l&&(u=-1,s+=l-1,i+=l-1);l-- >0;)s in t?t[i]=t[s]:delete t[i],i+=u,s+=u;return t},aTypedArray$l=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$m=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$m("copyWithin",(function copyWithin(e,r){return arrayCopyWithin.call(aTypedArray$l(this),e,r,arguments.length>2?arguments[2]:void 0)}));var $every=arrayIteration.every,aTypedArray$k=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$l=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$l("every",(function every(e){return $every(aTypedArray$k(this),e,arguments.length>1?arguments[1]:void 0)}));var aTypedArray$j=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$k=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$k("fill",(function fill(e){return arrayFill.apply(aTypedArray$j(this),arguments)}));var arrayFromConstructorAndList=function(e,r){for(var t=0,a=r.length,i=new e(a);a>t;)i[t]=r[t++];return i},TYPED_ARRAY_CONSTRUCTOR=arrayBufferViewCore.TYPED_ARRAY_CONSTRUCTOR,aTypedArrayConstructor=arrayBufferViewCore.aTypedArrayConstructor,typedArraySpeciesConstructor=function(e){return aTypedArrayConstructor(speciesConstructor(e,e[TYPED_ARRAY_CONSTRUCTOR]))},typedArrayFromSpeciesAndList=function(e,r){return arrayFromConstructorAndList(typedArraySpeciesConstructor(e),r)},$filter$1=arrayIteration.filter,aTypedArray$i=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$j=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$j("filter",(function filter(e){var r=$filter$1(aTypedArray$i(this),e,arguments.length>1?arguments[1]:void 0);return typedArrayFromSpeciesAndList(this,r)}));var $find=arrayIteration.find,aTypedArray$h=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$i=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$i("find",(function find(e){return $find(aTypedArray$h(this),e,arguments.length>1?arguments[1]:void 0)}));var $findIndex=arrayIteration.findIndex,aTypedArray$g=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$h=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$h("findIndex",(function findIndex(e){return $findIndex(aTypedArray$g(this),e,arguments.length>1?arguments[1]:void 0)}));var $forEach$1=arrayIteration.forEach,aTypedArray$f=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$g=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$g("forEach",(function forEach(e){$forEach$1(aTypedArray$f(this),e,arguments.length>1?arguments[1]:void 0)}));var $includes$1=arrayIncludes.includes,aTypedArray$e=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$f=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$f("includes",(function includes(e){return $includes$1(aTypedArray$e(this),e,arguments.length>1?arguments[1]:void 0)}));var $indexOf=arrayIncludes.indexOf,aTypedArray$d=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$e=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$e("indexOf",(function indexOf(e){return $indexOf(aTypedArray$d(this),e,arguments.length>1?arguments[1]:void 0)}));var PROPER_FUNCTION_NAME$1=functionName.PROPER,ITERATOR$3=wellKnownSymbol("iterator"),Uint8Array$2=global$1.Uint8Array,arrayValues=es_array_iterator.values,arrayKeys=es_array_iterator.keys,arrayEntries=es_array_iterator.entries,aTypedArray$c=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$d=arrayBufferViewCore.exportTypedArrayMethod,nativeTypedArrayIterator=Uint8Array$2&&Uint8Array$2.prototype[ITERATOR$3],PROPER_ARRAY_VALUES_NAME=!!nativeTypedArrayIterator&&"values"===nativeTypedArrayIterator.name,typedArrayValues=function values(){return arrayValues.call(aTypedArray$c(this))};exportTypedArrayMethod$d("entries",(function entries(){return arrayEntries.call(aTypedArray$c(this))})),exportTypedArrayMethod$d("keys",(function keys(){return arrayKeys.call(aTypedArray$c(this))})),exportTypedArrayMethod$d("values",typedArrayValues,PROPER_FUNCTION_NAME$1&&!PROPER_ARRAY_VALUES_NAME),exportTypedArrayMethod$d(ITERATOR$3,typedArrayValues,PROPER_FUNCTION_NAME$1&&!PROPER_ARRAY_VALUES_NAME);var aTypedArray$b=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$c=arrayBufferViewCore.exportTypedArrayMethod,$join=[].join;exportTypedArrayMethod$c("join",(function join(e){return $join.apply(aTypedArray$b(this),arguments)}));var arrayMethodIsStrict=function(e,r){var t=[][e];return!!t&&fails((function(){t.call(null,r||function(){throw 1},1)}))},min$2=Math.min,$lastIndexOf=[].lastIndexOf,NEGATIVE_ZERO=!!$lastIndexOf&&1/[1].lastIndexOf(1,-0)<0,STRICT_METHOD$2=arrayMethodIsStrict("lastIndexOf"),FORCED$5=NEGATIVE_ZERO||!STRICT_METHOD$2,arrayLastIndexOf=FORCED$5?function lastIndexOf(e){if(NEGATIVE_ZERO)return $lastIndexOf.apply(this,arguments)||0;var r=toIndexedObject(this),t=toLength(r.length),a=t-1;for(arguments.length>1&&(a=min$2(a,toInteger(arguments[1]))),a<0&&(a=t+a);a>=0;a--)if(a in r&&r[a]===e)return a||0;return-1}:$lastIndexOf,aTypedArray$a=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$b=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$b("lastIndexOf",(function lastIndexOf(e){return arrayLastIndexOf.apply(aTypedArray$a(this),arguments)}));var $map=arrayIteration.map,aTypedArray$9=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$a=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$a("map",(function map(e){return $map(aTypedArray$9(this),e,arguments.length>1?arguments[1]:void 0,(function(e,r){return new(typedArraySpeciesConstructor(e))(r)}))}));var createMethod$1=function(e){return function(r,t,a,i){aCallable(t);var s=toObject(r),c=indexedObject(s),l=toLength(s.length),u=e?l-1:0,p=e?-1:1;if(a<2)for(;;){if(u in c){i=c[u],u+=p;break}if(u+=p,e?u<0:l<=u)throw TypeError("Reduce of empty array with no initial value")}for(;e?u>=0:l>u;u+=p)u in c&&(i=t(i,c[u],u,s));return i}},arrayReduce={left:createMethod$1(!1),right:createMethod$1(!0)},$reduce=arrayReduce.left,aTypedArray$8=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$9=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$9("reduce",(function reduce(e){return $reduce(aTypedArray$8(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var $reduceRight=arrayReduce.right,aTypedArray$7=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$8=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$8("reduceRight",(function reduceRight(e){return $reduceRight(aTypedArray$7(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var aTypedArray$6=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$7=arrayBufferViewCore.exportTypedArrayMethod,floor$4=Math.floor;exportTypedArrayMethod$7("reverse",(function reverse(){for(var e,r=aTypedArray$6(this).length,t=floor$4(r/2),a=0;a<t;)e=this[a],this[a++]=this[--r],this[r]=e;return this}));var aTypedArray$5=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$6=arrayBufferViewCore.exportTypedArrayMethod,FORCED$4=fails((function(){new Int8Array(1).set({})}));exportTypedArrayMethod$6("set",(function set(e){aTypedArray$5(this);var r=toOffset(arguments.length>1?arguments[1]:void 0,1),t=this.length,a=toObject(e),i=toLength(a.length),s=0;if(i+r>t)throw RangeError("Wrong length");for(;s<i;)this[r+s]=a[s++]}),FORCED$4);var aTypedArray$4=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$5=arrayBufferViewCore.exportTypedArrayMethod,$slice$1=[].slice,FORCED$3=fails((function(){new Int8Array(1).slice()}));exportTypedArrayMethod$5("slice",(function slice(e,r){for(var t=$slice$1.call(aTypedArray$4(this),e,r),a=typedArraySpeciesConstructor(this),i=0,s=t.length,c=new a(s);s>i;)c[i]=t[i++];return c}),FORCED$3);var $some=arrayIteration.some,aTypedArray$3=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$4=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$4("some",(function some(e){return $some(aTypedArray$3(this),e,arguments.length>1?arguments[1]:void 0)}));var floor$3=Math.floor,mergeSort=function(e,r){var t=e.length,a=floor$3(t/2);return t<8?insertionSort(e,r):merge(mergeSort(e.slice(0,a),r),mergeSort(e.slice(a),r),r)},insertionSort=function(e,r){for(var t,a,i=e.length,s=1;s<i;){for(a=s,t=e[s];a&&r(e[a-1],t)>0;)e[a]=e[--a];a!==s++&&(e[a]=t)}return e},merge=function(e,r,t){for(var a=e.length,i=r.length,s=0,c=0,l=[];s<a||c<i;)s<a&&c<i?l.push(t(e[s],r[c])<=0?e[s++]:r[c++]):l.push(s<a?e[s++]:r[c++]);return l},arraySort=mergeSort,firefox=engineUserAgent.match(/firefox\/(\d+)/i),engineFfVersion=!!firefox&&+firefox[1],engineIsIeOrEdge=/MSIE|Trident/.test(engineUserAgent),webkit=engineUserAgent.match(/AppleWebKit\/(\d+)\./),engineWebkitVersion=!!webkit&&+webkit[1],aTypedArray$2=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$3=arrayBufferViewCore.exportTypedArrayMethod,Uint16Array$1=global$1.Uint16Array,nativeSort$1=Uint16Array$1&&Uint16Array$1.prototype.sort,ACCEPT_INCORRECT_ARGUMENTS=!!nativeSort$1&&!fails((function(){var e=new Uint16Array$1(2);e.sort(null),e.sort({})})),STABLE_SORT$1=!!nativeSort$1&&!fails((function(){if(engineV8Version)return engineV8Version<74;if(engineFfVersion)return engineFfVersion<67;if(engineIsIeOrEdge)return!0;if(engineWebkitVersion)return engineWebkitVersion<602;var e,r,t=new Uint16Array$1(516),a=Array(516);for(e=0;e<516;e++)r=e%4,t[e]=515-e,a[e]=e-2*r+3;for(t.sort((function(e,r){return(e/4|0)-(r/4|0)})),e=0;e<516;e++)if(t[e]!==a[e])return!0})),getSortCompare$1=function(e){return function(r,t){return void 0!==e?+e(r,t)||0:t!=t?-1:r!=r?1:0===r&&0===t?1/r>0&&1/t<0?1:-1:r>t}};exportTypedArrayMethod$3("sort",(function sort(e){if(void 0!==e&&aCallable(e),STABLE_SORT$1)return nativeSort$1.call(this,e);aTypedArray$2(this);var r,t=toLength(this.length),a=Array(t);for(r=0;r<t;r++)a[r]=this[r];for(a=arraySort(this,getSortCompare$1(e)),r=0;r<t;r++)this[r]=a[r];return this}),!STABLE_SORT$1||ACCEPT_INCORRECT_ARGUMENTS);var aTypedArray$1=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$2=arrayBufferViewCore.exportTypedArrayMethod;exportTypedArrayMethod$2("subarray",(function subarray(e,r){var t=aTypedArray$1(this),a=t.length,i=toAbsoluteIndex(e,a);return new(typedArraySpeciesConstructor(t))(t.buffer,t.byteOffset+i*t.BYTES_PER_ELEMENT,toLength((void 0===r?a:toAbsoluteIndex(r,a))-i))}));var Int8Array$1=global$1.Int8Array,aTypedArray=arrayBufferViewCore.aTypedArray,exportTypedArrayMethod$1=arrayBufferViewCore.exportTypedArrayMethod,$toLocaleString=[].toLocaleString,$slice=[].slice,TO_LOCALE_STRING_BUG=!!Int8Array$1&&fails((function(){$toLocaleString.call(new Int8Array$1(1))})),FORCED$2=fails((function(){return[1,2].toLocaleString()!=new Int8Array$1([1,2]).toLocaleString()}))||!fails((function(){Int8Array$1.prototype.toLocaleString.call([1,2])}));exportTypedArrayMethod$1("toLocaleString",(function toLocaleString(){return $toLocaleString.apply(TO_LOCALE_STRING_BUG?$slice.call(aTypedArray(this)):aTypedArray(this),arguments)}),FORCED$2);var exportTypedArrayMethod=arrayBufferViewCore.exportTypedArrayMethod,Uint8Array$1=global$1.Uint8Array,Uint8ArrayPrototype=Uint8Array$1&&Uint8Array$1.prototype||{},arrayToString=[].toString,arrayJoin=[].join;fails((function(){arrayToString.call({})}))&&(arrayToString=function toString(){return arrayJoin.call(this)});var IS_NOT_ARRAY_METHOD=Uint8ArrayPrototype.toString!=arrayToString;exportTypedArrayMethod("toString",arrayToString,IS_NOT_ARRAY_METHOD);var $includes=arrayIncludes.includes;_export({target:"Array",proto:!0},{includes:function includes(e){return $includes(this,e,arguments.length>1?arguments[1]:void 0)}}),addToUnscopables("includes");var notARegexp=function(e){if(isRegexp(e))throw TypeError("The method doesn't accept regular expressions");return e},MATCH=wellKnownSymbol("match"),correctIsRegexpLogic=function(e){var r=/./;try{"/./"[e](r)}catch(t){try{return r[MATCH]=!1,"/./"[e](r)}catch(e){}}return!1};_export({target:"String",proto:!0,forced:!correctIsRegexpLogic("includes")},{includes:function includes(e){return!!~toString(requireObjectCoercible(this)).indexOf(toString(notARegexp(e)),arguments.length>1?arguments[1]:void 0)}});var createProperty=function(e,r,t){var a=toPropertyKey(r);a in e?objectDefineProperty.f(e,a,createPropertyDescriptor(0,t)):e[a]=t},SPECIES$1=wellKnownSymbol("species"),arrayMethodHasSpeciesSupport=function(e){return engineV8Version>=51||!fails((function(){var r=[];return(r.constructor={})[SPECIES$1]=function(){return{foo:1}},1!==r[e](Boolean).foo}))},HAS_SPECIES_SUPPORT$1=arrayMethodHasSpeciesSupport("slice"),SPECIES=wellKnownSymbol("species"),nativeSlice=[].slice,max$1=Math.max;_export({target:"Array",proto:!0,forced:!HAS_SPECIES_SUPPORT$1},{slice:function slice(e,r){var t,a,i,s=toIndexedObject(this),c=toLength(s.length),l=toAbsoluteIndex(e,c),u=toAbsoluteIndex(void 0===r?c:r,c);if(isArray(s)&&(t=s.constructor,(isConstructor(t)&&(t===Array||isArray(t.prototype))||isObject(t)&&null===(t=t[SPECIES]))&&(t=void 0),t===Array||void 0===t))return nativeSlice.call(s,l,u);for(a=new(void 0===t?Array:t)(max$1(u-l,0)),i=0;l<u;l++,i++)l in s&&createProperty(a,i,s[l]);return a.length=i,a}}),typedArrayConstructor("Uint32",(function(e){return function Uint32Array(r,t,a){return e(this,r,t,a)}})),typedArrayConstructor("Uint16",(function(e){return function Uint16Array(r,t,a){return e(this,r,t,a)}}));var $filter=arrayIteration.filter,HAS_SPECIES_SUPPORT=arrayMethodHasSpeciesSupport("filter");_export({target:"Array",proto:!0,forced:!HAS_SPECIES_SUPPORT},{filter:function filter(e){return $filter(this,e,arguments.length>1?arguments[1]:void 0)}});var PROPER_FUNCTION_NAME=functionName.PROPER,TO_STRING="toString",RegExpPrototype=RegExp.prototype,nativeToString=RegExpPrototype[TO_STRING],NOT_GENERIC=fails((function(){return"/a/b"!=nativeToString.call({source:"a",flags:"b"})})),INCORRECT_NAME=PROPER_FUNCTION_NAME&&nativeToString.name!=TO_STRING;(NOT_GENERIC||INCORRECT_NAME)&&redefine(RegExp.prototype,TO_STRING,(function toString$1(){var e=anObject(this),r=toString(e.source),t=e.flags;return"/"+r+"/"+toString(void 0===t&&e instanceof RegExp&&!("flags"in RegExpPrototype)?regexpFlags.call(e):t)}),{unsafe:!0});var test=[],nativeSort=test.sort,FAILS_ON_UNDEFINED=fails((function(){test.sort(void 0)})),FAILS_ON_NULL=fails((function(){test.sort(null)})),STRICT_METHOD$1=arrayMethodIsStrict("sort"),STABLE_SORT=!fails((function(){if(engineV8Version)return engineV8Version<70;if(!(engineFfVersion&&engineFfVersion>3)){if(engineIsIeOrEdge)return!0;if(engineWebkitVersion)return engineWebkitVersion<603;var e,r,t,a,i="";for(e=65;e<76;e++){switch(r=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:t=3;break;case 68:case 71:t=4;break;default:t=2}for(a=0;a<47;a++)test.push({k:r+a,v:t})}for(test.sort((function(e,r){return r.v-e.v})),a=0;a<test.length;a++)r=test[a].k.charAt(0),i.charAt(i.length-1)!==r&&(i+=r);return"DGBEFHACIJK"!==i}})),FORCED$1=FAILS_ON_UNDEFINED||!FAILS_ON_NULL||!STRICT_METHOD$1||!STABLE_SORT,getSortCompare=function(e){return function(r,t){return void 0===t?-1:void 0===r?1:void 0!==e?+e(r,t)||0:toString(r)>toString(t)?1:-1}};_export({target:"Array",proto:!0,forced:FORCED$1},{sort:function sort(e){void 0!==e&&aCallable(e);var r=toObject(this);if(STABLE_SORT)return void 0===e?nativeSort.call(r):nativeSort.call(r,e);var t,a,i=[],s=toLength(r.length);for(a=0;a<s;a++)a in r&&i.push(r[a]);for(t=(i=arraySort(i,getSortCompare(e))).length,a=0;a<t;)r[a]=i[a++];for(;a<s;)delete r[a++];return r}});var UZIP_1=createCommonjsModule((function(e){var r,t,UZIP={};e.exports=UZIP,UZIP.parse=function(e,r){for(var t=UZIP.bin.readUshort,a=UZIP.bin.readUint,i=0,s={},c=new Uint8Array(e),l=c.length-4;101010256!=a(c,l);)l--;i=l;i+=4;var u=t(c,i+=4);t(c,i+=2);var p=a(c,i+=2),d=a(c,i+=4);i+=4,i=d;for(var h=0;h<u;h++){a(c,i),i+=4,i+=4,i+=4,a(c,i+=4);p=a(c,i+=4);var y=a(c,i+=4),A=t(c,i+=4),g=t(c,i+2),E=t(c,i+4);i+=6;var v=a(c,i+=8);i+=4,i+=A+g+E,UZIP._readLocal(c,v,s,p,y,r)}return s},UZIP._readLocal=function(e,r,t,a,i,s){var c=UZIP.bin.readUshort,l=UZIP.bin.readUint;l(e,r),c(e,r+=4),c(e,r+=2);var u=c(e,r+=2);l(e,r+=2),l(e,r+=4),r+=4;var p=c(e,r+=8),d=c(e,r+=2);r+=2;var h=UZIP.bin.readUTF8(e,r,p);if(r+=p,r+=d,s)t[h]={size:i,csize:a};else{var y=new Uint8Array(e.buffer,r);if(0==u)t[h]=new Uint8Array(y.buffer.slice(r,r+a));else{if(8!=u)throw"unknown compression method: "+u;var A=new Uint8Array(i);UZIP.inflateRaw(y,A),t[h]=A}}},UZIP.inflateRaw=function(e,r){return UZIP.F.inflate(e,r)},UZIP.inflate=function(e,r){return e[0],e[1],UZIP.inflateRaw(new Uint8Array(e.buffer,e.byteOffset+2,e.length-6),r)},UZIP.deflate=function(e,r){null==r&&(r={level:6});var t=0,a=new Uint8Array(50+Math.floor(1.1*e.length));a[t]=120,a[t+1]=156,t+=2,t=UZIP.F.deflateRaw(e,a,t,r.level);var i=UZIP.adler(e,0,e.length);return a[t+0]=i>>>24&255,a[t+1]=i>>>16&255,a[t+2]=i>>>8&255,a[t+3]=i>>>0&255,new Uint8Array(a.buffer,0,t+4)},UZIP.deflateRaw=function(e,r){null==r&&(r={level:6});var t=new Uint8Array(50+Math.floor(1.1*e.length)),a=UZIP.F.deflateRaw(e,t,a,r.level);return new Uint8Array(t.buffer,0,a)},UZIP.encode=function(e,r){null==r&&(r=!1);var t=0,a=UZIP.bin.writeUint,i=UZIP.bin.writeUshort,s={};for(var c in e){var l=!UZIP._noNeed(c)&&!r,u=e[c],p=UZIP.crc.crc(u,0,u.length);s[c]={cpr:l,usize:u.length,crc:p,file:l?UZIP.deflateRaw(u):u}}for(var c in s)t+=s[c].file.length+30+46+2*UZIP.bin.sizeUTF8(c);t+=22;var d=new Uint8Array(t),h=0,y=[];for(var c in s){var A=s[c];y.push(h),h=UZIP._writeHeader(d,h,c,A,0)}var g=0,E=h;for(var c in s){A=s[c];y.push(h),h=UZIP._writeHeader(d,h,c,A,1,y[g++])}var v=h-E;return a(d,h,101010256),h+=4,i(d,h+=4,g),i(d,h+=2,g),a(d,h+=2,v),a(d,h+=4,E),h+=4,h+=2,d.buffer},UZIP._noNeed=function(e){var r=e.split(".").pop().toLowerCase();return-1!="png,jpg,jpeg,zip".indexOf(r)},UZIP._writeHeader=function(e,r,t,a,i,s){var c=UZIP.bin.writeUint,l=UZIP.bin.writeUshort,u=a.file;return c(e,r,0==i?67324752:33639248),r+=4,1==i&&(r+=2),l(e,r,20),l(e,r+=2,0),l(e,r+=2,a.cpr?8:0),c(e,r+=2,0),c(e,r+=4,a.crc),c(e,r+=4,u.length),c(e,r+=4,a.usize),l(e,r+=4,UZIP.bin.sizeUTF8(t)),l(e,r+=2,0),r+=2,1==i&&(r+=2,r+=2,c(e,r+=6,s),r+=4),r+=UZIP.bin.writeUTF8(e,r,t),0==i&&(e.set(u,r),r+=u.length),r},UZIP.crc={table:function(){for(var e=new Uint32Array(256),r=0;r<256;r++){for(var t=r,a=0;a<8;a++)1&t?t=3988292384^t>>>1:t>>>=1;e[r]=t}return e}(),update:function(e,r,t,a){for(var i=0;i<a;i++)e=UZIP.crc.table[255&(e^r[t+i])]^e>>>8;return e},crc:function(e,r,t){return 4294967295^UZIP.crc.update(4294967295,e,r,t)}},UZIP.adler=function(e,r,t){for(var a=1,i=0,s=r,c=r+t;s<c;){for(var l=Math.min(s+5552,c);s<l;)i+=a+=e[s++];a%=65521,i%=65521}return i<<16|a},UZIP.bin={readUshort:function(e,r){return e[r]|e[r+1]<<8},writeUshort:function(e,r,t){e[r]=255&t,e[r+1]=t>>8&255},readUint:function(e,r){return 16777216*e[r+3]+(e[r+2]<<16|e[r+1]<<8|e[r])},writeUint:function(e,r,t){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},readASCII:function(e,r,t){for(var a="",i=0;i<t;i++)a+=String.fromCharCode(e[r+i]);return a},writeASCII:function(e,r,t){for(var a=0;a<t.length;a++)e[r+a]=t.charCodeAt(a)},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,r,t){for(var a,i="",s=0;s<t;s++)i+="%"+UZIP.bin.pad(e[r+s].toString(16));try{a=decodeURIComponent(i)}catch(a){return UZIP.bin.readASCII(e,r,t)}return a},writeUTF8:function(e,r,t){for(var a=t.length,i=0,s=0;s<a;s++){var c=t.charCodeAt(s);if(0==(4294967168&c))e[r+i]=c,i++;else if(0==(4294965248&c))e[r+i]=192|c>>6,e[r+i+1]=128|c>>0&63,i+=2;else if(0==(4294901760&c))e[r+i]=224|c>>12,e[r+i+1]=128|c>>6&63,e[r+i+2]=128|c>>0&63,i+=3;else{if(0!=(4292870144&c))throw"e";e[r+i]=240|c>>18,e[r+i+1]=128|c>>12&63,e[r+i+2]=128|c>>6&63,e[r+i+3]=128|c>>0&63,i+=4}}return i},sizeUTF8:function(e){for(var r=e.length,t=0,a=0;a<r;a++){var i=e.charCodeAt(a);if(0==(4294967168&i))t++;else if(0==(4294965248&i))t+=2;else if(0==(4294901760&i))t+=3;else{if(0!=(4292870144&i))throw"e";t+=4}}return t}},UZIP.F={},UZIP.F.deflateRaw=function(e,r,t,a){var i=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][a],s=UZIP.F.U,c=UZIP.F._goodIndex;UZIP.F._hash;var l=UZIP.F._putsE,u=0,p=t<<3,d=0,h=e.length;if(0==a){for(;u<h;){l(r,p,u+(T=Math.min(65535,h-u))==h?1:0),p=UZIP.F._copyExact(e,u,T,r,p+8),u+=T}return p>>>3}var y=s.lits,A=s.strt,g=s.prev,E=0,v=0,m=0,I=0,R=0,P=0;for(h>2&&(A[P=UZIP.F._hash(e,0)]=0),u=0;u<h;u++){if(R=P,u+1<h-2){P=UZIP.F._hash(e,u+1);var S=u+1&32767;g[S]=A[P],A[P]=S}if(d<=u){(E>14e3||v>26697)&&h-u>100&&(d<u&&(y[E]=u-d,E+=2,d=u),p=UZIP.F._writeBlock(u==h-1||d==h?1:0,y,E,I,e,m,u-m,r,p),E=v=I=0,m=u);var b=0;u<h-2&&(b=UZIP.F._bestMatch(e,u,g,R,Math.min(i[2],h-u),i[3]));var T=b>>>16,_=65535&b;if(0!=b){_=65535&b;var O=c(T=b>>>16,s.of0);s.lhst[257+O]++;var w=c(_,s.df0);s.dhst[w]++,I+=s.exb[O]+s.dxb[w],y[E]=T<<23|u-d,y[E+1]=_<<16|O<<8|w,E+=2,d=u+T}else s.lhst[e[u]]++;v++}}for(m==u&&0!=e.length||(d<u&&(y[E]=u-d,E+=2,d=u),p=UZIP.F._writeBlock(1,y,E,I,e,m,u-m,r,p),E=0,v=0,E=v=I=0,m=u);0!=(7&p);)p++;return p>>>3},UZIP.F._bestMatch=function(e,r,t,a,i,s){var c=32767&r,l=t[c],u=c-l+32768&32767;if(l==c||a!=UZIP.F._hash(e,r-u))return 0;for(var p=0,d=0,h=Math.min(32767,r);u<=h&&0!=--s&&l!=c;){if(0==p||e[r+p]==e[r+p-u]){var y=UZIP.F._howLong(e,r,u);if(y>p){if(d=u,(p=y)>=i)break;u+2<y&&(y=u+2);for(var A=0,g=0;g<y-2;g++){var E=r-u+g+32768&32767,v=E-t[E]+32768&32767;v>A&&(A=v,l=E)}}}u+=(c=l)-(l=t[c])+32768&32767}return p<<16|d},UZIP.F._howLong=function(e,r,t){if(e[r]!=e[r-t]||e[r+1]!=e[r+1-t]||e[r+2]!=e[r+2-t])return 0;var a=r,i=Math.min(e.length,r+258);for(r+=3;r<i&&e[r]==e[r-t];)r++;return r-a},UZIP.F._hash=function(e,r){return(e[r]<<8|e[r+1])+(e[r+2]<<4)&65535},UZIP.saved=0,UZIP.F._writeBlock=function(e,r,t,a,i,s,c,l,u){var p,d,h,y,A,g,E,v,m,I=UZIP.F.U,R=UZIP.F._putsF,P=UZIP.F._putsE;I.lhst[256]++,d=(p=UZIP.F.getTrees())[0],h=p[1],y=p[2],A=p[3],g=p[4],E=p[5],v=p[6],m=p[7];var S=32+(0==(u+3&7)?0:8-(u+3&7))+(c<<3),b=a+UZIP.F.contSize(I.fltree,I.lhst)+UZIP.F.contSize(I.fdtree,I.dhst),T=a+UZIP.F.contSize(I.ltree,I.lhst)+UZIP.F.contSize(I.dtree,I.dhst);T+=14+3*E+UZIP.F.contSize(I.itree,I.ihst)+(2*I.ihst[16]+3*I.ihst[17]+7*I.ihst[18]);for(var _=0;_<286;_++)I.lhst[_]=0;for(_=0;_<30;_++)I.dhst[_]=0;for(_=0;_<19;_++)I.ihst[_]=0;var O=S<b&&S<T?0:b<T?1:2;if(R(l,u,e),R(l,u+1,O),u+=3,0==O){for(;0!=(7&u);)u++;u=UZIP.F._copyExact(i,s,c,l,u)}else{var w,U;if(1==O&&(w=I.fltree,U=I.fdtree),2==O){UZIP.F.makeCodes(I.ltree,d),UZIP.F.revCodes(I.ltree,d),UZIP.F.makeCodes(I.dtree,h),UZIP.F.revCodes(I.dtree,h),UZIP.F.makeCodes(I.itree,y),UZIP.F.revCodes(I.itree,y),w=I.ltree,U=I.dtree,P(l,u,A-257),P(l,u+=5,g-1),P(l,u+=5,E-4),u+=4;for(var C=0;C<E;C++)P(l,u+3*C,I.itree[1+(I.ordr[C]<<1)]);u+=3*E,u=UZIP.F._codeTiny(v,I.itree,l,u),u=UZIP.F._codeTiny(m,I.itree,l,u)}for(var $=s,L=0;L<t;L+=2){for(var M=r[L],x=M>>>23,B=$+(8388607&M);$<B;)u=UZIP.F._writeLit(i[$++],w,l,u);if(0!=x){var D=r[L+1],G=D>>16,V=D>>8&255,k=255&D;P(l,u=UZIP.F._writeLit(257+V,w,l,u),x-I.of0[V]),u+=I.exb[V],R(l,u=UZIP.F._writeLit(k,U,l,u),G-I.df0[k]),u+=I.dxb[k],$+=x}}u=UZIP.F._writeLit(256,w,l,u)}return u},UZIP.F._copyExact=function(e,r,t,a,i){var s=i>>>3;return a[s]=t,a[s+1]=t>>>8,a[s+2]=255-a[s],a[s+3]=255-a[s+1],s+=4,a.set(new Uint8Array(e.buffer,r,t),s),i+(t+4<<3)},UZIP.F.getTrees=function(){for(var e=UZIP.F.U,r=UZIP.F._hufTree(e.lhst,e.ltree,15),t=UZIP.F._hufTree(e.dhst,e.dtree,15),a=[],i=UZIP.F._lenCodes(e.ltree,a),s=[],c=UZIP.F._lenCodes(e.dtree,s),l=0;l<a.length;l+=2)e.ihst[a[l]]++;for(l=0;l<s.length;l+=2)e.ihst[s[l]]++;for(var u=UZIP.F._hufTree(e.ihst,e.itree,7),p=19;p>4&&0==e.itree[1+(e.ordr[p-1]<<1)];)p--;return[r,t,u,i,c,p,a,s]},UZIP.F.getSecond=function(e){for(var r=[],t=0;t<e.length;t+=2)r.push(e[t+1]);return r},UZIP.F.nonZero=function(e){for(var r="",t=0;t<e.length;t+=2)0!=e[t+1]&&(r+=(t>>1)+",");return r},UZIP.F.contSize=function(e,r){for(var t=0,a=0;a<r.length;a++)t+=r[a]*e[1+(a<<1)];return t},UZIP.F._codeTiny=function(e,r,t,a){for(var i=0;i<e.length;i+=2){var s=e[i],c=e[i+1];a=UZIP.F._writeLit(s,r,t,a);var l=16==s?2:17==s?3:7;s>15&&(UZIP.F._putsE(t,a,c,l),a+=l)}return a},UZIP.F._lenCodes=function(e,r){for(var t=e.length;2!=t&&0==e[t-1];)t-=2;for(var a=0;a<t;a+=2){var i=e[a+1],s=a+3<t?e[a+3]:-1,c=a+5<t?e[a+5]:-1,l=0==a?-1:e[a-1];if(0==i&&s==i&&c==i){for(var u=a+5;u+2<t&&e[u+2]==i;)u+=2;(p=Math.min(u+1-a>>>1,138))<11?r.push(17,p-3):r.push(18,p-11),a+=2*p-2}else if(i==l&&s==i&&c==i){for(u=a+5;u+2<t&&e[u+2]==i;)u+=2;var p=Math.min(u+1-a>>>1,6);r.push(16,p-3),a+=2*p-2}else r.push(i,0)}return t>>>1},UZIP.F._hufTree=function(e,r,t){var a=[],i=e.length,s=r.length,c=0;for(c=0;c<s;c+=2)r[c]=0,r[c+1]=0;for(c=0;c<i;c++)0!=e[c]&&a.push({lit:c,f:e[c]});var l=a.length,u=a.slice(0);if(0==l)return 0;if(1==l){var p=a[0].lit;u=0==p?1:0;return r[1+(p<<1)]=1,r[1+(u<<1)]=1,1}a.sort((function(e,r){return e.f-r.f}));var d=a[0],h=a[1],y=0,A=1,g=2;for(a[0]={lit:-1,f:d.f+h.f,l:d,r:h,d:0};A!=l-1;)d=y!=A&&(g==l||a[y].f<a[g].f)?a[y++]:a[g++],h=y!=A&&(g==l||a[y].f<a[g].f)?a[y++]:a[g++],a[A++]={lit:-1,f:d.f+h.f,l:d,r:h};var E=UZIP.F.setDepth(a[A-1],0);for(E>t&&(UZIP.F.restrictDepth(u,t,E),E=t),c=0;c<l;c++)r[1+(u[c].lit<<1)]=u[c].d;return E},UZIP.F.setDepth=function(e,r){return-1!=e.lit?(e.d=r,r):Math.max(UZIP.F.setDepth(e.l,r+1),UZIP.F.setDepth(e.r,r+1))},UZIP.F.restrictDepth=function(e,r,t){var a=0,i=1<<t-r,s=0;for(e.sort((function(e,r){return r.d==e.d?e.f-r.f:r.d-e.d})),a=0;a<e.length&&e[a].d>r;a++){var c=e[a].d;e[a].d=r,s+=i-(1<<t-c)}for(s>>>=t-r;s>0;){(c=e[a].d)<r?(e[a].d++,s-=1<<r-c-1):a++}for(;a>=0;a--)e[a].d==r&&s<0&&(e[a].d--,s++);0!=s&&console.log("debt left")},UZIP.F._goodIndex=function(e,r){var t=0;return r[16|t]<=e&&(t|=16),r[8|t]<=e&&(t|=8),r[4|t]<=e&&(t|=4),r[2|t]<=e&&(t|=2),r[1|t]<=e&&(t|=1),t},UZIP.F._writeLit=function(e,r,t,a){return UZIP.F._putsF(t,a,r[e<<1]),a+r[1+(e<<1)]},UZIP.F.inflate=function(e,r){var t=Uint8Array;if(3==e[0]&&0==e[1])return r||new t(0);var a=UZIP.F,i=a._bitsF,s=a._bitsE,c=a._decodeTiny,l=a.makeCodes,u=a.codes2map,p=a._get17,d=a.U,h=null==r;h&&(r=new t(e.length>>>2<<3));for(var y,A,g=0,E=0,v=0,m=0,I=0,R=0,P=0,S=0,b=0;0==g;)if(g=i(e,b,1),E=i(e,b+1,2),b+=3,0!=E){if(h&&(r=UZIP.F._check(r,S+(1<<17))),1==E&&(y=d.flmap,A=d.fdmap,R=511,P=31),2==E){v=s(e,b,5)+257,m=s(e,b+5,5)+1,I=s(e,b+10,4)+4,b+=14;for(var T=0;T<38;T+=2)d.itree[T]=0,d.itree[T+1]=0;var _=1;for(T=0;T<I;T++){var O=s(e,b+3*T,3);d.itree[1+(d.ordr[T]<<1)]=O,O>_&&(_=O)}b+=3*I,l(d.itree,_),u(d.itree,_,d.imap),y=d.lmap,A=d.dmap,b=c(d.imap,(1<<_)-1,v+m,e,b,d.ttree);var w=a._copyOut(d.ttree,0,v,d.ltree);R=(1<<w)-1;var U=a._copyOut(d.ttree,v,m,d.dtree);P=(1<<U)-1,l(d.ltree,w),u(d.ltree,w,y),l(d.dtree,U),u(d.dtree,U,A)}for(;;){var C=y[p(e,b)&R];b+=15&C;var $=C>>>4;if($>>>8==0)r[S++]=$;else{if(256==$)break;var L=S+$-254;if($>264){var M=d.ldef[$-257];L=S+(M>>>3)+s(e,b,7&M),b+=7&M}var x=A[p(e,b)&P];b+=15&x;var B=x>>>4,D=d.ddef[B],G=(D>>>4)+i(e,b,15&D);for(b+=15&D,h&&(r=UZIP.F._check(r,S+(1<<17)));S<L;)r[S]=r[S++-G],r[S]=r[S++-G],r[S]=r[S++-G],r[S]=r[S++-G];S=L}}}else{0!=(7&b)&&(b+=8-(7&b));var V=4+(b>>>3),k=e[V-4]|e[V-3]<<8;h&&(r=UZIP.F._check(r,S+k)),r.set(new t(e.buffer,e.byteOffset+V,k),S),b=V+k<<3,S+=k}return r.length==S?r:r.slice(0,S)},UZIP.F._check=function(e,r){var t=e.length;if(r<=t)return e;var a=new Uint8Array(Math.max(t<<1,r));return a.set(e,0),a},UZIP.F._decodeTiny=function(e,r,t,a,i,s){for(var c=UZIP.F._bitsE,l=UZIP.F._get17,u=0;u<t;){var p=e[l(a,i)&r];i+=15&p;var d=p>>>4;if(d<=15)s[u]=d,u++;else{var h=0,y=0;16==d?(y=3+c(a,i,2),i+=2,h=s[u-1]):17==d?(y=3+c(a,i,3),i+=3):18==d&&(y=11+c(a,i,7),i+=7);for(var A=u+y;u<A;)s[u]=h,u++}}return i},UZIP.F._copyOut=function(e,r,t,a){for(var i=0,s=0,c=a.length>>>1;s<t;){var l=e[s+r];a[s<<1]=0,a[1+(s<<1)]=l,l>i&&(i=l),s++}for(;s<c;)a[s<<1]=0,a[1+(s<<1)]=0,s++;return i},UZIP.F.makeCodes=function(e,r){for(var t,a,i,s,c=UZIP.F.U,l=e.length,u=c.bl_count,p=0;p<=r;p++)u[p]=0;for(p=1;p<l;p+=2)u[e[p]]++;var d=c.next_code;for(t=0,u[0]=0,a=1;a<=r;a++)t=t+u[a-1]<<1,d[a]=t;for(i=0;i<l;i+=2)0!=(s=e[i+1])&&(e[i]=d[s],d[s]++)},UZIP.F.codes2map=function(e,r,t){for(var a=e.length,i=UZIP.F.U.rev15,s=0;s<a;s+=2)if(0!=e[s+1])for(var c=s>>1,l=e[s+1],u=c<<4|l,p=r-l,d=e[s]<<p,h=d+(1<<p);d!=h;){t[i[d]>>>15-r]=u,d++}},UZIP.F.revCodes=function(e,r){for(var t=UZIP.F.U.rev15,a=15-r,i=0;i<e.length;i+=2){var s=e[i]<<r-e[i+1];e[i]=t[s]>>>a}},UZIP.F._putsE=function(e,r,t){t<<=7&r;var a=r>>>3;e[a]|=t,e[a+1]|=t>>>8},UZIP.F._putsF=function(e,r,t){t<<=7&r;var a=r>>>3;e[a]|=t,e[a+1]|=t>>>8,e[a+2]|=t>>>16},UZIP.F._bitsE=function(e,r,t){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<t)-1},UZIP.F._bitsF=function(e,r,t){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<t)-1},UZIP.F._get17=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},UZIP.F._get25=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},UZIP.F.U=(r=Uint16Array,t=Uint32Array,{next_code:new r(16),bl_count:new r(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new r(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new t(32),flmap:new r(512),fltree:[],fdmap:new r(32),fdtree:[],lmap:new r(32768),ltree:[],ttree:[],dmap:new r(32768),dtree:[],imap:new r(512),itree:[],rev15:new r(32768),lhst:new t(286),dhst:new t(30),ihst:new t(19),lits:new t(15e3),strt:new r(65536),prev:new r(32768)}),function(){for(var e=UZIP.F.U,r=0;r<32768;r++){var t=r;t=(4278255360&(t=(4042322160&(t=(3435973836&(t=(2863311530&t)>>>1|(1431655765&t)<<1))>>>2|(858993459&t)<<2))>>>4|(252645135&t)<<4))>>>8|(16711935&t)<<8,e.rev15[r]=(t>>>16|t<<16)>>>17}function pushV(e,r,t){for(;0!=r--;)e.push(0,t)}for(r=0;r<32;r++)e.ldef[r]=e.of0[r]<<3|e.exb[r],e.ddef[r]=e.df0[r]<<4|e.dxb[r];pushV(e.fltree,144,8),pushV(e.fltree,112,9),pushV(e.fltree,24,7),pushV(e.fltree,8,8),UZIP.F.makeCodes(e.fltree,9),UZIP.F.codes2map(e.fltree,9,e.flmap),UZIP.F.revCodes(e.fltree,9),pushV(e.fdtree,32,5),UZIP.F.makeCodes(e.fdtree,5),UZIP.F.codes2map(e.fdtree,5,e.fdmap),UZIP.F.revCodes(e.fdtree,5),pushV(e.itree,19,0),pushV(e.ltree,286,0),pushV(e.dtree,30,0),pushV(e.ttree,320,0)}()})),UZIP=Object.freeze(_mergeNamespaces({__proto__:null,default:UZIP_1},[UZIP_1])),UPNG={},N,W,H;UPNG.toRGBA8=function(e){var r=e.width,t=e.height;if(null==e.tabs.acTL)return[UPNG.toRGBA8.decodeImage(e.data,r,t,e).buffer];var a=[];null==e.frames[0].data&&(e.frames[0].data=e.data);for(var i=r*t*4,s=new Uint8Array(i),c=new Uint8Array(i),l=new Uint8Array(i),u=0;u<e.frames.length;u++){var p=e.frames[u],d=p.rect.x,h=p.rect.y,y=p.rect.width,A=p.rect.height,g=UPNG.toRGBA8.decodeImage(p.data,y,A,e);if(0!=u)for(var E=0;E<i;E++)l[E]=s[E];if(0==p.blend?UPNG._copyTile(g,y,A,s,r,t,d,h,0):1==p.blend&&UPNG._copyTile(g,y,A,s,r,t,d,h,1),a.push(s.buffer.slice(0)),0==p.dispose);else if(1==p.dispose)UPNG._copyTile(c,y,A,s,r,t,d,h,0);else if(2==p.dispose)for(E=0;E<i;E++)s[E]=l[E]}return a},UPNG.toRGBA8.decodeImage=function(e,r,t,a){var i=r*t,s=UPNG.decode._getBPP(a),c=Math.ceil(r*s/8),l=new Uint8Array(4*i),u=new Uint32Array(l.buffer),p=a.ctype,d=a.depth,h=UPNG._bin.readUshort;if(6==p){var y=i<<2;if(8==d)for(var A=0;A<y;A+=4)l[A]=e[A],l[A+1]=e[A+1],l[A+2]=e[A+2],l[A+3]=e[A+3];if(16==d)for(A=0;A<y;A++)l[A]=e[A<<1]}else if(2==p){var g=a.tabs.tRNS;if(null==g){if(8==d)for(A=0;A<i;A++){var E=3*A;u[A]=255<<24|e[E+2]<<16|e[E+1]<<8|e[E]}if(16==d)for(A=0;A<i;A++){E=6*A;u[A]=255<<24|e[E+4]<<16|e[E+2]<<8|e[E]}}else{var v=g[0],m=g[1],I=g[2];if(8==d)for(A=0;A<i;A++){var R=A<<2;E=3*A;u[A]=255<<24|e[E+2]<<16|e[E+1]<<8|e[E],e[E]==v&&e[E+1]==m&&e[E+2]==I&&(l[R+3]=0)}if(16==d)for(A=0;A<i;A++){R=A<<2,E=6*A;u[A]=255<<24|e[E+4]<<16|e[E+2]<<8|e[E],h(e,E)==v&&h(e,E+2)==m&&h(e,E+4)==I&&(l[R+3]=0)}}}else if(3==p){var P=a.tabs.PLTE,S=a.tabs.tRNS,b=S?S.length:0;if(1==d)for(var T=0;T<t;T++){var _=T*c,O=T*r;for(A=0;A<r;A++){R=O+A<<2;var w=3*(U=e[_+(A>>3)]>>7-((7&A)<<0)&1);l[R]=P[w],l[R+1]=P[w+1],l[R+2]=P[w+2],l[R+3]=U<b?S[U]:255}}if(2==d)for(T=0;T<t;T++)for(_=T*c,O=T*r,A=0;A<r;A++){R=O+A<<2,w=3*(U=e[_+(A>>2)]>>6-((3&A)<<1)&3);l[R]=P[w],l[R+1]=P[w+1],l[R+2]=P[w+2],l[R+3]=U<b?S[U]:255}if(4==d)for(T=0;T<t;T++)for(_=T*c,O=T*r,A=0;A<r;A++){R=O+A<<2,w=3*(U=e[_+(A>>1)]>>4-((1&A)<<2)&15);l[R]=P[w],l[R+1]=P[w+1],l[R+2]=P[w+2],l[R+3]=U<b?S[U]:255}if(8==d)for(A=0;A<i;A++){var U;R=A<<2,w=3*(U=e[A]);l[R]=P[w],l[R+1]=P[w+1],l[R+2]=P[w+2],l[R+3]=U<b?S[U]:255}}else if(4==p){if(8==d)for(A=0;A<i;A++){R=A<<2;var C=e[$=A<<1];l[R]=C,l[R+1]=C,l[R+2]=C,l[R+3]=e[$+1]}if(16==d)for(A=0;A<i;A++){var $;R=A<<2,C=e[$=A<<2];l[R]=C,l[R+1]=C,l[R+2]=C,l[R+3]=e[$+2]}}else if(0==p)for(v=a.tabs.tRNS?a.tabs.tRNS:-1,T=0;T<t;T++){var L=T*c,M=T*r;if(1==d)for(var x=0;x<r;x++){var B=(C=255*(e[L+(x>>>3)]>>>7-(7&x)&1))==255*v?0:255;u[M+x]=B<<24|C<<16|C<<8|C}else if(2==d)for(x=0;x<r;x++){B=(C=85*(e[L+(x>>>2)]>>>6-((3&x)<<1)&3))==85*v?0:255;u[M+x]=B<<24|C<<16|C<<8|C}else if(4==d)for(x=0;x<r;x++){B=(C=17*(e[L+(x>>>1)]>>>4-((1&x)<<2)&15))==17*v?0:255;u[M+x]=B<<24|C<<16|C<<8|C}else if(8==d)for(x=0;x<r;x++){B=(C=e[L+x])==v?0:255;u[M+x]=B<<24|C<<16|C<<8|C}else if(16==d)for(x=0;x<r;x++){C=e[L+(x<<1)],B=h(e,L+(x<<A))==v?0:255;u[M+x]=B<<24|C<<16|C<<8|C}}return l},UPNG.decode=function(e){for(var r,t=new Uint8Array(e),a=8,i=UPNG._bin,s=i.readUshort,c=i.readUint,l={tabs:{},frames:[]},u=new Uint8Array(t.length),p=0,d=0,h=[137,80,78,71,13,10,26,10],y=0;y<8;y++)if(t[y]!=h[y])throw"The input is not a PNG file!";for(;a<t.length;){var A=i.readUint(t,a);a+=4;var g=i.readASCII(t,a,4);if(a+=4,"IHDR"==g)UPNG.decode._IHDR(t,a,l);else if("CgBI"==g)l.tabs[g]=t.slice(a,a+4);else if("IDAT"==g){for(y=0;y<A;y++)u[p+y]=t[a+y];p+=A}else if("acTL"==g)l.tabs[g]={num_frames:c(t,a),num_plays:c(t,a+4)},r=new Uint8Array(t.length);else if("fcTL"==g){var E;if(0!=d)(E=l.frames[l.frames.length-1]).data=UPNG.decode._decompress(l,r.slice(0,d),E.rect.width,E.rect.height),d=0;var v={x:c(t,a+12),y:c(t,a+16),width:c(t,a+4),height:c(t,a+8)},m=s(t,a+22);m=s(t,a+20)/(0==m?100:m);var I={rect:v,delay:Math.round(1e3*m),dispose:t[a+24],blend:t[a+25]};l.frames.push(I)}else if("fdAT"==g){for(y=0;y<A-4;y++)r[d+y]=t[a+y+4];d+=A-4}else if("pHYs"==g)l.tabs[g]=[i.readUint(t,a),i.readUint(t,a+4),t[a+8]];else if("cHRM"==g){l.tabs[g]=[];for(y=0;y<8;y++)l.tabs[g].push(i.readUint(t,a+4*y))}else if("tEXt"==g||"zTXt"==g){null==l.tabs[g]&&(l.tabs[g]={});var R=i.nextZero(t,a),P=i.readASCII(t,a,R-a),S=a+A-R-1;if("tEXt"==g)_=i.readASCII(t,R+1,S);else{var b=UPNG.decode._inflate(t.slice(R+2,R+2+S));_=i.readUTF8(b,0,b.length)}l.tabs[g][P]=_}else if("iTXt"==g){null==l.tabs[g]&&(l.tabs[g]={});R=0;var T=a;R=i.nextZero(t,T);P=i.readASCII(t,T,R-T);var _,O=t[T=R+1];t[T+1],T+=2,R=i.nextZero(t,T),i.readASCII(t,T,R-T),T=R+1,R=i.nextZero(t,T),i.readUTF8(t,T,R-T);S=A-((T=R+1)-a);if(0==O)_=i.readUTF8(t,T,S);else{b=UPNG.decode._inflate(t.slice(T,T+S));_=i.readUTF8(b,0,b.length)}l.tabs[g][P]=_}else if("PLTE"==g)l.tabs[g]=i.readBytes(t,a,A);else if("hIST"==g){var w=l.tabs.PLTE.length/3;l.tabs[g]=[];for(y=0;y<w;y++)l.tabs[g].push(s(t,a+2*y))}else if("tRNS"==g)3==l.ctype?l.tabs[g]=i.readBytes(t,a,A):0==l.ctype?l.tabs[g]=s(t,a):2==l.ctype&&(l.tabs[g]=[s(t,a),s(t,a+2),s(t,a+4)]);else if("gAMA"==g)l.tabs[g]=i.readUint(t,a)/1e5;else if("sRGB"==g)l.tabs[g]=t[a];else if("bKGD"==g)0==l.ctype||4==l.ctype?l.tabs[g]=[s(t,a)]:2==l.ctype||6==l.ctype?l.tabs[g]=[s(t,a),s(t,a+2),s(t,a+4)]:3==l.ctype&&(l.tabs[g]=t[a]);else if("IEND"==g)break;a+=A,i.readUint(t,a),a+=4}0!=d&&((E=l.frames[l.frames.length-1]).data=UPNG.decode._decompress(l,r.slice(0,d),E.rect.width,E.rect.height),d=0);return l.data=UPNG.decode._decompress(l,u,l.width,l.height),delete l.compress,delete l.interlace,delete l.filter,l},UPNG.decode._decompress=function(e,r,t,a){var i=UPNG.decode._getBPP(e),s=Math.ceil(t*i/8),c=new Uint8Array((s+1+e.interlace)*a);return r=e.tabs.CgBI?UPNG.inflateRaw(r,c):UPNG.decode._inflate(r,c),0==e.interlace?r=UPNG.decode._filterZero(r,e,0,t,a):1==e.interlace&&(r=UPNG.decode._readInterlace(r,e)),r},UPNG.decode._inflate=function(e,r){return UPNG.inflateRaw(new Uint8Array(e.buffer,2,e.length-6),r)},UPNG.inflateRaw=(H={},H.H={},H.H.N=function(e,r){var t,a,i=Uint8Array,s=0,c=0,l=0,u=0,p=0,d=0,h=0,y=0,A=0;if(3==e[0]&&0==e[1])return r||new i(0);var g=H.H,E=g.b,v=g.e,m=g.R,I=g.n,R=g.A,P=g.Z,S=g.m,b=null==r;for(b&&(r=new i(e.length>>>2<<5));0==s;)if(s=E(e,A,1),c=E(e,A+1,2),A+=3,0!=c){if(b&&(r=H.H.W(r,y+(1<<17))),1==c&&(t=S.J,a=S.h,d=511,h=31),2==c){l=v(e,A,5)+257,u=v(e,A+5,5)+1,p=v(e,A+10,4)+4,A+=14;for(var T=1,_=0;_<38;_+=2)S.Q[_]=0,S.Q[_+1]=0;for(_=0;_<p;_++){var O=v(e,A+3*_,3);S.Q[1+(S.X[_]<<1)]=O,O>T&&(T=O)}A+=3*p,I(S.Q,T),R(S.Q,T,S.u),t=S.w,a=S.d,A=m(S.u,(1<<T)-1,l+u,e,A,S.v);var w=g.V(S.v,0,l,S.C);d=(1<<w)-1;var U=g.V(S.v,l,u,S.D);h=(1<<U)-1,I(S.C,w),R(S.C,w,t),I(S.D,U),R(S.D,U,a)}for(;;){var C=t[P(e,A)&d];A+=15&C;var $=C>>>4;if($>>>8==0)r[y++]=$;else{if(256==$)break;var L=y+$-254;if($>264){var M=S.q[$-257];L=y+(M>>>3)+v(e,A,7&M),A+=7&M}var x=a[P(e,A)&h];A+=15&x;var B=x>>>4,D=S.c[B],G=(D>>>4)+E(e,A,15&D);for(A+=15&D;y<L;)r[y]=r[y++-G],r[y]=r[y++-G],r[y]=r[y++-G],r[y]=r[y++-G];y=L}}}else{0!=(7&A)&&(A+=8-(7&A));var V=4+(A>>>3),k=e[V-4]|e[V-3]<<8;b&&(r=H.H.W(r,y+k)),r.set(new i(e.buffer,e.byteOffset+V,k),y),A=V+k<<3,y+=k}return r.length==y?r:r.slice(0,y)},H.H.W=function(e,r){var t=e.length;if(r<=t)return e;var a=new Uint8Array(t<<1);return a.set(e,0),a},H.H.R=function(e,r,t,a,i,s){for(var c=H.H.e,l=H.H.Z,u=0;u<t;){var p=e[l(a,i)&r];i+=15&p;var d=p>>>4;if(d<=15)s[u]=d,u++;else{var h=0,y=0;16==d?(y=3+c(a,i,2),i+=2,h=s[u-1]):17==d?(y=3+c(a,i,3),i+=3):18==d&&(y=11+c(a,i,7),i+=7);for(var A=u+y;u<A;)s[u]=h,u++}}return i},H.H.V=function(e,r,t,a){for(var i=0,s=0,c=a.length>>>1;s<t;){var l=e[s+r];a[s<<1]=0,a[1+(s<<1)]=l,l>i&&(i=l),s++}for(;s<c;)a[s<<1]=0,a[1+(s<<1)]=0,s++;return i},H.H.n=function(e,r){for(var t,a,i,s,c=H.H.m,l=e.length,u=c.j,p=0;p<=r;p++)u[p]=0;for(p=1;p<l;p+=2)u[e[p]]++;var d=c.K;for(t=0,u[0]=0,a=1;a<=r;a++)t=t+u[a-1]<<1,d[a]=t;for(i=0;i<l;i+=2)0!=(s=e[i+1])&&(e[i]=d[s],d[s]++)},H.H.A=function(e,r,t){for(var a=e.length,i=H.H.m.r,s=0;s<a;s+=2)if(0!=e[s+1])for(var c=s>>1,l=e[s+1],u=c<<4|l,p=r-l,d=e[s]<<p,h=d+(1<<p);d!=h;)t[i[d]>>>15-r]=u,d++},H.H.l=function(e,r){for(var t=H.H.m.r,a=15-r,i=0;i<e.length;i+=2){var s=e[i]<<r-e[i+1];e[i]=t[s]>>>a}},H.H.M=function(e,r,t){t<<=7&r;var a=r>>>3;e[a]|=t,e[a+1]|=t>>>8},H.H.I=function(e,r,t){t<<=7&r;var a=r>>>3;e[a]|=t,e[a+1]|=t>>>8,e[a+2]|=t>>>16},H.H.e=function(e,r,t){return(e[r>>>3]|e[1+(r>>>3)]<<8)>>>(7&r)&(1<<t)-1},H.H.b=function(e,r,t){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)&(1<<t)-1},H.H.Z=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16)>>>(7&r)},H.H.i=function(e,r){return(e[r>>>3]|e[1+(r>>>3)]<<8|e[2+(r>>>3)]<<16|e[3+(r>>>3)]<<24)>>>(7&r)},H.H.m=(N=Uint16Array,W=Uint32Array,{K:new N(16),j:new N(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new N(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new W(32),J:new N(512),_:[],h:new N(32),$:[],w:new N(32768),C:[],v:[],d:new N(32768),D:[],u:new N(512),Q:[],r:new N(32768),s:new W(286),Y:new W(30),a:new W(19),t:new W(15e3),k:new N(65536),g:new N(32768)}),function(){for(var e=H.H.m,r=0;r<32768;r++){var t=r;t=(4278255360&(t=(4042322160&(t=(3435973836&(t=(2863311530&t)>>>1|(1431655765&t)<<1))>>>2|(858993459&t)<<2))>>>4|(252645135&t)<<4))>>>8|(16711935&t)<<8,e.r[r]=(t>>>16|t<<16)>>>17}function n(e,r,t){for(;0!=r--;)e.push(0,t)}for(r=0;r<32;r++)e.q[r]=e.S[r]<<3|e.T[r],e.c[r]=e.p[r]<<4|e.z[r];n(e._,144,8),n(e._,112,9),n(e._,24,7),n(e._,8,8),H.H.n(e._,9),H.H.A(e._,9,e.J),H.H.l(e._,9),n(e.$,32,5),H.H.n(e.$,5),H.H.A(e.$,5,e.h),H.H.l(e.$,5),n(e.Q,19,0),n(e.C,286,0),n(e.D,30,0),n(e.v,320,0)}(),H.H.N),UPNG.decode._readInterlace=function(e,r){for(var t=r.width,a=r.height,i=UPNG.decode._getBPP(r),s=i>>3,c=Math.ceil(t*i/8),l=new Uint8Array(a*c),u=0,p=[0,0,4,0,2,0,1],d=[0,4,0,2,0,1,0],h=[8,8,8,4,4,2,2],y=[8,8,4,4,2,2,1],A=0;A<7;){for(var g=h[A],E=y[A],v=0,m=0,I=p[A];I<a;)I+=g,m++;for(var R=d[A];R<t;)R+=E,v++;var P=Math.ceil(v*i/8);UPNG.decode._filterZero(e,r,u,v,m);for(var S=0,b=p[A];b<a;){for(var T=d[A],_=u+S*P<<3;T<t;){var O;if(1==i)O=(O=e[_>>3])>>7-(7&_)&1,l[b*c+(T>>3)]|=O<<7-((7&T)<<0);if(2==i)O=(O=e[_>>3])>>6-(7&_)&3,l[b*c+(T>>2)]|=O<<6-((3&T)<<1);if(4==i)O=(O=e[_>>3])>>4-(7&_)&15,l[b*c+(T>>1)]|=O<<4-((1&T)<<2);if(i>=8)for(var w=b*c+T*s,U=0;U<s;U++)l[w+U]=e[(_>>3)+U];_+=i,T+=E}S++,b+=g}v*m!=0&&(u+=m*(1+P)),A+=1}return l},UPNG.decode._getBPP=function(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth},UPNG.decode._filterZero=function(e,r,t,a,i){var s=UPNG.decode._getBPP(r),c=Math.ceil(a*s/8),l=UPNG.decode._paeth;s=Math.ceil(s/8);var u=0,p=1,d=e[t],h=0;if(d>1&&(e[t]=[0,0,1][d-2]),3==d)for(h=s;h<c;h++)e[h+1]=e[h+1]+(e[h+1-s]>>>1)&255;for(var y=0;y<i;y++)if(h=0,0==(d=e[(p=(u=t+y*c)+y+1)-1]))for(;h<c;h++)e[u+h]=e[p+h];else if(1==d){for(;h<s;h++)e[u+h]=e[p+h];for(;h<c;h++)e[u+h]=e[p+h]+e[u+h-s]}else if(2==d)for(;h<c;h++)e[u+h]=e[p+h]+e[u+h-c];else if(3==d){for(;h<s;h++)e[u+h]=e[p+h]+(e[u+h-c]>>>1);for(;h<c;h++)e[u+h]=e[p+h]+(e[u+h-c]+e[u+h-s]>>>1)}else{for(;h<s;h++)e[u+h]=e[p+h]+l(0,e[u+h-c],0);for(;h<c;h++)e[u+h]=e[p+h]+l(e[u+h-s],e[u+h-c],e[u+h-s-c])}return e},UPNG.decode._paeth=function(e,r,t){var a=e+r-t,i=a-e,s=a-r,c=a-t;return i*i<=s*s&&i*i<=c*c?e:s*s<=c*c?r:t},UPNG.decode._IHDR=function(e,r,t){var a=UPNG._bin;t.width=a.readUint(e,r),r+=4,t.height=a.readUint(e,r),r+=4,t.depth=e[r],r++,t.ctype=e[r],r++,t.compress=e[r],r++,t.filter=e[r],r++,t.interlace=e[r],r++},UPNG._bin={nextZero:function nextZero(e,r){for(;0!=e[r];)r++;return r},readUshort:function readUshort(e,r){return e[r]<<8|e[r+1]},writeUshort:function writeUshort(e,r,t){e[r]=t>>8&255,e[r+1]=255&t},readUint:function readUint(e,r){return 16777216*e[r]+(e[r+1]<<16|e[r+2]<<8|e[r+3])},writeUint:function writeUint(e,r,t){e[r]=t>>24&255,e[r+1]=t>>16&255,e[r+2]=t>>8&255,e[r+3]=255&t},readASCII:function readASCII(e,r,t){for(var a="",i=0;i<t;i++)a+=String.fromCharCode(e[r+i]);return a},writeASCII:function writeASCII(e,r,t){for(var a=0;a<t.length;a++)e[r+a]=t.charCodeAt(a)},readBytes:function readBytes(e,r,t){for(var a=[],i=0;i<t;i++)a.push(e[r+i]);return a},pad:function pad(e){return e.length<2?"0".concat(e):e},readUTF8:function readUTF8(e,r,t){for(var a,i="",s=0;s<t;s++)i+="%".concat(UPNG._bin.pad(e[r+s].toString(16)));try{a=decodeURIComponent(i)}catch(a){return UPNG._bin.readASCII(e,r,t)}return a}},UPNG._copyTile=function(e,r,t,a,i,s,c,l,u){for(var p=Math.min(r,i),d=Math.min(t,s),h=0,y=0,A=0;A<d;A++)for(var g=0;g<p;g++)if(c>=0&&l>=0?(h=A*r+g<<2,y=(l+A)*i+c+g<<2):(h=(-l+A)*r-c+g<<2,y=A*i+g<<2),0==u)a[y]=e[h],a[y+1]=e[h+1],a[y+2]=e[h+2],a[y+3]=e[h+3];else if(1==u){var E=e[h+3]*(1/255),v=e[h]*E,m=e[h+1]*E,I=e[h+2]*E,R=a[y+3]*(1/255),P=a[y]*R,S=a[y+1]*R,b=a[y+2]*R,T=1-E,_=E+R*T,O=0==_?0:1/_;a[y+3]=255*_,a[y+0]=(v+P*T)*O,a[y+1]=(m+S*T)*O,a[y+2]=(I+b*T)*O}else if(2==u){E=e[h+3],v=e[h],m=e[h+1],I=e[h+2],R=a[y+3],P=a[y],S=a[y+1],b=a[y+2];E==R&&v==P&&m==S&&I==b?(a[y]=0,a[y+1]=0,a[y+2]=0,a[y+3]=0):(a[y]=v,a[y+1]=m,a[y+2]=I,a[y+3]=E)}else if(3==u){E=e[h+3],v=e[h],m=e[h+1],I=e[h+2],R=a[y+3],P=a[y],S=a[y+1],b=a[y+2];if(E==R&&v==P&&m==S&&I==b)continue;if(E<220&&R>20)return!1}return!0},UPNG.encode=function(e,r,t,a,i,s,c){null==a&&(a=0),null==c&&(c=!1);var l=UPNG.encode.compress(e,r,t,a,[!1,!1,!1,0,c]);return UPNG.encode.compressPNG(l,-1),UPNG.encode._main(l,r,t,i,s)},UPNG.encodeLL=function(e,r,t,a,i,s,c,l){for(var u={ctype:0+(1==a?0:2)+(0==i?0:4),depth:s,frames:[]},p=(a+i)*s,d=p*r,h=0;h<e.length;h++)u.frames.push({rect:{x:0,y:0,width:r,height:t},img:new Uint8Array(e[h]),blend:0,dispose:1,bpp:Math.ceil(p/8),bpl:Math.ceil(d/8)});return UPNG.encode.compressPNG(u,0,!0),UPNG.encode._main(u,r,t,c,l)},UPNG.encode._main=function(e,r,t,a,i){null==i&&(i={});var s=UPNG.crc.crc,c=UPNG._bin.writeUint,l=UPNG._bin.writeUshort,u=UPNG._bin.writeASCII,p=8,d=e.frames.length>1,h=!1,y=33+(d?20:0);if(null!=i.sRGB&&(y+=13),null!=i.pHYs&&(y+=21),3==e.ctype){for(var A=e.plte.length,g=0;g<A;g++)e.plte[g]>>>24!=255&&(h=!0);y+=8+3*A+4+(h?8+1*A+4:0)}for(var E=0;E<e.frames.length;E++){d&&(y+=38),y+=(_=e.frames[E]).cimg.length+12,0!=E&&(y+=4)}y+=12;var v=new Uint8Array(y),m=[137,80,78,71,13,10,26,10];for(g=0;g<8;g++)v[g]=m[g];if(c(v,p,13),u(v,p+=4,"IHDR"),c(v,p+=4,r),c(v,p+=4,t),v[p+=4]=e.depth,v[++p]=e.ctype,v[++p]=0,v[++p]=0,v[++p]=0,c(v,++p,s(v,p-17,17)),p+=4,null!=i.sRGB&&(c(v,p,1),u(v,p+=4,"sRGB"),v[p+=4]=i.sRGB,c(v,++p,s(v,p-5,5)),p+=4),null!=i.pHYs&&(c(v,p,9),u(v,p+=4,"pHYs"),c(v,p+=4,i.pHYs[0]),c(v,p+=4,i.pHYs[1]),v[p+=4]=i.pHYs[2],c(v,++p,s(v,p-13,13)),p+=4),d&&(c(v,p,8),u(v,p+=4,"acTL"),c(v,p+=4,e.frames.length),c(v,p+=4,null!=i.loop?i.loop:0),c(v,p+=4,s(v,p-12,12)),p+=4),3==e.ctype){c(v,p,3*(A=e.plte.length)),u(v,p+=4,"PLTE"),p+=4;for(g=0;g<A;g++){var I=3*g,R=e.plte[g],P=255&R,S=R>>>8&255,b=R>>>16&255;v[p+I+0]=P,v[p+I+1]=S,v[p+I+2]=b}if(c(v,p+=3*A,s(v,p-3*A-4,3*A+4)),p+=4,h){c(v,p,A),u(v,p+=4,"tRNS"),p+=4;for(g=0;g<A;g++)v[p+g]=e.plte[g]>>>24&255;c(v,p+=A,s(v,p-A-4,A+4)),p+=4}}var T=0;for(E=0;E<e.frames.length;E++){var _=e.frames[E];d&&(c(v,p,26),u(v,p+=4,"fcTL"),c(v,p+=4,T++),c(v,p+=4,_.rect.width),c(v,p+=4,_.rect.height),c(v,p+=4,_.rect.x),c(v,p+=4,_.rect.y),l(v,p+=4,a[E]),l(v,p+=2,1e3),v[p+=2]=_.dispose,v[++p]=_.blend,c(v,++p,s(v,p-30,30)),p+=4);var O=_.cimg;c(v,p,(A=O.length)+(0==E?0:4));var w=p+=4;u(v,p,0==E?"IDAT":"fdAT"),p+=4,0!=E&&(c(v,p,T++),p+=4),v.set(O,p),c(v,p+=A,s(v,w,p-w)),p+=4}return c(v,p,0),u(v,p+=4,"IEND"),c(v,p+=4,s(v,p-4,4)),p+=4,v.buffer},UPNG.encode.compressPNG=function(e,r,t){for(var a=0;a<e.frames.length;a++){var i=e.frames[a];i.rect.width;var s=i.rect.height,c=new Uint8Array(s*i.bpl+s);i.cimg=UPNG.encode._filterZero(i.img,s,i.bpp,i.bpl,c,r,t)}},UPNG.encode.compress=function(e,r,t,a,i){for(var s=i[0],c=i[1],l=i[2],u=i[3],p=i[4],d=6,h=8,y=255,A=0;A<e.length;A++)for(var g=new Uint8Array(e[A]),E=g.length,v=0;v<E;v+=4)y&=g[v+3];var m=255!=y,I=UPNG.encode.framize(e,r,t,s,c,l),R={},P=[],S=[];if(0!=a){var b=[];for(v=0;v<I.length;v++)b.push(I[v].img.buffer);var T=UPNG.encode.concatRGBA(b),_=UPNG.quantize(T,a),O=0,w=new Uint8Array(_.abuf);for(v=0;v<I.length;v++){var U=(J=I[v].img).length;S.push(new Uint8Array(_.inds.buffer,O>>2,U>>2));for(A=0;A<U;A+=4)J[A]=w[O+A],J[A+1]=w[O+A+1],J[A+2]=w[O+A+2],J[A+3]=w[O+A+3];O+=U}for(v=0;v<_.plte.length;v++)P.push(_.plte[v].est.rgba)}else for(A=0;A<I.length;A++){var C=I[A],$=new Uint32Array(C.img.buffer),L=C.rect.width,M=(E=$.length,new Uint8Array(E));S.push(M);for(v=0;v<E;v++){var x=$[v];if(0!=v&&x==$[v-1])M[v]=M[v-1];else if(v>L&&x==$[v-L])M[v]=M[v-L];else{var B=R[x];if(null==B&&(R[x]=B=P.length,P.push(x),P.length>=300))break;M[v]=B}}}var D=P.length;D<=256&&0==p&&(h=D<=2?1:D<=4?2:D<=16?4:8,h=Math.max(h,u));for(A=0;A<I.length;A++){(C=I[A]).rect.x,C.rect.y;L=C.rect.width;var G=C.rect.height,V=C.img;new Uint32Array(V.buffer);var k=4*L,Y=4;if(D<=256&&0==p){k=Math.ceil(h*L/8);for(var Z=new Uint8Array(k*G),q=S[A],Q=0;Q<G;Q++){v=Q*k;var K=Q*L;if(8==h)for(var z=0;z<L;z++)Z[v+z]=q[K+z];else if(4==h)for(z=0;z<L;z++)Z[v+(z>>1)]|=q[K+z]<<4-4*(1&z);else if(2==h)for(z=0;z<L;z++)Z[v+(z>>2)]|=q[K+z]<<6-2*(3&z);else if(1==h)for(z=0;z<L;z++)Z[v+(z>>3)]|=q[K+z]<<7-1*(7&z)}V=Z,d=3,Y=1}else if(0==m&&1==I.length){Z=new Uint8Array(L*G*3);var X=L*G;for(v=0;v<X;v++){var J,ee=4*v;Z[J=3*v]=V[ee],Z[J+1]=V[ee+1],Z[J+2]=V[ee+2]}V=Z,d=2,Y=3,k=3*L}C.img=V,C.bpl=k,C.bpp=Y}return{ctype:d,depth:h,plte:P,frames:I}},UPNG.encode.framize=function(e,r,t,a,i,s){for(var c=[],l=0;l<e.length;l++){var u,p=new Uint8Array(e[l]),d=new Uint32Array(p.buffer),h=0,y=0,A=r,g=t,E=a?1:0;if(0!=l){for(var v=s||a||1==l||0!=c[l-2].dispose?1:2,m=0,I=1e9,R=0;R<v;R++){for(var P=new Uint8Array(e[l-1-R]),S=new Uint32Array(e[l-1-R]),b=r,T=t,_=-1,O=-1,w=0;w<t;w++)for(var U=0;U<r;U++){d[D=w*r+U]!=S[D]&&(U<b&&(b=U),U>_&&(_=U),w<T&&(T=w),w>O&&(O=w))}-1==_&&(b=T=_=O=0),i&&(1==(1&b)&&b--,1==(1&T)&&T--);var C=(_-b+1)*(O-T+1);C<I&&(I=C,m=R,h=b,y=T,A=_-b+1,g=O-T+1)}P=new Uint8Array(e[l-1-m]);1==m&&(c[l-1].dispose=2),u=new Uint8Array(A*g*4),UPNG._copyTile(P,r,t,u,A,g,-h,-y,0),1==(E=UPNG._copyTile(p,r,t,u,A,g,-h,-y,3)?1:0)?UPNG.encode._prepareDiff(p,r,t,u,{x:h,y:y,width:A,height:g}):UPNG._copyTile(p,r,t,u,A,g,-h,-y,0)}else u=p.slice(0);c.push({rect:{x:h,y:y,width:A,height:g},img:u,blend:E,dispose:0})}if(a)for(l=0;l<c.length;l++){if(1!=(G=c[l]).blend){var $=G.rect,L=c[l-1].rect,M=Math.min($.x,L.x),x=Math.min($.y,L.y),B={x:M,y:x,width:Math.max($.x+$.width,L.x+L.width)-M,height:Math.max($.y+$.height,L.y+L.height)-x};c[l-1].dispose=1,l-1!=0&&UPNG.encode._updateFrame(e,r,t,c,l-1,B,i),UPNG.encode._updateFrame(e,r,t,c,l,B,i)}}if(1!=e.length)for(var D=0;D<c.length;D++){var G;(G=c[D]).rect.width*G.rect.height}return c},UPNG.encode._updateFrame=function(e,r,t,a,i,s,c){for(var l=Uint8Array,u=Uint32Array,p=new l(e[i-1]),d=new u(e[i-1]),h=i+1<e.length?new l(e[i+1]):null,y=new l(e[i]),A=new u(y.buffer),g=r,E=t,v=-1,m=-1,I=0;I<s.height;I++)for(var R=0;R<s.width;R++){var P=s.x+R,S=s.y+I,b=S*r+P,T=A[b];0==T||0==a[i-1].dispose&&d[b]==T&&(null==h||0!=h[4*b+3])||(P<g&&(g=P),P>v&&(v=P),S<E&&(E=S),S>m&&(m=S))}-1==v&&(g=E=v=m=0),c&&(1==(1&g)&&g--,1==(1&E)&&E--),s={x:g,y:E,width:v-g+1,height:m-E+1};var _=a[i];_.rect=s,_.blend=1,_.img=new Uint8Array(s.width*s.height*4),0==a[i-1].dispose?(UPNG._copyTile(p,r,t,_.img,s.width,s.height,-s.x,-s.y,0),UPNG.encode._prepareDiff(y,r,t,_.img,s)):UPNG._copyTile(y,r,t,_.img,s.width,s.height,-s.x,-s.y,0)},UPNG.encode._prepareDiff=function(e,r,t,a,i){UPNG._copyTile(e,r,t,a,i.width,i.height,-i.x,-i.y,2)},UPNG.encode._filterZero=function(e,r,t,a,i,s,c){var l,u=[],p=[0,1,2,3,4];-1!=s?p=[s]:(r*a>5e5||1==t)&&(p=[0]),c&&(l={level:0});for(var d,h=UZIP,y=0;y<p.length;y++){for(var A=0;A<r;A++)UPNG.encode._filterLine(i,e,A,a,t,p[y]);u.push(h.deflate(i,l))}var g=1e9;for(y=0;y<u.length;y++)u[y].length<g&&(d=y,g=u[y].length);return u[d]},UPNG.encode._filterLine=function(e,r,t,a,i,s){var c=t*a,l=c+t,u=UPNG.decode._paeth;if(e[l]=s,l++,0==s)if(a<500)for(var p=0;p<a;p++)e[l+p]=r[c+p];else e.set(new Uint8Array(r.buffer,c,a),l);else if(1==s){for(p=0;p<i;p++)e[l+p]=r[c+p];for(p=i;p<a;p++)e[l+p]=r[c+p]-r[c+p-i]+256&255}else if(0==t){for(p=0;p<i;p++)e[l+p]=r[c+p];if(2==s)for(p=i;p<a;p++)e[l+p]=r[c+p];if(3==s)for(p=i;p<a;p++)e[l+p]=r[c+p]-(r[c+p-i]>>1)+256&255;if(4==s)for(p=i;p<a;p++)e[l+p]=r[c+p]-u(r[c+p-i],0,0)+256&255}else{if(2==s)for(p=0;p<a;p++)e[l+p]=r[c+p]+256-r[c+p-a]&255;if(3==s){for(p=0;p<i;p++)e[l+p]=r[c+p]+256-(r[c+p-a]>>1)&255;for(p=i;p<a;p++)e[l+p]=r[c+p]+256-(r[c+p-a]+r[c+p-i]>>1)&255}if(4==s){for(p=0;p<i;p++)e[l+p]=r[c+p]+256-u(0,r[c+p-a],0)&255;for(p=i;p<a;p++)e[l+p]=r[c+p]+256-u(r[c+p-i],r[c+p-a],r[c+p-i-a])&255}}},UPNG.crc={table:function(){for(var e=new Uint32Array(256),r=0;r<256;r++){for(var t=r,a=0;a<8;a++)1&t?t=3988292384^t>>>1:t>>>=1;e[r]=t}return e}(),update:function update(e,r,t,a){for(var i=0;i<a;i++)e=UPNG.crc.table[255&(e^r[t+i])]^e>>>8;return e},crc:function crc(e,r,t){return 4294967295^UPNG.crc.update(4294967295,e,r,t)}},UPNG.quantize=function(e,r){var t,a=new Uint8Array(e),i=a.slice(0),s=new Uint32Array(i.buffer),c=UPNG.quantize.getKDtree(i,r),l=c[0],u=c[1],p=UPNG.quantize.planeDst,d=a,h=s,y=d.length,A=new Uint8Array(a.length>>2);if(a.length<2e7)for(var g=0;g<y;g+=4){var E=d[g]*(1/255),v=d[g+1]*(1/255),m=d[g+2]*(1/255),I=d[g+3]*(1/255);t=UPNG.quantize.getNearest(l,E,v,m,I),A[g>>2]=t.ind,h[g>>2]=t.est.rgba}else for(g=0;g<y;g+=4){E=d[g]*(1/255),v=d[g+1]*(1/255),m=d[g+2]*(1/255),I=d[g+3]*(1/255);for(t=l;t.left;)t=p(t.est,E,v,m,I)<=0?t.left:t.right;A[g>>2]=t.ind,h[g>>2]=t.est.rgba}return{abuf:i.buffer,inds:A,plte:u}},UPNG.quantize.getKDtree=function(e,r,t){null==t&&(t=1e-4);var a=new Uint32Array(e.buffer),i={i0:0,i1:e.length,bst:null,est:null,tdst:0,left:null,right:null};i.bst=UPNG.quantize.stats(e,i.i0,i.i1),i.est=UPNG.quantize.estats(i.bst);for(var s=[i];s.length<r;){for(var c=0,l=0,u=0;u<s.length;u++)s[u].est.L>c&&(c=s[u].est.L,l=u);if(c<t)break;var p=s[l],d=UPNG.quantize.splitPixels(e,a,p.i0,p.i1,p.est.e,p.est.eMq255);if(p.i0>=d||p.i1<=d)p.est.L=0;else{var h={i0:p.i0,i1:d,bst:null,est:null,tdst:0,left:null,right:null};h.bst=UPNG.quantize.stats(e,h.i0,h.i1),h.est=UPNG.quantize.estats(h.bst);var y={i0:d,i1:p.i1,bst:null,est:null,tdst:0,left:null,right:null};y.bst={R:[],m:[],N:p.bst.N-h.bst.N};for(u=0;u<16;u++)y.bst.R[u]=p.bst.R[u]-h.bst.R[u];for(u=0;u<4;u++)y.bst.m[u]=p.bst.m[u]-h.bst.m[u];y.est=UPNG.quantize.estats(y.bst),p.left=h,p.right=y,s[l]=h,s.push(y)}}s.sort((function(e,r){return r.bst.N-e.bst.N}));for(u=0;u<s.length;u++)s[u].ind=u;return[i,s]},UPNG.quantize.getNearest=function(e,r,t,a,i){if(null==e.left)return e.tdst=UPNG.quantize.dist(e.est.q,r,t,a,i),e;var s=UPNG.quantize.planeDst(e.est,r,t,a,i),c=e.left,l=e.right;s>0&&(c=e.right,l=e.left);var u=UPNG.quantize.getNearest(c,r,t,a,i);if(u.tdst<=s*s)return u;var p=UPNG.quantize.getNearest(l,r,t,a,i);return p.tdst<u.tdst?p:u},UPNG.quantize.planeDst=function(e,r,t,a,i){var s=e.e;return s[0]*r+s[1]*t+s[2]*a+s[3]*i-e.eMq},UPNG.quantize.dist=function(e,r,t,a,i){var s=r-e[0],c=t-e[1],l=a-e[2],u=i-e[3];return s*s+c*c+l*l+u*u},UPNG.quantize.splitPixels=function(e,r,t,a,i,s){var c=UPNG.quantize.vecDot;for(a-=4;t<a;){for(;c(e,t,i)<=s;)t+=4;for(;c(e,a,i)>s;)a-=4;if(t>=a)break;var l=r[t>>2];r[t>>2]=r[a>>2],r[a>>2]=l,t+=4,a-=4}for(;c(e,t,i)>s;)t-=4;return t+4},UPNG.quantize.vecDot=function(e,r,t){return e[r]*t[0]+e[r+1]*t[1]+e[r+2]*t[2]+e[r+3]*t[3]},UPNG.quantize.stats=function(e,r,t){for(var a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],i=[0,0,0,0],s=t-r>>2,c=r;c<t;c+=4){var l=e[c]*(1/255),u=e[c+1]*(1/255),p=e[c+2]*(1/255),d=e[c+3]*(1/255);i[0]+=l,i[1]+=u,i[2]+=p,i[3]+=d,a[0]+=l*l,a[1]+=l*u,a[2]+=l*p,a[3]+=l*d,a[5]+=u*u,a[6]+=u*p,a[7]+=u*d,a[10]+=p*p,a[11]+=p*d,a[15]+=d*d}return a[4]=a[1],a[8]=a[2],a[9]=a[6],a[12]=a[3],a[13]=a[7],a[14]=a[11],{R:a,m:i,N:s}},UPNG.quantize.estats=function(e){var r=e.R,t=e.m,a=e.N,i=t[0],s=t[1],c=t[2],l=t[3],u=0==a?0:1/a,p=[r[0]-i*i*u,r[1]-i*s*u,r[2]-i*c*u,r[3]-i*l*u,r[4]-s*i*u,r[5]-s*s*u,r[6]-s*c*u,r[7]-s*l*u,r[8]-c*i*u,r[9]-c*s*u,r[10]-c*c*u,r[11]-c*l*u,r[12]-l*i*u,r[13]-l*s*u,r[14]-l*c*u,r[15]-l*l*u],d=p,h=UPNG.M4,y=[Math.random(),Math.random(),Math.random(),Math.random()],A=0,g=0;if(0!=a)for(var E=0;E<16&&(y=h.multVec(d,y),g=Math.sqrt(h.dot(y,y)),y=h.sml(1/g,y),!(0!=E&&Math.abs(g-A)<1e-9));E++)A=g;var v=[i*u,s*u,c*u,l*u];return{Cov:p,q:v,e:y,L:A,eMq255:h.dot(h.sml(255,v),y),eMq:h.dot(y,v),rgba:(Math.round(255*v[3])<<24|Math.round(255*v[2])<<16|Math.round(255*v[1])<<8|Math.round(255*v[0])<<0)>>>0}},UPNG.M4={multVec:function multVec(e,r){return[e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3],e[4]*r[0]+e[5]*r[1]+e[6]*r[2]+e[7]*r[3],e[8]*r[0]+e[9]*r[1]+e[10]*r[2]+e[11]*r[3],e[12]*r[0]+e[13]*r[1]+e[14]*r[2]+e[15]*r[3]]},dot:function dot(e,r){return e[0]*r[0]+e[1]*r[1]+e[2]*r[2]+e[3]*r[3]},sml:function sml(e,r){return[e*r[0],e*r[1],e*r[2],e*r[3]]}},UPNG.encode.concatRGBA=function(e){for(var r=0,t=0;t<e.length;t++)r+=e[t].byteLength;var a=new Uint8Array(r),i=0;for(t=0;t<e.length;t++){for(var s=new Uint8Array(e[t]),c=s.length,l=0;l<c;l+=4){var u=s[l],p=s[l+1],d=s[l+2],h=s[l+3];0==h&&(u=p=d=0),a[i+l]=u,a[i+l+1]=p,a[i+l+2]=d,a[i+l+3]=h}i+=c}return a.buffer};var BROWSER_NAME={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",MOBILE_SAFARI:"MOBILE_SAFARI",ETC:"ETC"},_BROWSER_NAME$CHROME$,MAX_CANVAS_SIZE=(_BROWSER_NAME$CHROME$={},_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.CHROME,16384),_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.FIREFOX,11180),_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.DESKTOP_SAFARI,16384),_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.IE,8192),_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.MOBILE_SAFARI,4096),_defineProperty(_BROWSER_NAME$CHROME$,BROWSER_NAME.ETC,8192),_BROWSER_NAME$CHROME$),isBrowser="undefined"!=typeof window,moduleMapper=isBrowser&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),CustomFile=isBrowser&&(moduleMapper&&moduleMapper.getOriginalSymbol(window,"File")||void 0!==window.File&&File),CustomFileReader=isBrowser&&(moduleMapper&&moduleMapper.getOriginalSymbol(window,"FileReader")||void 0!==window.FileReader&&FileReader);function getFilefromDataUrl(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Date.now();return new Promise((function(a){for(var i=e.split(","),s=i[0].match(/:(.*?);/)[1],c=globalThis.atob(i[1]),l=c.length,u=new Uint8Array(l);l--;)u[l]=c.charCodeAt(l);var p=new Blob([u],{type:s});p.name=r,p.lastModified=t,a(p)}))}function getDataUrlFromFile(e){return new Promise((function(r,t){var a=new CustomFileReader;a.onload=function(){return r(a.result)},a.onerror=function(e){return t(e)},a.readAsDataURL(e)}))}function loadImage(e){return new Promise((function(r,t){var a=new Image;a.onload=function(){return r(a)},a.onerror=function(e){return t(e)},a.src=e}))}function getBrowserName(){if(void 0!==getBrowserName.cachedResult)return getBrowserName.cachedResult;var e=BROWSER_NAME.ETC,r=navigator.userAgent;return/Chrom(e|ium)/i.test(r)?e=BROWSER_NAME.CHROME:/iP(ad|od|hone)/i.test(r)&&/WebKit/i.test(r)&&!/(CriOS|FxiOS|OPiOS|mercury)/i.test(r)?e=BROWSER_NAME.MOBILE_SAFARI:/Safari/i.test(r)?e=BROWSER_NAME.DESKTOP_SAFARI:/Firefox/i.test(r)?e=BROWSER_NAME.FIREFOX:(/MSIE/i.test(r)||!0==!!document.documentMode)&&(e=BROWSER_NAME.IE),getBrowserName.cachedResult=e,getBrowserName.cachedResult}function approximateBelowMaximumCanvasSizeOfBrowser(e,r){for(var t=getBrowserName(),a=MAX_CANVAS_SIZE[t],i=e,s=r,c=i*s,l=i>s?s/i:i/s;c>a*a;){var u=(a+i)/2,p=(a+s)/2;u<p?(s=p,i=p*l):(s=u*l,i=u),c=i*s}return{width:i,height:s}}function getNewCanvasAndCtx(e,r){var t,a;try{if(null===(a=(t=new OffscreenCanvas(e,r)).getContext("2d")))throw new Error("getContext of OffscreenCanvas returns null")}catch(e){a=(t=document.createElement("canvas")).getContext("2d")}return t.width=e,t.height=r,[t,a]}function drawImageInCanvas(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,t=approximateBelowMaximumCanvasSizeOfBrowser(e.width,e.height),a=t.width,i=t.height,s=getNewCanvasAndCtx(a,i),c=_slicedToArray(s,2),l=c[0],u=c[1];return r&&/jpe?g/.test(r)&&(u.fillStyle="white",u.fillRect(0,0,l.width,l.height)),u.drawImage(e,0,0,l.width,l.height),l}function isIOS(){return void 0!==isIOS.cachedResult||(isIOS.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document),isIOS.cachedResult}function drawFileInCanvas(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(t,a){var i,s,c=function $Try_2_Post(){try{return s=drawImageInCanvas(i,r.fileType||e.type),t([i,s])}catch(e){return a(e)}},l=function $Try_2_Catch(r){try{0;var t=function $Try_3_Catch(e){try{throw e}catch(e){return a(e)}};try{return getDataUrlFromFile(e).then((function(e){try{return loadImage(e).then((function(e){try{return i=e,function $Try_3_Post(){try{return c()}catch(e){return a(e)}}()}catch(e){return t(e)}}),t)}catch(e){return t(e)}}),t)}catch(e){t(e)}}catch(e){return a(e)}};try{if(isIOS()||[BROWSER_NAME.DESKTOP_SAFARI,BROWSER_NAME.MOBILE_SAFARI].includes(getBrowserName()))throw new Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(e).then((function(e){try{return i=e,c()}catch(e){return l()}}),l)}catch(e){l()}}))}function canvasToFile(e,r,t,a){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;return new Promise((function(s,c){var l,u,p;if("image/png"===r)return u=e.getContext("2d").getImageData(0,0,e.width,e.height).data,p=UPNG.encode([u],e.width,e.height,256*i),(l=new Blob([p],{type:r})).name=t,l.lastModified=a,$If_4.call(this);{return"function"==typeof OffscreenCanvas&&e instanceof OffscreenCanvas?e.convertToBlob({type:r,quality:i}).then(function(e){try{return(l=e).name=t,l.lastModified=a,$If_5.call(this)}catch(e){return c(e)}}.bind(this),c):getFilefromDataUrl(e.toDataURL(r,i),t,a).then(function(e){try{return l=e,$If_5.call(this)}catch(e){return c(e)}}.bind(this),c);function $If_5(){return $If_4.call(this)}}function $If_4(){return s(l)}}))}function cleanupCanvasMemory(e){e.width=0,e.height=0}function isAutoOrientationInBrowser(){return new Promise((function(e,r){var t,a,i,s;return void 0!==isAutoOrientationInBrowser.cachedResult?e(isAutoOrientationInBrowser.cachedResult):("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==",getFilefromDataUrl("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then((function(c){try{return drawFileInCanvas(t=c).then((function(c){try{return canvasToFile(a=c[1],t.type,t.name,t.lastModified).then((function(t){try{return i=t,cleanupCanvasMemory(a),drawFileInCanvas(i).then((function(t){try{return s=t[0],isAutoOrientationInBrowser.cachedResult=1===s.width&&2===s.height,e(isAutoOrientationInBrowser.cachedResult)}catch(e){return r(e)}}),r)}catch(e){return r(e)}}),r)}catch(e){return r(e)}}),r)}catch(e){return r(e)}}),r))}))}function getExifOrientation(e){return new Promise((function(r,t){var a=new CustomFileReader;a.onload=function(e){var t=new DataView(e.target.result);if(65496!=t.getUint16(0,!1))return r(-2);for(var a=t.byteLength,i=2;i<a;){if(t.getUint16(i+2,!1)<=8)return r(-1);var s=t.getUint16(i,!1);if(i+=2,65505==s){if(1165519206!=t.getUint32(i+=2,!1))return r(-1);var c=18761==t.getUint16(i+=6,!1);i+=t.getUint32(i+4,c);var l=t.getUint16(i,c);i+=2;for(var u=0;u<l;u++)if(274==t.getUint16(i+12*u,c))return r(t.getUint16(i+12*u+8,c))}else{if(65280!=(65280&s))break;i+=t.getUint16(i,!1)}}return r(-1)},a.onerror=function(e){return t(e)},a.readAsArrayBuffer(e)}))}function handleMaxWidthOrHeight(e,r){var t,a=e.width,i=e.height,s=r.maxWidthOrHeight,c=e;if(isFinite(s)&&(a>s||i>s)){var l=_slicedToArray(getNewCanvasAndCtx(a,i),2);c=l[0],t=l[1],a>i?(c.width=s,c.height=i/a*s):(c.width=a/i*s,c.height=s),t.drawImage(e,0,0,c.width,c.height),cleanupCanvasMemory(e)}return c}function followExifOrientation(e,r){var t=e.width,a=e.height,i=_slicedToArray(getNewCanvasAndCtx(t,a),2),s=i[0],c=i[1];switch(r>4&&r<9?(s.width=a,s.height=t):(s.width=t,s.height=a),r){case 2:c.transform(-1,0,0,1,t,0);break;case 3:c.transform(-1,0,0,-1,t,a);break;case 4:c.transform(1,0,0,-1,0,a);break;case 5:c.transform(0,1,1,0,0,0);break;case 6:c.transform(0,1,-1,0,a,0);break;case 7:c.transform(0,-1,-1,0,a,t);break;case 8:c.transform(0,-1,1,0,0,t)}return c.drawImage(e,0,0,t,a),cleanupCanvasMemory(e),s}function compress(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return new Promise((function(a,i){var s,c,l,u,p,d,h,y,A,g,E,v,m,I,R,P,S,b;function incProgress(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;s+=e,r.onProgress(Math.min(s,100))}function setProgress(e){s=Math.min(Math.max(e,s),100),r.onProgress(s)}return s=t,c=r.maxIteration||10,l=1024*r.maxSizeMB*1024,incProgress(),drawFileInCanvas(e,r).then(function(t){try{var s=_slicedToArray(t,2);return u=s[1],incProgress(),p=handleMaxWidthOrHeight(u,r),incProgress(),new Promise((function(t,a){var i;if(!(i=r.exifOrientation))return getExifOrientation(e).then(function(e){try{return i=e,$If_2.call(this)}catch(e){return a(e)}}.bind(this),a);function $If_2(){return t(i)}return $If_2.call(this)})).then(function(t){try{return d=t,incProgress(),isAutoOrientationInBrowser().then(function(t){try{return h=t?p:followExifOrientation(p,d),incProgress(),y=r.initialQuality||1,A=r.fileType||e.type,canvasToFile(h,A,e.name,e.lastModified,y).then(function(r){try{{if(g=r,incProgress(),E=g.size>l,v=g.size>e.size,!E&&!v)return setProgress(100),a(g);var t;function $Loop_3(){if(c--&&(R>l||R>m)){var r,t,a=_slicedToArray(getNewCanvasAndCtx(r=E?.95*b.width:b.width,t=E?.95*b.height:b.height),2);return S=a[0],a[1].drawImage(b,0,0,r,t),y*=.95,canvasToFile(S,A,e.name,e.lastModified,y).then((function(e){try{return P=e,cleanupCanvasMemory(b),b=S,R=P.size,setProgress(Math.min(99,Math.floor((I-R)/(I-l)*100))),$Loop_3}catch(e){return i(e)}}),i)}return[1]}return m=e.size,I=g.size,R=I,b=h,(t=function(e){for(;e;){if(e.then)return void e.then(t,i);try{if(e.pop){if(e.length)return e.pop()?$Loop_3_exit.call(this):e;e=$Loop_3}else e=e.call(this)}catch(e){return i(e)}}}.bind(this))($Loop_3);function $Loop_3_exit(){return cleanupCanvasMemory(b),cleanupCanvasMemory(S),cleanupCanvasMemory(p),cleanupCanvasMemory(h),cleanupCanvasMemory(u),setProgress(100),a(P)}}}catch(e){return i(e)}}.bind(this),i)}catch(e){return i(e)}}.bind(this),i)}catch(e){return i(e)}}.bind(this),i)}catch(e){return i(e)}}.bind(this),i)}))}var charAt=stringMultibyte.charAt,STRING_ITERATOR="String Iterator",setInternalState$2=internalState.set,getInternalState=internalState.getterFor(STRING_ITERATOR);defineIterator(String,"String",(function(e){setInternalState$2(this,{type:STRING_ITERATOR,string:toString(e),index:0})}),(function next(){var e,r=getInternalState(this),t=r.string,a=r.index;return a>=t.length?{value:void 0,done:!0}:(e=charAt(t,a),r.index+=e.length,{value:e,done:!1})}));var domIterables={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},classList=documentCreateElement("span").classList,DOMTokenListPrototype=classList&&classList.constructor&&classList.constructor.prototype,domTokenListPrototype=DOMTokenListPrototype===Object.prototype?void 0:DOMTokenListPrototype,ITERATOR$2=wellKnownSymbol("iterator"),TO_STRING_TAG=wellKnownSymbol("toStringTag"),ArrayValues=es_array_iterator.values,handlePrototype$1=function(e,r){if(e){if(e[ITERATOR$2]!==ArrayValues)try{createNonEnumerableProperty(e,ITERATOR$2,ArrayValues)}catch(r){e[ITERATOR$2]=ArrayValues}if(e[TO_STRING_TAG]||createNonEnumerableProperty(e,TO_STRING_TAG,r),domIterables[r])for(var t in es_array_iterator)if(e[t]!==es_array_iterator[t])try{createNonEnumerableProperty(e,t,es_array_iterator[t])}catch(r){e[t]=es_array_iterator[t]}}};for(var COLLECTION_NAME$1 in domIterables)handlePrototype$1(global$1[COLLECTION_NAME$1]&&global$1[COLLECTION_NAME$1].prototype,COLLECTION_NAME$1);handlePrototype$1(domTokenListPrototype,"DOMTokenList");var ITERATOR$1=wellKnownSymbol("iterator"),nativeUrl=!fails((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),r=e.searchParams,t="";return e.pathname="c%20d",r.forEach((function(e,a){r.delete("b"),t+=a+e})),isPure&&!e.toJSON||!r.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==r.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!r[ITERATOR$1]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==t||"x"!==new URL("http://x",void 0).host})),callWithSafeIterationClosing=function(e,r,t,a){try{return a?r(anObject(t)[0],t[1]):r(t)}catch(r){iteratorClose(e,"throw",r)}},arrayFrom=function from(e){var r=toObject(e),t=isConstructor(this),a=arguments.length,i=a>1?arguments[1]:void 0,s=void 0!==i;s&&(i=functionBindContext(i,a>2?arguments[2]:void 0,2));var c,l,u,p,d,h,y=getIteratorMethod(r),A=0;if(!y||this==Array&&isArrayIteratorMethod(y))for(c=toLength(r.length),l=t?new this(c):Array(c);c>A;A++)h=s?i(r[A],A):r[A],createProperty(l,A,h);else for(d=(p=getIterator(r,y)).next,l=t?new this:[];!(u=d.call(p)).done;A++)h=s?callWithSafeIterationClosing(p,i,[u.value,A],!0):u.value,createProperty(l,A,h);return l.length=A,l},maxInt=2147483647,base=36,tMin=1,tMax=26,skew=38,damp=700,initialBias=72,initialN=128,delimiter="-",regexNonASCII=/[^\0-\u007E]/,regexSeparators=/[.\u3002\uFF0E\uFF61]/g,OVERFLOW_ERROR="Overflow: input needs wider integers to process",baseMinusTMin=base-tMin,floor$2=Math.floor,stringFromCharCode=String.fromCharCode,ucs2decode=function(e){for(var r=[],t=0,a=e.length;t<a;){var i=e.charCodeAt(t++);if(i>=55296&&i<=56319&&t<a){var s=e.charCodeAt(t++);56320==(64512&s)?r.push(((1023&i)<<10)+(1023&s)+65536):(r.push(i),t--)}else r.push(i)}return r},digitToBasic=function(e){return e+22+75*(e<26)},adapt=function(e,r,t){var a=0;for(e=t?floor$2(e/damp):e>>1,e+=floor$2(e/r);e>baseMinusTMin*tMax>>1;a+=base)e=floor$2(e/baseMinusTMin);return floor$2(a+(baseMinusTMin+1)*e/(e+skew))},encode=function(e){var r,t,a=[],i=(e=ucs2decode(e)).length,s=initialN,c=0,l=initialBias;for(r=0;r<e.length;r++)(t=e[r])<128&&a.push(stringFromCharCode(t));var u=a.length,p=u;for(u&&a.push(delimiter);p<i;){var d=maxInt;for(r=0;r<e.length;r++)(t=e[r])>=s&&t<d&&(d=t);var h=p+1;if(d-s>floor$2((maxInt-c)/h))throw RangeError(OVERFLOW_ERROR);for(c+=(d-s)*h,s=d,r=0;r<e.length;r++){if((t=e[r])<s&&++c>maxInt)throw RangeError(OVERFLOW_ERROR);if(t==s){for(var y=c,A=base;;A+=base){var g=A<=l?tMin:A>=l+tMax?tMax:A-l;if(y<g)break;var E=y-g,v=base-g;a.push(stringFromCharCode(digitToBasic(g+E%v))),y=floor$2(E/v)}a.push(stringFromCharCode(digitToBasic(y))),l=adapt(c,h,p==u),c=0,++p}}++c,++s}return a.join("")},stringPunycodeToAscii=function(e){var r,t,a=[],i=e.toLowerCase().replace(regexSeparators,".").split(".");for(r=0;r<i.length;r++)t=i[r],a.push(regexNonASCII.test(t)?"xn--"+encode(t):t);return a.join(".")},nativeFetch=getBuiltIn("fetch"),NativeRequest=getBuiltIn("Request"),RequestPrototype=NativeRequest&&NativeRequest.prototype,Headers=getBuiltIn("Headers"),ITERATOR=wellKnownSymbol("iterator"),URL_SEARCH_PARAMS="URLSearchParams",URL_SEARCH_PARAMS_ITERATOR=URL_SEARCH_PARAMS+"Iterator",setInternalState$1=internalState.set,getInternalParamsState=internalState.getterFor(URL_SEARCH_PARAMS),getInternalIteratorState=internalState.getterFor(URL_SEARCH_PARAMS_ITERATOR),plus=/\+/g,sequences=Array(4),percentSequence=function(e){return sequences[e-1]||(sequences[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},percentDecode=function(e){try{return decodeURIComponent(e)}catch(r){return e}},deserialize=function(e){var r=e.replace(plus," "),t=4;try{return decodeURIComponent(r)}catch(e){for(;t;)r=r.replace(percentSequence(t--),percentDecode);return r}},find=/[!'()~]|%20/g,replace$1={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},replacer=function(e){return replace$1[e]},serialize=function(e){return encodeURIComponent(e).replace(find,replacer)},parseSearchParams=function(e,r){if(r)for(var t,a,i=r.split("&"),s=0;s<i.length;)(t=i[s++]).length&&(a=t.split("="),e.push({key:deserialize(a.shift()),value:deserialize(a.join("="))}))},updateSearchParams=function(e){this.entries.length=0,parseSearchParams(this.entries,e)},validateArgumentsLength=function(e,r){if(e<r)throw TypeError("Not enough arguments")},URLSearchParamsIterator=createIteratorConstructor((function Iterator(e,r){setInternalState$1(this,{type:URL_SEARCH_PARAMS_ITERATOR,iterator:getIterator(getInternalParamsState(e).entries),kind:r})}),"Iterator",(function next(){var e=getInternalIteratorState(this),r=e.kind,t=e.iterator.next(),a=t.value;return t.done||(t.value="keys"===r?a.key:"values"===r?a.value:[a.key,a.value]),t})),URLSearchParamsConstructor=function URLSearchParams(){anInstance(this,URLSearchParamsConstructor,URL_SEARCH_PARAMS);var e,r,t,a,i,s,c,l,u,p=arguments.length>0?arguments[0]:void 0,d=this,h=[];if(setInternalState$1(d,{type:URL_SEARCH_PARAMS,entries:h,updateURL:function(){},updateSearchParams:updateSearchParams}),void 0!==p)if(isObject(p))if(e=getIteratorMethod(p))for(t=(r=getIterator(p,e)).next;!(a=t.call(r)).done;){if((c=(s=(i=getIterator(anObject(a.value))).next).call(i)).done||(l=s.call(i)).done||!s.call(i).done)throw TypeError("Expected sequence with length 2");h.push({key:toString(c.value),value:toString(l.value)})}else for(u in p)has$1(p,u)&&h.push({key:u,value:toString(p[u])});else parseSearchParams(h,"string"==typeof p?"?"===p.charAt(0)?p.slice(1):p:toString(p))},URLSearchParamsPrototype=URLSearchParamsConstructor.prototype;if(redefineAll(URLSearchParamsPrototype,{append:function append(e,r){validateArgumentsLength(arguments.length,2);var t=getInternalParamsState(this);t.entries.push({key:toString(e),value:toString(r)}),t.updateURL()},delete:function(e){validateArgumentsLength(arguments.length,1);for(var r=getInternalParamsState(this),t=r.entries,a=toString(e),i=0;i<t.length;)t[i].key===a?t.splice(i,1):i++;r.updateURL()},get:function get(e){validateArgumentsLength(arguments.length,1);for(var r=getInternalParamsState(this).entries,t=toString(e),a=0;a<r.length;a++)if(r[a].key===t)return r[a].value;return null},getAll:function getAll(e){validateArgumentsLength(arguments.length,1);for(var r=getInternalParamsState(this).entries,t=toString(e),a=[],i=0;i<r.length;i++)r[i].key===t&&a.push(r[i].value);return a},has:function has(e){validateArgumentsLength(arguments.length,1);for(var r=getInternalParamsState(this).entries,t=toString(e),a=0;a<r.length;)if(r[a++].key===t)return!0;return!1},set:function set(e,r){validateArgumentsLength(arguments.length,1);for(var t,a=getInternalParamsState(this),i=a.entries,s=!1,c=toString(e),l=toString(r),u=0;u<i.length;u++)(t=i[u]).key===c&&(s?i.splice(u--,1):(s=!0,t.value=l));s||i.push({key:c,value:l}),a.updateURL()},sort:function sort(){var e,r,t,a=getInternalParamsState(this),i=a.entries,s=i.slice();for(i.length=0,t=0;t<s.length;t++){for(e=s[t],r=0;r<t;r++)if(i[r].key>e.key){i.splice(r,0,e);break}r===t&&i.push(e)}a.updateURL()},forEach:function forEach(e){for(var r,t=getInternalParamsState(this).entries,a=functionBindContext(e,arguments.length>1?arguments[1]:void 0,3),i=0;i<t.length;)a((r=t[i++]).value,r.key,this)},keys:function keys(){return new URLSearchParamsIterator(this,"keys")},values:function values(){return new URLSearchParamsIterator(this,"values")},entries:function entries(){return new URLSearchParamsIterator(this,"entries")}},{enumerable:!0}),redefine(URLSearchParamsPrototype,ITERATOR,URLSearchParamsPrototype.entries,{name:"entries"}),redefine(URLSearchParamsPrototype,"toString",(function toString(){for(var e,r=getInternalParamsState(this).entries,t=[],a=0;a<r.length;)e=r[a++],t.push(serialize(e.key)+"="+serialize(e.value));return t.join("&")}),{enumerable:!0}),setToStringTag(URLSearchParamsConstructor,URL_SEARCH_PARAMS),_export({global:!0,forced:!nativeUrl},{URLSearchParams:URLSearchParamsConstructor}),!nativeUrl&&isCallable(Headers)){var wrapRequestOptions=function(e){if(isObject(e)){var r,t=e.body;if(classof(t)===URL_SEARCH_PARAMS)return(r=e.headers?new Headers(e.headers):new Headers).has("content-type")||r.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),objectCreate(e,{body:createPropertyDescriptor(0,String(t)),headers:createPropertyDescriptor(0,r)})}return e};if(isCallable(nativeFetch)&&_export({global:!0,enumerable:!0,forced:!0},{fetch:function fetch(e){return nativeFetch(e,arguments.length>1?wrapRequestOptions(arguments[1]):{})}}),isCallable(NativeRequest)){var RequestConstructor=function Request(e){return anInstance(this,RequestConstructor,"Request"),new NativeRequest(e,arguments.length>1?wrapRequestOptions(arguments[1]):{})};RequestPrototype.constructor=RequestConstructor,RequestConstructor.prototype=RequestPrototype,_export({global:!0,forced:!0},{Request:RequestConstructor})}}var web_urlSearchParams={URLSearchParams:URLSearchParamsConstructor,getState:getInternalParamsState},codeAt=stringMultibyte.codeAt,NativeURL=global$1.URL,URLSearchParams$1=web_urlSearchParams.URLSearchParams,getInternalSearchParamsState=web_urlSearchParams.getState,setInternalState=internalState.set,getInternalURLState=internalState.getterFor("URL"),floor$1=Math.floor,pow=Math.pow,INVALID_AUTHORITY="Invalid authority",INVALID_SCHEME="Invalid scheme",INVALID_HOST="Invalid host",INVALID_PORT="Invalid port",ALPHA=/[A-Za-z]/,ALPHANUMERIC=/[\d+-.A-Za-z]/,DIGIT=/\d/,HEX_START=/^0x/i,OCT=/^[0-7]+$/,DEC=/^\d+$/,HEX=/^[\dA-Fa-f]+$/,FORBIDDEN_HOST_CODE_POINT=/[\0\t\n\r #%/:<>?@[\\\]^|]/,FORBIDDEN_HOST_CODE_POINT_EXCLUDING_PERCENT=/[\0\t\n\r #/:<>?@[\\\]^|]/,LEADING_AND_TRAILING_C0_CONTROL_OR_SPACE=/^[\u0000-\u0020]+|[\u0000-\u0020]+$/g,TAB_AND_NEW_LINE=/[\t\n\r]/g,EOF,parseHost=function(e,r){var t,a,i;if("["==r.charAt(0)){if("]"!=r.charAt(r.length-1))return INVALID_HOST;if(!(t=parseIPv6(r.slice(1,-1))))return INVALID_HOST;e.host=t}else if(isSpecial(e)){if(r=stringPunycodeToAscii(r),FORBIDDEN_HOST_CODE_POINT.test(r))return INVALID_HOST;if(null===(t=parseIPv4(r)))return INVALID_HOST;e.host=t}else{if(FORBIDDEN_HOST_CODE_POINT_EXCLUDING_PERCENT.test(r))return INVALID_HOST;for(t="",a=arrayFrom(r),i=0;i<a.length;i++)t+=percentEncode(a[i],C0ControlPercentEncodeSet);e.host=t}},parseIPv4=function(e){var r,t,a,i,s,c,l,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(r=u.length)>4)return e;for(t=[],a=0;a<r;a++){if(""==(i=u[a]))return e;if(s=10,i.length>1&&"0"==i.charAt(0)&&(s=HEX_START.test(i)?16:8,i=i.slice(8==s?1:2)),""===i)c=0;else{if(!(10==s?DEC:8==s?OCT:HEX).test(i))return e;c=parseInt(i,s)}t.push(c)}for(a=0;a<r;a++)if(c=t[a],a==r-1){if(c>=pow(256,5-r))return null}else if(c>255)return null;for(l=t.pop(),a=0;a<t.length;a++)l+=t[a]*pow(256,3-a);return l},parseIPv6=function(e){var r,t,a,i,s,c,l,u=[0,0,0,0,0,0,0,0],p=0,d=null,h=0,chr=function(){return e.charAt(h)};if(":"==chr()){if(":"!=e.charAt(1))return;h+=2,d=++p}for(;chr();){if(8==p)return;if(":"!=chr()){for(r=t=0;t<4&&HEX.test(chr());)r=16*r+parseInt(chr(),16),h++,t++;if("."==chr()){if(0==t)return;if(h-=t,p>6)return;for(a=0;chr();){if(i=null,a>0){if(!("."==chr()&&a<4))return;h++}if(!DIGIT.test(chr()))return;for(;DIGIT.test(chr());){if(s=parseInt(chr(),10),null===i)i=s;else{if(0==i)return;i=10*i+s}if(i>255)return;h++}u[p]=256*u[p]+i,2!=++a&&4!=a||p++}if(4!=a)return;break}if(":"==chr()){if(h++,!chr())return}else if(chr())return;u[p++]=r}else{if(null!==d)return;h++,d=++p}}if(null!==d)for(c=p-d,p=7;0!=p&&c>0;)l=u[p],u[p--]=u[d+c-1],u[d+--c]=l;else if(8!=p)return;return u},findLongestZeroSequence=function(e){for(var r=null,t=1,a=null,i=0,s=0;s<8;s++)0!==e[s]?(i>t&&(r=a,t=i),a=null,i=0):(null===a&&(a=s),++i);return i>t&&(r=a,t=i),r},serializeHost=function(e){var r,t,a,i;if("number"==typeof e){for(r=[],t=0;t<4;t++)r.unshift(e%256),e=floor$1(e/256);return r.join(".")}if("object"==typeof e){for(r="",a=findLongestZeroSequence(e),t=0;t<8;t++)i&&0===e[t]||(i&&(i=!1),a===t?(r+=t?":":"::",i=!0):(r+=e[t].toString(16),t<7&&(r+=":")));return"["+r+"]"}return e},C0ControlPercentEncodeSet={},fragmentPercentEncodeSet=objectAssign({},C0ControlPercentEncodeSet,{" ":1,'"':1,"<":1,">":1,"`":1}),pathPercentEncodeSet=objectAssign({},fragmentPercentEncodeSet,{"#":1,"?":1,"{":1,"}":1}),userinfoPercentEncodeSet=objectAssign({},pathPercentEncodeSet,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),percentEncode=function(e,r){var t=codeAt(e,0);return t>32&&t<127&&!has$1(r,e)?e:encodeURIComponent(e)},specialSchemes={ftp:21,file:null,http:80,https:443,ws:80,wss:443},isSpecial=function(e){return has$1(specialSchemes,e.scheme)},includesCredentials=function(e){return""!=e.username||""!=e.password},cannotHaveUsernamePasswordPort=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},isWindowsDriveLetter=function(e,r){var t;return 2==e.length&&ALPHA.test(e.charAt(0))&&(":"==(t=e.charAt(1))||!r&&"|"==t)},startsWithWindowsDriveLetter=function(e){var r;return e.length>1&&isWindowsDriveLetter(e.slice(0,2))&&(2==e.length||"/"===(r=e.charAt(2))||"\\"===r||"?"===r||"#"===r)},shortenURLsPath=function(e){var r=e.path,t=r.length;!t||"file"==e.scheme&&1==t&&isWindowsDriveLetter(r[0],!0)||r.pop()},isSingleDot=function(e){return"."===e||"%2e"===e.toLowerCase()},isDoubleDot=function(e){return".."===(e=e.toLowerCase())||"%2e."===e||".%2e"===e||"%2e%2e"===e},SCHEME_START={},SCHEME={},NO_SCHEME={},SPECIAL_RELATIVE_OR_AUTHORITY={},PATH_OR_AUTHORITY={},RELATIVE={},RELATIVE_SLASH={},SPECIAL_AUTHORITY_SLASHES={},SPECIAL_AUTHORITY_IGNORE_SLASHES={},AUTHORITY={},HOST={},HOSTNAME={},PORT={},FILE={},FILE_SLASH={},FILE_HOST={},PATH_START={},PATH={},CANNOT_BE_A_BASE_URL_PATH={},QUERY={},FRAGMENT={},parseURL=function(e,r,t,a){var i,s,c,l,u=t||SCHEME_START,p=0,d="",h=!1,y=!1,A=!1;for(t||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,r=r.replace(LEADING_AND_TRAILING_C0_CONTROL_OR_SPACE,"")),r=r.replace(TAB_AND_NEW_LINE,""),i=arrayFrom(r);p<=i.length;){switch(s=i[p],u){case SCHEME_START:if(!s||!ALPHA.test(s)){if(t)return INVALID_SCHEME;u=NO_SCHEME;continue}d+=s.toLowerCase(),u=SCHEME;break;case SCHEME:if(s&&(ALPHANUMERIC.test(s)||"+"==s||"-"==s||"."==s))d+=s.toLowerCase();else{if(":"!=s){if(t)return INVALID_SCHEME;d="",u=NO_SCHEME,p=0;continue}if(t&&(isSpecial(e)!=has$1(specialSchemes,d)||"file"==d&&(includesCredentials(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=d,t)return void(isSpecial(e)&&specialSchemes[e.scheme]==e.port&&(e.port=null));d="","file"==e.scheme?u=FILE:isSpecial(e)&&a&&a.scheme==e.scheme?u=SPECIAL_RELATIVE_OR_AUTHORITY:isSpecial(e)?u=SPECIAL_AUTHORITY_SLASHES:"/"==i[p+1]?(u=PATH_OR_AUTHORITY,p++):(e.cannotBeABaseURL=!0,e.path.push(""),u=CANNOT_BE_A_BASE_URL_PATH)}break;case NO_SCHEME:if(!a||a.cannotBeABaseURL&&"#"!=s)return INVALID_SCHEME;if(a.cannotBeABaseURL&&"#"==s){e.scheme=a.scheme,e.path=a.path.slice(),e.query=a.query,e.fragment="",e.cannotBeABaseURL=!0,u=FRAGMENT;break}u="file"==a.scheme?FILE:RELATIVE;continue;case SPECIAL_RELATIVE_OR_AUTHORITY:if("/"!=s||"/"!=i[p+1]){u=RELATIVE;continue}u=SPECIAL_AUTHORITY_IGNORE_SLASHES,p++;break;case PATH_OR_AUTHORITY:if("/"==s){u=AUTHORITY;break}u=PATH;continue;case RELATIVE:if(e.scheme=a.scheme,s==EOF)e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query=a.query;else if("/"==s||"\\"==s&&isSpecial(e))u=RELATIVE_SLASH;else if("?"==s)e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query="",u=QUERY;else{if("#"!=s){e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.path.pop(),u=PATH;continue}e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,e.path=a.path.slice(),e.query=a.query,e.fragment="",u=FRAGMENT}break;case RELATIVE_SLASH:if(!isSpecial(e)||"/"!=s&&"\\"!=s){if("/"!=s){e.username=a.username,e.password=a.password,e.host=a.host,e.port=a.port,u=PATH;continue}u=AUTHORITY}else u=SPECIAL_AUTHORITY_IGNORE_SLASHES;break;case SPECIAL_AUTHORITY_SLASHES:if(u=SPECIAL_AUTHORITY_IGNORE_SLASHES,"/"!=s||"/"!=d.charAt(p+1))continue;p++;break;case SPECIAL_AUTHORITY_IGNORE_SLASHES:if("/"!=s&&"\\"!=s){u=AUTHORITY;continue}break;case AUTHORITY:if("@"==s){h&&(d="%40"+d),h=!0,c=arrayFrom(d);for(var g=0;g<c.length;g++){var E=c[g];if(":"!=E||A){var v=percentEncode(E,userinfoPercentEncodeSet);A?e.password+=v:e.username+=v}else A=!0}d=""}else if(s==EOF||"/"==s||"?"==s||"#"==s||"\\"==s&&isSpecial(e)){if(h&&""==d)return INVALID_AUTHORITY;p-=arrayFrom(d).length+1,d="",u=HOST}else d+=s;break;case HOST:case HOSTNAME:if(t&&"file"==e.scheme){u=FILE_HOST;continue}if(":"!=s||y){if(s==EOF||"/"==s||"?"==s||"#"==s||"\\"==s&&isSpecial(e)){if(isSpecial(e)&&""==d)return INVALID_HOST;if(t&&""==d&&(includesCredentials(e)||null!==e.port))return;if(l=parseHost(e,d))return l;if(d="",u=PATH_START,t)return;continue}"["==s?y=!0:"]"==s&&(y=!1),d+=s}else{if(""==d)return INVALID_HOST;if(l=parseHost(e,d))return l;if(d="",u=PORT,t==HOSTNAME)return}break;case PORT:if(!DIGIT.test(s)){if(s==EOF||"/"==s||"?"==s||"#"==s||"\\"==s&&isSpecial(e)||t){if(""!=d){var m=parseInt(d,10);if(m>65535)return INVALID_PORT;e.port=isSpecial(e)&&m===specialSchemes[e.scheme]?null:m,d=""}if(t)return;u=PATH_START;continue}return INVALID_PORT}d+=s;break;case FILE:if(e.scheme="file","/"==s||"\\"==s)u=FILE_SLASH;else{if(!a||"file"!=a.scheme){u=PATH;continue}if(s==EOF)e.host=a.host,e.path=a.path.slice(),e.query=a.query;else if("?"==s)e.host=a.host,e.path=a.path.slice(),e.query="",u=QUERY;else{if("#"!=s){startsWithWindowsDriveLetter(i.slice(p).join(""))||(e.host=a.host,e.path=a.path.slice(),shortenURLsPath(e)),u=PATH;continue}e.host=a.host,e.path=a.path.slice(),e.query=a.query,e.fragment="",u=FRAGMENT}}break;case FILE_SLASH:if("/"==s||"\\"==s){u=FILE_HOST;break}a&&"file"==a.scheme&&!startsWithWindowsDriveLetter(i.slice(p).join(""))&&(isWindowsDriveLetter(a.path[0],!0)?e.path.push(a.path[0]):e.host=a.host),u=PATH;continue;case FILE_HOST:if(s==EOF||"/"==s||"\\"==s||"?"==s||"#"==s){if(!t&&isWindowsDriveLetter(d))u=PATH;else if(""==d){if(e.host="",t)return;u=PATH_START}else{if(l=parseHost(e,d))return l;if("localhost"==e.host&&(e.host=""),t)return;d="",u=PATH_START}continue}d+=s;break;case PATH_START:if(isSpecial(e)){if(u=PATH,"/"!=s&&"\\"!=s)continue}else if(t||"?"!=s)if(t||"#"!=s){if(s!=EOF&&(u=PATH,"/"!=s))continue}else e.fragment="",u=FRAGMENT;else e.query="",u=QUERY;break;case PATH:if(s==EOF||"/"==s||"\\"==s&&isSpecial(e)||!t&&("?"==s||"#"==s)){if(isDoubleDot(d)?(shortenURLsPath(e),"/"==s||"\\"==s&&isSpecial(e)||e.path.push("")):isSingleDot(d)?"/"==s||"\\"==s&&isSpecial(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&isWindowsDriveLetter(d)&&(e.host&&(e.host=""),d=d.charAt(0)+":"),e.path.push(d)),d="","file"==e.scheme&&(s==EOF||"?"==s||"#"==s))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==s?(e.query="",u=QUERY):"#"==s&&(e.fragment="",u=FRAGMENT)}else d+=percentEncode(s,pathPercentEncodeSet);break;case CANNOT_BE_A_BASE_URL_PATH:"?"==s?(e.query="",u=QUERY):"#"==s?(e.fragment="",u=FRAGMENT):s!=EOF&&(e.path[0]+=percentEncode(s,C0ControlPercentEncodeSet));break;case QUERY:t||"#"!=s?s!=EOF&&("'"==s&&isSpecial(e)?e.query+="%27":e.query+="#"==s?"%23":percentEncode(s,C0ControlPercentEncodeSet)):(e.fragment="",u=FRAGMENT);break;case FRAGMENT:s!=EOF&&(e.fragment+=percentEncode(s,fragmentPercentEncodeSet))}p++}},URLConstructor=function URL(e){var r,t,a=anInstance(this,URLConstructor,"URL"),i=arguments.length>1?arguments[1]:void 0,s=toString(e),c=setInternalState(a,{type:"URL"});if(void 0!==i)if(i instanceof URLConstructor)r=getInternalURLState(i);else if(t=parseURL(r={},toString(i)))throw TypeError(t);if(t=parseURL(c,s,null,r))throw TypeError(t);var l=c.searchParams=new URLSearchParams$1,u=getInternalSearchParamsState(l);u.updateSearchParams(c.query),u.updateURL=function(){c.query=String(l)||null},descriptors||(a.href=serializeURL.call(a),a.origin=getOrigin.call(a),a.protocol=getProtocol.call(a),a.username=getUsername.call(a),a.password=getPassword.call(a),a.host=getHost.call(a),a.hostname=getHostname.call(a),a.port=getPort.call(a),a.pathname=getPathname.call(a),a.search=getSearch.call(a),a.searchParams=getSearchParams.call(a),a.hash=getHash.call(a))},URLPrototype=URLConstructor.prototype,serializeURL=function(){var e=getInternalURLState(this),r=e.scheme,t=e.username,a=e.password,i=e.host,s=e.port,c=e.path,l=e.query,u=e.fragment,p=r+":";return null!==i?(p+="//",includesCredentials(e)&&(p+=t+(a?":"+a:"")+"@"),p+=serializeHost(i),null!==s&&(p+=":"+s)):"file"==r&&(p+="//"),p+=e.cannotBeABaseURL?c[0]:c.length?"/"+c.join("/"):"",null!==l&&(p+="?"+l),null!==u&&(p+="#"+u),p},getOrigin=function(){var e=getInternalURLState(this),r=e.scheme,t=e.port;if("blob"==r)try{return new URLConstructor(r.path[0]).origin}catch(e){return"null"}return"file"!=r&&isSpecial(e)?r+"://"+serializeHost(e.host)+(null!==t?":"+t:""):"null"},getProtocol=function(){return getInternalURLState(this).scheme+":"},getUsername=function(){return getInternalURLState(this).username},getPassword=function(){return getInternalURLState(this).password},getHost=function(){var e=getInternalURLState(this),r=e.host,t=e.port;return null===r?"":null===t?serializeHost(r):serializeHost(r)+":"+t},getHostname=function(){var e=getInternalURLState(this).host;return null===e?"":serializeHost(e)},getPort=function(){var e=getInternalURLState(this).port;return null===e?"":String(e)},getPathname=function(){var e=getInternalURLState(this),r=e.path;return e.cannotBeABaseURL?r[0]:r.length?"/"+r.join("/"):""},getSearch=function(){var e=getInternalURLState(this).query;return e?"?"+e:""},getSearchParams=function(){return getInternalURLState(this).searchParams},getHash=function(){var e=getInternalURLState(this).fragment;return e?"#"+e:""},accessorDescriptor=function(e,r){return{get:e,set:r,configurable:!0,enumerable:!0}};if(descriptors&&objectDefineProperties(URLPrototype,{href:accessorDescriptor(serializeURL,(function(e){var r=getInternalURLState(this),t=toString(e),a=parseURL(r,t);if(a)throw TypeError(a);getInternalSearchParamsState(r.searchParams).updateSearchParams(r.query)})),origin:accessorDescriptor(getOrigin),protocol:accessorDescriptor(getProtocol,(function(e){var r=getInternalURLState(this);parseURL(r,toString(e)+":",SCHEME_START)})),username:accessorDescriptor(getUsername,(function(e){var r=getInternalURLState(this),t=arrayFrom(toString(e));if(!cannotHaveUsernamePasswordPort(r)){r.username="";for(var a=0;a<t.length;a++)r.username+=percentEncode(t[a],userinfoPercentEncodeSet)}})),password:accessorDescriptor(getPassword,(function(e){var r=getInternalURLState(this),t=arrayFrom(toString(e));if(!cannotHaveUsernamePasswordPort(r)){r.password="";for(var a=0;a<t.length;a++)r.password+=percentEncode(t[a],userinfoPercentEncodeSet)}})),host:accessorDescriptor(getHost,(function(e){var r=getInternalURLState(this);r.cannotBeABaseURL||parseURL(r,toString(e),HOST)})),hostname:accessorDescriptor(getHostname,(function(e){var r=getInternalURLState(this);r.cannotBeABaseURL||parseURL(r,toString(e),HOSTNAME)})),port:accessorDescriptor(getPort,(function(e){var r=getInternalURLState(this);cannotHaveUsernamePasswordPort(r)||(""==(e=toString(e))?r.port=null:parseURL(r,e,PORT))})),pathname:accessorDescriptor(getPathname,(function(e){var r=getInternalURLState(this);r.cannotBeABaseURL||(r.path=[],parseURL(r,toString(e),PATH_START))})),search:accessorDescriptor(getSearch,(function(e){var r=getInternalURLState(this);""==(e=toString(e))?r.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),r.query="",parseURL(r,e,QUERY)),getInternalSearchParamsState(r.searchParams).updateSearchParams(r.query)})),searchParams:accessorDescriptor(getSearchParams),hash:accessorDescriptor(getHash,(function(e){var r=getInternalURLState(this);""!=(e=toString(e))?("#"==e.charAt(0)&&(e=e.slice(1)),r.fragment="",parseURL(r,e,FRAGMENT)):r.fragment=null}))}),redefine(URLPrototype,"toJSON",(function toJSON(){return serializeURL.call(this)}),{enumerable:!0}),redefine(URLPrototype,"toString",(function toString(){return serializeURL.call(this)}),{enumerable:!0}),NativeURL){var nativeCreateObjectURL=NativeURL.createObjectURL,nativeRevokeObjectURL=NativeURL.revokeObjectURL;nativeCreateObjectURL&&redefine(URLConstructor,"createObjectURL",(function createObjectURL(e){return nativeCreateObjectURL.apply(NativeURL,arguments)})),nativeRevokeObjectURL&&redefine(URLConstructor,"revokeObjectURL",(function revokeObjectURL(e){return nativeRevokeObjectURL.apply(NativeURL,arguments)}))}setToStringTag(URLConstructor,"URL"),_export({global:!0,forced:!nativeUrl,sham:!descriptors},{URL:URLConstructor});var $forEach=arrayIteration.forEach,STRICT_METHOD=arrayMethodIsStrict("forEach"),arrayForEach=STRICT_METHOD?[].forEach:function forEach(e){return $forEach(this,e,arguments.length>1?arguments[1]:void 0)},handlePrototype=function(e){if(e&&e.forEach!==arrayForEach)try{createNonEnumerableProperty(e,"forEach",arrayForEach)}catch(r){e.forEach=arrayForEach}};for(var COLLECTION_NAME in domIterables)handlePrototype(global$1[COLLECTION_NAME]&&global$1[COLLECTION_NAME].prototype);handlePrototype(domTokenListPrototype);var propertyIsEnumerable=objectPropertyIsEnumerable.f,createMethod=function(e){return function(r){for(var t,a=toIndexedObject(r),i=objectKeys(a),s=i.length,c=0,l=[];s>c;)t=i[c++],descriptors&&!propertyIsEnumerable.call(a,t)||l.push(e?[t,a[t]]:a[t]);return l}},objectToArray={entries:createMethod(!0),values:createMethod(!1)},$entries=objectToArray.entries;_export({target:"Object",stat:!0},{entries:function entries(e){return $entries(e)}});var getOwnPropertyDescriptor=objectGetOwnPropertyDescriptor.f,$startsWith="".startsWith,min$1=Math.min,CORRECT_IS_REGEXP_LOGIC=correctIsRegexpLogic("startsWith"),MDN_POLYFILL_BUG=!(CORRECT_IS_REGEXP_LOGIC||(descriptor=getOwnPropertyDescriptor(String.prototype,"startsWith"),!descriptor||descriptor.writable)),descriptor;_export({target:"String",proto:!0,forced:!MDN_POLYFILL_BUG&&!CORRECT_IS_REGEXP_LOGIC},{startsWith:function startsWith(e){var r=toString(requireObjectCoercible(this));notARegexp(e);var t=toLength(min$1(arguments.length>1?arguments[1]:void 0,r.length)),a=toString(e);return $startsWith?$startsWith.call(r,a,t):r.slice(t,t+a.length)===a}});var floor=Math.floor,replace="".replace,SUBSTITUTION_SYMBOLS=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,SUBSTITUTION_SYMBOLS_NO_NAMED=/\$([$&'`]|\d{1,2})/g,getSubstitution=function(e,r,t,a,i,s){var c=t+e.length,l=a.length,u=SUBSTITUTION_SYMBOLS_NO_NAMED;return void 0!==i&&(i=toObject(i),u=SUBSTITUTION_SYMBOLS),replace.call(s,u,(function(s,u){var p;switch(u.charAt(0)){case"$":return"$";case"&":return e;case"`":return r.slice(0,t);case"'":return r.slice(c);case"<":p=i[u.slice(1,-1)];break;default:var d=+u;if(0===d)return s;if(d>l){var h=floor(d/10);return 0===h?s:h<=l?void 0===a[h-1]?u.charAt(1):a[h-1]+u.charAt(1):s}p=a[d-1]}return void 0===p?"":p}))},REPLACE=wellKnownSymbol("replace"),max=Math.max,min=Math.min,maybeToString=function(e){return void 0===e?e:String(e)},REPLACE_KEEPS_$0="$0"==="a".replace(/./,"$0"),REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE=!!/./[REPLACE]&&""===/./[REPLACE]("a","$0"),REPLACE_SUPPORTS_NAMED_GROUPS=!fails((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));fixRegexpWellKnownSymbolLogic("replace",(function(e,r,t){var a=REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE?"$":"$0";return[function replace(e,t){var a=requireObjectCoercible(this),i=null==e?void 0:getMethod(e,REPLACE);return i?i.call(e,a,t):r.call(toString(a),e,t)},function(e,i){var s=anObject(this),c=toString(e);if("string"==typeof i&&-1===i.indexOf(a)&&-1===i.indexOf("$<")){var l=t(r,s,c,i);if(l.done)return l.value}var u=isCallable(i);u||(i=toString(i));var p=s.global;if(p){var d=s.unicode;s.lastIndex=0}for(var h=[];;){var y=regexpExecAbstract(s,c);if(null===y)break;if(h.push(y),!p)break;""===toString(y[0])&&(s.lastIndex=advanceStringIndex(c,toLength(s.lastIndex),d))}for(var A="",g=0,E=0;E<h.length;E++){y=h[E];for(var v=toString(y[0]),m=max(min(toInteger(y.index),c.length),0),I=[],R=1;R<y.length;R++)I.push(maybeToString(y[R]));var P=y.groups;if(u){var S=[v].concat(I,m,c);void 0!==P&&S.push(P);var b=toString(i.apply(void 0,S))}else b=getSubstitution(v,c,m,I,P,i);m>=g&&(A+=c.slice(g,m)+b,g=m+v.length)}return A+c.slice(g)}]}),!REPLACE_SUPPORTS_NAMED_GROUPS||!REPLACE_KEEPS_$0||REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE);var IS_CONCAT_SPREADABLE=wellKnownSymbol("isConcatSpreadable"),MAX_SAFE_INTEGER=9007199254740991,MAXIMUM_ALLOWED_INDEX_EXCEEDED="Maximum allowed index exceeded",IS_CONCAT_SPREADABLE_SUPPORT=engineV8Version>=51||!fails((function(){var e=[];return e[IS_CONCAT_SPREADABLE]=!1,e.concat()[0]!==e})),SPECIES_SUPPORT=arrayMethodHasSpeciesSupport("concat"),isConcatSpreadable=function(e){if(!isObject(e))return!1;var r=e[IS_CONCAT_SPREADABLE];return void 0!==r?!!r:isArray(e)},FORCED=!IS_CONCAT_SPREADABLE_SUPPORT||!SPECIES_SUPPORT;_export({target:"Array",proto:!0,forced:FORCED},{concat:function concat(e){var r,t,a,i,s,c=toObject(this),l=arraySpeciesCreate(c,0),u=0;for(r=-1,a=arguments.length;r<a;r++)if(isConcatSpreadable(s=-1===r?c:arguments[r])){if(u+(i=toLength(s.length))>MAX_SAFE_INTEGER)throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);for(t=0;t<i;t++,u++)t in s&&createProperty(l,u,s[t])}else{if(u>=MAX_SAFE_INTEGER)throw TypeError(MAXIMUM_ALLOWED_INDEX_EXCEEDED);createProperty(l,u++,s)}return l.length=u,l}});var cnt=0,imageCompressionLibUrl,worker;function createWorker(e){var r=[];return"function"==typeof e?r.push("(".concat(e,")()")):r.push(e),new Worker(URL.createObjectURL(new Blob(r)))}function createSourceObject(e){return URL.createObjectURL(new Blob([e],{type:"application/javascript"}))}function stringify(e){return JSON.stringify(e,(function(e,r){return"function"==typeof r?"BIC_FN:::(function () { return ".concat(r.toString()," })()"):r}))}function parse(o){if("string"==typeof o)return o;var result={};return Object.entries(o).forEach((function(_ref){var _ref2=_slicedToArray(_ref,2),key=_ref2[0],value=_ref2[1];if("string"==typeof value&&value.startsWith("BIC_FN:::"))try{result[key]=eval(value.replace(/^BIC_FN:::/,""))}catch(e){throw e}else result[key]=parse(value)})),result}function generateLib(){return createSourceObject("\n    // reconstruct library\n    function imageCompression (){return (".concat(imageCompression,").apply(null, arguments)}\n\n    imageCompression.getDataUrlFromFile = ").concat(imageCompression.getDataUrlFromFile,"\n    imageCompression.getFilefromDataUrl = ").concat(imageCompression.getFilefromDataUrl,"\n    imageCompression.loadImage = ").concat(imageCompression.loadImage,"\n    imageCompression.drawImageInCanvas = ").concat(imageCompression.drawImageInCanvas,"\n    imageCompression.drawFileInCanvas = ").concat(imageCompression.drawFileInCanvas,"\n    imageCompression.canvasToFile = ").concat(imageCompression.canvasToFile,"\n    imageCompression.getExifOrientation = ").concat(imageCompression.getExifOrientation,"\n    imageCompression.handleMaxWidthOrHeight = ").concat(imageCompression.handleMaxWidthOrHeight,"\n    imageCompression.followExifOrientation = ").concat(imageCompression.followExifOrientation,"\n    imageCompression.cleanupCanvasMemory = ").concat(imageCompression.cleanupCanvasMemory,"\n    imageCompression.isAutoOrientationInBrowser = ").concat(imageCompression.isAutoOrientationInBrowser,"\n    imageCompression.approximateBelowMaximumCanvasSizeOfBrowser = ").concat(imageCompression.approximateBelowMaximumCanvasSizeOfBrowser,"\n    imageCompression.getBrowserName = ").concat(imageCompression.getBrowserName,"\n\n    // functions / objects\n    getDataUrlFromFile = imageCompression.getDataUrlFromFile\n    getFilefromDataUrl = imageCompression.getFilefromDataUrl\n    loadImage = imageCompression.loadImage\n    drawImageInCanvas = imageCompression.drawImageInCanvas\n    drawFileInCanvas = imageCompression.drawFileInCanvas\n    canvasToFile = imageCompression.canvasToFile\n    getExifOrientation = imageCompression.getExifOrientation\n    handleMaxWidthOrHeight = imageCompression.handleMaxWidthOrHeight\n    followExifOrientation = imageCompression.followExifOrientation\n    cleanupCanvasMemory = imageCompression.cleanupCanvasMemory\n    isAutoOrientationInBrowser = imageCompression.isAutoOrientationInBrowser\n    approximateBelowMaximumCanvasSizeOfBrowser = imageCompression.approximateBelowMaximumCanvasSizeOfBrowser\n    getBrowserName = imageCompression.getBrowserName\n    isIOS = ").concat(isIOS,"\n    \n    getNewCanvasAndCtx = ").concat(getNewCanvasAndCtx,"\n    CustomFileReader = FileReader\n    CustomFile = File\n    MAX_CANVAS_SIZE = ").concat(JSON.stringify(MAX_CANVAS_SIZE),"\n    BROWSER_NAME = ").concat(JSON.stringify(BROWSER_NAME),"\n    function compress (){return (").concat(compress,").apply(null, arguments)}\n\n    // core-js\n    function _slicedToArray(arr, n) { return arr }\n    function _typeof(a) { return typeof a }\n    function _objectSpread2(target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i] != null ? arguments[i] : {};\n  \n        Object.assign(target, source)\n      }\n  \n      return target;\n    }\n\n    // Libraries\n    const parse = ").concat(parse,"\n    const UPNG = {}\n    UPNG.toRGBA8 = ").concat(UPNG.toRGBA8,"\n    UPNG.toRGBA8.decodeImage = ").concat(UPNG.toRGBA8.decodeImage,"\n    UPNG.decode = ").concat(UPNG.decode,"\n    UPNG.decode._decompress = ").concat(UPNG.decode._decompress,"\n    UPNG.decode._inflate = ").concat(UPNG.decode._inflate,"\n    UPNG.decode._readInterlace = ").concat(UPNG.decode._readInterlace,"\n    UPNG.decode._getBPP = ").concat(UPNG.decode._getBPP," \n    UPNG.decode._filterZero = ").concat(UPNG.decode._filterZero,"\n    UPNG.decode._paeth = ").concat(UPNG.decode._paeth,"\n    UPNG.decode._IHDR = ").concat(UPNG.decode._IHDR,"\n    UPNG._bin = parse(").concat(stringify(UPNG._bin),")\n    UPNG._copyTile = ").concat(UPNG._copyTile,"\n    UPNG.encode = ").concat(UPNG.encode,"\n    UPNG.encodeLL = ").concat(UPNG.encodeLL," \n    UPNG.encode._main = ").concat(UPNG.encode._main,"\n    UPNG.encode.compressPNG = ").concat(UPNG.encode.compressPNG," \n    UPNG.encode.compress = ").concat(UPNG.encode.compress,"\n    UPNG.encode.framize = ").concat(UPNG.encode.framize," \n    UPNG.encode._updateFrame = ").concat(UPNG.encode._updateFrame," \n    UPNG.encode._prepareDiff = ").concat(UPNG.encode._prepareDiff," \n    UPNG.encode._filterZero = ").concat(UPNG.encode._filterZero," \n    UPNG.encode._filterLine = ").concat(UPNG.encode._filterLine,"\n    UPNG.encode.concatRGBA = ").concat(UPNG.encode.concatRGBA,"\n    UPNG.crc = parse(").concat(stringify(UPNG.crc),")\n    UPNG.crc.table = ( function() {\n    var tab = new Uint32Array(256);\n    for (var n=0; n<256; n++) {\n      var c = n;\n      for (var k=0; k<8; k++) {\n        if (c & 1)  c = 0xedb88320 ^ (c >>> 1);\n        else        c = c >>> 1;\n      }\n      tab[n] = c;  }\n    return tab;  })()\n    UPNG.quantize = ").concat(UPNG.quantize," \n    UPNG.quantize.getKDtree = ").concat(UPNG.quantize.getKDtree," \n    UPNG.quantize.getNearest = ").concat(UPNG.quantize.getNearest," \n    UPNG.quantize.planeDst = ").concat(UPNG.quantize.planeDst," \n    UPNG.quantize.dist = ").concat(UPNG.quantize.dist,"     \n    UPNG.quantize.splitPixels = ").concat(UPNG.quantize.splitPixels," \n    UPNG.quantize.vecDot = ").concat(UPNG.quantize.vecDot," \n    UPNG.quantize.stats = ").concat(UPNG.quantize.stats," \n    UPNG.quantize.estats = ").concat(UPNG.quantize.estats,"\n    UPNG.M4 = parse(").concat(stringify(UPNG.M4),")\n    UPNG.encode.concatRGBA = ").concat(UPNG.encode.concatRGBA,'\n    UPNG.inflateRaw=function(){\n    var H={};H.H={};H.H.N=function(N,W){var R=Uint8Array,i=0,m=0,J=0,h=0,Q=0,X=0,u=0,w=0,d=0,v,C;\n      if(N[0]==3&&N[1]==0)return W?W:new R(0);var V=H.H,n=V.b,A=V.e,l=V.R,M=V.n,I=V.A,e=V.Z,b=V.m,Z=W==null;\n      if(Z)W=new R(N.length>>>2<<5);while(i==0){i=n(N,d,1);m=n(N,d+1,2);d+=3;if(m==0){if((d&7)!=0)d+=8-(d&7);\n        var D=(d>>>3)+4,q=N[D-4]|N[D-3]<<8;if(Z)W=H.H.W(W,w+q);W.set(new R(N.buffer,N.byteOffset+D,q),w);d=D+q<<3;\n        w+=q;continue}if(Z)W=H.H.W(W,w+(1<<17));if(m==1){v=b.J;C=b.h;X=(1<<9)-1;u=(1<<5)-1}if(m==2){J=A(N,d,5)+257;\n        h=A(N,d+5,5)+1;Q=A(N,d+10,4)+4;d+=14;var E=d,j=1;for(var c=0;c<38;c+=2){b.Q[c]=0;b.Q[c+1]=0}for(var c=0;\n                                                                                                        c<Q;c++){var K=A(N,d+c*3,3);b.Q[(b.X[c]<<1)+1]=K;if(K>j)j=K}d+=3*Q;M(b.Q,j);I(b.Q,j,b.u);v=b.w;C=b.d;\n        d=l(b.u,(1<<j)-1,J+h,N,d,b.v);var r=V.V(b.v,0,J,b.C);X=(1<<r)-1;var S=V.V(b.v,J,h,b.D);u=(1<<S)-1;M(b.C,r);\n        I(b.C,r,v);M(b.D,S);I(b.D,S,C)}while(!0){var T=v[e(N,d)&X];d+=T&15;var p=T>>>4;if(p>>>8==0){W[w++]=p}else if(p==256){break}else{var z=w+p-254;\n        if(p>264){var _=b.q[p-257];z=w+(_>>>3)+A(N,d,_&7);d+=_&7}var $=C[e(N,d)&u];d+=$&15;var s=$>>>4,Y=b.c[s],a=(Y>>>4)+n(N,d,Y&15);\n        d+=Y&15;while(w<z){W[w]=W[w++-a];W[w]=W[w++-a];W[w]=W[w++-a];W[w]=W[w++-a]}w=z}}}return W.length==w?W:W.slice(0,w)};\n      H.H.W=function(N,W){var R=N.length;if(W<=R)return N;var V=new Uint8Array(R<<1);V.set(N,0);return V};\n      H.H.R=function(N,W,R,V,n,A){var l=H.H.e,M=H.H.Z,I=0;while(I<R){var e=N[M(V,n)&W];n+=e&15;var b=e>>>4;\n        if(b<=15){A[I]=b;I++}else{var Z=0,m=0;if(b==16){m=3+l(V,n,2);n+=2;Z=A[I-1]}else if(b==17){m=3+l(V,n,3);\n          n+=3}else if(b==18){m=11+l(V,n,7);n+=7}var J=I+m;while(I<J){A[I]=Z;I++}}}return n};H.H.V=function(N,W,R,V){var n=0,A=0,l=V.length>>>1;\n        while(A<R){var M=N[A+W];V[A<<1]=0;V[(A<<1)+1]=M;if(M>n)n=M;A++}while(A<l){V[A<<1]=0;V[(A<<1)+1]=0;A++}return n};\n      H.H.n=function(N,W){var R=H.H.m,V=N.length,n,A,l,M,I,e=R.j;for(var M=0;M<=W;M++)e[M]=0;for(M=1;M<V;M+=2)e[N[M]]++;\n        var b=R.K;n=0;e[0]=0;for(A=1;A<=W;A++){n=n+e[A-1]<<1;b[A]=n}for(l=0;l<V;l+=2){I=N[l+1];if(I!=0){N[l]=b[I];\n          b[I]++}}};H.H.A=function(N,W,R){var V=N.length,n=H.H.m,A=n.r;for(var l=0;l<V;l+=2)if(N[l+1]!=0){var M=l>>1,I=N[l+1],e=M<<4|I,b=W-I,Z=N[l]<<b,m=Z+(1<<b);\n        while(Z!=m){var J=A[Z]>>>15-W;R[J]=e;Z++}}};H.H.l=function(N,W){var R=H.H.m.r,V=15-W;for(var n=0;n<N.length;\n                                                                                                 n+=2){var A=N[n]<<W-N[n+1];N[n]=R[A]>>>V}};H.H.M=function(N,W,R){R=R<<(W&7);var V=W>>>3;N[V]|=R;N[V+1]|=R>>>8};\n      H.H.I=function(N,W,R){R=R<<(W&7);var V=W>>>3;N[V]|=R;N[V+1]|=R>>>8;N[V+2]|=R>>>16};H.H.e=function(N,W,R){return(N[W>>>3]|N[(W>>>3)+1]<<8)>>>(W&7)&(1<<R)-1};\n      H.H.b=function(N,W,R){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16)>>>(W&7)&(1<<R)-1};H.H.Z=function(N,W){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16)>>>(W&7)};\n      H.H.i=function(N,W){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16|N[(W>>>3)+3]<<24)>>>(W&7)};H.H.m=function(){var N=Uint16Array,W=Uint32Array;\n        return{K:new N(16),j:new N(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new N(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new W(32),J:new N(512),_:[],h:new N(32),$:[],w:new N(32768),C:[],v:[],d:new N(32768),D:[],u:new N(512),Q:[],r:new N(1<<15),s:new W(286),Y:new W(30),a:new W(19),t:new W(15e3),k:new N(1<<16),g:new N(1<<15)}}();\n      (function(){var N=H.H.m,W=1<<15;for(var R=0;R<W;R++){var V=R;V=(V&2863311530)>>>1|(V&1431655765)<<1;\n        V=(V&3435973836)>>>2|(V&858993459)<<2;V=(V&4042322160)>>>4|(V&252645135)<<4;V=(V&4278255360)>>>8|(V&16711935)<<8;\n        N.r[R]=(V>>>16|V<<16)>>>17}function n(A,l,M){while(l--!=0)A.push(0,M)}for(var R=0;R<32;R++){N.q[R]=N.S[R]<<3|N.T[R];\n        N.c[R]=N.p[R]<<4|N.z[R]}n(N._,144,8);n(N._,255-143,9);n(N._,279-255,7);n(N._,287-279,8);H.H.n(N._,9);\n        H.H.A(N._,9,N.J);H.H.l(N._,9);n(N.$,32,5);H.H.n(N.$,5);H.H.A(N.$,5,N.h);H.H.l(N.$,5);n(N.Q,19,0);n(N.C,286,0);\n        n(N.D,30,0);n(N.v,320,0)}());return H.H.N}()\n    \n    const UZIP = {}\n    UZIP["parse"] = ').concat(UZIP_1.parse,"\n    UZIP._readLocal = ").concat(UZIP_1._readLocal,"\n    UZIP.inflateRaw = ").concat(UZIP_1.inflateRaw,"\n    UZIP.inflate = ").concat(UZIP_1.inflate,"\n    UZIP.deflate = ").concat(UZIP_1.deflate,"\n    UZIP.deflateRaw = ").concat(UZIP_1.deflateRaw,"\n    UZIP.encode = ").concat(UZIP_1.encode,"\n    UZIP._noNeed = ").concat(UZIP_1._noNeed,"\n    UZIP._writeHeader = ").concat(UZIP_1._writeHeader,"\n    UZIP.crc = parse(").concat(stringify(UZIP_1.crc),")\n    UZIP.crc.table = ( function() {\n      var tab = new Uint32Array(256);\n      for (var n=0; n<256; n++) {\n        var c = n;\n        for (var k=0; k<8; k++) {\n          if (c & 1)  c = 0xedb88320 ^ (c >>> 1);\n          else        c = c >>> 1;\n        }\n        tab[n] = c;  }\n      return tab;  })()\n    \n    UZIP.adler = ").concat(UZIP_1.adler,"\n    UZIP.bin = parse(").concat(stringify(UZIP_1.bin),")\n    UZIP.F = {}\n    UZIP.F.deflateRaw = ").concat(UZIP_1.F.deflateRaw,"\n    UZIP.F._bestMatch = ").concat(UZIP_1.F._bestMatch,"\n    UZIP.F._howLong = ").concat(UZIP_1.F._howLong,"\n    UZIP.F._hash = ").concat(UZIP_1.F._hash,"\n    UZIP.saved = ").concat(UZIP_1.saved,"\n    UZIP.F._writeBlock = ").concat(UZIP_1.F._writeBlock,"\n    UZIP.F._copyExact = ").concat(UZIP_1.F._copyExact,"\n    UZIP.F.getTrees = ").concat(UZIP_1.F.getTrees,"\n    UZIP.F.getSecond = ").concat(UZIP_1.F.getSecond,"\n    UZIP.F.nonZero = ").concat(UZIP_1.F.nonZero,"\n    UZIP.F.contSize = ").concat(UZIP_1.F.contSize,"\n    UZIP.F._codeTiny = ").concat(UZIP_1.F._codeTiny," \n    UZIP.F._lenCodes = ").concat(UZIP_1.F._lenCodes," \n    UZIP.F._hufTree = ").concat(UZIP_1.F._hufTree," \n    UZIP.F.setDepth = ").concat(UZIP_1.F.setDepth," \n    UZIP.F.restrictDepth = ").concat(UZIP_1.F.restrictDepth,"\n    UZIP.F._goodIndex = ").concat(UZIP_1.F._goodIndex," \n    UZIP.F._writeLit = ").concat(UZIP_1.F._writeLit," \n    UZIP.F.inflate = ").concat(UZIP_1.F.inflate," \n    UZIP.F._check = ").concat(UZIP_1.F._check," \n    UZIP.F._decodeTiny = ").concat(UZIP_1.F._decodeTiny," \n    UZIP.F._copyOut = ").concat(UZIP_1.F._copyOut," \n    UZIP.F.makeCodes = ").concat(UZIP_1.F.makeCodes," \n    UZIP.F.codes2map = ").concat(UZIP_1.F.codes2map," \n    UZIP.F.revCodes = ").concat(UZIP_1.F.revCodes," \n\n    // used only in deflate\n    UZIP.F._putsE = ").concat(UZIP_1.F._putsE,"\n    UZIP.F._putsF = ").concat(UZIP_1.F._putsF,"\n  \n    UZIP.F._bitsE = ").concat(UZIP_1.F._bitsE,"\n    UZIP.F._bitsF = ").concat(UZIP_1.F._bitsF,"\n\n    UZIP.F._get17 = ").concat(UZIP_1.F._get17,"\n    UZIP.F._get25 = ").concat(UZIP_1.F._get25,"\n    UZIP.F.U = function(){\n      var u16=Uint16Array, u32=Uint32Array;\n      return {\n        next_code : new u16(16),\n        bl_count  : new u16(16),\n        ordr : [ 16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15 ],\n        of0  : [3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],\n        exb  : [0,0,0,0,0,0,0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4,  4,  5,  5,  5,  5,  0,  0,  0,  0],\n        ldef : new u16(32),\n        df0  : [1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577, 65535, 65535],\n        dxb  : [0,0,0,0,1,1,2, 2, 3, 3, 4, 4, 5, 5,  6,  6,  7,  7,  8,  8,   9,   9,  10,  10,  11,  11,  12,   12,   13,   13,     0,     0],\n        ddef : new u32(32),\n        flmap: new u16(  512),  fltree: [],\n        fdmap: new u16(   32),  fdtree: [],\n        lmap : new u16(32768),  ltree : [],  ttree:[],\n        dmap : new u16(32768),  dtree : [],\n        imap : new u16(  512),  itree : [],\n        //rev9 : new u16(  512)\n        rev15: new u16(1<<15),\n        lhst : new u32(286), dhst : new u32( 30), ihst : new u32(19),\n        lits : new u32(15000),\n        strt : new u16(1<<16),\n        prev : new u16(1<<15)\n      };\n    } ();\n\n    (function(){\n      var U = UZIP.F.U;\n      var len = 1<<15;\n      for(var i=0; i<len; i++) {\n        var x = i;\n        x = (((x & 0xaaaaaaaa) >>> 1) | ((x & 0x55555555) << 1));\n        x = (((x & 0xcccccccc) >>> 2) | ((x & 0x33333333) << 2));\n        x = (((x & 0xf0f0f0f0) >>> 4) | ((x & 0x0f0f0f0f) << 4));\n        x = (((x & 0xff00ff00) >>> 8) | ((x & 0x00ff00ff) << 8));\n        U.rev15[i] = (((x >>> 16) | (x << 16)))>>>17;\n      }\n  \n      function pushV(tgt, n, sv) {  while(n--!=0) tgt.push(0,sv);  }\n  \n      for(var i=0; i<32; i++) {  U.ldef[i]=(U.of0[i]<<3)|U.exb[i];  U.ddef[i]=(U.df0[i]<<4)|U.dxb[i];  }\n  \n      pushV(U.fltree, 144, 8);  pushV(U.fltree, 255-143, 9);  pushV(U.fltree, 279-255, 7);  pushV(U.fltree,287-279,8);\n      /*\n        var i = 0;\n        for(; i<=143; i++) U.fltree.push(0,8);\n        for(; i<=255; i++) U.fltree.push(0,9);\n        for(; i<=279; i++) U.fltree.push(0,7);\n        for(; i<=287; i++) U.fltree.push(0,8);\n        */\n      UZIP.F.makeCodes(U.fltree, 9);\n      UZIP.F.codes2map(U.fltree, 9, U.flmap);\n      UZIP.F.revCodes (U.fltree, 9)\n  \n      pushV(U.fdtree,32,5);\n      //for(i=0;i<32; i++) U.fdtree.push(0,5);\n      UZIP.F.makeCodes(U.fdtree, 5);\n      UZIP.F.codes2map(U.fdtree, 5, U.fdmap);\n      UZIP.F.revCodes (U.fdtree, 5)\n  \n      pushV(U.itree,19,0);  pushV(U.ltree,286,0);  pushV(U.dtree,30,0);  pushV(U.ttree,320,0);\n      /*\n        for(var i=0; i< 19; i++) U.itree.push(0,0);\n        for(var i=0; i<286; i++) U.ltree.push(0,0);\n        for(var i=0; i< 30; i++) U.dtree.push(0,0);\n        for(var i=0; i<320; i++) U.ttree.push(0,0);\n        */\n    })()\n    "))}function generateWorkerScript(){return createWorker("\n    let scriptImported = false\n    self.addEventListener('message', async (e) => {\n      const { file, id, imageCompressionLibUrl, options } = e.data\n      options.onProgress = (progress) => self.postMessage({ progress, id })\n      try {\n        if (!scriptImported) {\n          // console.log('[worker] importScripts', imageCompressionLibUrl)\n          self.importScripts(imageCompressionLibUrl)\n          scriptImported = true\n        }\n        // console.log('[worker] self', self)\n        const compressedFile = await imageCompression(file, options)\n        self.postMessage({ file: compressedFile, id })\n      } catch (e) {\n        // console.error('[worker] error', e)\n        self.postMessage({ error: e.message + '\\n' + e.stack, id })\n      }\n    })\n  ")}function compressOnWebWorker(e,r){return new Promise((function(t,a){var i=cnt+=1;imageCompressionLibUrl||(imageCompressionLibUrl=generateLib()),worker||(worker=generateWorkerScript()),worker.addEventListener("message",(function handler(e){if(e.data.id===i){if(void 0!==e.data.progress)return void r.onProgress(e.data.progress);worker.removeEventListener("message",handler),e.data.error&&a(new Error(e.data.error)),t(e.data.file)}})),worker.addEventListener("error",a),worker.postMessage({file:e,id:i,imageCompressionLibUrl:imageCompressionLibUrl,options:_objectSpread2(_objectSpread2({},r),{},{onProgress:void 0})})}))}function imageCompression(e,r){return new Promise((function(t,a){var i,s,c,l,u,p;if(i=_objectSpread2({},r),c=0,l=i.onProgress,i.maxSizeMB=i.maxSizeMB||Number.POSITIVE_INFINITY,u="boolean"!=typeof i.useWebWorker||i.useWebWorker,delete i.useWebWorker,i.onProgress=function(e){c=e,"function"==typeof l&&l(c)},!(e instanceof Blob||e instanceof CustomFile))return a(new Error("The file given is not an instance of Blob or File"));if(!/^image/.test(e.type))return a(new Error("The file given is not an image"));if(p="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,!u||"function"!=typeof Worker||p)return compress(e,i).then(function(e){try{return s=e,$If_3.call(this)}catch(e){return a(e)}}.bind(this),a);var d=function(){try{return $If_3.call(this)}catch(e){return a(e)}}.bind(this),h=function $Try_1_Catch(r){try{return compress(e,i).then((function(e){try{return s=e,d()}catch(e){return a(e)}}),a)}catch(e){return a(e)}};try{return compressOnWebWorker(e,i).then((function(e){try{return s=e,d()}catch(e){return h()}}),h)}catch(e){h()}function $If_3(){try{s.name=e.name,s.lastModified=e.lastModified}catch(e){}return t(s)}}))}return imageCompression.getDataUrlFromFile=getDataUrlFromFile,imageCompression.getFilefromDataUrl=getFilefromDataUrl,imageCompression.loadImage=loadImage,imageCompression.drawImageInCanvas=drawImageInCanvas,imageCompression.drawFileInCanvas=drawFileInCanvas,imageCompression.canvasToFile=canvasToFile,imageCompression.getExifOrientation=getExifOrientation,imageCompression.handleMaxWidthOrHeight=handleMaxWidthOrHeight,imageCompression.followExifOrientation=followExifOrientation,imageCompression.cleanupCanvasMemory=cleanupCanvasMemory,imageCompression.isAutoOrientationInBrowser=isAutoOrientationInBrowser,imageCompression.approximateBelowMaximumCanvasSizeOfBrowser=approximateBelowMaximumCanvasSizeOfBrowser,imageCompression.getBrowserName=getBrowserName,imageCompression.version="1.0.17",imageCompression}));
 //# sourceMappingURL=browser-image-compression.js.map