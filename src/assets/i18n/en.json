{"Hello": "Hello", "Dashboard": "Dashboard", "Users": "Users", "Add or manage users": "Add or manage users", "Teams": "Teams", "Create & add users to teams": "Create & add users to teams", "Forms": "Forms", "Create & publish forms": "Create & publish forms", "Formsets": "Formsets", "Create and manage formsets": "Create and manage formsets", "MasterData": "MasterData", "Resources for forms": "Resources for forms", "Reports": "Reports", "View form activity": "View form activity", "Workflows": "Workflows", "Design Workflows": "Design Workflows", "Settings": "Settings", "Manage Settings": "Manage Settings", "Showing all users": " Showing all users", "Import Bulk Users": "Import Bulk Users", "or drag and drop file here": "or drag and drop file here", "Assign Teams": "Assign Teams", "Disable User": "Disable User", "Search users": "Search users", "Firstname": "Firstname", "Lastname": "Lastname", "Role": "Role", "Last Accessed": "Last Accessed", "Loading your users, please wait...": "Loading your users, please wait...", "No users found. Please check your search and try again.": "No users found. Please check your search and try again.", "Loading your teams, please wait...": "Loading your teams, please wait...", "No teams found. It's good to be in a team": "No teams found. It's good to be in a team", "Showing team users": "Showing team users", "Remove Users from team": "Remove Users from team", "Search teams": "Search teams", "Loading team users, please wait": "Loading team users, please wait", "No team users found. This team is feeling lonely": "No team users found. This team is feeling lonely", "now": "now", "No forms assigned yet": "No forms assigned yet", "assign": "assign", "forms to this team": "forms to this team", "Technical Name": "Technical Name", "Details": "Details", "Delete": "Delete", "Category": "Category", "Add Category": "Add Category", "Remote System": "Remote System", "Import": "Import", "Export": "Export", "Revisions": "Revisions", "Assignments": "Assignments", "Documents": "Documents", "Attributes": "Attributes", "Sharing": "Sharing", "Schedules": "Schedules", "Translations": "Translations", "No assignees yet": "No assignees yet", "Remove team assignments": "Remove team assignments", "Name": "Name", "Description": "Description", "Members": "Members", "Key": "Key", "Value": "Value", "Remove": "Remove", "Add Row": "Add Row", "Save": "Save", "Unlink": "Unlink", "Last updated By": "Last updated By", "Type": "Type", "No Documents found": "No Documents found", "Not part of any formsets": "Not part of any formsets", "No revisions yet": "No revisions yet", "Date": "Date", "Options": "Options", "Publish": "Publish", "Click on any revision number to revert to that version": "Click on any revision number to revert to that version", "This form is not yet scheduled": "This form is not yet scheduled", "set up": "set up", "Recurrence": "Recurrence", "Status": "Status", "Expires": "Expires", "Shared with": "Shared with", "Private": "Private", "Public": "Public", "List of emails": "List of emails", "Comma seperated list of emails": "Comma seperated list of emails", "Entered list is not valid, please check and try again": "Entered list is not valid, please check and try again", "help": "help", "Submit Handler": "Submit <PERSON>", "Name of custon handler": "Name of custon handler", "Whitespaces are not allowed, please enter valid name": "Whitespaces are not allowed, please enter valid name", "Execute Workflow": "Execute Workflow", "Select Workflow": "Select Workflow", "Workflow name is required": "Workflow name is required", "System Properties": "System Properties", "Select System property": "Select System property", "System Property is required": "System Property is required", "Inputs": "Inputs", "your first formset now": "your first formset now", "Import from File": "Import from File", "Import from Remote System": "Import from Remote System", "Delete Schedules": "Delete Schedules", "This formset does not have any forms yet": "This formset does not have any forms yet", "Mandatory": "Mandatory", "get this formset": "get this formset", "This formset is not yet scheduled": "This formset is not yet scheduled", "You don't have any master data to use in your forms": "You don't have any master data to use in your forms", "Back": "Back", "No Masterdata found": "No Masterdata found", "Go back home": "Go back home", "Start Date": "Start Date", "Start date is required": "Start date is required", "Start date can't be greater than today's date": "Start date can't be greater than today's date", "End Date": "End Date", "End date is required": "End date is required", "End date can't be greater than today's date": "End date can't be greater than today's date", "Form Name": "Form Name", "Submission": "Submission", "Submitted by": "Submitted by", "Submitted on": "Submitted on", "Delayed Submission only": "Delayed Submission only", "passwords do not match": "passwords do not match", "Whitespaces are not allowed": "Whitespaces are not allowed", "Fill out company name": "Fill out company name", "Select company logo": "Select company logo", "Next": "Next", "Select company working days": "Select company working days", "Working Days": "Working Days", "You are now done": "You are now done", "Reset": "Reset", "Password": "Password", "User name": "User name", "Preview": "Preview", "Subject": "Subject", "Include Bootstrap CSS in addition to the custom CSS classes below": "Include Bootstrap CSS in addition to the custom CSS classes below", "Custom CSS Classes": "Custom CSS Classes", "Choose image": "Choose image", "or drag and drop image here": "or drag and drop image here", "Image Uploaded Succesfuly!": "Image Uploaded Succesfuly!", "Image Upload Failed!": "Image Upload Failed!", "Active forms version allowed": "Active forms version allowed", "Allow managers to edit submissions": "Allow managers to edit submissions", "API Authentication": "API Authentication", "Submit User and Token via Basic Authentication": "Submit User and Token via Basic Authentication", "Token": "Token", "Header": "Header", "Paper": "Paper", "Margin": "<PERSON><PERSON>", "General": "General", "Email Server": "Email Server", "Email Templates": "Email Templates", "Customization": "Customization", "About": "About", "Update": "Update", "Start typing to search teams": "Start typing to search teams", "Update Assignments": "Update Assignments", "Enable Shared mode (Everyone works on same data)": "Enable Shared mode (Everyone works on same data)", "Only one user can fill the form till released": "Only one user can fill the form till released", "All users can fill concurrently": "All users can fill concurrently", "Enable Review (Submissions needs to be reviewed)": "Enable Review (Submissions needs to be reviewed)", "Assign Reviewer": "Assign Reviewer", "Enable Shift Handoff (Teams work in shifts on forms)": "Enable Shift Handoff (Teams work in shifts on forms)", "Assembling Mannual": "Assemb<PERSON> <PERSON>", "Document name is required": "Document name is required", "This document helps in": "This document helps in", "Auto Download": "Auto Download", "Content Type": "Content Type", "Upload Document": "Upload Document", "Associate Document": "Associate Document", "Create Share": "Create Share", "form title is required": "form title is required", "Allow Multiple Save": "Allow Multiple Save", "Expiry date is required": "Expiry date is required", "Expiry date must be greater than today's date": "Expiry date must be greater than today's date", "Create form": "Create form", "Copy form": "Create form", "Create Master Form": "Create Master Form", "shortname is required": "shortname is required", "Maximum of 100 characters": "Maximum of 100 characters", "Use form as": "Use form as", "Resource": "Resource", "Wizard": "<PERSON>", "Create formset": "Create formset", "formset title is required": "formset title is required", "Create Operation": "Create Operation", "Operation": "Operation", "Title": "Title", "Language": "Language", "Operation Name must be alphanumeric(small case letters and numbers)": "Operation Name must be alphanumeric(small case letters and numbers)", "Operation title is required": "Operation title is required", "Create Schedule": "Create Schedule", "Update Schedule": "Update Schedule", "Priority of created tasks": "Priority of created tasks", "Due after number of days": "Due after number of days", "Repeat": "Repeat", "No end date": "No end date", "Team Manager": "Team Manager", "Please enter valid email id": "Please enter valid email id", "Let user choose their password": "Let user choose their password", "Minimum 3 letters are needed to set password": "Minimum 3 letters are needed to set password", "Email password to user": "Email password to user", "FirstName is required": "FirstName is required", "LastName is required": "LastName is required", "Create Workflow": "Create Workflow", "Workflow": "Workflow", "Template Preview": "Template Preview", "Test": "Test", "Assign formsets": "Assign formsets", "Assign forms": "Assign forms", "Import Forms": "Import Forms", "Publish after import": "Publish after import", "Overwrite existing drafts during import": "Overwrite existing drafts during import", "Select All": "Select All", "Publish Form": "Publish Form", "Comment to identify this revision": "Comment to identify this revision", "comment is required": "comment is required", "Integration": "Integration", "Execute Function": "Execute Function", "Operation Configuration": "Operation Configuration", "Short Name": "Short Name", "Name should not include _ , spaces and Uppercase letters": "Name should not include _ , spaces and Uppercase letters", "Configure System property": "Configure System property", "Archive": "Archive", "Method": "Method", "Update operation properties": "Update operation properties", "Write python script you want to execute": "Write python script you want to execute", "Input Parameters": "Input Parameters", "Add or Remove input parameters of the operation": "Add or Remove input parameters of the operation", "Configure result and error variables": "Configure result and error variables", "Result": "Result", "Error": "Error", "Configure Success and Failure conditions": "Configure Success and Failure conditions", "Success": "Success", "Failure": "Failure", "Add Optional Parameters": "Add Optional Parameters", "Select Forms": "Select Forms", "Simulate Barcode Scan": "Simulate Barcode Scan", "test the scanning by simulating the barcode or QRCode data.Enter your test data below": "est the scanning by simulating the barcode or QRCode data.Enter your test data below", "Test data": "Test data", "Simulate Scan": "Simulate <PERSON><PERSON>", "Cancel": "Cancel", "A system properties file contains the properties keyword which is mapped to a list of key:value pairs that define system property names and values.The key:value format is systemName.propertyName:value.The system properties file can have properties for one or more systems as required": "A system properties file contains the properties keyword which is mapped to a list of key:value pairs that define system property names and values.The key:value format is systemName.propertyName:value.The system properties file can have properties for one or more systems as required", "Here’s what the contents of our system properties file looks like:": "Here’s what the contents of our system properties file looks like:", "In the example above, the system name is forms-db and the properties are type, connection_pool etc. When the workflow is invoked, only the name of the system needs to be passed in the input system. For e.g. in the above case the system name passed would be forms-db": "In the example above, the system name is forms-db and the properties are type, connection_pool etc. When the workflow is invoked, only the name of the system needs to be passed in the input system. For e.g. in the above case the system name passed would be forms-db", "Selected workflow data will be lost, Are you sure you want to do this": "Selected workflow data will be lost, Are you sure you want to do this", "Workflow Saved Successfully": "Workflow Saved Successfully", "Previously selected workflow data will be lost, you still want to proceed?": "Previously selected workflow data will be lost, you still want to proceed?", "Enter a list of comma separated email ids who will receive a PDF version of the form once its completed": "Enter a list of comma separated email ids who will receive a PDF version of the form once its completed", "Enter the name of the function that has been developed to using the Unvired Modeler that will receive the data once the form is completed.  This function can use the Unvired SDK to process the data further.  This is typically required when complete customization of the submitted data is required in rare cases": "Enter the name of the function that has been developed to using the Unvired Modeler that will receive the data once the form is completed.  This function can use the Unvired SDK to process the data further.  This is typically required when complete customization of the submitted data is required in rare cases.", "Formset has been assigned, you can now configure additional workflow options by selecting the assignment.": "Formset has been assigned, you can now configure additional workflow options by selecting the assignment.", "Token regenerated and saved": "<PERSON><PERSON> regenerated and saved", "Token copied to clipboard": "Token copied to clipboard", "Email Template preview saved successfully": "Email Template preview saved successfully", "Company Details submited successfully": "Company Details submited successfully", "Are you sure you want to archive this workflow?": "Are you sure you want to archive this workflow?", "Workflow Operation Updated/Saved successfully": "Workflow Operation Updated/Saved successfully", "You haven't designed the form yet, please click 'Design' to design the form": "You haven't designed the form yet, please click 'Design' to design the form", "Formset has been assigned, you can now configure additional workflow options by selecting the assignment": "Formset has been assigned, you can now configure additional workflow options by selecting the assignment", "Schedule Task Queued": "Schedule Task Queued", "Revert version action aborted!": "Revert version action aborted!", "Are you sure you want to revert back to version": "Are you sure you want to revert back to version", "Setting already exists": "Setting already exists", "Form has been assigned, you can now configure additional workflow options by selecting the assignment": "Form has been assigned, you can now configure additional workflow options by selecting the assignment"}