
const Components = Formio.Components;
const HTMLComponent = Components.components.htmlelement;

class SmartDataComponent extends HTMLComponent {
  constructor(component, options, data) {
    super(component, options, data);
  }
  static schema(...extend) {
    return HTMLComponent.schema({
      label: 'Smart Data Component',
      key: 'sdc',
      type: 'smartdata',
      tag: 'h5',
      attrs: [
        { "attr": "style", "value": "padding: 15px;text-align: center;border: 2px dashed #ddd;" }
      ],
      content: 'Smart Data Component',
      input: false,
      persistent: false,
      hidden: true
    },
      ...extend
    );
  }
  get key() {
    return _.get(this.component, 'key', '');
  }
  static get builderInfo() {
    return {
      title: "Smart Data",
      icon: "cloud",
      group: "premium",
      documentation: "http://help.form.io/userguide/#sdc",
      weight: 30,
      schema: SmartDataComponent.schema()
    };
  }
  get defaultSchema() {
    return SmartDataComponent.schema();
  }
  get content() {
    if (this.builderMode) {
      return this.component.content;
    }
    const submission = _.get(this.root, 'submission', {});
    return this.component.content ? this.interpolate(this.component.content, {
      metadata: submission.metadata || {},
      submission: submission,
      data: this.rootValue,
      row: this.data
    }) : '';
  }

  get singleTags() {
    return ['br', 'img', 'hr'];
  }

  renderContent() {
    const submission = _.get(this.root, 'submission', {});
    return this.renderTemplate('html', {
      component: this.component,
      tag: this.component.tag,
      attrs: (this.component.attrs || []).map((attr) => {
        return {
          attr: attr.attr,
          value: this.interpolate(attr.value, {
            metadata: submission.metadata || {},
            submission: submission,
            data: this.rootValue,
            row: this.data
          })
        };
      }),
      content: this.component.label,
      singleTags: this.singleTags,
    });
  }

  render() {
    this.component.hidden = true;
    return super.render(this.renderContent());

  }

  async attach(element) {
    this.loadRefs(element, { html: "single" });
    // console.log(this.refs.html);
    console.log("attach called")
    let _self = this;

    // if(_self.component.trigger){
    // this.evaluate(_self.component.trigger.beforefetch.panel.javascript)
    // }
    switch (this.component.dataSrc) {
      case "url":
        console.log("inside switch url ");
        // Formio.makeStaticRequest(
        //   this.interpolate(this.component.fetchapi.url),
        //   (this.component.fetchapi.method || "get").toUpperCase(),
        //   null,
        //   {
        //     headers: this.requestHeaders,
        //     noToken: !(
        //       this.component.fetchapi && this.component.fetchapi.authenticate
        //     ),
        //   }
        // ).then(function (urldata) {
        //   // console.log(_self);
        //   _self.assign(urldata);
        // });
        if (this.component.trigger.prefetch.init) {
          let flag = await this.executeJS();
          if (flag) {
          // console.log('SDC called on init', this.key);
          Formio.makeStaticRequest(
            this.interpolate(this.component.fetchapi.url),
            (this.component.fetchapi.method || "get").toUpperCase(),
            null,
            {
              headers: this.requestHeaders,
              noToken: !(
                this.component.fetchapi && this.component.fetchapi.authenticate
              ),
            }
          ).then(function (urldata) {
            // console.log(_self);
            _self.assign(urldata);
          });
        }
        }
        this.on(
          this.key,
          async (data) => {
            if (_self.component.trigger && _self.component.trigger.beforefetch) {
              console.log("Executing beforefetch Javascript URL - Button Click");
              this.evaluate(_self.component.trigger.beforefetch.panel.javascript)
            }
            let flag = await this.executeJS();
            if (flag) {
            // console.log(data.detail);
            Formio.makeStaticRequest(
              this.interpolate(this.component.fetchapi.url),
              (this.component.fetchapi.method || "get").toUpperCase(),
              null,
              {
                headers: this.requestHeaders,
                noToken: !(
                  this.component.fetchapi && this.component.fetchapi.authenticate
                ),
              }
            ).then(function (urldata) {
              // console.log(_self);
              _self.assign(urldata);
            });
          }
          },
          true
        );
        break;
      case "workflow":
        console.log("inside switch workflow ");

        if (this.component.trigger.prefetch.init) {
          if (_self.component.trigger && _self.component.trigger.beforefetch) {
            console.log("Executing beforefetch Javascript workflow - Init");
            this.evaluate(_self.component.trigger.beforefetch.panel.javascript)
          }
          // console.log('SDC called on init', this.key);
          if (!this.builderMode) {
            let flag = await this.executeJS();
            if (flag) {
              await this.executeworkflow(this.component, this.data, data.detail)
            }
          }
        }
        if (this.component.trigger.prefetch.triggerbycomponent) {
          this.on(
            this.key,
            async (data) => {
              // console.log(data.detail);
              let flag = false;
              if (_self.component.trigger && _self.component.trigger.beforefetch) {
                console.log("Executing beforefetch Javascript workflow - Button Click");
                if (!this.builderMode) {
                  let flag = await this.executeJS();
                  if (flag) {
                    await this.executeworkflow(this.component, this.data, data.detail)
                  }
                }
              }
            },
            true
          );
        }
        break;
    }
    return super.attach(element);
  }
  assign(DataSourcedata) {
    if (this.component.trigger.prefetch.init) {
      this.data = DataSourcedata;
      this.emitcustomformioevent(this.component.trigger.postfetch.event, this.component, DataSourcedata);
    }
    if (this.component.trigger.prefetch.triggerbycomponent) {
      this.data = DataSourcedata;
      this.emitcustomformioevent(this.component.trigger.postfetch.event, this.component, DataSourcedata);
    }
  }
  async executeJS() {
    if (this.component.trigger && this.component.trigger.beforefetch) {
      console.log("Executing beforefetch Javascript code");
      await this.evaluate(this.component.trigger.beforefetch.panel.javascript);
    }
    return true;
  }
  executeworkflow(component, formdata, eventdata) {
    let nodecontainer = document.getElementsByClassName(
      `sdc-spinner`
    );
    // console.log(nodecontainer)
    if (nodecontainer && nodecontainer.length > 0) {
      nodecontainer[0].style.display = 'flex';
      nodecontainer[0].style.justifyContent = 'center';
    }

    if (!eventdata) {
      eventdata = formdata.equipNr;
    }
    let _self = this;
    let formId;
    let wfname = '';
    if (window.formId) {
      formId = window.formId;
    } else {
      const URLArray = window.location.pathname.split('/');
      const urlmatrixstrings = URLArray[3].split(';');
      formId = urlmatrixstrings[0];
    }
    if (component.fetchapi) {
      wfname = component.fetchapi.workflowname;
    }
    let inputparams = `formId=${formId}&formdata=${encodeURIComponent(JSON.stringify(formdata))}`;
    if (eventdata) {
      if (typeof eventdata === 'object') {
        inputparams = inputparams + `&eventdata=${encodeURIComponent(JSON.stringify(eventdata))}`;
      } else {
        inputparams = inputparams + `&eventdata=${encodeURIComponent(eventdata)}`;
      }

    }
    window.ump.logInfo('sdc', 'executeworkflow()','jwt token - '+ localStorage.getItem('token'));
    Formio.makeStaticRequest(`${localStorage.getItem('UMP_url')}/UMP/API/v3/applications/DIGITAL_FORMS/workflow/${wfname}?${inputparams}`, 'POST', null, {

      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token'),
        'Accept': 'application/json',
        'Content-type': 'application/json'
      }
    }).then((function (workflowdata) {
    window.ump.logInfo('sdc', 'executeworkflow()','workflowdata - '+ JSON.stringify(workflowdata));

      if (nodecontainer && nodecontainer.length > 0) {
        nodecontainer[0].style.display = "none";
        nodecontainer[0].style.justifyContent = '';
      }

      if (workflowdata) {
        try {
          _self.data.error = workflowdata.error;
          // console.log('Object Assign',Object.assign(_self.data,JSON.parse(workflowdata.data)));
          if (workflowdata.data !== "") {
            _self.data = Object.assign(_self.data, JSON.parse(workflowdata.data));
          } else {
            _self.data = Object.assign(_self.data, workflowdata.data);
          }
        } catch (err) {
          if (workflowdata.data !== "") {
            _self.data = Object.assign(_self.data, JSON.parse(JSON.stringify(workflowdata.data)));
          }
        }
        _self.emitcustomformioevent(
          _self.component.trigger.postfetch.event,
          _self.component,
          _self.data
        );
      }
    }))
      .catch((error) => {
        window.ump.logInfo('sdc', 'executeworkflow()','workflowdata  error- '+ JSON.stringify(error));
        if (nodecontainer && nodecontainer.length > 0) {
          nodecontainer[0].style.display = "none";
          nodecontainer[0].style.justifyContent = '';
        }
        console.error('Error occured while fetching workflow data:', error);
        this.emitcustomformioevent('WorkflowExecuteError', this.component, error);
      }
      );
  }
  emitcustomformioevent(eventtype, component, data) {
    window.eventtype = data;
    this.emit(eventtype, {
      component: component,
      data: data
    });
    this.evaluate(this.component.trigger.postfetch.panel.javascript)

    let eventCustom = new CustomEvent('sdcDoneEvent',
      {
        detail: {
          type: eventtype,
          component: component,
          data: data
        }
      });
    document.dispatchEvent(eventCustom);
  }
}
SmartDataComponent.requestHeaders = {
  get: function () {
    let _self = this,
      DataSourceHeaders = new Formio.Headers();
    if (this.component.fetchapi && this.component.fetchapi.headers) {
      try {
        this.component.fetchapi.headers.forEach((function (header) {
          header.key && DataSourceHeaders.set(n.key, _self.interpolate(header.value)
          );
        }
        ));
      } catch (e) {
        console.warn(e.message);
      }
    }
    return DataSourceHeaders;
  },
  enumerable: true,
  configurable: true
};
Formio.Components.addComponent('smartdata', SmartDataComponent);
