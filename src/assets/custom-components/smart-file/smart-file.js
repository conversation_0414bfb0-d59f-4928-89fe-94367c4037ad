let Camera;
let webViewCamera = navigator.camera || Camera;
const uniqueName = FormioUtils.uniqueName;
const FieldComponent = Formio.Components.components.field;
const Templates = Formio.Templates;
let imagUrl = "";
let isfileBrowse = false
const imageTypes = ['image/gif', 'image/jpeg', 'image/png'];
// import fileProcessor from '../../providers/processor/fileProcessor';

var BMF = window.form.BMF
var download = window.form.downloadFile;

let htmlCanvasElement;
if (typeof window !== 'undefined') {
  htmlCanvasElement = window.HTMLCanvasElement;
} else if (typeof global !== 'undefined') {
  htmlCanvasElement = global.HTMLCanvasElement;
}

if (htmlCanvasElement && !htmlCanvasElement.prototype.toBlob) {
  Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {
    value: function (callback, type, quality) {
      var canvas = this;
      setTimeout(function () {
        var binStr = atob(canvas.toDataURL(type, quality).split(',')[1]),
          len = binStr.length,
          arr = new Uint8Array(len);

        for (var i = 0; i < len; i++) {
          arr[i] = binStr.charCodeAt(i);
        }

        callback(new Blob([arr], {
          type: type || 'image/png'
        }));
      });
    }
  });
}

class SmartFileComponent extends FieldComponent {
  static schema(...extend) {
    return FieldComponent.schema({
      type: 'file',
      label: 'Upload',
      key: 'file',
      image: false,
      privateDownload: false,
      imageSize: '200',
      filePattern: '*',
      fileMinSize: '0KB',
      fileMaxSize: '1GB',
      uploadOnly: false,
    }, ...extend);
  }

  static get builderInfo() {
    return {
      title: 'File',
      group: 'premium',
      icon: 'file',
      documentation: '/userguide/#file',
      weight: 100,
      schema: SmartFileComponent.schema(),
    };
  }


  init() {

    Templates.templates.bootstrap.customtemplate = {
      form: `
   {% if (!ctx.self.imageUpload) { %}
<ul class="list-group list-group-striped">
  <li class="list-group-item list-group-header hidden-xs hidden-sm">
    <div class="row">
      {% if (!ctx.disabled) { %}
      <div class="col-md-1"></div>
      {% } %}
      <div class="col-md-{% if (ctx.self.hasTypes) { %}7{% } else { %}9{% } %}"><strong>{{ctx.t('File Name')}}</strong>
      </div>
      <div class="col-md-2"><strong>{{ctx.t('Size')}}</strong></div>
      {% if (ctx.self.hasTypes) { %}
      <div class="col-md-2"><strong>{{ctx.t('Type')}}</strong></div>
      {% } %}
    </div>
  </li>
  {% ctx.files.forEach(function(file) { %}
  <li class="list-group-item">
    <div class="row">
      {% if (!ctx.disabled) { %}
      <div class="col-md-1">
      </div>
      {% } %}
      <div class="col-md-{% if (ctx.self.hasTypes) { %}7{% } else { %}9{% } %}">
        {% if (ctx.component.uploadOnly) { %}
        {{file.originalName || file.name}}
        {% } else { %}
        <a href="{{file.url || '#'}}" target="_blank" ref="fileLink">{{file.originalName || file.name}}</a>
        {% } %}
      </div>
      <div class="col-md-2">{{ctx.fileSize(file.size)}}</div>
      {% if (ctx.self.hasTypes && !ctx.disabled) { %}
      <div class="col-md-2">
        <select class="file-type" ref="fileType">
          {% ctx.component.fileTypes.map(function(type) { %}
          <option class="test" value="{{ type.value }}" {% if (type.label===file.fileType) { %}selected="selected" {% }
            %}>{{ type.label }}</option>
          {% }); %}
        </select>
      </div>
      {% } %}
      {% if (ctx.self.hasTypes && ctx.disabled) { %}
      <div class="col-md-2">{{file.fileType}}</div>
      {% } %}
    </div>
  </li>
  {% }) %}
</ul>
{% } else { %}
<div class="un-file-uploades">
  {% ctx.files.forEach(function(file) { %}
  {% if (file.type.split('/')[0] === 'image') { %}
  <div class="un-file-upload-item">
    <div class="un-file-upload-item-image">
      <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
        alt="{{file.originalName || file.name}}" style="width: {{ctx.component.imageSize}}px; pointer-events: all">
    </div>
    {% if (!ctx.disabled) { %}
    <div class="un-file-upload-item-actions">
      <button class="ui button basic icon tiny" ref="cropImage">
        <i class="icon crop"></i>
      </button>
      <button class="ui button basic icon tiny" ref="annotateImage">
        <i class="icon pencil square"></i>
      </button>
      <button class="ui button basic icon tiny" ref="removeLink">
        <i class="icon trash"></i>
      </button>
      {% } %}
    </div>
  </div>
  {% } %}
  {% if (file.type.split('/')[0] !== 'image') { %}
  <div class="un-file-upload-item">
    <div class="un-file-upload-item">
      <div class="un-file-upload-item-image">
        {% if (ctx.component.imageSize === '100%') { %}
        <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
          alt="{{file.originalName || file.name}}" style="width:{{ctx.component.imageSize}}">
        {% } else { %}
        <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
          alt="{{file.originalName || file.name}}" style="width:{{ctx.component.imageSize}}px">
        {% } %}
      </div>
      {% if (!ctx.disabled) { %}
      <div class="un-file-upload-item-actions">
        {% if (file.type.split('/')[1] === 'pdf') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file pdf outline icon"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'plain') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon address card outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'zip') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'mp4') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'doc') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file word outline"></i>
        </button>
        {% } %}
        <button class="ui button basic icon tiny" ref="removeLink">
          <i class="{{ctx.iconClass('trash')}}"></i>
        </button>
      </div>
      {% } %}
    </div>
  </div>
  {% } %}
  {% }) %}
</div>
{% } %}
{% if (!ctx.disabled && (ctx.component.multiple || !ctx.files.length)) { %}
{% if (!ctx.self.cameraMode) { %}
<div class="fileSelector un-file-selector" ref="fileDrop" {{ctx.fileDropHidden ? 'hidden' : '' }}>
    {% if (ctx.self.imageUpload) { %}

    {% } %}
    <a href="#" ref="fileBrowse" class="browse"><i class="fa fa-camera"></i>{{ctx.t('Take Picture')}}</a>
  <div ref="fileProcessingLoader" class="loader-wrapper" style="display:none;">
    <div class="loader text-center"></div>
  </div>
</div>
{% } else { %}
<div class="video-container">
  <video class="video" autoplay="true" ref="videoPlayer"></video>
</div>
<div class="upload-camera-btns">
  <button class="ui button primary" ref="takePictureButton"><i class="icon camera"></i>
    {{ctx.t('Take Picture')}}</button>
  <button class="ui button primary" ref="toggleCameraMode">{{ctx.t('Switch to file upload')}}</button>
</div>
{% } %}
{% } %}

{% ctx.statuses.forEach(function(status) { %}
<div class="file {{ctx.statuses.status === 'error' ? ' has-error' : ''}}">
  <!-- <div class="row">
    <div class="fileName col-form-label col-sm-10">{{status.originalName}} <i class="{{ctx.iconClass('trash')}}"
        style="font-size:22px !important;color:red;" ref="fileStatusRemove"></i></div>
    <div class="fileSize col-form-label col-sm-2 text-right">{{ctx.fileSize(status.size)}}</div>
  </div> -->
  <div class="row">
    <div class="col-sm-12">
      {% if (status.status === 'progress') { %}
      <div class="progress">
        <div class="progress-bar" role="progressbar" aria-valuenow="{{status.progress}}" aria-valuemin="0"
          aria-valuemax="100" style="width: {{status.progress}}%">
          <span class="sr-only">{{status.progress}}% {{ctx.t('Complete')}}</span>
        </div>
      </div>
      {% } else if (status.status === 'error') { %}
      <div class="alert alert-danger uv-upload-alert bg-{{status.status}}">{{ctx.t(status.message)}}</div>
      {% } else { %}
      <div class="bg-{{status.status}}">{{ctx.t(status.message)}}</div>
      {% } %}
    </div>
  </div>
</div>
{% }) %}
{% if (!ctx.component.storage || ctx.support.hasWarning) { %}
<div class="alert alert-warning">
  {% if (!ctx.component.storage) { %}
  <p>{{ctx.t('No storage has been set for this field. File uploads are disabled until storage is set up.')}}</p>
  {% } %}
  {% if (!ctx.support.filereader) { %}
  <p>{{ctx.t('File API & FileReader API not supported.')}}</p>
  {% } %}
  {% if (!ctx.support.formdata) { %}
  <p>{{ctx.t("XHR2's FormData is not supported.")}}</p>
  {% } %}
  {% if (!ctx.support.progress) { %}
  <p>{{ctx.t("XHR2's upload progress isn't supported.")}}</p>
  {% } %}
</div>
{% } %}

{% if (ctx.component.hideLabel && ctx.component.validate.required) { %}
<p class="form-required-text-field">This field is required</p>
{% } %}
`
    };

    Templates.templates.bootstrap.filetemplate = {
      form: `
    {% if (!ctx.self.imageUpload) { %}
<ul class="list-group list-group-striped">
  <li class="list-group-item list-group-header hidden-xs hidden-sm">
    <div class="row">
      {% if (!ctx.disabled) { %}
      <div class="col-md-1"></div>
      {% } %}
      <div class="col-md-{% if (ctx.self.hasTypes) { %}7{% } else { %}9{% } %}"><strong>{{ctx.t('File Name')}}</strong>
      </div>
      <div class="col-md-2"><strong>{{ctx.t('Size')}}</strong></div>
      {% if (ctx.self.hasTypes) { %}
      <div class="col-md-2"><strong>{{ctx.t('Type')}}</strong></div>
      {% } %}
    </div>
  </li>
  {% ctx.files.forEach(function(file) { %}
  <li class="list-group-item">
    <div class="row">
      {% if (!ctx.disabled) { %}
      <div class="col-md-1">
      </div>
      {% } %}
      <div class="col-md-{% if (ctx.self.hasTypes) { %}7{% } else { %}9{% } %}">
        {% if (ctx.component.uploadOnly) { %}
        {{file.originalName || file.name}}
        {% } else { %}
        <a href="{{file.url || '#'}}" target="_blank" ref="fileLink">{{file.originalName || file.name}}</a>
        {% } %}
      </div>
      <div class="col-md-2">{{ctx.fileSize(file.size)}}</div>
      {% if (ctx.self.hasTypes && !ctx.disabled) { %}
      <div class="col-md-2">
        <select class="file-type" ref="fileType">
          {% ctx.component.fileTypes.map(function(type) { %}
          <option class="test" value="{{ type.value }}" {% if (type.label===file.fileType) { %}selected="selected" {% }
            %}>{{ type.label }}</option>
          {% }); %}
        </select>
      </div>
      {% } %}
      {% if (ctx.self.hasTypes && ctx.disabled) { %}
      <div class="col-md-2">{{file.fileType}}</div>
      {% } %}
    </div>
  </li>
  {% }) %}
</ul>
{% } else { %}
<div class="un-file-uploades">
  {% ctx.files.forEach(function(file) { %}
  {% if (file.type.split('/')[0] === 'image') { %}
  <div class="un-file-upload-item">
    <div class="un-file-upload-item-image">
      <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
        alt="{{file.originalName || file.name}}" style="width: {{ctx.component.imageSize}}px; pointer-events: all">
    </div>
    {% if (!ctx.disabled) { %}
    <div class="un-file-upload-item-actions">
      <button class="ui button basic icon tiny" ref="cropImage">
        <i class="icon crop"></i>
      </button>
      <button class="ui button basic icon tiny" ref="annotateImage">
        <i class="icon pencil square"></i>
      </button>
      <button class="ui button basic icon tiny" ref="removeLink">
        <i class="icon trash"></i>
      </button>
      {% } %}
    </div>
  </div>
  {% } %}
  {% if (file.type.split('/')[0] !== 'image') { %}
  <div class="un-file-upload-item">
    <div class="un-file-upload-item">
      <div class="un-file-upload-item-image">
        {% if (ctx.component.imageSize === '100%') { %}
        <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
          alt="{{file.originalName || file.name}}" style="width:{{ctx.component.imageSize}}">
        {% } else { %}
        <img ref="fileImage" class="fileImageData" src="" id="{{file.compnentId}}"
          alt="{{file.originalName || file.name}}" style="width:{{ctx.component.imageSize}}px">
        {% } %}
      </div>
      {% if (!ctx.disabled) { %}
      <div class="un-file-upload-item-actions">
        {% if (file.type.split('/')[1] === 'pdf') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file pdf outline icon"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'plain') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon address card outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'zip') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'mp4') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file outline"></i>
        </button>
        {% } %}
        {% if (file.type.split('/')[1] === 'doc') { %}
        <button class="ui button basic icon tiny" ref="annotateImage">
          <i class="icon file word outline"></i>
        </button>
        {% } %}
        <button class="ui button basic icon tiny" ref="removeLink">
          <i class="{{ctx.iconClass('trash')}}"></i>
        </button>
      </div>
      {% } %}
    </div>
  </div>
  {% } %}
  {% }) %}
</div>
{% } %}
{% if (!ctx.disabled && (ctx.component.multiple || !ctx.files.length)) { %}
{% if (ctx.self.useWebViewCamera) { %}
<div class="fileSelector un-file-selector">
  <button class="ui button primary" ref="galleryButton"><i class="icon book"></i> {{ctx.t('Gallery')}}</button>
  <button class="ui button primary" ref="cameraButton"><i class="icon camera"></i> {{ctx.t('Camera')}}</button>
</div>
{% } else if (!ctx.self.cameraMode) { %}
<div class="fileSelector un-file-selector" ref="fileDrop" {{ctx.fileDropHidden ? 'hidden' : '' }}>
  <span class="un-upload-icon-box">
    <i class="{{ctx.iconClass('cloud-upload')}}"></i>
  </span>
  <p class="un-upload-text"><a href="#" ref="fileBrowse"
      class="browse">{{ctx.t('Click to upload, ')}}</a>{{ctx.t('drop files to attach')}}</p>
  <p class="un-upload-text">{{ctx.t('or')}}
    {% if (ctx.self.imageUpload) { %}
    <a href="#" ref="toggleCameraMode">{{ctx.t('Use Camera')}}</a>
    {% } %}
  </p>
  <div ref="fileProcessingLoader" class="loader-wrapper" style="display:none;">
    <div class="loader text-center"></div>
  </div>
</div>
{% } else { %}
<div class="video-container">
  <video class="video" autoplay="true" ref="videoPlayer"></video>
</div>
<div class="upload-camera-btns">
  <button class="ui button primary" ref="takePictureButton"><i class="icon camera"></i>
    {{ctx.t('Take Picture')}}</button>
  <button class="ui button primary" ref="toggleCameraMode">{{ctx.t('Switch to file upload')}}</button>
</div>
{% } %}
{% } %}
{% ctx.statuses.forEach(function(status) { %}
<div class="file {{ctx.statuses.status === 'error' ? ' has-error' : ''}}">
  <!-- <div class="row">
    <div class="fileName col-form-label col-sm-10">{{status.originalName}} <i class="{{ctx.iconClass('trash')}}"
        style="font-size:22px !important;color:red;" ref="fileStatusRemove"></i></div>
    <div class="fileSize col-form-label col-sm-2 text-right">{{ctx.fileSize(status.size)}}</div>
  </div> -->
  <div class="row">
    <div class="col-sm-12">
      {% if (status.status === 'progress') { %}
      <div class="progress">
        <div class="progress-bar" role="progressbar" aria-valuenow="{{status.progress}}" aria-valuemin="0"
          aria-valuemax="100" style="width: {{status.progress}}%">
          <span class="sr-only">{{status.progress}}% {{ctx.t('Complete')}}</span>
        </div>
      </div>
      {% } else if (status.status === 'error') { %}
      <div class="alert alert-danger uv-upload-alert bg-{{status.status}}">{{ctx.t(status.message)}}</div>
      {% } else { %}
      <div class="bg-{{status.status}}">{{ctx.t(status.message)}}</div>
      {% } %}
    </div>
  </div>
</div>
{% }) %}
{% if (!ctx.component.storage || ctx.support.hasWarning) { %}
<div class="alert alert-warning">
  {% if (!ctx.component.storage) { %}
  <p>{{ctx.t('No storage has been set for this field. File uploads are disabled until storage is set up.')}}</p>
  {% } %}
  {% if (!ctx.support.filereader) { %}
  <p>{{ctx.t('File API & FileReader API not supported.')}}</p>
  {% } %}
  {% if (!ctx.support.formdata) { %}
  <p>{{ctx.t("XHR2's FormData is not supported.")}}</p>
  {% } %}
  {% if (!ctx.support.progress) { %}
  <p>{{ctx.t("XHR2's upload progress isn't supported.")}}</p>
  {% } %}
</div>
{% } %}

{% if (ctx.component.hideLabel && ctx.component.validate.required) { %}
<p class="form-required-text-field">This field is required</p>
{% } %}
`
    };

    super.init();
    webViewCamera = navigator.camera || Camera;
    const fileReaderSupported = (typeof FileReader !== 'undefined');
    const formDataSupported = typeof window !== 'undefined' ? Boolean(window.FormData) : false;
    const progressSupported = (typeof window !== 'undefined' && window.XMLHttpRequest) ? ('upload' in new XMLHttpRequest) : false;

    this.support = {
      filereader: fileReaderSupported,
      formdata: formDataSupported,
      hasWarning: !fileReaderSupported || !formDataSupported || !progressSupported,
      progress: progressSupported,
    };
    // Called when our files are ready.
    this.filesReady = new Promise((resolve, reject) => {
      this.filesReadyResolve = resolve;
      this.filesReadyReject = reject;
    });
    this.cameraMode = false;
    this.statuses = [];
    this.fileDropHidden = false;
  }

  get dataReady() {
    return this.filesReady || Promise.resolve();
  }

  get defaultSchema() {
    return SmartFileComponent.schema();
  }

  loadImage(fileInfo) {
    if (this.component.privateDownload) {
      fileInfo.private = true;
    }
    return this.fileService.downloadFile(fileInfo).then((result) => result.url);
  }

  get emptyValue() {
    return [];
  }

  getValueAsString(value) {
    if (_.isArray(value)) {
      return _.map(value, 'originalName').join(', ');
    }

    return _.get(value, 'originalName', '');
  }

  getValue() {
    return this.dataValue;
  }

  get defaultValue() {
    const value = super.defaultValue;
    return Array.isArray(value) ? value : [];
  }

  get hasTypes() {
    return this.component.fileTypes &&
      Array.isArray(this.component.fileTypes) &&
      this.component.fileTypes.length !== 0 &&
      (this.component.fileTypes[0].label !== '' || this.component.fileTypes[0].value !== '');
  }

  get fileDropHidden() {
    return this._fileBrowseHidden;
  }

  set fileDropHidden(value) {
    if (typeof value !== 'boolean' || this.component.multiple) {
      return;
    }
    this._fileBrowseHidden = value;
  }

  render() {
    console.log("window.platform.isAndroid = " + window.platform.isAndroid)
    console.log("window.platform.isAndroid = " + window.platform.iosPlatform)
    let templateType = 'filetemplate';
    if (window.platform.iosPlatform) {
      templateType = 'customtemplate';
    }
    return super.render(this.renderTemplate(templateType, {
      fileSize: this.fileSize,
      files: this.dataValue || [],
      statuses: this.statuses,
      disabled: this.disabled,
      support: this.support,
      fileDropHidden: this.fileDropHidden
    }));
  }

  getVideoStream() {
    const options = {
      audio: false,
      video: {
        width: {
          min: 640,
          ideal: 1920
        },
        height: {
          min: 360,
          ideal: 1080
        },
        aspectRatio: {
          ideal: 16 / 9
        },
      },
    };
    return navigator.mediaDevices.getUserMedia(options);
  }

  stopVideoStream(videoStream) {
    videoStream.getVideoTracks().forEach((track) => track.stop());
  }

  getFrame(videoPlayer) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      canvas.height = videoPlayer.videoHeight;
      canvas.width = videoPlayer.videoWidth;
      const context = canvas.getContext('2d');
      context.drawImage(videoPlayer, 0, 0);
      canvas.toBlob(resolve);
    });
  }

  startVideo() {

    this.getVideoStream()
      .then((stream) => {
        console.log("inside getVideoStream")
        this.videoStream = stream;
        const {
          videoPlayer
        } = this.refs;
        console.log("after videoPlayer recieved")
        if (!videoPlayer) {
          alert('Video player not found in template.')
          console.warn('Video player not found in template.');
          this.cameraMode = false;
          this.redraw();
          return;
        }

        videoPlayer.srcObject = stream;
        console.log("videoPlayer.srcObject = " + videoPlayer.srcObject);
        const width = parseInt(this.component.webcamSize) || 320;
        videoPlayer.setAttribute('width', width);
        videoPlayer.play();

      })
      .catch((err) => {
        console.error(err);
        this.cameraMode = false;
        this.redraw();
      });
  }

  stopVideo() {
    if (this.videoStream) {
      this.stopVideoStream(this.videoStream);
      this.videoStream = null;
    }
  }

  takePicture() {
    const {
      videoPlayer
    } = this.refs;
    if (!videoPlayer) {
      console.warn('Video player not found in template.');
      this.cameraMode = false;
      this.redraw();
      return;
    }

    this.getFrame(videoPlayer)
      .then((frame) => {
        frame.name = `photo-${Date.now()}.png`;
        this.compressFile([frame])
        this.cameraMode = false;
        this.redraw();
      });
  }

  browseFiles(attrs = {}) {
    console.log("attrs = " + JSON.stringify(attrs))
    return new Promise((resolve) => {
      const obj = Object.assign({
        type: 'file',
        style: 'height: 0; width: 0; visibility: hidden;image-orientation: from-image;',
        tabindex: '-1',
      }, attrs);
      const fileInput = this.ce('input', obj);
      document.body.appendChild(fileInput);

      fileInput.addEventListener('change', () => {
        resolve(fileInput.files);
        document.body.removeChild(fileInput);
      }, true);

      // There is no direct way to trigger a file dialog. To work around this, create an input of type file and trigger
      // a click event on it.
      if (typeof fileInput.trigger === 'function') {
        fileInput.trigger('click');
      } else {
        fileInput.click();
      }
    });
  }

  set cameraMode(value) {
    this._cameraMode = value;
    if (value) {
      this.startVideo();
    } else {
      this.stopVideo();
    }
  }

  get cameraMode() {
    return this._cameraMode;
  }

  get useWebViewCamera() {
    return this.imageUpload && webViewCamera;
  }

  get imageUpload() {
    return Boolean(this.component.image);
  }

  get browseOptions() {
    const options = {};

    if (this.component.multiple) {
      options.multiple = true;
    }
    //use "accept" attribute only for desktop devices because of its limited support by mobile browsers
    if (!this.isMobile.any) {
      const filePattern = this.component.filePattern.trim() || '';
      const imagesPattern = 'image/*';

      if (this.imageUpload && (!filePattern || filePattern === '*')) {
        options.accept = imagesPattern;
      } else if (this.imageUpload && !filePattern.includes(imagesPattern)) {
        options.accept = `${imagesPattern},${filePattern}`;
      } else {
        options.accept = filePattern;
      }
    }

    return options;
  }

  deleteFile(fileInfo) {
    const { options = {} } = this.component;

    if (fileInfo && (['url', 'indexeddb'].includes(this.component.storage))) {
      const { fileService } = this;
      if (fileService && typeof fileService.deleteFile === 'function') {
        fileService.deleteFile(fileInfo, options);
      }
      else {
        const formio = this.options.formio || (this.root && this.root.formio);

        if (formio) {
          formio.makeRequest('', fileInfo.url, 'delete');
        }
      }
    }
  }

  attach(element) {
    this.loadRefs(element, {
      fileDrop: 'single',
      fileBrowse: 'single',
      galleryButton: 'single',
      cameraButton: 'single',
      takePictureButton: 'single',
      toggleCameraMode: 'single',
      videoPlayer: 'single',
      fileLink: 'multiple',
      removeLink: 'multiple',
      fileStatusRemove: 'multiple',
      fileImage: 'multiple',
      fileType: 'multiple',
      fileProcessingLoader: 'single',
      cropImage: 'multiple',
      annotateImage: 'multiple'
    });
    // Ensure we have an empty input refs. We need this for the setValue method to redraw the control when it is set.
    this.refs.input = [];
    const superAttach = super.attach(element);

    if (this.refs.fileDrop) {
      if (!this.statuses.length) {
        this.refs.fileDrop.removeAttribute('hidden');
      }
      const element = this;
      this.addEventListener(this.refs.fileDrop, 'dragover', function (event) {
        this.className = 'fileSelector fileDragOver';
        event.preventDefault();
      });
      this.addEventListener(this.refs.fileDrop, 'dragleave', function (event) {
        this.className = 'fileSelector';
        event.preventDefault();
      });
      this.addEventListener(this.refs.fileDrop, 'drop', function (event) {
        this.className = 'fileSelector';
        event.preventDefault();
        element.upload(event.dataTransfer.files);
        // return false;
      });
    }

    if (this.refs.fileBrowse) {
      this.addEventListener(this.refs.fileBrowse, 'click', (event) => {
        event.preventDefault();
        if (!this.component.multiple && this.statuses.some(fileUpload => fileUpload.status === 'progress')) {
          return;
        }
        this.statuses = [];
        this.browseFiles(this.browseOptions)
          .then((files) => {
            for(let i=0; i<files.length;i++){
            let file = files[i];
            const reader = new FileReader();
            reader.onloadend = (evt) => {
              console.log("onloadend = " + evt)
              const blob = new Blob([new Uint8Array(evt.target.result)], {
                type: file.type
              });
              blob.name = file.name;
              blob.lastModified = file.lastModified;
              blob.lastModifiedDate = file.lastModifiedDate;
              console.log("blob instanceOf Blob = " + blob)
              isfileBrowse = true;
              this.compressFile([blob])
            };
            reader.readAsArrayBuffer(file);
          }
          });
      });
    }

    this.refs.fileLink.forEach((fileLink, index) => {
      this.addEventListener(fileLink, 'click', (event) => {
        event.preventDefault();
        this.getFile(this.dataValue[index]);
      });
    });

    this.refs.removeLink.forEach((removeLink, index) => {
      this.addEventListener(removeLink, 'click', (event) => {
        const fileInfo = this.dataValue[index];
        this.deleteFile(fileInfo);
        event.preventDefault();
        this.splice(index);
        this.redraw();
      });
    });

    this.refs.fileImage.forEach((fileImage, index) => {
      this.addEventListener(fileImage, 'click', (event) => {
        const fileInfo = this.dataValue[index];
         console.log("fileInfo "+ JSON.stringify(fileInfo));
         let eventCustom = new CustomEvent('openImagePreview', {
          detail: {
            data: fileInfo
          }
        });
        console.log("dispatching openImagePreview event");
        document.dispatchEvent(eventCustom);

      });
    });

    this.refs.cropImage.forEach((cropImage, index) => {
      this.addEventListener(cropImage, 'click', (event) => {
        const fileInfo = this.dataValue[index];
        console.log("cropImage clicked")
        let that = this;
        const sampleImage = document.getElementsByClassName('fileImageData')
        for (var i = 0; i < sampleImage.length; i++) {
          if (sampleImage[i].alt == fileInfo.originalName && (sampleImage[i].id == fileInfo.compnentId || this.component.key == fileInfo.key)) {
            showCropArea(sampleImage[i]);
          }
        }

        function showCropArea(target) {
          new cropro.Activator.addKey('CRPR-P238-M097-0451');
          const cropArea = new cropro.CropArea(target);
          cropArea.displayMode = "popup";
          cropArea.styles.settings.toolbarBackgroundColor = '#2586c7';
          cropArea.styles.settings.toolbarBackgroundHoverColor = '#286f9f';
          cropArea.addRenderEventListener((imgURL) => (
            target.src = imgURL,
            console.log('cropped imgURL length = ' + imgURL.length),
            that.dataValue[index].url = imgURL,
            b64CroppedImagetoBlob(imgURL.split(",")[1], 'image/jpeg', that.dataValue[index].originalName)
          ));
          cropArea.show();
        }

        function b64CroppedImagetoBlob(b64Data, contentType, originalName) {
          var sliceSize = 512;
          var byteCharacters = atob(b64Data);
          var byteArrays = [];

          for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
            var slice = byteCharacters.slice(offset, offset + sliceSize);

            var byteNumbers = new Array(slice.length);
            for (var i = 0; i < slice.length; i++) {
              byteNumbers[i] = slice.charCodeAt(i);
            }
            var byteArray = new Uint8Array(byteNumbers);

            byteArrays.push(byteArray);
          }

          var blob = new Blob(byteArrays, {
            type: contentType
          });
          blob.name = originalName
          console.log(`Cropped Image file size ${blob.size / 1024 / 1024} MB`);
          that.upload([blob], true);
        }
      });
    });

    this.refs.annotateImage.forEach((annotateImage, index) => {
      this.addEventListener(annotateImage, 'click', (event) => {
        const fileInfo = this.dataValue[index];
        console.log("annotateImage clicked")
        const sampleImage = document.getElementsByClassName('fileImageData')
        for (var i = 0; i < sampleImage.length; i++) {
          if (sampleImage[i].alt == fileInfo.originalName && (sampleImage[i].id == fileInfo.compnentId || this.component.key == fileInfo.key)) {
            showMarkerArea(sampleImage[i]);
          }
        }

      }, false)

      function showMarkerArea(target) {
        new markerjs2.Activator.addKey('MJS2-P013-M397-4100');
        const markerArea = new markerjs2.MarkerArea(target);
        // open the editor in a popup
        markerArea.settings.displayMode = 'popup';
        markerArea.uiStyleSettings.toolbarBackgroundColor = '#2586c7';
        markerArea.uiStyleSettings.toolbarBackgroundHoverColor = '#286f9f';
        markerArea.addRenderEventListener((imgURL) =>
          (
            target.src = imgURL,
            that.dataValue[index].url = imgURL,
            b64AnnotatedImagetoBlob(imgURL.split(",")[1], 'image/jpeg', that.dataValue[index].originalName)
          ));
        markerArea.show();
      }


      function b64AnnotatedImagetoBlob(b64Data, contentType, originalName) {
        contentType = contentType || '';
        var sliceSize = 512;
        var byteCharacters = atob(b64Data);
        var byteArrays = [];

        for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
          var slice = byteCharacters.slice(offset, offset + sliceSize);

          var byteNumbers = new Array(slice.length);
          for (var i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          var byteArray = new Uint8Array(byteNumbers);

          byteArrays.push(byteArray);
        }

        var blob = new Blob(byteArrays, {
          type: contentType
        });
        blob.name = originalName;
        console.log(`Annotatted Image file size ${blob.size / 1024 / 1024} MB`);
        that.upload([blob], true);
      }
    });

    this.refs.fileStatusRemove.forEach((fileStatusRemove, index) => {
      this.addEventListener(fileStatusRemove, 'click', (event) => {
        event.preventDefault();

        const fileUpload = this.statuses[index];
        _.pull(this.filesUploading, fileUpload.originalName);

        if (fileUpload.abort) {
          fileUpload.abort();
        }

        this.statuses.splice(index, 1);
        this.redraw();
      });
    });

    if (this.refs.galleryButton && webViewCamera) {
      this.addEventListener(this.refs.galleryButton, 'click', (event) => {
        event.preventDefault();
        webViewCamera.getPicture((success) => {
          window.resolveLocalFileSystemURL(success, (fileEntry) => {
            fileEntry.file((file) => {
              const reader = new FileReader();
              reader.onloadend = (evt) => {
                const blob = new Blob([new Uint8Array(evt.target.result)], {
                  type: file.type
                });
                blob.name = file.name;
                this.upload([blob]);
              };
              reader.readAsArrayBuffer(file);
            });
          });
        }, (err) => {
          console.error(err);
        }, {
          sourceType: webViewCamera.PictureSourceType.PHOTOLIBRARY,
        });
      });
    }

    if (this.refs.cameraButton && webViewCamera) {
      this.addEventListener(this.refs.cameraButton, 'click', (event) => {
        event.preventDefault();
        webViewCamera.getPicture((success) => {
          window.resolveLocalFileSystemURL(success, (fileEntry) => {
            fileEntry.file((file) => {
              const reader = new FileReader();
              reader.onloadend = (evt) => {
                const blob = new Blob([new Uint8Array(evt.target.result)], {
                  type: file.type
                });
                blob.name = file.name;
                this.upload([blob]);
              };
              reader.readAsArrayBuffer(file);
            });
          });
        }, (err) => {
          console.error(err);
        }, {
          sourceType: webViewCamera.PictureSourceType.CAMERA,
          encodingType: webViewCamera.EncodingType.PNG,
          mediaType: webViewCamera.MediaType.PICTURE,
          saveToPhotoAlbum: true,
          correctOrientation: false,
        });
      });
    }

    if (this.refs.takePictureButton) {
      this.addEventListener(this.refs.takePictureButton, 'click', (event) => {
        event.preventDefault();
        this.takePicture();
      });
    }
    let currentObj = this;
    if (this.refs.toggleCameraMode) {
      this.addEventListener(this.refs.toggleCameraMode, 'click', (event) => {
        event.preventDefault();
        let id = currentObj.id
        isfileBrowse = false;

        // let isbrowser = (!document.URL.startsWith('http:') || document.URL.startsWith('http://localhost:8000'))
        // if (window.platform.isAndroid && !isbrowser) {
        if (window.platform.isAndroid) {
          window.isListenerSet = false;
          let eventCustom = new CustomEvent('openCamera', {
            detail: {
              controlId: id
            }
          });
          console.log("dispatching openCamera event");
          document.dispatchEvent(eventCustom);
          this.redraw();
        } else {
          this.cameraMode = !this.cameraMode;
          this.redraw();
        }
      });
    }

    this.refs.fileType.forEach((fileType, index) => {
      this.dataValue[index].fileType = this.dataValue[index].fileType || this.component.fileTypes[0].label;

      this.addEventListener(fileType, 'change', (event) => {
        event.preventDefault();

        const fileType = this.component.fileTypes.find((typeObj) => typeObj.value === event.target.value);

        this.dataValue[index].fileType = fileType.label;
      });
    });

    const fileService = this.fileService;
    if (fileService) {
      const loadingImages = [];
      this.filesReady = new Promise((resolve, reject) => {
        this.filesReadyResolve = resolve;
        this.filesReadyReject = reject;
      });
      this.refs.fileImage.forEach((image, index) => {
        loadingImages.push(this.loadImage(this.dataValue[index]).then((url) => (image.src = url)));
      });
      if (loadingImages.length) {
        Promise.all(loadingImages).then(() => {
          this.filesReadyResolve();
        }).catch(() => this.filesReadyReject());
      } else {
        this.filesReadyResolve();
      }
    }
    let that = this;
    document.addEventListener('capureImageData', function (event) {
      console.log("event.detail.controlId = " + event.detail.controlId)
      if (that.component.id == event.detail.controlId) {
        if (!window.isListenerSet) {
          window.resolveLocalFileSystemURL(event.detail.imageData, (fileEntry) => {
            fileEntry.file((file) => {
              console.log("fileEntry = " + file)
              const reader = new FileReader();
              reader.onloadend = (evt) => {
                console.log("onloadend = " + evt)
                const blob = new Blob([new Uint8Array(evt.target.result)], {
                  type: file.type
                });
                blob.name = file.name;
                console.log("file.name = "+ file.name);
                console.log("blob instanceOf Blob = " + blob)
                that.compressFile([blob])
              };
              reader.readAsArrayBuffer(file);
            });
          })
          window.isListenerSet = true;
        }
      }
    }, false);


    document.addEventListener('capureImageDataForAndroidBrowser', function (event) {
      console.log("event.detail.controlId capureImageDataForAndroidBrowser = " + event.detail.controlId)
      if (that.component.id == event.detail.controlId) {
        if (!window.isListenerSet) {
          that.compressFile([event.detail.imageData])
          // window.resolveLocalFileSystemURL(event.detail.imageData, (fileEntry) => {
          //   fileEntry.file((file) => {
          //     console.log("fileEntry = " + file)
          //     const reader = new FileReader();
          //     reader.onloadend = (evt) => {
          //       console.log("onloadend = " + evt)
          //       const blob = new Blob([new Uint8Array(evt.target.result)], {
          //         type: file.type
          //       });
          //       blob.name = file.name;
          //       console.log("blob instanceOf Blob = " + blob)
          //       that.compressFile([blob])
          //     };
          //     reader.readAsArrayBuffer(file);
          //   });
          // })
          window.isListenerSet = true;
        }
      }
    }, false);


    return superAttach;
  }

  /* eslint-disable max-len */
  fileSize(a, b, c, d, e) {
    return `${(b = Math, c = b.log, d = 1024, e = c(a) / c(d) | 0, a / b.pow(d, e)).toFixed(2)} ${e ? `${'kMGTPEZY'[--e]}B` : 'Bytes'}`;
  }

  /* eslint-enable max-len */

  /* eslint-disable max-depth */
  globStringToRegex(str) {
    str = str.replace(/\s/g, '');

    let regexp = '', excludes = [];
    if (str.length > 2 && str[0] === '/' && str[str.length - 1] === '/') {
      regexp = str.substring(1, str.length - 1);
    }
    else {
      const split = str.split(',');
      if (split.length > 1) {
        for (let i = 0; i < split.length; i++) {
          const r = this.globStringToRegex(split[i]);
          if (r.regexp) {
            regexp += `(${r.regexp})`;
            if (i < split.length - 1) {
              regexp += '|';
            }
          }
          else {
            excludes = excludes.concat(r.excludes);
          }
        }
      }
      else {
        if (str.startsWith('!')) {
          excludes.push(`^((?!${this.globStringToRegex(str.substring(1)).regexp}).)*$`);
        }
        else {
          if (str.startsWith('.')) {
            str = `*${str}`;
          }
          regexp = `^${str.replace(new RegExp('[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\-]', 'g'), '\\$&')}$`;
          regexp = regexp.replace(/\\\*/g, '.*').replace(/\\\?/g, '.');
        }
      }
    }
    return { regexp, excludes };
  }

  /* eslint-enable max-depth */

  translateScalars(str) {
    if (typeof str === 'string') {
      if (str.search(/kb/i) === str.length - 2) {
        return parseFloat(str.substring(0, str.length - 2) * 1024);
      }
      if (str.search(/mb/i) === str.length - 2) {
        return parseFloat(str.substring(0, str.length - 2) * 1024 * 1024);
      }
      if (str.search(/gb/i) === str.length - 2) {
        return parseFloat(str.substring(0, str.length - 2) * 1024 * 1024 * 1024);
      }
      if (str.search(/b/i) === str.length - 1) {
        return parseFloat(str.substring(0, str.length - 1));
      }
      if (str.search(/s/i) === str.length - 1) {
        return parseFloat(str.substring(0, str.length - 1));
      }
      if (str.search(/m/i) === str.length - 1) {
        return parseFloat(str.substring(0, str.length - 1) * 60);
      }
      if (str.search(/h/i) === str.length - 1) {
        return parseFloat(str.substring(0, str.length - 1) * 3600);
      }
    }
    return str;
  }

  validatePattern(file, val) {
    if (!val) {
      return true;
    }
    const pattern = this.globStringToRegex(val);
    let valid = true;
    if (pattern.regexp && pattern.regexp.length) {
      const regexp = new RegExp(pattern.regexp, 'i');
      valid = (!_.isNil(file.type) && regexp.test(file.type)) ||
        (!_.isNil(file.name) && regexp.test(file.name));
    }
    valid = pattern.excludes.reduce((result, excludePattern) => {
      const exclude = new RegExp(excludePattern, 'i');
      return result && (_.isNil(file.type) || !exclude.test(file.type)) &&
        (_.isNil(file.name) || !exclude.test(file.name));
    }, valid);
    return valid;
  }

  validateMinSize(file, val) {
    return file.size + 0.1 >= this.translateScalars(val);
  }

  validateMaxSize(file, val) {
    return file.size - 0.1 <= this.translateScalars(val);
  }
  compressFile(files) {
    let that = this;
    console.log("files[0] = " + JSON.stringify(files[0]));
    console.log('originalFile instanceof Blob', files[0] instanceof Blob); // true
    console.log(`originalFile size ${files[0].size / 1024 / 1024} MB`);

    //default file compression values
    let maxFileSizeMB = 2;
    let MaximumCompressionHeightOrWidth = 2048;

    // setting component compression values
    if (that.component.MaximumCompressionHeightOrWidth) {
      MaximumCompressionHeightOrWidth = that.component.MaximumCompressionHeightOrWidth
    }
    if (that.component.fileCompressionSize) {
      maxFileSizeMB = that.component.fileCompressionSize;
    }
    console.log("maxFileSizeMB = " + maxFileSizeMB)
    console.log("MaximumCompressionHeightOrWidth = " + MaximumCompressionHeightOrWidth)

    var options = {
      maxSizeMB: maxFileSizeMB,
      maxWidthOrHeight: MaximumCompressionHeightOrWidth,
      useWebWorker: true
    }

    const acceptedImageTypes = ['image/gif', 'image/jpeg', 'image/png'];
 
     if(files && files[0] && acceptedImageTypes.includes(files[0].type)){
      imageCompression(files[0], options)
      .then(function (compressedFile) {
        console.log('compressedFile instanceof Blob', compressedFile instanceof Blob); // true
        console.log(`compressedFile size ${compressedFile.size / 1024 / 1024} MB`); // smaller than maxSizeMB
        that.blobToBase64(compressedFile).then(res => {
          imagUrl = res;
          that.upload([compressedFile])
        });
      })
      .catch(function (error) {
        console.log(error.message);
        alert(error.message)
      });
    }else{
      that.upload(files)
    }
  }

  upload(files, replace) {
    // this.component.storage = 'SmartStorage';
    if (!this.component.multiple) {
      if (this.statuses.length) {
        this.statuses = [];
      }
      files = Array.prototype.slice.call(files, 0, 1);
    }
    if (this.component.storage && files && files.length) {
      // if (this.component.storage && files) {
        Array.prototype.forEach.call(files, async(file) => {
          const fileName = uniqueName(file.name, this.component.fileNameTemplate, this.evalContext());
          const escapedFileName = file.name ? file.name.replaceAll('<', '&lt;').replaceAll('>', '&gt;') : file.name;
          const fileUpload = {
            abort: () => null,
            originalName: escapedFileName,
            name: fileName,
            size: file.size,
            status: 'info',
            message: this.t('Processing file. Please wait...'),
            hash: '',
          };
          if (this.root.form.submissionRevisions === 'true') {
            this.statuses.push(fileUpload);
            this.redraw();
            const bmf = new window.form.BMF();
            const hash = await new Promise((resolve, reject) => {
              this.emit('fileUploadingStart');
              bmf.md5(file, (err, md5)=>{
                if (err) {
                  return reject(err);
                }
                return resolve(md5);
              });
            });
            this.emit('fileUploadingEnd');
            fileUpload.hash = hash;
          }
  
          // Check if file with the same name is being uploaded
          if (!this.filesUploading) {
            this.filesUploading = [];
          }
        // Check if file with the same name is being uploaded
        var fileWithSameNameUploaded = false;
        if (window.platform.iosPlatform) {
          fileWithSameNameUploaded = (this.dataValue.some(fileStatus => fileStatus.url === imagUrl));
        }else{
          fileWithSameNameUploaded = isfileBrowse ? (this.dataValue.some(fileStatus => fileStatus.originalName === file.name)) : (this.dataValue.some(fileStatus => fileStatus.url === imagUrl));
        }
        const fileWithSameNameUploadedWithError = this.statuses.findIndex(fileStatus =>
          fileStatus.url === imagUrl &&
          fileStatus.status === 'error'
        );

        if (fileWithSameNameUploaded && !replace) {
          fileUpload.status = 'error';
          fileUpload.message = this.t('File with the same name is already uploaded');
        }

        if (fileWithSameNameUploadedWithError !== -1) {
          this.statuses.splice(fileWithSameNameUploadedWithError, 1);
          this.redraw();
        }

        // Check file pattern
        if (this.component.filePattern && !this.validatePattern(file, this.component.filePattern)) {
          fileUpload.status = 'error';
          fileUpload.message = this.t('File is the wrong type; it must be {{ pattern }}', {
            pattern: this.component.filePattern,
          });
        }

        // Check file minimum size
        if (this.component.fileMinSize && !this.validateMinSize(file, this.component.fileMinSize)) {
          fileUpload.status = 'error';
          fileUpload.message = this.t('File is too small; it must be at least {{ size }}', {
            size: this.component.fileMinSize,
          });
        }

        // Check file maximum size0
        if (this.component.fileMaxSize && !this.validateMaxSize(file, this.component.fileMaxSize)) {
          fileUpload.status = 'error';
          fileUpload.message = this.t('File is too big; it must be at most {{ size }}', {
            size: this.component.fileMaxSize,
          });
        }

        // Get a unique name for this file to keep file collisions from occurring.
        const dir = this.interpolate(this.component.dir || '');
        const {
          fileService
        } = this;
        if (!fileService) {
          fileUpload.status = 'error';
          fileUpload.message = this.t('File Service not provided.');
        }
        if (this.root.form.submissionRevisions !== 'true') {
          this.statuses.push(fileUpload);
          this.redraw();
        }

        if (fileUpload.status !== 'error') {
          if (this.component.privateDownload) {
            file.private = true;
          }
          const {
            storage,
            options = {}
          } = this.component;
          const url = this.interpolate(this.component.url, {
            file: fileUpload
          });
          let groupKey = null;
          let groupPermissions = null;

          //Iterate through form components to find group resource if one exists
          this.root.everyComponent((element) => {
            if (element.component ? element.component.submissionAccess : null || element.component ? element.component.defaultPermission : null) {
              groupPermissions = !element.component.submissionAccess ? [{
                type: element.component.defaultPermission,
                roles: [],
              }, ] : element.component.submissionAccess;

              groupPermissions.forEach((permission) => {
                groupKey = ['admin', 'write', 'create'].includes(permission.type) ? element.component.key : null;
              });
            }
          });

          const fileKey = this.component.fileKey || 'file';
          const groupResourceId = groupKey ? this.currentForm.submission.data[groupKey]._id : null;
          let processedFile = null;

          if (this.root.options.fileProcessor) {
            try {
              if (this.refs.fileProcessingLoader) {
                this.refs.fileProcessingLoader.style.display = 'block';
              }
              const fileProcessorHandler = fileProcessor(this.fileService, this.root.options.fileProcessor);
              processedFile = await fileProcessorHandler(file, this.component.properties);
            } catch (err) {
              fileUpload.status = 'error';
              fileUpload.message = this.t('File processing has been failed.');
              this.fileDropHidden = false;
              this.redraw();
              return;
            }
            finally {
              if (this.refs.fileProcessingLoader) {
                this.refs.fileProcessingLoader.style.display = 'none';
              }
            }
            this.refs.fileProcessingLoader.style.display = 'none';
          }
          let count = 0;
          const multipartOptions = this.component.useMultipartUpload && this.component.multipart ? {
            ...this.component.multipart,
            progressCallback: (total) => {
              count++;
              fileUpload.status = 'progress';
              fileUpload.progress = parseInt(100 * count / total);
              delete fileUpload.message;
              this.redraw();
            },
            changeMessage: (message) => {
              fileUpload.message = message;
              this.redraw();
            },
          } : false;

          fileUpload.message = this.t('Starting upload...');
          this.redraw();

          const filePromise = fileService.uploadFile(
              storage,
              processedFile || file,
              fileName,
              dir,
              // Progress callback
              (evt) => {
                fileUpload.status = 'progress';
                fileUpload.progress = parseInt(100.0 * evt.loaded / evt.total);
                delete fileUpload.message;
                this.redraw();
              },
              url,
              options,
              fileKey,
              groupPermissions,
              groupResourceId,
              // Upload start callback
              () => {
                // this.fileDropHidden = true;
                this.emit('fileUploadingStart', filePromise);
              },
              // Abort upload callback
              (abort) => fileUpload.abort = abort,
            multipartOptions
          ).then((fileInfo) => {
              const index = this.statuses.indexOf(fileUpload);
              if (index !== -1) {
                this.statuses.splice(index, 1);
              }
              fileInfo.originalName = escapedFileName;
              fileInfo.hash = fileUpload.hash;
              fileInfo.compnentId = this.component.id
              if (!this.hasValue()) {
                this.dataValue = [];
              }
              if (replace) {
                let ind;
                for (let i = 0; i < this.dataValue.length; i++) {
                  if (this.dataValue[i].originalName == fileInfo.originalName) {
                    console.log("original name matches")
                    ind = i;
                  }
                }
                if (ind !== -1) {
                  let originalFileId = this.dataValue[ind].id;
                  console.log("originalFileId = " + originalFileId)
                  this.dataValue[ind] = fileInfo;
                  let eventCustom = new CustomEvent('deleteOriginalFileFromDB', {
                    detail: {
                      fileId: originalFileId,
                    }
                  });
                  document.dispatchEvent(eventCustom);
                }
              } else {
                this.dataValue.push(fileInfo)
              }
              _.pull(this.filesUploading, fileInfo.originalName);
              this.fileDropHidden = false;
              this.redraw();
              this.triggerChange();
              this.emit('fileUploadingEnd', filePromise);
            })
            .catch((response) => {
              fileUpload.status = 'error';
              fileUpload.message = typeof response === 'string' ? response : response.toString();
              delete fileUpload.progress;
              this.fileDropHidden = false;
              _.pull(this.filesUploading, file.name);
              this.redraw();
              this.emit('fileUploadingEnd', filePromise);
            });
        }
        else {
          this.filesUploading.splice(this.filesUploading.indexOf(file.name),1);
        }
      });
    }
  }

  blobToBase64(blob) {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  }

  getFile(fileInfo) {
    const {
      options = {}
    } = this.component;
    const {
      fileService
    } = this;
    if (!fileService) {
      return alert('File Service not provided');
    }
    if (this.component.privateDownload) {
      fileInfo.private = true;
    }
    fileService.downloadFile(fileInfo, options).then((file) => {
        if (file) {
          if (['base64', 'indexeddb'].includes(file.storage)) {
            window.form.downloadFile(file.url, file.originalName || file.name, file.type);
          } else {
            window.open(file.url, '_blank');
          }
        }
      })
      .catch((response) => {
        // Is alert the best way to do this?
        // User is expecting an immediate notification due to attempting to download a file.
        alert(response);
      });
  }

  focus() {
    if ('beforeFocus' in this.parent) {
      this.parent.beforeFocus(this);
    }

    if (this.refs.fileBrowse) {
      this.refs.fileBrowse.focus();
    }
  }

  destroy() {
    this.stopVideo();
    super.destroy();
  }

}

Formio.registerComponent('file', SmartFileComponent);