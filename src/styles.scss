/* You can add global styles to this file, and also import other style files */
$gutter: 1rem; //for primeflex grid system
@import "assets/layout/styles/layout/layout.scss";

/* PrimeNG */
@import "../node_modules/primeng/resources/primeng.min.css";
@import "../node_modules/primeflex/primeflex.scss";
@import "../node_modules/primeicons/primeicons.css";

// font awesome
@import "~@fortawesome/fontawesome-free/css/all.css";

// JSON Editor
@import '~jsoneditor/dist/jsoneditor.min.css';

//RETE BACKGROUND
.rete-background.default {
    z-index: -5;
    // margin-top: 50px !important;
   // background-size: 14px 14px !important;
    // width: 500px !important;
     height: calc(100vh - 118px) !important
  }
 
  //node sockets css
  .socket{
   // border: 0px solid #000 !important;
  //  border-width:4px !important;
  }
  .socket:hover{
    border-width:2.5px !important;
  }
.animationpath {
  stroke:antiquewhite;
  stroke-width:2.5;
  fill: none;
  stroke-dasharray: 20;
  animation: dash 3s linear infinite reverse both running;
}

  /* SOCKETS */
  .socket.Start {
    display: inline-block;
    cursor: pointer;
    // border-radius: 0px !important;
    // width: 30px !important;
    // height: 8px !important;
    border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
    margin: 0px !important;
    vertical-align: middle;
    background: rgb(10, 10, 10) !important;
    position: relative;
  }

  .socket.Success {
    display: inline-block;
    cursor: pointer;
    // border-radius: 0px !important;
    // width: 30px !important;
    // height: 8px !important;
    border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
    margin: 0px !important;
    vertical-align: middle;
    background: #0a0a0a !important;
    position: relative;
  }
  .socket.Error {
    display: inline-block;
    cursor: pointer;
    // border-radius: 0px !important;
    // width: 30px !important;
    // height: 8px !important;
    border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
    margin: 0px !important;
    vertical-align: middle;
   // border: 0px !important;
    background: #bb0000 !important;
    position: relative;
  }
  .connection.socket-input-error,.connection.socket-output-error .main-path {
    stroke: #bb0000!important;
     cursor: pointer;
}
.connection.socket-input-error, .connection.socket-output-error .main-path:hover {
    stroke-width: 2px !important;
    cursor: pointer;

}
.connection .main-path:hover{
   stroke-width: 2px !important;
  //  stroke: #240eef!important;
   cursor: pointer;
  stroke-dasharray: 20;
  animation: dash 5s linear infinite reverse;
}
@keyframes dash {
  from {  stroke-dashoffset: 0;  }
   to   {  stroke-dashoffset: 500;  }
}
  .socket.Integration {
    display: inline-block;
    
   border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
    margin: 0px !important;
    vertical-align: middle;
    box-sizing: border-box;
    background: rgb(0, 0, 0) !important;
     position: relative;
    // display: inline-block;
    // cursor: pointer;
    // border: 1px solid #fff;
    // border-radius: 10px !important;
    // width: 20px !important;
    // height: 20px !important;
    // margin-bottom: 4px !important;
    // vertical-align: middle;
    // z-index: 2;
    // box-sizing: border-box;
    // background: teal !important;
}
.socket.GeneralInput {
  display: inline-block;
  cursor: pointer;
  //border: 0px !important;
  // border-radius:0px !important;
  // width: 30px !important;
  // height: 8px !important;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  vertical-align: middle;
  box-sizing: border-box;
  background: black !important;
  position: relative;
}
.socket.Input {
  display: inline-block;
  cursor: pointer;
  // border-radius: 0px !important;
  // width: 30px !important;
  // height: 8px !important;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  background: #0d0d0d !important;
  position: relative;
}
.socket.FormsOperation {
  display: inline-block;
  cursor: pointer;
  // border-radius: 0px !important;
  // width: 30px !important;
  // height: 8px !important;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  vertical-align: middle;
  box-sizing: border-box;
  background: rgb(5, 5, 5) !important;
  position: relative;
}
.socket.Combine {
  display: inline-block;
  cursor: pointer;
  border-radius: 10px !important;
  // width: 30px !important;
  // height: 8px !important;
  width:12px !important;
  height:12px !important;
  margin: 0px !important;
  vertical-align: middle;
  box-sizing: border-box;
  background: rgb(5, 5, 5) !important;
  position: relative;
}
.socket.response {
  display: inline-block;
  cursor: pointer;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  background: #0d0d0d !important;
  position: relative;
}
.socket.Then {
  display: inline-block;
  cursor: pointer;
  // border-radius: 0px !important;
  // width: 30px !important;
  // height: 8px !important;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  vertical-align: middle;
  z-index: 2;
  box-sizing: border-box;
  background: darkgreen !important;
  position: relative;
}
.socket.Else {
  display: inline-block;
  cursor: pointer;
  // border-radius: 0px !important;
  // width: 30px !important;
  // height: 8px !important;
  border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
  margin: 0px !important;
  vertical-align: middle;
  box-sizing: border-box;
  background:  rgb(18, 41, 245) !important;
}
.socket.Approved {
// border-radius: 0px !important;
// width: 30px !important;
// height: 8px !important;
border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
padding: 0px !important;
background: #007E33 !important;
margin: 0px !important;
position: relative;
}
.socket.Rejected {
// border-radius: 0px !important;
// width: 30px !important;
// height: 8px !important;
border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
padding: 0px !important;
background: orangered !important;
margin: 0px !important;
position: relative;
}
.socket.ReturnToRequestor {
// border-radius: 0px !important;
// width: 30px !important;
// height: 8px !important;
border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
padding: 0px !important;
background: black !important;
margin: 0px !important;
position: relative;
}
.socket.NextApprovar {
// border-radius: 0px !important;
// width: 30px !important;
// height: 8px !important;
border-radius: 10px !important;
    width:12px !important;
    height:12px !important;
padding: 0px !important;
background:  yellowgreen !important;
margin: 0px !important;
position: relative;
} 
//rete socket connections 1:1 and n:1
.connection .main-path {
    fill: none ;
    stroke: #000 !important;
    // stroke: var(--primary-color) !important;
    stroke-width: 1px !important;
    width: auto !important;
    height: auto !important;
    z-index: 100;
}
.dashedpathanimatoion {
    fill: none;
    stroke-width:0;
  }
//START,ERROR,SUCCESS node container
.start-node-container{
  min-height: 50px;
  width: 150px;
  // background-color: #ffffff;
  border: 1px solid rgb(224, 223, 223);
  text-align: center;
}
.start-node-container:hover{
 cursor: move;
}

//Other Nodes Container size
  .node-container {
    width: 350px;
    // background-color: #ffffff;
    border: 1px solid rgb(224, 223, 223);
    // text-align: center;
  }
  .node-container:hover{
    cursor: auto;
  }
  .nodes-input-socket{
    margin-bottom: -8px;
    margin-left: 169px;
  // text-align: center;
  }
  .nodes-input-socket-merge{
    display: flex;
    margin-bottom: -8px;
  }

  .nodes-input-socket-response{
    margin-bottom: -12px;
    margin-left: 178px;
  }
  .nodes-outputs{
    display: flex;
    margin-top: -12px !important;
  }
  .output-sockets-error{
    margin-left: 77.5px;
  }
  .output-sockets-success{
    margin-left: 57px;
  }

  .cardBody{
    min-height: 100px !important;
     max-height: 330px !important;  
     overflow-y: auto; 
   }

   /* open panel */
  .startPanel .p-panel .p-panel-header .p-panel-header-icon{
    display: none !important;
  }
  .startPanel .p-panel.p-panel-toggleable .p-panel-header{
    padding:0px;
    height: 48px;
    cursor: move;
  }
  .startPanel .p-panel .p-panel-header{
    // background: #fff;
    border-top-left-radius:0px;
    border-top-right-radius:0px;
    border: none;
    border-bottom: 1px solid #dee2e6;
    cursor: move;
  }
  //panel
.panelOpen .p-panel .p-panel-header .p-panel-header-icon{
  margin-right: 16px !important;
}
.panelOpen .p-panel .p-panel-header .p-panel-header-icon:focus{
  box-shadow:0 0 0 0rem !important;
}
.panelOpen .p-panel.p-panel-toggleable .p-panel-header{
  padding:0px;
  height: 48px;
  cursor: move;
}
.panelOpen .p-panel .p-panel-header{
  // background: #fff;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border: none;
  border-bottom: 1px solid #dee2e6;
  cursor: move;
}
.panelOpen .p-panel .p-panel-content{
  padding: 0px 0px 10px 7px;
  // min-height: 190px !important;
  border: none;

}
.panelOpenCombain .p-panel .p-panel-header .p-panel-header-icon{
  margin-right: 16px !important;
  display: none !important;
}
.panelOpenCombain .p-panel .p-panel-header .p-panel-header-icon:focus{
  box-shadow:0 0 0 0rem !important;
}
.panelOpenCombain .p-panel.p-panel-toggleable .p-panel-header{
  padding:0px;
  height: 48px;
  cursor: move;
}
.panelOpenCombain .p-panel .p-panel-header{
  padding: 0px !important;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border: none;
  border-bottom: 1px solid #dee2e6;
  cursor: move;
}
.panelOpenCombain .p-panel .p-panel-content{
  padding: 0px;
  // min-height: 190px !important;
  border: none;

}



.formPanelOpen .p-panel .p-panel-content{
  padding: 0px 0px 10px 0px;
  min-height: 130px !important;
} 


::-webkit-scrollbar {
  width: 4px !important;
  height: 5px !important;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: grey;
  box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
}
::-webkit-scrollbar-thumb:window-inactive {
   background: grey;
}


.integrationNode .p-dropdown{
  width: -webkit-fill-available !important;
}
.approvalNode .p-dropdown{
  width: -webkit-fill-available !important;
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}
.multiSelect .p-multiselect {
  width: -webkit-fill-available !important;
} 
/* panel button */
.nodeBtn .p-button {
 //  background: rgb(254, 254, 254) !important;
   margin-top: 10px;
}
.nodeBtnTest .p-button.p-button-text{
  background: rgb(254, 254, 254) !important;
}
.nodeBtnbg .p-button.p-button-text{
  background: rgb(254, 254, 254) !important;
  margin-top: 10px;
}
.nodeBtn1 .p-button.p-button-text{
  background: rgb(254, 254, 254) !important;
}
  // .required:after {
  //   content: " *";
  //   color: red;
  //   }

// overlay panal
.p-overlaypanel .p-overlaypanel-content{
  padding: 10px !important;
}
.cursor{
  cursor: pointer;
}
._required:after {
  content: " *";
  color: red;
  }

  .ui.visible.sidebar {
    position: absolute;
    background: white;
}
.headerClass{
  display: inline-flex !important;
  align-items: center !important;
  padding-bottom: 30px !important;
  // text-align: end !important;

}
// .ui .primary{
//   background: var(--primary-color) !important;
// }
.ui 
// .button:hover
// .fluid:focus,
// .fluid.focus,
// .fluid:active,
// .fluid.active,
.button:focus,
.button.focus,
.button:active,
.button.active
{
  background: var(--primary-color) !important;
}

a:hover,
a:focus,
a.focus,
a:active,
a:active{
  color: var(--primary-color) !important;
}
.ui.input:active, .input.active{
  color: var(--primary-color) !important;

}

// .fileOption:hover,
// .fileOption:focus,
// .fileOption.focus,
// .fileOption:active,
// .fileOption.active{
//   background: var(--primary-color) !important;
// }


// .formio-dialog-content{
//   width:50% !important
// }
.helpdiv{
  margin-right: 10px !important;

}
.helpicon{
  opacity: 0.9 !important;
  font-weight: 700 !important;
}
.formareaWidth{
  width: 60% !important;
}
.ui.form .required.field>label:after {
  margin: 0 !important;
}
.ui.segment{
  text-align: left !important;
}
// .formbuilder .formio-component-hidden, .formio-component-form {
//   text-align: left !important;
//   border: 0 !important;
// }
.component-edit-container{
  padding: 10px !important;
  height: 100% !important;
}
.builder-sidebar-button{
  color: var(--primary-color) !important;
  font: bold !important;
}
.ui fieldset{
  border: 0 !important;
}
.formio-component-datagrid .datagrid-table {
  padding: 0 !important;
}
.ui.checkbox {
display:inline-flex !important
}

[data-tooltip]:before{
  visibility: visible;
  transform: matrix(1, 0, 0, 1, 0, 0);
  opacity: 1;
  position: static;
  box-shadow: none
  }
  
  [data-tooltip]:after {
    content: none !important;
  }
  
  .alert-danger {
    color: #721c24 !important;
    border-color: #f5c6cb !important;
    background-color: #f8d7da !important;
  }

  .choices__item.choices__item--selectable {
    overflow: visible !important;
  }

  .formio-component-modal-wrapper .open-modal-button {
    padding: 0 0 0 5px !important;
    font-size: 1rem !important;
}

button[ref="dialogCancelButton"], button[ref="dialogYesButton"]{
    cursor: pointer;
    display: inline-block;
    min-height: 1em;
    outline: 0;
    border: none;
    vertical-align: baseline;
    background: #e0e1e2 none;
    color: rgba(0,0,0,.6);
    font-family: Lato,'Helvetica Neue',Arial,Helvetica,sans-serif;
    margin: 0 0.25em 0 0;
    padding: 0.78571429em 1.5em 0.78571429em;
    text-transform: none;
    text-shadow: none;
    font-weight: 700;
    line-height: 1em;
    font-style: normal;
    text-align: center;
    text-decoration: none;
    border-radius: 0.28571429rem;
    box-shadow: 0 0 0 1px transparent inset, 0 0 0 0 rgba(34,36,38,.15) inset;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: opacity .1s ease,background-color .1s ease,color .1s ease,box-shadow .1s ease,background .1s ease;
    will-change: '';
    -webkit-tap-highlight-color: transparent;
}

button[ref="dialogYesButton"]{
  background-color: #db2828;
    color: #fff;
    text-shadow: none;
    background-image: none;
}
button[ref="removeLink"]{
font-size: 15px !important;
}
.inline { 
  display: inline-flex !important;
}

input[type='radio']:checked {
  background: var(--primary-color) !important;
}

.formio-component-datagrid .formio-datagrid-remove  {
    visibility: visible !important;
    opacity: 1 !important;
}

// button[ref="datagrid-properties-removeRow"] {
//   font-size: 10px !important;
//   i {
//     font-size: 15px !important;
//   }
// }

.ui.checkbox input[type="checkbox"], .ui.checkbox input[type="radio"] {
// opacity: 1 !important;

}
.minimap{
 // width: 160px !important;
  //height: 160px !important;
  //display: none !important;
}
.ui.form input:not([type]):focus, .ui.form input[type=date]:focus, .ui.form input[type=datetime-local]:focus, .ui.form input[type=email]:focus, .ui.form input[type=file]:focus, .ui.form input[type=number]:focus, .ui.form input[type=password]:focus, .ui.form input[type=search]:focus, .ui.form input[type=tel]:focus, .ui.form input[type=text]:focus, .ui.form input[type=time]:focus, .ui.form input[type=url]:focus, .ui.selection.dropdown:focus {
  border-color: var(--primary-color) !important;
}
.ui.checkbox input[type='checkbox']:checked + label::before {
  background: var(--primary-color) !important;
  color: white !important;
}

.formbuilder .formio-component-datasource, .formbuilder .formio-component-form, .formbuilder .formio-component-hidden {
  height: 3em;
  text-align: center;
  color: #aaa;
  padding-top: 0.5em;

}
.formbuilder .formio-component-content, .formbuilder .formio-component-datasource, .formbuilder .formio-component-form, .formbuilder .formio-component-hidden {
  border: 2px dashed #ddd;
}
.create-flow{
  height: 45px !important;
  margin-top: 12px !important;
}
.ui.primary.button, .ui.primary.buttons .button {
  background-color: var(--primary-color) !important;
}
.formio-component-htmlelement{
color:black
}
// .choices__list{
//   color:black;
// }
.component-btn-group .button.component-settings-button-paste {
  display: none !important;
}

.builder-paste-mode .button.component-settings-button-paste {
  display: inherit !important;
}
// .ui.basic.button, .ui.basic.buttons .button{
//   color: var(--text-color) !important;
// }
// .ui.checkbox input[type=checkbox] .ui.checkbox-icon{
//   transition-duration: 0.2s !important;
// color: #ffffff !important;
// font-size: 14px !important;
// }

// .heighlight-comp{
//   padding:10px;
//   border-radius:0.28571429rem;
//   box-shadow: 0 0 0 2px var(--primary-color);
// }
.border-helptext{
padding:10px;
border-radius:0.28571429rem;
border: 1px solid rgba(34, 36, 38, .15);
}
.formio-error-wrapper {
  background-color: transparent !important;
  --background: transparent !important;
  color: #9f3a38 !important;

}

.formio-error-wrapper > div > .input.is-invalid, .formio-error-wrapper > div > .input.error, .formio-error-wrapper > div > .input >.error{
  background: #fff6f6 !important;
  border-color: #e0b4b4 !important;
  color: #9f3a38 !important;
}
.formio-error-wrapper > label{
  color: #9f3a38 !important;
}
.formarea{
    overflow-y:scroll; 
    height: calc(100vh - 190px);
}
.error {
  display: flex;
  flex-direction: row;
  color: #dc2626;
  border-radius: 5px;
  margin: 4px auto auto 2px;
  gap: 2px;
  align-items: center;
  small {
    font-size: 14px !important;
  }
}