<?xml version="1.0" encoding="UTF-8"?>
<project name="Unvired_Forms_Admin" default="package" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">

	<taskdef resource="net/sf/antcontrib/antlib.xml">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.6.jar"/>
		</classpath>
	</taskdef>

    <!-- Get the release number -->
	<target name="getbuildno">
		<property environment="env" />

		<!-- Now read into the build numberfile into release.str property -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Using release number : ${release.str}"/>
	</target>


    <!-- Compiles this project's .java files into .class files. -->
    <target name="updatesource" depends="getbuildno">

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<property environment="env" />
		<propertyregex property="GIT_COMMIT_ABBREV" input="${env.GIT_COMMIT}" regexp="^.{1,9}" select="\0"/>
		<propertyregex property="VERSION_ABBREV" input="${release.str}" regexp="^.{3,13}" select="\0"/>

		<echo message="BUILD_NUMBER - BUILD_ID	: ${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}"/>
		<echo message="BUILD_URL				: ${env.BUILD_URL}"/>
		<echo message="GIT_REVISION				: ${GIT_COMMIT_ABBREV}"/>
		<echo message="GIT_URL					: ${env.GIT_URL} - ${env.GIT_BRANCH}"/>

	    <property name="source.dir" value="src/assets" />

		<exec executable="/usr/bin/jq"  failonerror="true">
			<arg value="--arg"/>
			<arg value="vers"/>
			<arg value="${release.str}"/>
			<arg value="--arg"/>
			<arg value="btnum"/>
			<arg value="${env.BUILD_NUMBER}"/>
			<arg value="--arg"/>
			<arg value="btm"/>			
			<arg value="${env.BUILD_TIMESTAMP}"/>
			<arg value="--arg"/>
			<arg value="abbr"/>
			<arg value="${GIT_COMMIT_ABBREV}"/>
			<arg value="--arg"/>
			<arg value="url"/>
			<arg value="${env.GIT_URL}"/>
			<arg value="--arg"/>
			<arg value="br"/>
			<arg value="${env.GIT_BRANCH}"/>
			<arg value=".version=$vers | .buildNumber=$btnum + &quot; - &quot; +  $btm | .revision=$abbr | .repo=$url + &quot; - &quot; + $br"/>
			<arg value="src/assets/appversion.json"/>
			<redirector output="src/assets/updatedversion.json"/>
		</exec>

		<move file="src/assets/updatedversion.json" tofile="src/assets/appversion.json" force="true"/>
    </target>

	<target name="npminstall" depends="updatesource">
		<exec executable="npm"  failonerror="true">
			<arg value="install"/>
			<arg value="--force"/>
		</exec>
	</target>

	<target name="ngbuild" depends="npminstall">
		<exec executable="npm"  failonerror="true">
			<arg value="run"/>
			<arg value="antbuild"/>
		</exec>
	</target>

	<target name="package" depends="ngbuild">
		<echo message="Creating build zip file"/>
		<zip destfile="dist/Unvired_Forms_Admin.zip" basedir="dist/turboapps-builder" includes="**/*" excludes="assets/config.json, *.map"/>
		<zip destfile="dist/Unvired_Forms_Admin_Map.zip" basedir="dist/turboapps-builder" includes="*.map"/>
    </target>

	<target name="publish" depends="package">		
		<property environment="env"/>

		<property name="pub.repo" value="apps-release"/>
		<property name="pub.repodir" value="unvired-apps-release"/>

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Publishing to artifactory repository: ${pub.repodir}"/>
		<ivy:resolve revision="${release.str}"/>
		<ivy:publish resolver="artifactory-publish" revision="${release.str}" update="true" publishivy="false">
			<artifacts pattern="dist/[artifact].[ext]" />
		</ivy:publish>

		<!-- Copy to dropbox  -->
		<exec dir="dist" executable="/Users/<USER>/Jenkins/Slave/uploadtodropbox.sh" failonerror="true">
			<arg value="Unvired_Forms_Admin.zip"/>
			<arg value="/Forms/trunk/AdminV2/Unvired_Forms_Admin.zip"/>
		</exec>
	</target> 

</project>
