#! /bin/sh                                                                                    
echo "Generating build number"                                                                                    
BUILDNUM=$(curl --silent --request POST --url https://buildnum.onunvired.com/generate --header 'Content-Type: application/json'  --data '{"project":"UNVIRED_FORMS_ADMIN_origin/master","format":"zero","new":true,"release":true,"quote":true}')
VERSTR1='{"version":'                                                                                                           
VERSTR2=',"buildNumber":"'
DATESTR=$(date +"%Y-%m-%d %H:%M:%S")
VERSTR3='","revision":"'
VERSTR4='","repo":"https://gitlab.unvired.io/*********************/angular-forms-admin.git"}'
VERSTR=$VERSTR1$BUILDNUM$VERSTR2$DATESTR$VERSTR3$DATESTR$VERSTR4
echo $VERSTR > src/assets/appversion.json 
echo $VERSTR
