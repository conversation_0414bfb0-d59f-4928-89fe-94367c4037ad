/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "extends": "./tsconfig.json",
    "compilerOptions": {
        "outDir": "./out-tsc/app",
        "types": []
    },
    "files": [
        "src/main.ts",
        "src/polyfills.ts",
        "src/app/custom-components/smart-table/smart-table.editForm.ts",
        "src/app/custom-components/select-barcode/select-barcode.editForm.ts",
        "src/app/custom-components/smart-data/new-smart-data.editForm.ts",
        "src/app/custom-components/barcode/barcode.editForm.ts",
        "src/app/custom-components/autocomplete/autocompletefield.editForm.ts",
        "src/app/custom-components/smart-button/smart-button.editForm.ts",
        "src/app/custom-components/smart-file/smart-file.editForm.ts",
        "src/app/custom-components/smart-select/smart-select.editForm.ts",
        "src/app/custom-components/smart-id/smart-id.editForm.ts",
        "src/app/custom-components/nested-component/nested-component.editForm.ts",
        "src/app/custom-components/smart-signature/signature.editForm.ts",
        "src/app/custom-components/Location/location.editForm.ts",
        "src/app/custom-components/column/column-editform.ts"

    ],
    "include": [
        "src/**/*.d.ts"
    ]
}
