{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"turboapps-builder": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["lodash", "jsoneditor", "jwt-decode", "form<PERSON><PERSON><PERSON>", "json-logic-js", "formiojs/utils"], "outputPath": "dist/turboapps-builder", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "aot": true, "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "assets/monaco-editor"}, {"glob": "**/*", "input": "node_modules/monaco-editor/min-maps", "output": "assets/min-maps/"}], "styles": ["src/styles.scss"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-typescript.js", "node_modules/less/dist/less.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "turboapps-builder:build"}, "configurations": {"production": {"browserTarget": "turboapps-builder:build:production"}, "development": {"browserTarget": "turboapps-builder:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "turboapps-builder:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "styles": ["src/styles.scss"], "scripts": ["node_modules/less/dist/less.min.js"], "assets": ["src/favicon.ico", "src/upload.php", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}}, "cli": {"analytics": false}}