{"name": "turboapps-builder", "version": "2.0.0", "scripts": {"ng": "ng", "start": "ng serve", "antbuild": "ng build turboapps-builder -c production --source-map=true", "build": "ng build turboapps-builder", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "prebuild": "buildnum.sh"}, "private": true, "dependencies": {"@angular/animations": "^16.1.0", "@angular/cdk": "^16.1.0", "@angular/common": "^16.1.0", "@angular/compiler": "^16.1.0", "@angular/core": "^16.1.0", "@angular/forms": "^16.1.0", "@angular/platform-browser": "^16.1.0", "@angular/platform-browser-dynamic": "^16.1.0", "@angular/router": "^16.1.0", "@formio/angular": "^7.0.0", "@formio/semantic": "git+https://semantic-coolify:<EMAIL>/unvired-digital-forms/semantic-ui.git", "@formio/semantic-template": "git+https://semantic-coolify:<EMAIL>/unvired-digital-forms/semantic-ui-template.git", "@fortawesome/fontawesome-free": "^6.5.1", "@fullcalendar/angular": "^6.0.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/timegrid": "^6.0.3", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^5.0.0", "@webcomponents/custom-elements": "^1.4.3", "ang-jsoneditor": "^3.1.1", "angular-split": "^17.2.0", "chart.js": "^3.3.2", "dayjs": "^1.11.10", "formiojs": "^4.18.0", "i": "^0.3.7", "json-logic-js": "^2.0.2", "jsoneditor": "^10.0.0", "jwt-decode": "^4.0.0", "moment": "^2.29.4", "ngx-angular-query-builder": "^16.0.0", "ngx-image-cropper": "^7.2.1", "ngx-monaco-editor": "^12.0.0", "npm": "^10.2.5", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "16.0.2", "prismjs": "^1.29.0", "quill": "^1.3.7", "rete": "^1.4.4", "rete-angular-render-plugin": "^0.3.0", "rete-area-plugin": "^0.2.1", "rete-auto-arrange-plugin": "^0.4.0", "rete-connection-path-plugin": "^0.3.1", "rete-connection-plugin": "^0.9.0", "rete-engine": "^2.0.0", "rete-history-plugin": "^0.2.2", "rete-minimap-plugin": "^0.3.1", "rete-profiler-plugin": "^0.2.0", "rete-reorder-nodes-plugin": "^0.3.0", "rete-vue-render-plugin": "^0.5.0", "rxjs": "~7.8.0", "stream": "^0.0.2", "tabulator-tables": "^4.9.3", "timers": "^0.1.1", "tslib": "^2.3.0", "webfontloader": "^1.6.28", "xml2js": "^0.6.2", "zone.js": "~0.13.0", "gulp": "^5.0.0", "gulp-clean-css": "^4.3.0", "gulp-less": "^5.0.0", "gulp-rename": "^2.0.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.1.5", "@angular/cli": "~16.1.5", "@angular/compiler-cli": "^16.1.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}, "volta": {"node": "18.20.4"}}